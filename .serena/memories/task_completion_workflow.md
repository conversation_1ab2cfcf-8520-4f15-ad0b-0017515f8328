# Task Completion Workflow

## When a Development Task is Completed

### 1. Code Quality Checks
```bash
# Run linting and fix issues
npm run lint

# Format code
npm run format

# Check TypeScript compilation
npm run build
```

### 2. Testing
```bash
# Run unit tests
npm run test

# Run tests with coverage
npm run test:cov

# Run E2E tests (if applicable)
npm run test:e2e

# Test Odoo integration (if changes affect Odoo connectivity)
npm run test:odoo
```

### 3. Documentation Updates
- Update API documentation if endpoints changed
- Update README if new features added
- Add/update code comments for complex logic
- Update environment variables in `.env.example` if needed

### 4. Git Workflow
```bash
# Check status
git status

# Stage changes
git add .

# Commit with descriptive message
git commit -m "feat: add new feature description"

# Push to remote (if authorized)
git push origin feature-branch
```

### 5. API Testing
```bash
# Start development server
npm run start:dev

# Test API endpoints manually or with tools like:
# - Swagger UI: http://localhost:3000/api/docs
# - Postman/Insomnia
# - curl commands
```

### 6. Performance Verification
- Check connection pool metrics: `GET /api/v1/info/pool-stats`
- Monitor API response times
- Verify memory usage doesn't increase significantly
- Test with multiple concurrent requests

### 7. Integration Testing
- Test with different Odoo versions if applicable
- Verify multi-tenant functionality works correctly
- Check authentication and authorization flows

### 8. Deployment Preparation (if applicable)
```bash
# Build for production
npm run build

# Test production build
npm run start:prod

# Verify environment variables are properly configured
```

## Quality Gates Checklist
- [ ] Code compiles without errors
- [ ] All tests pass
- [ ] Code coverage maintained or improved
- [ ] No linting errors
- [ ] Code is properly formatted
- [ ] Documentation updated
- [ ] API endpoints tested
- [ ] Performance impact assessed
- [ ] Security considerations reviewed