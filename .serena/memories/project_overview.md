# Zenoo Project Overview

## Project Purpose
Zenoo is an Odoo mobile application project that aims to create a comprehensive, user-friendly mobile and web interface for Odoo ERP systems. The project consists of:

1. **Backend**: A NestJS-based Universal Odoo Adapter that provides enterprise-grade connection pooling and multi-tenant support for various Odoo versions (13, 15, 17, 18+)
2. **Mobile App**: Planned SwiftUI iOS application (not yet implemented)
3. **Web App**: Planned Next.js web application (not yet implemented)

## Key Features
- Multi-version Odoo support (13, 15, 17, 18+)
- Multi-protocol support (XML-RPC, JSON-RPC, REST API)
- Enterprise-grade connection pooling
- Multi-tenant architecture
- Clean Architecture implementation
- CQRS pattern with event sourcing
- Real-time monitoring and health checks
- JWT authentication with encrypted password storage

## Business Model
The project follows a freemium SaaS B2B model:
- **Free tier**: Basic connectivity and CRUD operations
- **Pro tier**: Advanced features like custom dashboards and offline sync
- **Enterprise tier**: AI-powered analytics, multi-instance support, priority support

## Current Status
- Backend is well-developed with comprehensive Odoo integration
- Mobile and web frontends are planned but not yet implemented
- Project is in active development with recent migrations to modular architecture