# Project Structure

## Root Directory
```
Zenoo/
├── .serena/                  # Serena configuration
├── .vscode/                  # VS Code settings
├── backend/                  # NestJS backend application
└── Phân tích ứng dụng Odoo mobile_.md  # Project analysis document (Vietnamese)
```

## Backend Structure (Clean Architecture)
```
backend/src/
├── main.ts                   # Application entry point
├── app.module.ts            # Root application module
├── app.controller.ts        # Root controller
├── app.service.ts           # Root service
├── shared/                  # Shared kernel
│   ├── application/         # Shared use cases and DTOs
│   ├── domain/             # Shared domain entities and value objects
│   └── infrastructure/     # Shared infrastructure services
├── common/                  # Cross-cutting concerns
│   ├── dto/                # Common DTOs
│   ├── interceptors/       # Response interceptors
│   ├── services/           # Common services
│   └── interfaces/         # Common interfaces
├── infrastructure/         # External concerns
│   ├── adapters/           # Odoo adapters and protocols
│   ├── auth/               # Authentication services
│   ├── config/             # Configuration
│   ├── database/           # Database configuration
│   └── filters/            # Exception filters
├── modules/                # Domain modules
│   └── crm/                # CRM module (CQRS implementation)
│       ├── application/    # Use cases, commands, queries
│       ├── domain/         # Entities, value objects, services
│       ├── infrastructure/ # Repositories, event store
│       └── presentation/   # Controllers
└── presentation/           # API controllers
    └── controllers/        # REST API endpoints
```

## Key Directories Explained

### `/shared` - Shared Kernel
- Common domain logic used across modules
- Odoo connection management
- Base entities and value objects

### `/infrastructure/adapters/odoo`
- Universal Odoo adapter with version-specific implementations
- Connection pooling service
- Protocol implementations (XML-RPC, JSON-RPC, REST)

### `/modules/crm` - CRM Domain Module
- Complete CQRS implementation
- Event sourcing with domain events
- Advanced features like lead scoring and analytics

### Configuration Files
- `package.json` - Dependencies and scripts
- `tsconfig.json` - TypeScript configuration
- `.env.example` - Environment variables template
- `nest-cli.json` - NestJS CLI configuration