# Technology Stack

## Backend (NestJS)
- **Framework**: NestJS 11.0.1 (Node.js framework)
- **Language**: TypeScript 5.7.3
- **Architecture**: Clean Architecture with CQRS pattern
- **Database**: MongoDB with Mongoose ODM
- **Caching**: Redis with BullMQ for job queues
- **Authentication**: JWT with Passport.js
- **API Documentation**: Swagger/OpenAPI
- **Testing**: Jest for unit and E2E testing

## Planned Frontend Stack
- **Mobile**: SwiftUI for iOS (native)
- **Web**: Next.js with React (SSR/SSG)
- **Architecture**: Server-Driven UI (SDUI) pattern

## Odoo Integration
- **Protocols**: XML-RPC, JSON-RPC, REST API
- **Versions**: Support for Odoo 13, 15, 17, 18+
- **Connection**: Enterprise-grade connection pooling
- **Multi-tenant**: Support for multiple Odoo instances per user

## Development Tools
- **Node.js**: v23.6.1
- **npm**: v11.2.0
- **Git**: Version control
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **Docker**: Containerization (MongoDB setup)

## System Environment
- **OS**: macOS (Darwin 24.5.0)
- **Architecture**: ARM64 (Apple Silicon)