# Code Style and Conventions

## TypeScript Standards
- **Strict TypeScript**: Full type safety with strict mode enabled
- **Naming Conventions**:
  - Classes: PascalCase (e.g., `UniversalOdooAdapter`)
  - Methods/Variables: camelCase (e.g., `getUserConnection`)
  - Constants: UPPER_SNAKE_CASE (e.g., `MAX_POOL_SIZE`)
  - Interfaces: PascalCase with descriptive names (e.g., `OdooConnectionConfig`)
  - Files: kebab-case (e.g., `odoo-connection-pool.service.ts`)

## Architecture Patterns
- **Clean Architecture**: Strict separation of concerns with domain, application, infrastructure, and presentation layers
- **CQRS**: Command Query Responsibility Segregation with separate handlers
- **Event Sourcing**: Domain events for state changes
- **Repository Pattern**: Abstract data access with interfaces
- **Dependency Injection**: NestJS DI container for loose coupling

## Code Organization
- **Modules**: Feature-based modules (CRM, Sales, Inventory)
- **Shared Kernel**: Common domain logic in shared module
- **Infrastructure**: External concerns (database, adapters, protocols)
- **Domain**: Business logic and entities
- **Application**: Use cases and DTOs
- **Presentation**: Controllers and API endpoints

## Documentation Standards
- **API Documentation**: Comprehensive Swagger/OpenAPI specs
- **Code Comments**: JSDoc for public APIs
- **README**: Detailed setup and usage instructions
- **Type Definitions**: Explicit interfaces for all data structures

## Testing Conventions
- **Unit Tests**: Jest with `.spec.ts` suffix
- **E2E Tests**: Integration tests in `/test` directory
- **Coverage**: Aim for high test coverage
- **Mocking**: Use dependency injection for testable code