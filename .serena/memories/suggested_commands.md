# Suggested Development Commands

## Backend Development (NestJS)

### Setup and Installation
```bash
cd backend
npm install                    # Install dependencies
cp .env.example .env          # Copy environment template
```

### Development Commands
```bash
npm run start:dev             # Start development server with hot reload
npm run start:debug           # Start with debug mode
npm run build                 # Build for production
npm run start:prod            # Start production server
```

### Testing Commands
```bash
npm run test                  # Run unit tests
npm run test:watch            # Run tests in watch mode
npm run test:cov              # Run tests with coverage
npm run test:e2e              # Run end-to-end tests
npm run test:odoo             # Test Odoo connection
npm run test:odoo:direct      # Direct Odoo connection test
```

### Code Quality
```bash
npm run lint                  # Run ESLint
npm run format                # Format code with Prettier
```

### API Documentation
```bash
npm run docs                  # Show API documentation info
# Visit http://localhost:3000/api/docs for Swagger UI
```

## System Commands (macOS)

### Git Operations
```bash
git status                    # Check repository status
git add .                     # Stage all changes
git commit -m "message"       # Commit changes
git push origin main          # Push to remote
```

### Process Management
```bash
ps aux | grep node            # Find Node.js processes
kill -9 <PID>                # Kill specific process
lsof -i :3000                # Check what's using port 3000
```

### File Operations
```bash
find . -name "*.ts" -type f   # Find TypeScript files
grep -r "pattern" src/        # Search in source files
ls -la                        # List files with details
```

### MongoDB (if using local instance)
```bash
brew services start mongodb-community  # Start MongoDB
brew services stop mongodb-community   # Stop MongoDB
mongosh                               # MongoDB shell
```

### Redis (if using local instance)
```bash
brew services start redis     # Start Redis
brew services stop redis      # Stop Redis
redis-cli                     # Redis CLI
```