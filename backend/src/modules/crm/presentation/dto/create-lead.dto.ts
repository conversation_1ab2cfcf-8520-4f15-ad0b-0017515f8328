import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsOptional,
  IsNumber,
  IsArray,
  IsDateString,
  IsEnum,
  IsUrl,
  <PERSON><PERSON><PERSON>th,
  <PERSON><PERSON><PERSON>th,
  <PERSON>,
  Max,
  IsInt,
  IsPositive,
  ArrayMaxSize,
  Matches,
  ValidateIf,
} from 'class-validator';

/**
 * Create Lead DTO
 * Data Transfer Object for creating a new lead with comprehensive validation
 */
export class CreateLeadDto {
  @ApiProperty({
    description: 'Lead name (required)',
    example: '<PERSON> - <PERSON>tial Customer',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @MinLength(2, { message: 'Lead name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Lead name cannot exceed 100 characters' })
  name: string;

  @ApiPropertyOptional({
    description: 'Contact email address',
    example: '<EMAIL>',
    format: 'email',
  })
  @IsOptional()
  @IsEmail({}, { message: 'Please provide a valid email address' })
  email?: string;

  @ApiPropertyOptional({
    description: 'Contact phone number',
    example: '******-123-4567',
    pattern: '^[+]?[1-9]\\d{1,14}$',
  })
  @IsOptional()
  @IsString()
  @Matches(/^[+]?[1-9]\d{1,14}$/, {
    message: 'Phone number must be in international format (e.g., ******-123-4567)',
  })
  phone?: string;

  @ApiPropertyOptional({
    description: 'Company name',
    example: 'Acme Corporation',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'Company name cannot exceed 100 characters' })
  company?: string;

  @ApiPropertyOptional({
    description: 'Company website URL',
    example: 'https://www.acme.com',
    format: 'uri',
  })
  @IsOptional()
  @IsUrl({}, { message: 'Please provide a valid website URL' })
  website?: string;

  @ApiPropertyOptional({
    description: 'Street address',
    example: '123 Main Street',
    maxLength: 200,
  })
  @IsOptional()
  @IsString()
  @MaxLength(200, { message: 'Address cannot exceed 200 characters' })
  address?: string;

  @ApiPropertyOptional({
    description: 'City',
    example: 'New York',
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50, { message: 'City name cannot exceed 50 characters' })
  city?: string;

  @ApiPropertyOptional({
    description: 'Country',
    example: 'United States',
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50, { message: 'Country name cannot exceed 50 characters' })
  country?: string;

  @ApiPropertyOptional({
    description: 'Lead source',
    example: 'website',
    enum: ['website', 'referral', 'social_media', 'email_campaign', 'phone_call', 'trade_show', 'direct', 'other'],
    default: 'website',
  })
  @IsOptional()
  @IsEnum(['website', 'referral', 'social_media', 'email_campaign', 'phone_call', 'trade_show', 'direct', 'other'], {
    message: 'Source must be one of: website, referral, social_media, email_campaign, phone_call, trade_show, direct, other',
  })
  source?: string;

  @ApiPropertyOptional({
    description: 'Lead type',
    example: 'lead',
    enum: ['lead', 'opportunity'],
    default: 'lead',
  })
  @IsOptional()
  @IsEnum(['lead', 'opportunity'], {
    message: 'Type must be either "lead" or "opportunity"',
  })
  type?: string;

  @ApiPropertyOptional({
    description: 'Lead priority level (0=Low, 1=Medium, 2=High, 3=Urgent)',
    example: 1,
    minimum: 0,
    maximum: 3,
    default: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Priority must be an integer' })
  @Min(0, { message: 'Priority must be at least 0 (Low)' })
  @Max(3, { message: 'Priority cannot exceed 3 (Urgent)' })
  priority?: number;

  @ApiPropertyOptional({
    description: 'Expected revenue in USD',
    example: 50000,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Expected revenue must be a valid number' })
  @IsPositive({ message: 'Expected revenue must be positive' })
  expectedRevenue?: number;

  @ApiPropertyOptional({
    description: 'Probability of closing (0-100%)',
    example: 25,
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @IsInt({ message: 'Probability must be an integer' })
  @Min(0, { message: 'Probability must be at least 0%' })
  @Max(100, { message: 'Probability cannot exceed 100%' })
  probability?: number;

  @ApiPropertyOptional({
    description: 'Lead description or notes',
    example: 'Interested in our enterprise solution. Contacted through website form.',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000, { message: 'Description cannot exceed 1000 characters' })
  description?: string;

  @ApiPropertyOptional({
    description: 'ID of assigned user',
    example: 123,
    minimum: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Assigned user ID must be an integer' })
  @IsPositive({ message: 'Assigned user ID must be positive' })
  assignedUserId?: number;

  @ApiPropertyOptional({
    description: 'ID of assigned team',
    example: 5,
    minimum: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Team ID must be an integer' })
  @IsPositive({ message: 'Team ID must be positive' })
  teamId?: number;

  @ApiPropertyOptional({
    description: 'Campaign ID',
    example: 10,
    minimum: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Campaign ID must be an integer' })
  @IsPositive({ message: 'Campaign ID must be positive' })
  campaignId?: number;

  @ApiPropertyOptional({
    description: 'Source ID',
    example: 7,
    minimum: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Source ID must be an integer' })
  @IsPositive({ message: 'Source ID must be positive' })
  sourceId?: number;

  @ApiPropertyOptional({
    description: 'Medium ID',
    example: 3,
    minimum: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Medium ID must be an integer' })
  @IsPositive({ message: 'Medium ID must be positive' })
  mediumId?: number;

  @ApiPropertyOptional({
    description: 'Tags associated with the lead',
    example: ['enterprise', 'high-value', 'urgent'],
    type: [String],
    maxItems: 10,
  })
  @IsOptional()
  @IsArray({ message: 'Tags must be an array' })
  @IsString({ each: true, message: 'Each tag must be a string' })
  @ArrayMaxSize(10, { message: 'Cannot have more than 10 tags' })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Deadline date (ISO 8601 format)',
    example: '2024-12-31T23:59:59.000Z',
    format: 'date-time',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Date deadline must be a valid ISO 8601 date string' })
  dateDeadline?: string;

  @ApiPropertyOptional({
    description: 'Company ID (for multi-tenant systems)',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsInt({ message: 'Company ID must be an integer' })
  @IsPositive({ message: 'Company ID must be positive' })
  companyId?: number;

  /**
   * Custom validation: At least one contact method required
   */
  @ValidateIf((o) => !o.email && !o.phone)
  @IsString({ message: 'Either email or phone number is required' })
  _contactValidation?: never;
}
