import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsOptional,
  IsString,
  IsNumber,
  IsArray,
  IsEnum,
  IsDateString,
  IsInt,
  Min,
  Max,
  IsBoolean,
  ArrayMaxSize,
  IsPositive,
} from 'class-validator';

/**
 * Pagination DTO
 * Standard pagination parameters
 */
export class PaginationDto {
  @ApiPropertyOptional({
    description: 'Page number (1-based)',
    example: 1,
    minimum: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 20,
    minimum: 1,
    maximum: 100,
    default: 20,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Limit must be an integer' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit cannot exceed 100' })
  limit?: number = 20;

  @ApiPropertyOptional({
    description: 'Number of items to skip (alternative to page)',
    example: 0,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Offset must be an integer' })
  @Min(0, { message: 'Offset must be at least 0' })
  offset?: number;
}

/**
 * Sorting DTO
 * Standard sorting parameters
 */
export class SortingDto {
  @ApiPropertyOptional({
    description: 'Field to sort by',
    example: 'createdAt',
    enum: [
      'id',
      'name',
      'createdAt',
      'updatedAt',
      'expectedRevenue',
      'probability',
      'priority',
      'dateDeadline',
    ],
    default: 'createdAt',
  })
  @IsOptional()
  @IsEnum([
    'id',
    'name',
    'createdAt',
    'updatedAt',
    'expectedRevenue',
    'probability',
    'priority',
    'dateDeadline',
  ], {
    message: 'Sort field must be a valid field name',
  })
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'desc',
    enum: ['asc', 'desc'],
    default: 'desc',
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'], {
    message: 'Sort order must be either "asc" or "desc"',
  })
  sortOrder?: 'asc' | 'desc' = 'desc';
}

/**
 * Date Range DTO
 * For filtering by date ranges
 */
export class DateRangeDto {
  @ApiPropertyOptional({
    description: 'Start date (ISO 8601 format)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Date from must be a valid ISO 8601 date string' })
  dateFrom?: string;

  @ApiPropertyOptional({
    description: 'End date (ISO 8601 format)',
    example: '2024-12-31T23:59:59.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Date to must be a valid ISO 8601 date string' })
  dateTo?: string;
}

/**
 * Lead Filter DTO
 * Comprehensive filtering options for leads
 */
export class LeadFilterDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Filter by lead status',
    example: 'new',
    enum: ['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'won', 'lost'],
  })
  @IsOptional()
  @IsEnum(['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'won', 'lost'], {
    message: 'Status must be a valid lead status',
  })
  status?: string;

  @ApiPropertyOptional({
    description: 'Filter by lead type',
    example: 'lead',
    enum: ['lead', 'opportunity'],
  })
  @IsOptional()
  @IsEnum(['lead', 'opportunity'], {
    message: 'Type must be either "lead" or "opportunity"',
  })
  type?: string;

  @ApiPropertyOptional({
    description: 'Filter by priority level',
    example: 2,
    minimum: 0,
    maximum: 3,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Priority must be an integer' })
  @Min(0, { message: 'Priority must be at least 0' })
  @Max(3, { message: 'Priority cannot exceed 3' })
  priority?: number;

  @ApiPropertyOptional({
    description: 'Filter by assigned user ID',
    example: 123,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Assigned user ID must be an integer' })
  @IsPositive({ message: 'Assigned user ID must be positive' })
  assignedUserId?: number;

  @ApiPropertyOptional({
    description: 'Filter by team ID',
    example: 5,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Team ID must be an integer' })
  @IsPositive({ message: 'Team ID must be positive' })
  teamId?: number;

  @ApiPropertyOptional({
    description: 'Filter by source',
    example: 'website',
    enum: ['website', 'referral', 'social_media', 'email_campaign', 'phone_call', 'trade_show', 'direct', 'other'],
  })
  @IsOptional()
  @IsEnum(['website', 'referral', 'social_media', 'email_campaign', 'phone_call', 'trade_show', 'direct', 'other'], {
    message: 'Source must be a valid source type',
  })
  source?: string;

  @ApiPropertyOptional({
    description: 'Filter by tags (comma-separated or array)',
    example: ['enterprise', 'high-value'],
    type: [String],
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    }
    return value;
  })
  @IsArray({ message: 'Tags must be an array' })
  @IsString({ each: true, message: 'Each tag must be a string' })
  @ArrayMaxSize(10, { message: 'Cannot filter by more than 10 tags' })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Minimum expected revenue',
    example: 10000,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Minimum revenue must be a number' })
  @Min(0, { message: 'Minimum revenue must be at least 0' })
  minRevenue?: number;

  @ApiPropertyOptional({
    description: 'Maximum expected revenue',
    example: 100000,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Maximum revenue must be a number' })
  @Min(0, { message: 'Maximum revenue must be at least 0' })
  maxRevenue?: number;

  @ApiPropertyOptional({
    description: 'Minimum probability percentage',
    example: 25,
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Minimum probability must be an integer' })
  @Min(0, { message: 'Minimum probability must be at least 0' })
  @Max(100, { message: 'Minimum probability cannot exceed 100' })
  minProbability?: number;

  @ApiPropertyOptional({
    description: 'Maximum probability percentage',
    example: 75,
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Maximum probability must be an integer' })
  @Min(0, { message: 'Maximum probability must be at least 0' })
  @Max(100, { message: 'Maximum probability cannot exceed 100' })
  maxProbability?: number;

  @ApiPropertyOptional({
    description: 'Filter by creation date range - start date',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Created from date must be a valid ISO 8601 date string' })
  createdFrom?: string;

  @ApiPropertyOptional({
    description: 'Filter by creation date range - end date',
    example: '2024-12-31T23:59:59.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Created to date must be a valid ISO 8601 date string' })
  createdTo?: string;

  @ApiPropertyOptional({
    description: 'Filter by update date range - start date',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Updated from date must be a valid ISO 8601 date string' })
  updatedFrom?: string;

  @ApiPropertyOptional({
    description: 'Filter by update date range - end date',
    example: '2024-12-31T23:59:59.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Updated to date must be a valid ISO 8601 date string' })
  updatedTo?: string;

  @ApiPropertyOptional({
    description: 'Filter by deadline date range - start date',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Deadline from date must be a valid ISO 8601 date string' })
  deadlineFrom?: string;

  @ApiPropertyOptional({
    description: 'Filter by deadline date range - end date',
    example: '2024-12-31T23:59:59.000Z',
  })
  @IsOptional()
  @IsDateString({}, { message: 'Deadline to date must be a valid ISO 8601 date string' })
  deadlineTo?: string;

  @ApiPropertyOptional({
    description: 'Include only leads with deadlines',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  @IsBoolean({ message: 'Has deadline must be a boolean' })
  hasDeadline?: boolean;

  @ApiPropertyOptional({
    description: 'Include only overdue leads',
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  @IsBoolean({ message: 'Is overdue must be a boolean' })
  isOverdue?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by company ID',
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Company ID must be an integer' })
  @IsPositive({ message: 'Company ID must be positive' })
  companyId?: number;

  // Sorting options
  @ApiPropertyOptional({
    description: 'Field to sort by',
    example: 'createdAt',
    enum: [
      'id',
      'name',
      'createdAt',
      'updatedAt',
      'expectedRevenue',
      'probability',
      'priority',
      'dateDeadline',
    ],
    default: 'createdAt',
  })
  @IsOptional()
  @IsEnum([
    'id',
    'name',
    'createdAt',
    'updatedAt',
    'expectedRevenue',
    'probability',
    'priority',
    'dateDeadline',
  ], {
    message: 'Sort field must be a valid field name',
  })
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'desc',
    enum: ['asc', 'desc'],
    default: 'desc',
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'], {
    message: 'Sort order must be either "asc" or "desc"',
  })
  sortOrder?: 'asc' | 'desc' = 'desc';
}
