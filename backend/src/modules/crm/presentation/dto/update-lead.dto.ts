import { ApiPropertyOptional } from '@nestjs/swagger';
import { PartialType, OmitType } from '@nestjs/mapped-types';
import { CreateLeadDto } from './create-lead.dto';
import {
  IsOptional,
  IsString,
  IsEnum,
  MaxLength,
} from 'class-validator';

/**
 * Update Lead DTO
 * Data Transfer Object for updating an existing lead
 * Uses PartialType to make all CreateLeadDto properties optional
 * Omits the custom validation field that's only needed for creation
 */
export class UpdateLeadDto extends PartialType(
  OmitType(CreateLeadDto, ['_contactValidation'] as const)
) {
  @ApiPropertyOptional({
    description: 'Reason for the update',
    example: 'Updated contact information after client meeting',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Update reason cannot exceed 500 characters' })
  updateReason?: string;

  @ApiPropertyOptional({
    description: 'Update status for tracking changes',
    example: 'contact_updated',
    enum: [
      'contact_updated',
      'status_changed',
      'assignment_changed',
      'priority_updated',
      'revenue_updated',
      'notes_added',
      'tags_updated',
      'deadline_set',
      'other'
    ],
  })
  @IsOptional()
  @IsEnum([
    'contact_updated',
    'status_changed',
    'assignment_changed',
    'priority_updated',
    'revenue_updated',
    'notes_added',
    'tags_updated',
    'deadline_set',
    'other'
  ], {
    message: 'Update status must be a valid status type',
  })
  updateStatus?: string;
}

/**
 * Quick Update Lead DTO
 * For simple updates that only require specific fields
 * Uses PickType to select only commonly updated fields
 */
export class QuickUpdateLeadDto extends PartialType(
  OmitType(CreateLeadDto, [
    '_contactValidation',
    'companyId',
    'campaignId',
    'sourceId',
    'mediumId',
  ] as const)
) {}

/**
 * Lead Assignment DTO
 * Specifically for updating lead assignment
 */
export class AssignLeadDto {
  @ApiPropertyOptional({
    description: 'ID of user to assign the lead to',
    example: 123,
  })
  @IsOptional()
  assignedUserId?: number;

  @ApiPropertyOptional({
    description: 'ID of team to assign the lead to',
    example: 5,
  })
  @IsOptional()
  teamId?: number;

  @ApiPropertyOptional({
    description: 'Reason for assignment change',
    example: 'Reassigning to specialist team for better handling',
    maxLength: 200,
  })
  @IsOptional()
  @IsString()
  @MaxLength(200, { message: 'Assignment reason cannot exceed 200 characters' })
  assignmentReason?: string;
}

/**
 * Lead Priority Update DTO
 * Specifically for updating lead priority
 */
export class UpdateLeadPriorityDto {
  @ApiPropertyOptional({
    description: 'New priority level (0=Low, 1=Medium, 2=High, 3=Urgent)',
    example: 2,
    minimum: 0,
    maximum: 3,
  })
  priority: number;

  @ApiPropertyOptional({
    description: 'Reason for priority change',
    example: 'Client requested urgent response',
    maxLength: 200,
  })
  @IsOptional()
  @IsString()
  @MaxLength(200, { message: 'Priority reason cannot exceed 200 characters' })
  priorityReason?: string;
}
