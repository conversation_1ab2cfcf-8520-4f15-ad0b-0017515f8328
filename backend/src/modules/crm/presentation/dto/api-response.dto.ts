import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { LeadResponseDto } from './lead-response.dto';

/**
 * Pagination Metadata DTO
 */
export class PaginationMetaDto {
  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 20,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of items',
    example: 150,
  })
  total: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 8,
  })
  totalPages: number;

  @ApiProperty({
    description: 'Whether there is a next page',
    example: true,
  })
  hasNext: boolean;

  @ApiProperty({
    description: 'Whether there is a previous page',
    example: false,
  })
  hasPrev: boolean;
}

/**
 * Lead API Response DTO
 */
export class LeadApiResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Lead retrieved successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Lead data',
    type: LeadResponseDto,
  })
  @Type(() => LeadResponseDto)
  data: LeadResponseDto;

  @ApiProperty({
    description: 'Response timestamp',
    example: '2024-01-20T10:30:00.000Z',
  })
  timestamp: string;

  @ApiProperty({
    description: 'API version',
    example: 'v1',
  })
  version: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
  })
  meta?: any;
}

/**
 * Lead List Response DTO
 */
export class LeadListResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Leads retrieved successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Array of leads',
    type: [LeadResponseDto],
  })
  @Type(() => LeadResponseDto)
  data: LeadResponseDto[];

  @ApiProperty({
    description: 'Pagination metadata',
    type: PaginationMetaDto,
  })
  @Type(() => PaginationMetaDto)
  pagination: PaginationMetaDto;

  @ApiProperty({
    description: 'Response timestamp',
    example: '2024-01-20T10:30:00.000Z',
  })
  timestamp: string;

  @ApiProperty({
    description: 'API version',
    example: 'v1',
  })
  version: string;

  @ApiPropertyOptional({
    description: 'Additional metadata',
  })
  meta?: any;
}

/**
 * Error Response DTO
 */
export class ErrorResponseDto {
  @ApiProperty({
    description: 'Success status (always false for errors)',
    example: false,
  })
  success: boolean;

  @ApiProperty({
    description: 'Error message',
    example: 'Validation failed',
  })
  message: string;

  @ApiProperty({
    description: 'HTTP status code',
    example: 400,
  })
  statusCode: number;

  @ApiPropertyOptional({
    description: 'Detailed error information',
    example: {
      field: 'email',
      constraint: 'isEmail',
      value: 'invalid-email',
    },
  })
  error?: any;

  @ApiPropertyOptional({
    description: 'Validation errors (for 400 responses)',
    example: [
      {
        field: 'email',
        message: 'Email must be a valid email address',
        value: 'invalid-email',
      },
    ],
  })
  validationErrors?: Array<{
    field: string;
    message: string;
    value: any;
  }>;

  @ApiProperty({
    description: 'Error timestamp',
    example: '2024-01-20T10:30:00.000Z',
  })
  timestamp: string;

  @ApiPropertyOptional({
    description: 'Request path',
    example: '/api/v1/leads',
  })
  path?: string;
}
