import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsOptional,
  IsString,
  IsNumber,
  IsArray,
  IsEnum,
  IsDateString,
  IsInt,
  Min,
  Max,
  IsBoolean,
  IsPositive,
} from 'class-validator';
import { PaginationDto, DateRangeDto } from './lead-filter.dto';

/**
 * Search Lead DTO
 * For full-text search functionality
 */
export class SearchLeadDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Search term for full-text search',
    example: 'John Doe Acme',
  })
  @IsOptional()
  @IsString({ message: 'Search term must be a string' })
  searchTerm?: string;

  @ApiPropertyOptional({
    description: 'Search in specific fields only',
    example: ['name', 'email', 'company'],
    type: [String],
    enum: ['name', 'email', 'phone', 'company', 'description'],
  })
  @IsOptional()
  @IsArray({ message: 'Search fields must be an array' })
  @IsEnum(['name', 'email', 'phone', 'company', 'description'], {
    each: true,
    message: 'Each search field must be a valid field name',
  })
  searchFields?: string[];

  @ApiPropertyOptional({
    description: 'Minimum search relevance score (0-1)',
    example: 0.5,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Min score must be a number' })
  @Min(0, { message: 'Min score must be at least 0' })
  @Max(1, { message: 'Min score cannot exceed 1' })
  minScore?: number;

  @ApiPropertyOptional({
    description: 'Enable fuzzy search',
    example: true,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  @IsBoolean({ message: 'Fuzzy search must be a boolean' })
  fuzzySearch?: boolean = false;

  // Include basic filters
  @ApiPropertyOptional({
    description: 'Filter by lead type',
    example: 'lead',
    enum: ['lead', 'opportunity'],
  })
  @IsOptional()
  @IsEnum(['lead', 'opportunity'], {
    message: 'Type must be either "lead" or "opportunity"',
  })
  type?: string;

  @ApiPropertyOptional({
    description: 'Filter by assigned user ID',
    example: 123,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Assigned user ID must be an integer' })
  @IsPositive({ message: 'Assigned user ID must be positive' })
  assignedUserId?: number;

  @ApiPropertyOptional({
    description: 'Filter by team ID',
    example: 5,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Team ID must be an integer' })
  @IsPositive({ message: 'Team ID must be positive' })
  teamId?: number;

  // Sorting options
  @ApiPropertyOptional({
    description: 'Field to sort by',
    example: 'relevance',
    enum: ['relevance', 'createdAt', 'updatedAt', 'name', 'expectedRevenue'],
    default: 'relevance',
  })
  @IsOptional()
  @IsEnum(['relevance', 'createdAt', 'updatedAt', 'name', 'expectedRevenue'], {
    message: 'Sort field must be a valid field name',
  })
  sortBy?: string = 'relevance';

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'desc',
    enum: ['asc', 'desc'],
    default: 'desc',
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'], {
    message: 'Sort order must be either "asc" or "desc"',
  })
  sortOrder?: 'asc' | 'desc' = 'desc';
}

/**
 * Lead Analytics Filter DTO
 * For analytics and reporting queries
 */
export class LeadAnalyticsFilterDto extends DateRangeDto {
  @ApiPropertyOptional({
    description: 'Group results by field',
    example: 'source',
    enum: ['source', 'type', 'priority', 'status', 'assignedUser', 'team', 'createdDate'],
  })
  @IsOptional()
  @IsEnum(['source', 'type', 'priority', 'status', 'assignedUser', 'team', 'createdDate'], {
    message: 'Group by field must be a valid field name',
  })
  groupBy?: string;

  @ApiPropertyOptional({
    description: 'Time granularity for date grouping',
    example: 'month',
    enum: ['day', 'week', 'month', 'quarter', 'year'],
  })
  @IsOptional()
  @IsEnum(['day', 'week', 'month', 'quarter', 'year'], {
    message: 'Time granularity must be a valid option',
  })
  timeGranularity?: string;

  @ApiPropertyOptional({
    description: 'Include conversion metrics',
    example: true,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  @IsBoolean({ message: 'Include conversion metrics must be a boolean' })
  includeConversionMetrics?: boolean = false;

  @ApiPropertyOptional({
    description: 'Include revenue metrics',
    example: true,
    default: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  @IsBoolean({ message: 'Include revenue metrics must be a boolean' })
  includeRevenueMetrics?: boolean = true;

  @ApiPropertyOptional({
    description: 'Filter by team ID for analytics',
    example: 5,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Team ID must be an integer' })
  @IsPositive({ message: 'Team ID must be positive' })
  teamId?: number;

  @ApiPropertyOptional({
    description: 'Filter by assigned user ID for analytics',
    example: 123,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Assigned user ID must be an integer' })
  @IsPositive({ message: 'Assigned user ID must be positive' })
  assignedUserId?: number;
}

/**
 * Advanced Lead Query DTO
 * For complex queries with multiple criteria
 */
export class AdvancedLeadQueryDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Complex filter expression (JSON)',
    example: '{"and": [{"field": "priority", "operator": "gte", "value": 2}, {"field": "expectedRevenue", "operator": "gt", "value": 10000}]}',
  })
  @IsOptional()
  @IsString({ message: 'Filter expression must be a string' })
  filterExpression?: string;

  @ApiPropertyOptional({
    description: 'Include related data',
    example: ['assignedUser', 'team', 'activities'],
    type: [String],
    enum: ['assignedUser', 'team', 'activities', 'notes', 'attachments'],
  })
  @IsOptional()
  @IsArray({ message: 'Include fields must be an array' })
  @IsEnum(['assignedUser', 'team', 'activities', 'notes', 'attachments'], {
    each: true,
    message: 'Each include field must be a valid field name',
  })
  include?: string[];

  @ApiPropertyOptional({
    description: 'Exclude specific fields from response',
    example: ['description', 'tags'],
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Exclude fields must be an array' })
  @IsString({ each: true, message: 'Each exclude field must be a string' })
  exclude?: string[];

  @ApiPropertyOptional({
    description: 'Custom aggregation functions',
    example: ['count', 'sum:expectedRevenue', 'avg:probability'],
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Aggregations must be an array' })
  @IsString({ each: true, message: 'Each aggregation must be a string' })
  aggregations?: string[];
}

/**
 * Lead Export Query DTO
 * For exporting lead data
 */
export class LeadExportQueryDto {
  @ApiPropertyOptional({
    description: 'Export format',
    example: 'csv',
    enum: ['csv', 'xlsx', 'json', 'pdf'],
    default: 'csv',
  })
  @IsOptional()
  @IsEnum(['csv', 'xlsx', 'json', 'pdf'], {
    message: 'Export format must be a valid format',
  })
  format?: string = 'csv';

  @ApiPropertyOptional({
    description: 'Fields to include in export',
    example: ['id', 'name', 'email', 'phone', 'company', 'status', 'expectedRevenue'],
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Fields must be an array' })
  @IsString({ each: true, message: 'Each field must be a string' })
  fields?: string[];

  @ApiPropertyOptional({
    description: 'Maximum number of records to export',
    example: 10000,
    minimum: 1,
    maximum: 50000,
    default: 10000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Max records must be an integer' })
  @Min(1, { message: 'Max records must be at least 1' })
  @Max(50000, { message: 'Max records cannot exceed 50000' })
  maxRecords?: number = 10000;

  @ApiPropertyOptional({
    description: 'Include headers in export',
    example: true,
    default: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  @IsBoolean({ message: 'Include headers must be a boolean' })
  includeHeaders?: boolean = true;

  @ApiPropertyOptional({
    description: 'Date format for export',
    example: 'YYYY-MM-DD',
    enum: ['YYYY-MM-DD', 'DD/MM/YYYY', 'MM/DD/YYYY', 'ISO'],
    default: 'YYYY-MM-DD',
  })
  @IsOptional()
  @IsEnum(['YYYY-MM-DD', 'DD/MM/YYYY', 'MM/DD/YYYY', 'ISO'], {
    message: 'Date format must be a valid format',
  })
  dateFormat?: string = 'YYYY-MM-DD';
}
