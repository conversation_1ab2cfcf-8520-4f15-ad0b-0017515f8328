import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * Contact Information Response DTO
 */
export class ContactInfoResponseDto {
  @ApiPropertyOptional({
    description: 'Contact email address',
    example: '<EMAIL>',
  })
  @Expose()
  email?: string;

  @ApiPropertyOptional({
    description: 'Contact phone number',
    example: '******-123-4567',
  })
  @Expose()
  phone?: string;

  @ApiPropertyOptional({
    description: 'Company name',
    example: 'Acme Corporation',
  })
  @Expose()
  company?: string;

  @ApiPropertyOptional({
    description: 'Company website',
    example: 'https://www.acme.com',
  })
  @Expose()
  website?: string;

  @ApiPropertyOptional({
    description: 'Street address',
    example: '123 Main Street',
  })
  @Expose()
  address?: string;

  @ApiPropertyOptional({
    description: 'City',
    example: 'New York',
  })
  @Expose()
  city?: string;

  @ApiPropertyOptional({
    description: 'Country',
    example: 'United States',
  })
  @Expose()
  country?: string;
}

/**
 * Lead Status Response DTO
 */
export class LeadStatusResponseDto {
  @ApiProperty({
    description: 'Status value',
    example: 'new',
  })
  @Expose()
  value: string;

  @ApiProperty({
    description: 'Status display name',
    example: 'New Lead',
  })
  @Expose()
  displayName: string;

  @ApiProperty({
    description: 'Whether this is an active status',
    example: true,
  })
  @Expose()
  isActive: boolean;

  @ApiProperty({
    description: 'Whether this is a won status',
    example: false,
  })
  @Expose()
  isWon: boolean;

  @ApiProperty({
    description: 'Whether this is a lost status',
    example: false,
  })
  @Expose()
  isLost: boolean;
}

/**
 * Lead Priority Response DTO
 */
export class LeadPriorityResponseDto {
  @ApiProperty({
    description: 'Priority value (0=Low, 1=Medium, 2=High, 3=Urgent)',
    example: 1,
  })
  @Expose()
  value: number;

  @ApiProperty({
    description: 'Priority display name',
    example: 'Medium',
  })
  @Expose()
  displayName: string;

  @ApiProperty({
    description: 'Priority color code',
    example: '#FFA500',
  })
  @Expose()
  color: string;
}

/**
 * Lead Type Response DTO
 */
export class LeadTypeResponseDto {
  @ApiProperty({
    description: 'Type value',
    example: 'lead',
  })
  @Expose()
  value: string;

  @ApiProperty({
    description: 'Type display name',
    example: 'Lead',
  })
  @Expose()
  displayName: string;

  @ApiProperty({
    description: 'Whether this type can have revenue forecast',
    example: false,
  })
  @Expose()
  canHaveRevenueForecast: boolean;
}

/**
 * Assigned User Response DTO
 */
export class AssignedUserResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: 123,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'User name',
    example: 'John Smith',
  })
  @Expose()
  name: string;

  @ApiPropertyOptional({
    description: 'User email',
    example: '<EMAIL>',
  })
  @Expose()
  email?: string;

  @ApiPropertyOptional({
    description: 'User avatar URL',
    example: 'https://example.com/avatars/john.jpg',
  })
  @Expose()
  avatar?: string;
}

/**
 * Lead Team Response DTO
 */
export class LeadTeamResponseDto {
  @ApiProperty({
    description: 'Team ID',
    example: 5,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Team name',
    example: 'Sales Team Alpha',
  })
  @Expose()
  name: string;

  @ApiPropertyOptional({
    description: 'Team leader ID',
    example: 123,
  })
  @Expose()
  leaderId?: number;
}

/**
 * Lead Response DTO
 * Complete response object for lead data
 */
export class LeadResponseDto {
  @ApiProperty({
    description: 'Lead ID',
    example: 12345,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Lead name',
    example: 'John Doe - Potential Customer',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Contact information',
    type: ContactInfoResponseDto,
  })
  @Expose()
  @Type(() => ContactInfoResponseDto)
  contactInfo: ContactInfoResponseDto;

  @ApiPropertyOptional({
    description: 'Lead status',
    type: LeadStatusResponseDto,
  })
  @Expose()
  @Type(() => LeadStatusResponseDto)
  status?: LeadStatusResponseDto;

  @ApiProperty({
    description: 'Lead source',
    example: 'website',
  })
  @Expose()
  source: string;

  @ApiPropertyOptional({
    description: 'Lead type',
    type: LeadTypeResponseDto,
  })
  @Expose()
  @Type(() => LeadTypeResponseDto)
  type?: LeadTypeResponseDto;

  @ApiPropertyOptional({
    description: 'Lead priority',
    type: LeadPriorityResponseDto,
  })
  @Expose()
  @Type(() => LeadPriorityResponseDto)
  priority?: LeadPriorityResponseDto;

  @ApiPropertyOptional({
    description: 'Expected revenue in USD',
    example: 50000,
  })
  @Expose()
  expectedRevenue?: number;

  @ApiPropertyOptional({
    description: 'Probability of closing (0-100%)',
    example: 25,
  })
  @Expose()
  probability?: number;

  @ApiPropertyOptional({
    description: 'Lead description',
    example: 'Interested in our enterprise solution',
  })
  @Expose()
  description?: string;

  @ApiPropertyOptional({
    description: 'Assigned user',
    type: AssignedUserResponseDto,
  })
  @Expose()
  @Type(() => AssignedUserResponseDto)
  assignedUser?: AssignedUserResponseDto;

  @ApiPropertyOptional({
    description: 'Assigned team',
    type: LeadTeamResponseDto,
  })
  @Expose()
  @Type(() => LeadTeamResponseDto)
  team?: LeadTeamResponseDto;

  @ApiPropertyOptional({
    description: 'Tags associated with the lead',
    example: ['enterprise', 'high-value', 'urgent'],
    type: [String],
  })
  @Expose()
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Deadline date',
    example: '2024-12-31T23:59:59.000Z',
  })
  @Expose()
  dateDeadline?: Date;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-15T10:30:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Last update date',
    example: '2024-01-20T14:45:00.000Z',
  })
  @Expose()
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Company ID',
    example: 1,
  })
  @Expose()
  companyId?: number;

  @ApiPropertyOptional({
    description: 'Campaign ID',
    example: 10,
  })
  @Expose()
  campaignId?: number;

  @ApiPropertyOptional({
    description: 'Source ID',
    example: 7,
  })
  @Expose()
  sourceId?: number;

  @ApiPropertyOptional({
    description: 'Medium ID',
    example: 3,
  })
  @Expose()
  mediumId?: number;
}
