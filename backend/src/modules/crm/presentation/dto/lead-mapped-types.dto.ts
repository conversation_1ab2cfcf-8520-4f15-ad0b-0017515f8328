import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PartialType, PickType, OmitType, IntersectionType } from '@nestjs/mapped-types';
import { CreateLeadDto } from './create-lead.dto';
import { LeadResponseDto } from './lead-response.dto';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsDateString,
  MaxLength,
  IsInt,
  IsPositive,
} from 'class-validator';

/**
 * Lead Contact Info DTO
 * Uses PickType to select only contact-related fields
 */
export class LeadContactInfoDto extends PickType(CreateLeadDto, [
  'name',
  'email',
  'phone',
  'company',
  'website',
  'address',
  'city',
  'country',
] as const) {}

/**
 * Lead Business Info DTO
 * Uses PickType to select only business-related fields
 */
export class LeadBusinessInfoDto extends PickType(CreateLeadDto, [
  'expectedRevenue',
  'probability',
  'priority',
  'source',
  'type',
  'description',
] as const) {}

/**
 * Lead Assignment Info DTO
 * Uses PickType to select only assignment-related fields
 */
export class LeadAssignmentInfoDto extends PickType(CreateLeadDto, [
  'assignedUserId',
  'teamId',
] as const) {}

/**
 * Lead Metadata DTO
 * Uses PickType to select only metadata fields
 */
export class LeadMetadataDto extends PickType(CreateLeadDto, [
  'campaignId',
  'sourceId',
  'mediumId',
  'tags',
  'dateDeadline',
  'companyId',
] as const) {}

/**
 * Public Lead DTO
 * Uses OmitType to exclude sensitive internal fields
 */
export class PublicLeadDto extends OmitType(CreateLeadDto, [
  'assignedUserId',
  'teamId',
  'companyId',
  'campaignId',
  'sourceId',
  'mediumId',
  '_contactValidation',
] as const) {}

/**
 * Lead Summary DTO
 * Uses PickType to select only essential summary fields
 */
export class LeadSummaryDto extends PickType(LeadResponseDto, [
  'id',
  'name',
  'contactInfo',
  'status',
  'priority',
  'expectedRevenue',
  'assignedUser',
  'createdAt',
] as const) {}

/**
 * Lead List Item DTO
 * Optimized for list views with minimal data
 */
export class LeadListItemDto extends PickType(LeadResponseDto, [
  'id',
  'name',
  'status',
  'priority',
  'expectedRevenue',
  'assignedUser',
  'team',
  'createdAt',
  'updatedAt',
] as const) {
  @ApiPropertyOptional({
    description: 'Primary contact method (email or phone)',
    example: '<EMAIL>',
  })
  primaryContact?: string;

  @ApiPropertyOptional({
    description: 'Company name from contact info',
    example: 'Acme Corporation',
  })
  companyName?: string;
}

/**
 * Opportunity Additional Fields DTO
 */
class OpportunityAdditionalFieldsDto {
  @ApiPropertyOptional({
    description: 'Partner ID for the opportunity',
    example: 456,
  })
  @IsOptional()
  @IsInt()
  @IsPositive()
  partnerId?: number;

  @ApiPropertyOptional({
    description: 'Stage ID for the opportunity',
    example: 2,
  })
  @IsOptional()
  @IsInt()
  @IsPositive()
  stageId?: number;

  @ApiPropertyOptional({
    description: 'Reason for conversion',
    example: 'Client showed strong interest and budget confirmed',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  conversionReason?: string;

  @ApiPropertyOptional({
    description: 'Expected close date',
    example: '2024-06-30T23:59:59.000Z',
  })
  @IsOptional()
  @IsDateString()
  expectedCloseDate?: string;
}

/**
 * Convert Lead to Opportunity DTO
 * Combines lead data with opportunity-specific fields
 */
export class ConvertLeadToOpportunityDto extends IntersectionType(
  PickType(CreateLeadDto, [
    'expectedRevenue',
    'probability',
    'description',
    'assignedUserId',
    'teamId',
    'dateDeadline',
  ] as const),
  OpportunityAdditionalFieldsDto
) {}

/**
 * Bulk Update Lead DTO
 * For updating multiple leads with common fields
 */
export class BulkUpdateLeadDto extends PartialType(
  PickType(CreateLeadDto, [
    'assignedUserId',
    'teamId',
    'priority',
    'source',
    'tags',
  ] as const)
) {
  @ApiProperty({
    description: 'Array of lead IDs to update',
    example: [1, 2, 3, 4, 5],
    type: [Number],
  })
  leadIds: number[];

  @ApiPropertyOptional({
    description: 'Reason for bulk update',
    example: 'Reassigning leads to new team member',
    maxLength: 200,
  })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  updateReason?: string;
}

/**
 * Import Additional Fields DTO
 */
class ImportAdditionalFieldsDto {
  @ApiPropertyOptional({
    description: 'External ID from source system',
    example: 'EXT-12345',
  })
  @IsOptional()
  @IsString()
  externalId?: string;

  @ApiPropertyOptional({
    description: 'Import source',
    example: 'csv_upload',
    enum: ['csv_upload', 'api_import', 'manual_entry', 'web_form', 'integration'],
  })
  @IsOptional()
  @IsEnum(['csv_upload', 'api_import', 'manual_entry', 'web_form', 'integration'])
  importSource?: string;

  @ApiPropertyOptional({
    description: 'Import batch ID for tracking',
    example: 'BATCH-2024-001',
  })
  @IsOptional()
  @IsString()
  importBatchId?: string;
}

/**
 * Lead Import DTO
 * For importing leads from external sources
 */
export class LeadImportDto extends IntersectionType(
  OmitType(CreateLeadDto, ['_contactValidation'] as const),
  ImportAdditionalFieldsDto
) {}

/**
 * Lead Export DTO
 * For exporting lead data
 */
export class LeadExportDto extends PickType(LeadResponseDto, [
  'id',
  'name',
  'contactInfo',
  'status',
  'source',
  'type',
  'priority',
  'expectedRevenue',
  'probability',
  'description',
  'assignedUser',
  'team',
  'tags',
  'dateDeadline',
  'createdAt',
  'updatedAt',
] as const) {
  @ApiPropertyOptional({
    description: 'Export format',
    example: 'csv',
    enum: ['csv', 'xlsx', 'json', 'pdf'],
  })
  exportFormat?: string;

  @ApiPropertyOptional({
    description: 'Export timestamp',
    example: '2024-01-20T10:30:00.000Z',
  })
  exportedAt?: Date;
}
