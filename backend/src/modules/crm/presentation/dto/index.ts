// Base DTOs
export * from './create-lead.dto';
export * from './update-lead.dto';
export * from './lead-response.dto';

// Mapped Types DTOs
export * from './lead-mapped-types.dto';

// Filter and Query DTOs
export * from './lead-filter.dto';
export * from './lead-search.dto';

// API Response DTOs
export * from './api-response.dto';

// Re-export commonly used types for convenience
export type {
  UpdateLeadDto,
  QuickUpdateLeadDto,
  AssignLeadDto,
  UpdateLeadPriorityDto,
} from './update-lead.dto';

export type {
  LeadResponseDto,
  ContactInfoResponseDto,
  LeadStatusResponseDto,
  LeadPriorityResponseDto,
  LeadTypeResponseDto,
  AssignedUserResponseDto,
  LeadTeamResponseDto,
} from './lead-response.dto';

export type {
  LeadContactInfoDto,
  LeadBusinessInfoDto,
  LeadAssignmentInfoDto,
  LeadMetadataDto,
  PublicLeadDto,
  LeadSummaryDto,
  LeadListItemDto,
  ConvertLeadToOpportunityDto,
  BulkUpdateLeadDto,
  LeadImportDto,
  LeadExportDto,
} from './lead-mapped-types.dto';

export type {
  PaginationDto,
  SortingDto,
  DateRangeDto,
  LeadFilterDto,
} from './lead-filter.dto';

export type {
  SearchLeadDto,
  LeadAnalyticsFilterDto,
  AdvancedLeadQueryDto,
  LeadExportQueryDto,
} from './lead-search.dto';
