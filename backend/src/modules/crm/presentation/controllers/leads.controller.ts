import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpCode,
  UseGuards,
  UseInterceptors,
  Logger,
  ParseIntPipe,
  ValidationPipe,
  Req,
} from '@nestjs/common';
import { Request } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiExtraModels,
} from '@nestjs/swagger';
import { ClassSerializerInterceptor } from '@nestjs/common';

// Guards and Decorators
import { JwtAuthGuard } from '@/shared/infrastructure/guards/jwt-auth.guard';
import { CurrentUser } from '@/shared/infrastructure/decorators/current-user.decorator';

// Services
import { LeadsService } from '@/modules/crm/application/services/leads.service';
import { ResponseBuilderService } from '@/common/services/response-builder.service';

// DTOs
import {
  CreateLeadDto,
  UpdateLeadDto,
  LeadResponseDto,
  LeadFilterDto,
  SearchLeadDto,
  LeadApiResponseDto,
  LeadListResponseDto,
  ErrorResponseDto,
  PaginationMetaDto,
  AssignLeadDto,
  UpdateLeadPriorityDto,
  BulkUpdateLeadDto,
  BulkOperationResponseDto,
} from '../dto';

/**
 * Traditional REST API Controller for Leads
 * Provides standard CRUD operations with proper HTTP semantics
 * Integrates with CQRS infrastructure through service layer
 */
@ApiTags('leads')
@Controller('leads')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@UseInterceptors(ClassSerializerInterceptor)
@ApiExtraModels(LeadResponseDto, PaginationMetaDto, ErrorResponseDto)
export class LeadsController {
  private readonly logger = new Logger(LeadsController.name);

  constructor(
    private readonly leadsService: LeadsService,
    private readonly responseBuilder: ResponseBuilderService,
  ) {}

  /**
   * Create a new lead
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new lead',
    description: 'Creates a new lead in the CRM system with comprehensive validation and business rules',
  })
  @ApiResponse({
    status: 201,
    description: 'Lead created successfully',
    type: LeadApiResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: 409,
    description: 'Lead with this email already exists',
    type: ErrorResponseDto,
  })
  async create(
    @Body(ValidationPipe) createLeadDto: CreateLeadDto,
    @CurrentUser() user: any,
    @Req() request: Request,
  ) {
    this.logger.log(`Creating lead: ${createLeadDto.name} by user: ${user.id}`);

    const result = await this.leadsService.create(createLeadDto, user.id, user.companyId);

    return this.responseBuilder.success(
      result.lead,
      'Lead created successfully',
      HttpStatus.CREATED,
      request,
      {
        leadId: result.leadId,
        assignedAutomatically: !!result.lead.assignedUserId,
        businessRulesApplied: ['validation', 'auto_assignment'],
      },
    );
  }

  /**
   * Get all leads with pagination and filtering
   */
  @Get()
  @ApiOperation({
    summary: 'Get all leads',
    description: 'Retrieves all leads with optional filtering, pagination, and sorting',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20, max: 100)' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field (default: createdAt)' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Sort order (default: desc)' })
  @ApiResponse({
    status: 200,
    description: 'Leads retrieved successfully',
    type: LeadListResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ErrorResponseDto,
  })
  async findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 20,
    @Query('sortBy') sortBy: string = 'createdAt',
    @Query('sortOrder') sortOrder: 'asc' | 'desc' = 'desc',
    @Req() request: Request,
  ) {
    this.logger.log(`Getting all leads - page: ${page}, limit: ${limit}`);

    // Validate limit
    const validLimit = Math.min(Math.max(limit, 1), 100);

    const result = await this.leadsService.findAll(page, validLimit, sortBy, sortOrder);

    const totalPages = Math.ceil(result.total / validLimit);
    const pagination: PaginationMetaDto = {
      page,
      limit: validLimit,
      total: result.total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    return this.responseBuilder.list(
      result.leads,
      pagination,
      `Retrieved ${result.leads.length} leads successfully`,
      request,
    );
  }

  /**
   * Get lead by ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get lead by ID',
    description: 'Retrieves a specific lead by its ID',
  })
  @ApiParam({ name: 'id', type: Number, description: 'Lead ID' })
  @ApiResponse({
    status: 200,
    description: 'Lead retrieved successfully',
    type: LeadApiResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Lead not found',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ErrorResponseDto,
  })
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @Req() request: Request,
  ) {
    this.logger.log(`Getting lead by ID: ${id}`);

    const lead = await this.leadsService.findByIdOrFail(id);

    return this.responseBuilder.success(
      lead,
      'Lead retrieved successfully',
      HttpStatus.OK,
      request,
      {
        leadId: lead.id,
        lastUpdated: lead.updatedAt,
      },
    );
  }

  /**
   * Update lead
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Update lead',
    description: 'Updates an existing lead with new information',
  })
  @ApiParam({ name: 'id', type: Number, description: 'Lead ID' })
  @ApiResponse({
    status: 200,
    description: 'Lead updated successfully',
    type: LeadApiResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Lead not found',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ErrorResponseDto,
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body(ValidationPipe) updateLeadDto: UpdateLeadDto,
    @CurrentUser() user: any,
    @Req() request: Request,
  ) {
    this.logger.log(`Updating lead ${id} by user: ${user.id}`);

    const updatedLead = await this.leadsService.update(id, updateLeadDto, user.id);

    return this.responseBuilder.success(
      updatedLead,
      'Lead updated successfully',
      HttpStatus.OK,
      request,
      {
        leadId: updatedLead.id,
        updatedBy: user.id,
        updatedAt: updatedLead.updatedAt,
        changes: Object.keys(updateLeadDto).filter(key => updateLeadDto[key] !== undefined),
      },
    );
  }

  /**
   * Delete lead
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete lead',
    description: 'Deletes a lead from the system (soft delete)',
  })
  @ApiParam({ name: 'id', type: Number, description: 'Lead ID' })
  @ApiResponse({
    status: 204,
    description: 'Lead deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Lead not found',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ErrorResponseDto,
  })
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: any,
  ): Promise<void> {
    this.logger.log(`Deleting lead ${id} by user: ${user.id}`);

    await this.leadsService.delete(id, user.id);
    
    this.logger.log(`Successfully deleted lead ${id}`);
  }

  /**
   * Search leads
   */
  @Get('search')
  @ApiOperation({
    summary: 'Search leads',
    description: 'Search leads using text query with optional filters',
  })
  @ApiQuery({ name: 'q', required: false, type: String, description: 'Search query' })
  @ApiQuery({ name: 'type', required: false, enum: ['lead', 'opportunity'], description: 'Lead type filter' })
  @ApiQuery({ name: 'assignedUserId', required: false, type: Number, description: 'Assigned user filter' })
  @ApiQuery({ name: 'teamId', required: false, type: Number, description: 'Team filter' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Results limit (default: 20, max: 100)' })
  @ApiResponse({
    status: 200,
    description: 'Search completed successfully',
    type: LeadListResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ErrorResponseDto,
  })
  async search(
    @Query('q') searchTerm: string = '',
    @Query('type') type?: string,
    @Query('assignedUserId', new ParseIntPipe({ optional: true })) assignedUserId?: number,
    @Query('teamId', new ParseIntPipe({ optional: true })) teamId?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 20,
    @Req() request?: Request,
  ) {
    this.logger.log(`Searching leads with term: "${searchTerm}"`);

    const validLimit = Math.min(Math.max(limit, 1), 100);

    const searchDto: SearchLeadDto = {
      searchTerm,
      type,
      assignedUserId,
      teamId,
      limit: validLimit,
    };

    const leads = await this.leadsService.search(searchDto);

    return this.responseBuilder.success(
      leads,
      `Found ${leads.length} leads matching search criteria`,
      HttpStatus.OK,
      request,
      {
        searchTerm,
        resultCount: leads.length,
        filters: { type, assignedUserId, teamId },
      },
    );
  }

  /**
   * Assign lead to user
   */
  @Put(':id/assign/user')
  @ApiOperation({
    summary: 'Assign lead to user',
    description: 'Assigns a lead to a specific user',
  })
  @ApiParam({ name: 'id', type: Number, description: 'Lead ID' })
  @ApiResponse({
    status: 200,
    description: 'Lead assigned successfully',
    type: LeadApiResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Lead not found',
    type: ErrorResponseDto,
  })
  async assignToUser(
    @Param('id', ParseIntPipe) id: number,
    @Body(ValidationPipe) assignDto: AssignLeadDto,
    @CurrentUser() user: any,
    @Req() request: Request,
  ) {
    this.logger.log(`Assigning lead ${id} to user ${assignDto.assignedUserId} by user: ${user.id}`);

    const updatedLead = await this.leadsService.assignToUser(id, assignDto.assignedUserId!, user.id);

    return this.responseBuilder.success(
      updatedLead,
      'Lead assigned to user successfully',
      HttpStatus.OK,
      request,
      {
        leadId: updatedLead.id,
        assignedUserId: assignDto.assignedUserId,
        assignedBy: user.id,
        assignmentReason: assignDto.assignmentReason,
      },
    );
  }

  /**
   * Assign lead to team
   */
  @Put(':id/assign/team')
  @ApiOperation({
    summary: 'Assign lead to team',
    description: 'Assigns a lead to a specific team',
  })
  @ApiParam({ name: 'id', type: Number, description: 'Lead ID' })
  @ApiResponse({
    status: 200,
    description: 'Lead assigned successfully',
    type: LeadApiResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Lead not found',
    type: ErrorResponseDto,
  })
  async assignToTeam(
    @Param('id', ParseIntPipe) id: number,
    @Body(ValidationPipe) assignDto: AssignLeadDto,
    @CurrentUser() user: any,
    @Req() request: Request,
  ) {
    this.logger.log(`Assigning lead ${id} to team ${assignDto.teamId} by user: ${user.id}`);

    const updatedLead = await this.leadsService.assignToTeam(id, assignDto.teamId!, user.id);

    return this.responseBuilder.success(
      updatedLead,
      'Lead assigned to team successfully',
      HttpStatus.OK,
      request,
      {
        leadId: updatedLead.id,
        teamId: assignDto.teamId,
        assignedBy: user.id,
        assignmentReason: assignDto.assignmentReason,
      },
    );
  }

  /**
   * Update lead priority
   */
  @Put(':id/priority')
  @ApiOperation({
    summary: 'Update lead priority',
    description: 'Updates the priority level of a lead',
  })
  @ApiParam({ name: 'id', type: Number, description: 'Lead ID' })
  @ApiResponse({
    status: 200,
    description: 'Lead priority updated successfully',
    type: LeadApiResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Lead not found',
    type: ErrorResponseDto,
  })
  async updatePriority(
    @Param('id', ParseIntPipe) id: number,
    @Body(ValidationPipe) priorityDto: UpdateLeadPriorityDto,
    @CurrentUser() user: any,
    @Req() request: Request,
  ) {
    this.logger.log(`Updating priority for lead ${id} to ${priorityDto.priority} by user: ${user.id}`);

    const updateDto: UpdateLeadDto = {
      priority: priorityDto.priority,
      updateReason: priorityDto.priorityReason,
    };

    const updatedLead = await this.leadsService.update(id, updateDto, user.id);

    return this.responseBuilder.success(
      updatedLead,
      'Lead priority updated successfully',
      HttpStatus.OK,
      request,
      {
        leadId: updatedLead.id,
        newPriority: priorityDto.priority,
        updatedBy: user.id,
        reason: priorityDto.priorityReason,
      },
    );
  }

  /**
   * Convert lead to opportunity
   */
  @Post(':id/convert')
  @ApiOperation({
    summary: 'Convert lead to opportunity',
    description: 'Converts a lead to an opportunity in the sales pipeline',
  })
  @ApiParam({ name: 'id', type: Number, description: 'Lead ID' })
  @ApiResponse({
    status: 200,
    description: 'Lead converted to opportunity successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Lead not found',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Lead cannot be converted (invalid state)',
    type: ErrorResponseDto,
  })
  async convertToOpportunity(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: any,
    @Req() request: Request,
  ) {
    this.logger.log(`Converting lead ${id} to opportunity by user: ${user.id}`);

    const result = await this.leadsService.convertToOpportunity(id, user.id);

    return this.responseBuilder.success(
      result,
      'Lead converted to opportunity successfully',
      HttpStatus.OK,
      request,
      {
        originalLeadId: id,
        convertedBy: user.id,
        conversionDate: new Date(),
      },
    );
  }

  /**
   * Get leads with advanced filtering
   */
  @Get('filter')
  @ApiOperation({
    summary: 'Filter leads with advanced criteria',
    description: 'Retrieves leads using advanced filtering options including date ranges, revenue filters, and complex criteria',
  })
  @ApiResponse({
    status: 200,
    description: 'Filtered leads retrieved successfully',
    type: LeadListResponseDto,
  })
  async filterLeads(
    @Query(ValidationPipe) filterDto: LeadFilterDto,
    @Req() request: Request,
  ) {
    this.logger.log(`Filtering leads with criteria: ${JSON.stringify(filterDto)}`);

    const leads = await this.leadsService.findByFilters(filterDto);

    return this.responseBuilder.success(
      leads,
      `Found ${leads.length} leads matching filter criteria`,
      HttpStatus.OK,
      request,
      {
        filterCriteria: filterDto,
        resultCount: leads.length,
      },
    );
  }

  /**
   * Get leads by assigned user
   */
  @Get('user/:userId')
  @ApiOperation({
    summary: 'Get leads by assigned user',
    description: 'Retrieves all leads assigned to a specific user',
  })
  @ApiParam({ name: 'userId', type: Number, description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'User leads retrieved successfully',
    type: LeadListResponseDto,
  })
  async getLeadsByUser(
    @Param('userId', ParseIntPipe) userId: number,
    @Req() request: Request,
  ) {
    this.logger.log(`Getting leads for user: ${userId}`);

    const leads = await this.leadsService.findByAssignedUser(userId);

    return this.responseBuilder.success(
      leads,
      `Found ${leads.length} leads for user ${userId}`,
      HttpStatus.OK,
      request,
      {
        userId,
        leadCount: leads.length,
      },
    );
  }

  /**
   * Get leads by team
   */
  @Get('team/:teamId')
  @ApiOperation({
    summary: 'Get leads by team',
    description: 'Retrieves all leads assigned to a specific team',
  })
  @ApiParam({ name: 'teamId', type: Number, description: 'Team ID' })
  @ApiResponse({
    status: 200,
    description: 'Team leads retrieved successfully',
    type: LeadListResponseDto,
  })
  async getLeadsByTeam(
    @Param('teamId', ParseIntPipe) teamId: number,
    @Req() request: Request,
  ) {
    this.logger.log(`Getting leads for team: ${teamId}`);

    const leads = await this.leadsService.findByTeam(teamId);

    return this.responseBuilder.success(
      leads,
      `Found ${leads.length} leads for team ${teamId}`,
      HttpStatus.OK,
      request,
      {
        teamId,
        leadCount: leads.length,
      },
    );
  }

  /**
   * Get lead analytics
   */
  @Get('analytics')
  @ApiOperation({
    summary: 'Get lead analytics',
    description: 'Retrieves analytics and metrics for leads including pipeline data',
  })
  @ApiQuery({ name: 'teamId', required: false, type: Number, description: 'Team ID filter' })
  @ApiQuery({ name: 'dateFrom', required: false, type: String, description: 'Start date (ISO format)' })
  @ApiQuery({ name: 'dateTo', required: false, type: String, description: 'End date (ISO format)' })
  @ApiResponse({
    status: 200,
    description: 'Analytics retrieved successfully',
  })
  async getAnalytics(
    @Query('teamId', new ParseIntPipe({ optional: true })) teamId?: number,
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string,
    @Req() request?: Request,
  ) {
    this.logger.log(`Getting analytics for team: ${teamId}, dateRange: ${dateFrom} - ${dateTo}`);

    const analytics = await this.leadsService.getPipelineAnalytics(
      teamId,
      dateFrom ? new Date(dateFrom) : undefined,
      dateTo ? new Date(dateTo) : undefined,
    );

    return this.responseBuilder.success(
      analytics,
      'Analytics retrieved successfully',
      HttpStatus.OK,
      request,
      {
        teamId,
        dateRange: { from: dateFrom, to: dateTo },
        generatedAt: new Date(),
      },
    );
  }

  /**
   * Check if lead exists
   */
  @Get(':id/exists')
  @ApiOperation({
    summary: 'Check if lead exists',
    description: 'Checks whether a lead with the given ID exists',
  })
  @ApiParam({ name: 'id', type: Number, description: 'Lead ID' })
  @ApiResponse({
    status: 200,
    description: 'Existence check completed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            exists: { type: 'boolean' },
            leadId: { type: 'number' },
          },
        },
      },
    },
  })
  async checkExists(
    @Param('id', ParseIntPipe) id: number,
    @Req() request: Request,
  ) {
    this.logger.log(`Checking if lead exists: ${id}`);

    const exists = await this.leadsService.exists(id);

    return this.responseBuilder.success(
      { exists, leadId: id },
      exists ? 'Lead exists' : 'Lead does not exist',
      HttpStatus.OK,
      request,
    );
  }

  /**
   * Get lead count
   */
  @Get('count')
  @ApiOperation({
    summary: 'Get lead count',
    description: 'Returns the total number of leads with optional filtering',
  })
  @ApiQuery({ name: 'status', required: false, type: String, description: 'Status filter' })
  @ApiQuery({ name: 'assignedUserId', required: false, type: Number, description: 'Assigned user filter' })
  @ApiQuery({ name: 'teamId', required: false, type: Number, description: 'Team filter' })
  @ApiResponse({
    status: 200,
    description: 'Lead count retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            count: { type: 'number' },
            filters: { type: 'object' },
          },
        },
      },
    },
  })
  async getCount(
    @Query('status') status?: string,
    @Query('assignedUserId') assignedUserIdStr?: string,
    @Query('teamId') teamIdStr?: string,
    @Req() request?: Request,
  ) {
    // Parse optional numeric parameters
    const assignedUserId = assignedUserIdStr ? parseInt(assignedUserIdStr, 10) : undefined;
    const teamId = teamIdStr ? parseInt(teamIdStr, 10) : undefined;

    // Validate parsed numbers
    if (assignedUserIdStr && (isNaN(assignedUserId!) || assignedUserId! <= 0)) {
      throw new Error('assignedUserId must be a positive integer');
    }

    if (teamIdStr && (isNaN(teamId!) || teamId! <= 0)) {
      throw new Error('teamId must be a positive integer');
    }

    this.logger.log(`Getting lead count with filters: status=${status}, assignedUserId=${assignedUserId}, teamId=${teamId}`);

    const filters = { status, assignedUserId, teamId };
    const count = await this.leadsService.getCount(filters);

    return this.responseBuilder.success(
      { count, filters },
      `Found ${count} leads matching criteria`,
      HttpStatus.OK,
      request,
    );
  }

  /**
   * Bulk update leads
   */
  @Put('bulk')
  @ApiOperation({
    summary: 'Bulk update leads',
    description: 'Updates multiple leads with the same data in a single operation',
  })
  @ApiResponse({
    status: 200,
    description: 'Bulk update completed',
    type: BulkOperationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid bulk update data',
    type: ErrorResponseDto,
  })
  async bulkUpdate(
    @Body(ValidationPipe) bulkUpdateDto: BulkUpdateLeadDto,
    @CurrentUser() user: any,
    @Req() request: Request,
  ) {
    this.logger.log(`Bulk updating ${bulkUpdateDto.leadIds.length} leads by user: ${user.id}`);

    const result = await this.leadsService.bulkUpdate(bulkUpdateDto, user.id);

    return this.responseBuilder.success(
      result,
      `Bulk update completed: ${result.successful.length} successful, ${result.failed.length} failed`,
      HttpStatus.OK,
      request,
      {
        totalRequested: bulkUpdateDto.leadIds.length,
        totalSuccessful: result.successful.length,
        totalFailed: result.failed.length,
        operationType: 'bulk_update',
        updatedBy: user.id,
      },
    );
  }

  /**
   * Bulk delete leads
   */
  @Delete('bulk')
  @ApiOperation({
    summary: 'Bulk delete leads',
    description: 'Deletes multiple leads in a single operation',
  })
  @ApiResponse({
    status: 200,
    description: 'Bulk delete completed',
    type: BulkOperationResponseDto,
  })
  async bulkDelete(
    @Body() body: { leadIds: number[] },
    @CurrentUser() user: any,
    @Req() request: Request,
  ) {
    this.logger.log(`Bulk deleting ${body.leadIds.length} leads by user: ${user.id}`);

    const successful: number[] = [];
    const failed: Array<{ id: number; error: string }> = [];

    for (const leadId of body.leadIds) {
      try {
        await this.leadsService.delete(leadId, user.id);
        successful.push(leadId);
      } catch (error) {
        this.logger.warn(`Failed to delete lead ${leadId}: ${error.message}`);
        failed.push({ id: leadId, error: error.message });
      }
    }

    const result = { successful, failed };

    return this.responseBuilder.success(
      result,
      `Bulk delete completed: ${successful.length} successful, ${failed.length} failed`,
      HttpStatus.OK,
      request,
      {
        totalRequested: body.leadIds.length,
        totalSuccessful: successful.length,
        totalFailed: failed.length,
        operationType: 'bulk_delete',
        deletedBy: user.id,
      },
    );
  }
}
