import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// Shared modules (automatically available via @Global decorator)
import { CommonModule } from '../../common/common.module';

// CQRS Module
import { CrmCqrsModule } from './application/cqrs/cqrs.module';

// Event Sourcing Module - commented out for now
// import { EventSourcingModule } from './infrastructure/event-sourcing/event-sourcing.module';

// Async Processing Module - commented out for now
// import { QueueModule } from './infrastructure/async/queue.module';

// Infrastructure layer (legacy - commented out for now)
// import { OdooLeadRepository } from './infrastructure/repositories/odoo-lead.repository';
// import { OdooStageRepository } from './infrastructure/repositories/odoo-stage.repository';
// import { OdooTeamRepository } from './infrastructure/repositories/odoo-team.repository';

// MongoDB Repository Implementations - commented out for now due to incomplete implementation

// Odoo Repository Implementations
import { OdooLeadRepository } from './infrastructure/repositories/odoo-lead.repository';
import { OdooStageRepository } from './infrastructure/repositories/odoo-stage.repository';
import { OdooTeamRepository } from './infrastructure/repositories/odoo-team.repository';
import { OdooUserRepository } from './infrastructure/repositories/odoo-user.repository';

// Domain repositories (interfaces) - commented out as we use injection tokens
// import { ILeadRepository } from './domain/repositories/lead.repository';
// import { IStageRepository } from './domain/repositories/stage.repository';
// import { ITeamRepository } from './domain/repositories/team.repository';
// import { IUserRepository } from './domain/repositories/user.repository';

// Injection Tokens
import {
  LEAD_REPOSITORY_TOKEN,
  STAGE_REPOSITORY_TOKEN,
  TEAM_REPOSITORY_TOKEN,
  USER_REPOSITORY_TOKEN
} from './domain/repositories/injection-tokens';

// MongoDB Schemas

// Application layer (legacy - will be replaced by CQRS)
// import { CreateLeadUseCase } from './application/use-cases/create-lead.use-case';
// import { UpdateLeadUseCase } from './application/use-cases/update-lead.use-case';
// import { ConvertLeadToOpportunityUseCase } from './application/use-cases/convert-lead-to-opportunity.use-case';
// import { AssignLeadUseCase } from './application/use-cases/assign-lead.use-case';

// Queries (legacy - will be replaced by CQRS)
// import { GetLeadsQuery } from './application/queries/get-leads.query';

// Presentation layer
import { LeadsController } from './presentation/controllers/leads.controller';
import { LeadsCqrsController } from './presentation/controllers/leads-cqrs.controller';

// Application Services
import { LeadsService } from './application/services/leads.service';

// Event Handlers
// import { AdvancedLeadCreatedHandler } from './application/event-handlers/advanced-lead-created.handler';

// Use cases (only implemented ones) - temporarily disabled for type fixes
const UseCases = [
  // CreateLeadUseCase,
  // UpdateLeadUseCase,
  // ConvertLeadToOpportunityUseCase,
  // AssignLeadUseCase,
];

// Queries (only implemented ones) - commented out for now
const Queries = [
  // GetLeadsQuery,
];

// Controllers (only implemented ones)
const Controllers = [
  LeadsController, // Traditional REST controller
  LeadsCqrsController, // CQRS-based controller
];

@Module({
  imports: [
    CommonModule,
    CrmCqrsModule, // CQRS pattern support with commands, queries, events, and sagas
    // EventSourcingModule, // Event sourcing infrastructure with MongoDB - commented out for now
    // QueueModule, // Async processing with BullMQ and Redis - commented out for now
    // Note: SharedModule is @Global, so database and auth are automatically available
  ],
  providers: [
    // Repository implementations using Odoo
    {
      provide: LEAD_REPOSITORY_TOKEN,
      useClass: OdooLeadRepository,
    },
    {
      provide: STAGE_REPOSITORY_TOKEN,
      useClass: OdooStageRepository,
    },
    {
      provide: TEAM_REPOSITORY_TOKEN,
      useClass: OdooTeamRepository,
    },
    {
      provide: USER_REPOSITORY_TOKEN,
      useClass: OdooUserRepository,
    },

    // MongoDB Repository implementations - disabled due to incomplete implementation

    // Application Services
    LeadsService,

    // Legacy application layer (will be gradually replaced by CQRS)
    ...UseCases,
    ...Queries,

    // Advanced Event Handlers - commented out for now
    // AdvancedLeadCreatedHandler,

    // Note: CQRS handlers are provided by CrmCqrsModule
    // Note: Event sourcing services are provided by EventSourcingModule
    // Note: Queue producers/processors are provided by QueueModule
  ],
  controllers: Controllers,
  exports: [
    // Export CQRS module for other modules
    CrmCqrsModule,

    // Export event sourcing module for other modules - commented out for now
    // EventSourcingModule,

    // Export async processing module for other modules - commented out for now
    // QueueModule,

    // Export repository implementations via injection tokens
    // Repository interfaces are available via injection tokens

    // Legacy exports (will be gradually removed)
    ...UseCases,
    ...Queries,
  ],
})
export class CrmModule {}

/**
 * CRM Module Configuration
 * 
 * This module implements the CRM bounded context with:
 * 
 * 1. **Domain Layer**:
 *    - Entities: Lead, Opportunity, Customer, Activity, Pipeline
 *    - Value Objects: LeadStatus, OpportunityStage, ContactInfo, RevenueForecast
 *    - Domain Services: Lead scoring, pipeline analytics, conversion tracking
 *    - Domain Events: Lead created, opportunity won, customer converted
 * 
 * 2. **Application Layer**:
 *    - Use Cases: Create lead, convert to opportunity, generate reports
 *    - Queries: Get leads, pipeline analytics, customer history
 *    - Event Handlers: Handle domain events and trigger side effects
 *    - DTOs: Data transfer objects for API communication
 * 
 * 3. **Infrastructure Layer**:
 *    - Adapters: Odoo CRM integration, email, calendar sync
 *    - Repositories: Data persistence implementations
 *    - Mappers: Domain ↔ Odoo model mapping
 *    - External Services: Email, SMS, calendar integrations
 * 
 * 4. **Presentation Layer**:
 *    - Controllers: REST API endpoints for CRM operations
 *    - DTOs: Request/response data structures
 *    - Validators: Input validation logic
 * 
 * **Design Patterns Used**:
 * - Domain-Driven Design (DDD)
 * - Command Query Responsibility Segregation (CQRS)
 * - Event Sourcing (for domain events)
 * - Repository Pattern
 * - Adapter Pattern
 * - Strategy Pattern (for lead scoring)
 * - Factory Pattern (for entity creation)
 * 
 * **Integration Points**:
 * - Odoo CRM models (crm.lead, crm.opportunity, res.partner)
 * - Email systems (SMTP, Exchange, Gmail)
 * - Calendar systems (Google Calendar, Outlook)
 * - SMS providers (Twilio, AWS SNS)
 * - Analytics platforms (for reporting)
 */
