import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { Injectable, Logger } from '@nestjs/common';
import { LeadCreatedEvent } from '@/modules/crm/domain/events/lead-events';
import { EmailQueueProducer } from '@/modules/crm/infrastructure/async/producers/email-queue.producer';
import { NotificationQueueProducer } from '@/modules/crm/infrastructure/async/producers/notification-queue.producer';
import { IntegrationQueueProducer } from '@/modules/crm/infrastructure/async/producers/integration-queue.producer';
import { WorkflowQueueProducer } from '@/modules/crm/infrastructure/async/producers/workflow-queue.producer';
import { JobPriority } from '@/modules/crm/infrastructure/async/queue.module';

/**
 * Advanced Lead Created Event Handler
 * Orchestrates multiple async workflows when a lead is created
 */
@Injectable()
@EventsHandler(LeadCreatedEvent)
export class AdvancedLeadCreatedHandler implements IEventHandler<LeadCreatedEvent> {
  private readonly logger = new Logger(AdvancedLeadCreatedHandler.name);

  constructor(
    private readonly emailQueueProducer: EmailQueueProducer,
    private readonly notificationQueueProducer: NotificationQueueProducer,
    private readonly integrationQueueProducer: IntegrationQueueProducer,
    private readonly workflowQueueProducer: WorkflowQueueProducer,
  ) {}

  async handle(event: LeadCreatedEvent): Promise<void> {
    this.logger.log(`Handling LeadCreatedEvent for lead: ${event.lead.id}`);

    try {
      // Execute all workflows in parallel for better performance
      await Promise.allSettled([
        this.handleWelcomeEmail(event),
        this.handleAssignmentNotifications(event),
        this.handleIntegrationSync(event),
        this.handleWorkflowAutomation(event),
        this.handleDataEnrichment(event),
        this.handleComplianceChecks(event),
      ]);

      this.logger.log(`Successfully processed LeadCreatedEvent for lead: ${event.lead.id}`);
    } catch (error) {
      this.logger.error(`Failed to process LeadCreatedEvent for lead: ${event.lead.id}`, error);
      // Don't throw - we want other event handlers to continue processing
    }
  }

  /**
   * Send welcome email to the lead
   */
  private async handleWelcomeEmail(event: LeadCreatedEvent): Promise<void> {
    if (!event.lead.contactInfo?.email) {
      this.logger.debug(`No email address for lead: ${event.lead.id}, skipping welcome email`);
      return;
    }

    try {
      await this.emailQueueProducer.sendWelcomeEmail(
        {
          email: event.lead.contactInfo.email,
          name: event.lead.name,
          company: event.lead.contactInfo.company,
          source: event.source,
        },
        {
          priority: JobPriority.HIGH,
          delay: 5000, // 5 second delay to ensure lead is fully processed
        },
      );

      this.logger.debug(`Queued welcome email for lead: ${event.lead.id}`);
    } catch (error) {
      this.logger.error(`Failed to queue welcome email for lead: ${event.lead.id}`, error);
    }
  }

  /**
   * Send notifications to assigned users and team members
   */
  private async handleAssignmentNotifications(event: LeadCreatedEvent): Promise<void> {
    try {
      // Notify assigned user
      if (event.lead.assignedUserId) {
        await this.notificationQueueProducer.sendAssignmentNotification({
          userId: event.lead.assignedUserId.toString(),
          type: 'lead_assigned',
          title: 'New Lead Assigned',
          message: `You have been assigned a new lead: ${event.lead.name}`,
          data: {
            leadId: event.lead.id,
            leadName: event.lead.name,
            leadPriority: event.lead.priority?.value,
            source: event.source,
          },
        });
      }

      // Notify team members
      if (event.lead.teamId) {
        await this.notificationQueueProducer.sendTeamNotification({
          teamId: event.lead.teamId.toString(),
          type: 'new_lead_in_team',
          title: 'New Lead in Team',
          message: `A new lead has been added to your team: ${event.lead.name}`,
          data: {
            leadId: event.lead.id,
            leadName: event.lead.name,
            assignedUserId: event.lead.assignedUserId,
          },
        });
      }

      this.logger.debug(`Queued assignment notifications for lead: ${event.lead.id}`);
    } catch (error) {
      this.logger.error(`Failed to queue assignment notifications for lead: ${event.lead.id}`, error);
    }
  }

  /**
   * Update analytics and metrics
   */
  /**
   * Sync with external integrations
   */
  private async handleIntegrationSync(event: LeadCreatedEvent): Promise<void> {
    try {
      // Sync to Odoo CRM
      await this.integrationQueueProducer.syncToOdoo({
        entityType: 'lead',
        entityId: event.lead.id.toString(),
        operation: 'create',
        data: event.getPayload().leadData,
      });

      // Send webhook notifications to external systems
      if (event.shouldPublishExternally()) {
        await this.integrationQueueProducer.sendWebhook({
          event: 'lead.created',
          data: event.toJSON(),
          endpoints: await this.getWebhookEndpoints('lead.created'),
        });
      }

      this.logger.debug(`Queued integration sync for lead: ${event.lead.id}`);
    } catch (error) {
      this.logger.error(`Failed to queue integration sync for lead: ${event.lead.id}`, error);
    }
  }

  /**
   * Start workflow automation
   */
  private async handleWorkflowAutomation(event: LeadCreatedEvent): Promise<void> {
    try {
      // Auto-assignment workflow if not already assigned
      if (!event.lead.assignedUserId && event.lead.teamId) {
        await this.workflowQueueProducer.startAutoAssignment({
          leadId: event.lead.id,
          teamId: event.lead.teamId,
          criteria: {
            source: event.source,
            priority: event.lead.priority?.value,
            expectedRevenue: event.lead.expectedRevenue,
          },
        });
      }

      // Lead nurturing workflow
      await this.workflowQueueProducer.startLeadNurturing({
        leadId: event.lead.id,
        nurtureType: this.determineNurtureType(event),
        schedule: this.getNurturingSchedule(event),
      });

      this.logger.debug(`Queued workflow automation for lead: ${event.lead.id}`);
    } catch (error) {
      this.logger.error(`Failed to queue workflow automation for lead: ${event.lead.id}`, error);
    }
  }

  /**
   * Start data enrichment process
   */
  private async handleDataEnrichment(event: LeadCreatedEvent): Promise<void> {
    try {
      const needsEnrichment = this.checkIfEnrichmentNeeded(event.lead);
      
      if (needsEnrichment) {
        await this.workflowQueueProducer.startDataEnrichment({
          leadId: event.lead.id,
          enrichmentTypes: this.getEnrichmentTypes(event.lead),
          priority: event.lead.priority?.value >= 2 ? JobPriority.HIGH : JobPriority.NORMAL,
        });

        this.logger.debug(`Queued data enrichment for lead: ${event.lead.id}`);
      }
    } catch (error) {
      this.logger.error(`Failed to queue data enrichment for lead: ${event.lead.id}`, error);
    }
  }

  /**
   * Handle compliance and data protection checks
   */
  private async handleComplianceChecks(event: LeadCreatedEvent): Promise<void> {
    try {
      // GDPR consent verification
      await this.workflowQueueProducer.verifyGDPRConsent({
        leadId: event.lead.id,
        email: event.lead.contactInfo?.email,
        source: event.source,
        consentRequired: this.isGDPRConsentRequired(event),
      });

      // Data retention policy application
      await this.workflowQueueProducer.applyDataRetentionPolicy({
        leadId: event.lead.id,
        dataTypes: ['contact_info', 'activities', 'communications'],
        retentionPeriod: this.getRetentionPeriod(event),
      });

      this.logger.debug(`Queued compliance checks for lead: ${event.lead.id}`);
    } catch (error) {
      this.logger.error(`Failed to queue compliance checks for lead: ${event.lead.id}`, error);
    }
  }

  /**
   * Helper methods
   */
  private determineNurtureType(event: LeadCreatedEvent): string {
    if (event.lead.priority?.value >= 2) return 'aggressive';
    if (event.source === 'website') return 'educational';
    if (event.source === 'referral') return 'relationship';
    return 'standard';
  }

  private getNurturingSchedule(event: LeadCreatedEvent): string[] {
    // Return schedule based on lead characteristics
    return ['immediate', '1_day', '3_days', '1_week', '2_weeks'];
  }

  private checkIfEnrichmentNeeded(lead: any): boolean {
    return !lead.contactInfo?.company || 
           !lead.contactInfo?.phone || 
           !lead.contactInfo?.website;
  }

  private getEnrichmentTypes(lead: any): string[] {
    const types: string[] = [];
    if (!lead.contactInfo?.company) types.push('company_info');
    if (!lead.contactInfo?.phone) types.push('contact_details');
    if (!lead.contactInfo?.website) types.push('social_profiles');
    return types;
  }

  private isGDPRConsentRequired(event: LeadCreatedEvent): boolean {
    // Check if lead is from EU or consent is required based on source
    return event.source === 'website' || event.source === 'form';
  }

  private getRetentionPeriod(event: LeadCreatedEvent): number {
    // Return retention period in days based on lead type and regulations
    return 2555; // 7 years default
  }

  private async getWebhookEndpoints(eventType: string): Promise<string[]> {
    // In a real implementation, this would fetch from configuration
    return [
      'https://external-system.com/webhooks/crm',
      'https://analytics-platform.com/events',
    ];
  }
}
