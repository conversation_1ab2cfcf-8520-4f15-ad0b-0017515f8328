import { Injectable, Logger } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { CacheService, CacheKeyService, CACHE_CONSTANTS } from '@/shared/infrastructure/cache';

// Import query classes
import { GetLeadsByFiltersQuery } from '../cqrs/queries/get-leads-by-filters.query';
import { SearchLeadsQuery } from '../cqrs/queries/search-leads.query';
import { GetPipelineAnalyticsQuery } from '../cqrs/queries/get-pipeline-analytics.query';
import { GetLeadStatisticsQuery } from '../cqrs/queries/get-lead-statistics.query';
import { GetOverdueLeadsQuery } from '../cqrs/queries/get-overdue-leads.query';
import { GetLeadsRequiringAttentionQuery } from '../cqrs/queries/get-leads-requiring-attention.query';

/**
 * Enhanced Query Service
 * Provides high-level query operations with caching, optimization, and analytics
 */
@Injectable()
export class QueryService {
  private readonly logger = new Logger(QueryService.name);

  constructor(
    private readonly queryBus: QueryBus,
    private readonly cacheService: CacheService,
    private readonly cacheKeyService: CacheKeyService,
  ) {}

  /**
   * Execute filtered lead query with caching
   */
  async getLeadsByFilters(filters: any): Promise<{
    leads: any[];
    total: number;
    hasMore: boolean;
    cached: boolean;
  }> {
    this.logger.log(`Executing filtered leads query`);

    try {
      const query = new GetLeadsByFiltersQuery(filters);
      const result = await this.queryBus.execute(query);
      
      return result;
    } catch (error) {
      this.logger.error(`Failed to execute filtered leads query:`, error);
      throw error;
    }
  }

  /**
   * Execute search query with caching and analytics
   */
  async searchLeads(searchTerm: string, filters?: any, limit?: number): Promise<{
    leads: any[];
    searchTerm: string;
    totalResults: number;
    searchTime: number;
    suggestions?: string[];
    cached: boolean;
  }> {
    this.logger.log(`Executing search query for: "${searchTerm}"`);

    try {
      const query = new SearchLeadsQuery(searchTerm, filters || {}, limit);
      const result = await this.queryBus.execute(query);
      
      return result;
    } catch (error) {
      this.logger.error(`Failed to execute search query:`, error);
      throw error;
    }
  }

  /**
   * Get pipeline analytics with advanced caching
   */
  async getPipelineAnalytics(options: {
    teamId?: number;
    userId?: number;
    dateFrom?: Date;
    dateTo?: Date;
    includeStageMetrics?: boolean;
    includeConversionRates?: boolean;
    includeBottlenecks?: boolean;
    includeForecast?: boolean;
    includeComparisons?: boolean;
  }): Promise<any> {
    this.logger.log(`Executing pipeline analytics query`);

    try {
      const query = new GetPipelineAnalyticsQuery(
        options.teamId,
        options.userId,
        options.dateFrom,
        options.dateTo,
        options.includeStageMetrics,
        options.includeConversionRates,
        options.includeBottlenecks,
        options.includeForecast,
        options.includeComparisons,
      );

      const result = await this.queryBus.execute(query);
      return result;
    } catch (error) {
      this.logger.error(`Failed to execute pipeline analytics query:`, error);
      throw error;
    }
  }

  /**
   * Get lead statistics with caching
   */
  async getLeadStatistics(filters?: any): Promise<any> {
    this.logger.log(`Executing lead statistics query`);

    try {
      const cacheKey = this.cacheKeyService.generateAnalyticsKey('lead.statistics', filters || {});
      
      const result = await this.cacheService.getOrSet(
        cacheKey,
        async () => {
          const query = new GetLeadStatisticsQuery(filters);
          return await this.queryBus.execute(query);
        },
        CACHE_CONSTANTS.ANALYTICS_TTL,
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to execute lead statistics query:`, error);
      throw error;
    }
  }

  /**
   * Get overdue leads with caching
   */
  async getOverdueLeads(teamId?: number, userId?: number): Promise<any[]> {
    this.logger.log(`Executing overdue leads query`);

    try {
      const cacheKey = this.cacheKeyService.generateQueryKey('leads.overdue', { teamId, userId });
      
      const result = await this.cacheService.getOrSet(
        cacheKey,
        async () => {
          const query = new GetOverdueLeadsQuery(teamId, userId);
          return await this.queryBus.execute(query);
        },
        CACHE_CONSTANTS.LIST_TTL,
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to execute overdue leads query:`, error);
      throw error;
    }
  }

  /**
   * Get leads requiring attention with caching
   */
  async getLeadsRequiringAttention(teamId?: number, userId?: number): Promise<any[]> {
    this.logger.log(`Executing leads requiring attention query`);

    try {
      const cacheKey = this.cacheKeyService.generateQueryKey('leads.attention', { teamId, userId });
      
      const result = await this.cacheService.getOrSet(
        cacheKey,
        async () => {
          const query = new GetLeadsRequiringAttentionQuery(teamId, userId);
          return await this.queryBus.execute(query);
        },
        CACHE_CONSTANTS.LIST_TTL,
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to execute leads requiring attention query:`, error);
      throw error;
    }
  }

  /**
   * Execute multiple queries in parallel with caching
   */
  async executeParallelQueries(queries: Array<{
    name: string;
    query: any;
    cacheKey?: string;
    ttl?: number;
  }>): Promise<Record<string, any>> {
    this.logger.log(`Executing ${queries.length} parallel queries`);

    try {
      const promises = queries.map(async ({ name, query, cacheKey, ttl }) => {
        if (cacheKey) {
          const result = await this.cacheService.getOrSet(
            cacheKey,
            async () => await this.queryBus.execute(query),
            ttl || CACHE_CONSTANTS.DEFAULT_TTL,
          );
          return { name, result };
        } else {
          const result = await this.queryBus.execute(query);
          return { name, result };
        }
      });

      const results = await Promise.all(promises);
      
      return results.reduce((acc, { name, result }) => {
        acc[name] = result;
        return acc;
      }, {} as Record<string, any>);

    } catch (error) {
      this.logger.error(`Failed to execute parallel queries:`, error);
      throw error;
    }
  }

  /**
   * Warm up cache for common queries
   */
  async warmUpCache(): Promise<void> {
    this.logger.log(`Warming up query cache`);

    try {
      const warmUpQueries = [
        {
          name: 'recent_leads',
          factory: async () => {
            const result = await this.getLeadsByFilters({ limit: 20, sortBy: 'createdAt', sortOrder: 'desc' });
            return result;
          },
          ttl: CACHE_CONSTANTS.LIST_TTL,
        },
        {
          name: 'overdue_leads',
          factory: async () => {
            const result = await this.getOverdueLeads();
            return { leads: result, total: result.length, hasMore: false, cached: false };
          },
          ttl: CACHE_CONSTANTS.LIST_TTL,
        },
        {
          name: 'attention_leads',
          factory: async () => {
            const result = await this.getLeadsRequiringAttention();
            return { leads: result, total: result.length, hasMore: false, cached: false };
          },
          ttl: CACHE_CONSTANTS.LIST_TTL,
        },
      ];

      const promises = warmUpQueries.map(async ({ name, factory, ttl }) => {
        try {
          const key = this.cacheKeyService.generateQueryKey(`warmup.${name}`, {});
          await this.cacheService.warmUp(key, factory, ttl);
        } catch (error) {
          this.logger.warn(`Failed to warm up cache for ${name}:`, error);
        }
      });

      await Promise.all(promises);
      this.logger.log(`Cache warm up completed`);

    } catch (error) {
      this.logger.error(`Failed to warm up cache:`, error);
    }
  }

  /**
   * Invalidate query cache by pattern
   */
  async invalidateQueryCache(pattern: string): Promise<void> {
    this.logger.log(`Invalidating query cache with pattern: ${pattern}`);

    try {
      await this.cacheService.invalidateQueryPattern(pattern);
    } catch (error) {
      this.logger.error(`Failed to invalidate query cache:`, error);
    }
  }

  /**
   * Get query performance metrics
   */
  async getQueryMetrics(): Promise<{
    cacheStats: any;
    queryCount: number;
    averageResponseTime: number;
  }> {
    try {
      const cacheStats = await this.cacheService.getStats();
      
      return {
        cacheStats,
        queryCount: 0, // Would need to implement query counting
        averageResponseTime: 0, // Would need to implement response time tracking
      };
    } catch (error) {
      this.logger.error(`Failed to get query metrics:`, error);
      return {
        cacheStats: { status: 'error' },
        queryCount: 0,
        averageResponseTime: 0,
      };
    }
  }
}
