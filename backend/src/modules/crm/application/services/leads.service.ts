import { Injectable, Logger, Inject, NotFoundException, BadRequestException } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
import { LeadType } from '@/modules/crm/domain/value-objects/lead-type.vo';
import { LeadPriority } from '@/modules/crm/domain/value-objects/lead-priority.vo';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';

// CQRS Commands
import { CreateLeadCommand } from '../cqrs/commands/create-lead.command';
import { UpdateLeadCommand } from '../cqrs/commands/update-lead.command';
import { DeleteLeadCommand } from '../cqrs/commands/delete-lead.command';
import { AssignLeadToUserCommand } from '../cqrs/commands/assign-lead-to-user.command';
import { AssignLeadToTeamCommand } from '../cqrs/commands/assign-lead-to-team.command';
import { ConvertLeadToOpportunityCommand } from '../cqrs/commands/convert-lead-to-opportunity.command';

// CQRS Queries
import { GetLeadByIdQuery } from '../cqrs/queries/get-lead-by-id.query';
import { GetLeadsByFiltersQuery } from '../cqrs/queries/get-leads-by-filters.query';
import { SearchLeadsQuery } from '../cqrs/queries/search-leads.query';
import { GetPipelineAnalyticsQuery } from '../cqrs/queries/get-pipeline-analytics.query';

// DTOs
import { CreateLeadDto } from '@/modules/crm/presentation/dto/create-lead.dto';
import { UpdateLeadDto } from '@/modules/crm/presentation/dto/update-lead.dto';
import { LeadFilterDto } from '@/modules/crm/presentation/dto/lead-filter.dto';
import { SearchLeadDto } from '@/modules/crm/presentation/dto/lead-search.dto';
import { LeadResponseDto } from '@/modules/crm/presentation/dto/lead-response.dto';
import { BulkUpdateLeadDto } from '@/modules/crm/presentation/dto/lead-mapped-types.dto';

/**
 * Traditional Leads Service
 * Provides business logic abstraction and integrates with CQRS infrastructure
 * Supports both CQRS patterns and direct repository access for flexibility
 */
@Injectable()
export class LeadsService {
  private readonly logger = new Logger(LeadsService.name);

  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  /**
   * Create a new lead
   * Uses CQRS command for complex business logic
   */
  async create(createLeadDto: CreateLeadDto, userId: number, companyId?: number): Promise<{ leadId: number; lead: Lead }> {
    this.logger.log(`Creating lead: ${createLeadDto.name} by user: ${userId}`);

    try {
      const command = new CreateLeadCommand(
        createLeadDto.name,
        createLeadDto.email,
        createLeadDto.phone,
        createLeadDto.company,
        createLeadDto.website,
        createLeadDto.address,
        createLeadDto.city,
        createLeadDto.country,
        createLeadDto.source || 'website',
        createLeadDto.type ? LeadType.fromValue(createLeadDto.type) : LeadType.LEAD,
        createLeadDto.priority !== undefined ? LeadPriority.fromValue(createLeadDto.priority) : LeadPriority.MEDIUM,
        createLeadDto.expectedRevenue,
        createLeadDto.probability,
        createLeadDto.description,
        createLeadDto.assignedUserId,
        createLeadDto.teamId,
        createLeadDto.campaignId,
        createLeadDto.sourceId,
        createLeadDto.mediumId,
        createLeadDto.tags,
        createLeadDto.dateDeadline ? new Date(createLeadDto.dateDeadline) : undefined,
        userId, // createdBy
        companyId,
      );

      const result = await this.commandBus.execute(command);
      this.logger.log(`Successfully created lead with ID: ${result.leadId}`);
      
      return result;
    } catch (error) {
      this.logger.error(`Failed to create lead: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to create lead: ${error.message}`);
    }
  }

  /**
   * Find lead by ID
   * Uses CQRS query for consistency
   */
  async findById(id: number): Promise<Lead | null> {
    this.logger.log(`Finding lead by ID: ${id}`);

    try {
      const query = new GetLeadByIdQuery(id);
      const lead = await this.queryBus.execute(query);
      
      if (!lead) {
        this.logger.warn(`Lead not found with ID: ${id}`);
        return null;
      }

      return lead;
    } catch (error) {
      this.logger.error(`Failed to find lead by ID ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Find lead by ID with exception if not found
   */
  async findByIdOrFail(id: number): Promise<Lead> {
    const lead = await this.findById(id);
    if (!lead) {
      throw new NotFoundException(`Lead with ID ${id} not found`);
    }
    return lead;
  }

  /**
   * Find leads with filters
   * Uses CQRS query for complex filtering
   */
  async findByFilters(filterDto: LeadFilterDto): Promise<Lead[]> {
    this.logger.log(`Finding leads with filters: ${JSON.stringify(filterDto)}`);

    try {
      const query = new GetLeadsByFiltersQuery({
        priority: filterDto.priority?.toString(),
        status: filterDto.status,
        assignedUserId: filterDto.assignedUserId,
        teamId: filterDto.teamId,
        source: filterDto.source,
        tags: filterDto.tags,
        dateFrom: filterDto.createdFrom ? new Date(filterDto.createdFrom) : undefined,
        dateTo: filterDto.createdTo ? new Date(filterDto.createdTo) : undefined,
        limit: filterDto.limit,
        offset: filterDto.offset,
      });

      const leads = await this.queryBus.execute(query);
      this.logger.log(`Found ${leads.length} leads with filters`);
      
      return leads;
    } catch (error) {
      this.logger.error(`Failed to find leads with filters: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Search leads with text query
   * Uses CQRS query for search functionality
   */
  async search(searchDto: SearchLeadDto): Promise<Lead[]> {
    this.logger.log(`Searching leads with term: ${searchDto.searchTerm}`);

    try {
      const query = new SearchLeadsQuery(
        searchDto.searchTerm || '',
        {
          assignedUserId: searchDto.assignedUserId,
          teamId: searchDto.teamId,
        },
        searchDto.limit,
      );

      const leads = await this.queryBus.execute(query);
      this.logger.log(`Found ${leads.length} leads with search term: ${searchDto.searchTerm}`);
      
      return leads;
    } catch (error) {
      this.logger.error(`Failed to search leads: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update lead
   * Uses CQRS command for business logic
   */
  async update(id: number, updateLeadDto: UpdateLeadDto, userId: number): Promise<Lead> {
    this.logger.log(`Updating lead ${id} by user: ${userId}`);

    try {
      // First check if lead exists
      await this.findByIdOrFail(id);

      const command = new UpdateLeadCommand(
        id,
        {
          name: updateLeadDto.name,
          description: updateLeadDto.description,
          expectedRevenue: updateLeadDto.expectedRevenue,
          probability: updateLeadDto.probability,
          priority: updateLeadDto.priority,
          assignedUserId: updateLeadDto.assignedUserId,
          teamId: updateLeadDto.teamId,
          tags: updateLeadDto.tags,
          dateDeadline: updateLeadDto.dateDeadline ? new Date(updateLeadDto.dateDeadline) : undefined,
          contactInfo: {
            email: updateLeadDto.email,
            phone: updateLeadDto.phone,
            company: updateLeadDto.company,
            website: updateLeadDto.website,
          },
        }
      );

      await this.commandBus.execute(command);
      
      // Return updated lead
      const updatedLead = await this.findByIdOrFail(id);
      this.logger.log(`Successfully updated lead with ID: ${id}`);
      
      return updatedLead;
    } catch (error) {
      this.logger.error(`Failed to update lead ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete lead
   * Uses CQRS command for business logic
   */
  async delete(id: number, userId: number): Promise<void> {
    this.logger.log(`Deleting lead ${id} by user: ${userId}`);

    try {
      // First check if lead exists
      await this.findByIdOrFail(id);

      const command = new DeleteLeadCommand(id);
      await this.commandBus.execute(command);
      
      this.logger.log(`Successfully deleted lead with ID: ${id}`);
    } catch (error) {
      this.logger.error(`Failed to delete lead ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Assign lead to user
   * Uses CQRS command for business logic
   */
  async assignToUser(id: number, userId: number, assignedBy: number): Promise<Lead> {
    this.logger.log(`Assigning lead ${id} to user ${userId} by user: ${assignedBy}`);

    try {
      // First check if lead exists
      await this.findByIdOrFail(id);

      const command = new AssignLeadToUserCommand(id, userId, assignedBy);
      await this.commandBus.execute(command);
      
      // Return updated lead
      const updatedLead = await this.findByIdOrFail(id);
      this.logger.log(`Successfully assigned lead ${id} to user ${userId}`);
      
      return updatedLead;
    } catch (error) {
      this.logger.error(`Failed to assign lead ${id} to user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Assign lead to team
   * Uses CQRS command for business logic
   */
  async assignToTeam(id: number, teamId: number, assignedBy: number): Promise<Lead> {
    this.logger.log(`Assigning lead ${id} to team ${teamId} by user: ${assignedBy}`);

    try {
      // First check if lead exists
      await this.findByIdOrFail(id);

      const command = new AssignLeadToTeamCommand(id, teamId, assignedBy);
      await this.commandBus.execute(command);
      
      // Return updated lead
      const updatedLead = await this.findByIdOrFail(id);
      this.logger.log(`Successfully assigned lead ${id} to team ${teamId}`);
      
      return updatedLead;
    } catch (error) {
      this.logger.error(`Failed to assign lead ${id} to team ${teamId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Convert lead to opportunity
   * Uses CQRS command for complex business logic
   */
  async convertToOpportunity(id: number, userId: number): Promise<any> {
    this.logger.log(`Converting lead ${id} to opportunity by user: ${userId}`);

    try {
      // First check if lead exists
      await this.findByIdOrFail(id);

      const command = new ConvertLeadToOpportunityCommand(id, userId);
      const result = await this.commandBus.execute(command);

      this.logger.log(`Successfully converted lead ${id} to opportunity`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to convert lead ${id} to opportunity: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get pipeline analytics
   * Uses CQRS query for analytics
   */
  async getPipelineAnalytics(teamId?: number, dateFrom?: Date, dateTo?: Date): Promise<any> {
    this.logger.log(`Getting pipeline analytics for team: ${teamId}`);

    try {
      const query = new GetPipelineAnalyticsQuery(teamId, undefined, dateFrom, dateTo);
      const analytics = await this.queryBus.execute(query);

      this.logger.log(`Successfully retrieved pipeline analytics`);
      return analytics;
    } catch (error) {
      this.logger.error(`Failed to get pipeline analytics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk update leads
   * Uses direct repository access for performance
   */
  async bulkUpdate(bulkUpdateDto: BulkUpdateLeadDto, userId: number): Promise<{ successful: number[]; failed: Array<{ id: number; error: string }> }> {
    this.logger.log(`Bulk updating ${bulkUpdateDto.leadIds.length} leads by user: ${userId}`);

    const successful: number[] = [];
    const failed: Array<{ id: number; error: string }> = [];

    for (const leadId of bulkUpdateDto.leadIds) {
      try {
        // Check if lead exists
        await this.findByIdOrFail(leadId);

        // Prepare update data
        const updates: any = {};
        if (bulkUpdateDto.assignedUserId !== undefined) updates.assignedUserId = bulkUpdateDto.assignedUserId;
        if (bulkUpdateDto.teamId !== undefined) updates.teamId = bulkUpdateDto.teamId;
        if (bulkUpdateDto.priority !== undefined) updates.priority = bulkUpdateDto.priority;
        if (bulkUpdateDto.source !== undefined) updates.source = bulkUpdateDto.source;
        if (bulkUpdateDto.tags !== undefined) updates.tags = bulkUpdateDto.tags;

        // Use repository for bulk operations (performance)
        await this.leadRepository.bulkUpdate([leadId], updates);
        successful.push(leadId);

      } catch (error) {
        this.logger.warn(`Failed to update lead ${leadId}: ${error.message}`);
        failed.push({ id: leadId, error: error.message });
      }
    }

    this.logger.log(`Bulk update completed: ${successful.length} successful, ${failed.length} failed`);
    return { successful, failed };
  }

  /**
   * Find all leads with pagination
   * Uses direct repository access for simple queries
   */
  async findAll(page: number = 1, limit: number = 20, sortBy: string = 'createdAt', sortOrder: 'asc' | 'desc' = 'desc'): Promise<{ leads: Lead[]; total: number }> {
    this.logger.log(`Finding all leads - page: ${page}, limit: ${limit}`);

    try {
      const offset = (page - 1) * limit;

      const result = await this.leadRepository.findMany({
        offset,
        limit,
        sortBy,
        sortOrder,
      });

      this.logger.log(`Found ${result.leads.length} leads out of ${result.total} total`);
      return {
        leads: result.leads,
        total: result.total,
      };
    } catch (error) {
      this.logger.error(`Failed to find all leads: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Find leads by email
   * Uses direct repository access for simple queries
   */
  async findByEmail(email: string): Promise<Lead | null> {
    this.logger.log(`Finding lead by email: ${email}`);

    try {
      const lead = await this.leadRepository.findByEmail(email);
      return lead;
    } catch (error) {
      this.logger.error(`Failed to find lead by email ${email}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Find leads by assigned user
   * Uses direct repository access for simple queries
   */
  async findByAssignedUser(userId: number): Promise<Lead[]> {
    this.logger.log(`Finding leads by assigned user: ${userId}`);

    try {
      const leads = await this.leadRepository.findByAssignedUserId(userId);
      this.logger.log(`Found ${leads.length} leads for user ${userId}`);
      return leads;
    } catch (error) {
      this.logger.error(`Failed to find leads by user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Find leads by team
   * Uses direct repository access for simple queries
   */
  async findByTeam(teamId: number): Promise<Lead[]> {
    this.logger.log(`Finding leads by team: ${teamId}`);

    try {
      const leads = await this.leadRepository.findByTeamId(teamId);
      this.logger.log(`Found ${leads.length} leads for team ${teamId}`);
      return leads;
    } catch (error) {
      this.logger.error(`Failed to find leads by team ${teamId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check if lead exists
   * Utility method for validation
   */
  async exists(id: number): Promise<boolean> {
    try {
      const lead = await this.findById(id);
      return lead !== null;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get lead count by filters
   * Uses direct repository access for analytics
   */
  async getCount(filters?: any): Promise<number> {
    this.logger.log(`Getting lead count with filters: ${JSON.stringify(filters)}`);

    try {
      const result = await this.leadRepository.findMany({
        ...filters,
        limit: 0, // Only count
      });

      return result.total;
    } catch (error) {
      this.logger.error(`Failed to get lead count: ${error.message}`, error.stack);
      throw error;
    }
  }
}
