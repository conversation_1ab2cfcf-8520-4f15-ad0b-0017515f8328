import { IEvent } from '@nestjs/cqrs';
import { Opportunity } from '@/modules/crm/domain/entities/opportunity.entity';

/**
 * Lead Converted to Opportunity Event
 * Published when a lead is successfully converted to an opportunity
 */
export class LeadConvertedToOpportunityEvent implements IEvent {
  constructor(
    public readonly leadId: number,
    public readonly opportunityId: number,
    public readonly opportunity: Opportunity,
    public readonly convertedBy?: number,
    public readonly convertedAt: Date = new Date(),
    public readonly reason?: string,
    public readonly commandMetadata?: any,
    public readonly timestamp: Date = new Date(),
  ) {}

  /**
   * Get event metadata for logging and auditing
   */
  getMetadata() {
    return {
      eventType: 'LeadConvertedToOpportunityEvent',
      leadId: this.leadId,
      opportunityId: this.opportunityId,
      opportunityName: this.opportunity.name,
      expectedRevenue: this.opportunity.expectedRevenue,
      probability: this.opportunity.probability,
      weightedRevenue: this.opportunity.getWeightedRevenue(),
      convertedBy: this.convertedBy,
      reason: this.reason,
      timestamp: this.timestamp.toISOString(),
      commandMetadata: this.commandMetadata,
    };
  }

  /**
   * Get event payload for external systems
   */
  getPayload() {
    return {
      eventId: `lead-converted-${this.leadId}-${this.opportunityId}-${this.timestamp.getTime()}`,
      eventType: 'lead.converted_to_opportunity',
      version: '1.0',
      timestamp: this.timestamp.toISOString(),
      data: {
        leadId: this.leadId,
        opportunity: this.opportunity.toPlainObject(),
        convertedBy: this.convertedBy,
        reason: this.reason,
        metadata: this.getMetadata(),
      },
    };
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject() {
    return {
      leadId: this.leadId,
      opportunityId: this.opportunityId,
      opportunity: this.opportunity.toPlainObject(),
      convertedBy: this.convertedBy,
      reason: this.reason,
      commandMetadata: this.commandMetadata,
      timestamp: this.timestamp,
      metadata: this.getMetadata(),
      payload: this.getPayload(),
    };
  }
}
