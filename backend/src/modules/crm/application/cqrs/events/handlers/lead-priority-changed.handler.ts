import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { Logger } from '@nestjs/common';
import { LeadPriorityChangedEvent } from '../lead-priority-changed.event';

/**
 * Lead Priority Changed Event Handler
 * Handles the lead priority changed event
 */
@EventsHandler(LeadPriorityChangedEvent)
export class LeadPriorityChangedHandler implements IEventHandler<LeadPriorityChangedEvent> {
  private readonly logger = new Logger(LeadPriorityChangedHandler.name);

  async handle(event: LeadPriorityChangedEvent): Promise<void> {
    const { leadId, previousPriority, newPriority, changedBy, changedAt } = event;

    this.logger.log(`Lead ${leadId} priority changed from ${previousPriority} to ${newPriority} by user ${changedBy}`);

    try {
      // Example: Send priority change notification for high priority leads
      if (newPriority === 'high') {
        // await this.notificationService.sendHighPriorityNotification(leadId);
      }
      
      // Example: Update priority metrics
      // await this.analyticsService.updatePriorityMetrics(leadId, newPriority);
      
      // Example: Trigger priority-based workflows
      // await this.workflowService.triggerPriorityWorkflow(leadId, newPriority);
      
    } catch (error) {
      this.logger.error(`Failed to handle lead priority changed event for lead ${leadId}`, error);
    }
  }
}
