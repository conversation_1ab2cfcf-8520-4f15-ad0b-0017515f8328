import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { Logger } from '@nestjs/common';
import { LeadConvertedToOpportunityEvent } from '../lead-converted-to-opportunity.event';

/**
 * Lead Converted to Opportunity Event Handler
 * Handles the lead converted to opportunity event
 */
@EventsHandler(LeadConvertedToOpportunityEvent)
export class LeadConvertedToOpportunityHandler implements IEventHandler<LeadConvertedToOpportunityEvent> {
  private readonly logger = new Logger(LeadConvertedToOpportunityHandler.name);

  async handle(event: LeadConvertedToOpportunityEvent): Promise<void> {
    const { leadId, opportunityId, convertedBy, convertedAt } = event;

    this.logger.log(`Lead ${leadId} converted to opportunity ${opportunityId} by user ${convertedBy}`);

    try {
      // Example: Send conversion notification
      // await this.notificationService.sendConversionNotification(leadId, opportunityId);
      
      // Example: Update conversion metrics
      // await this.analyticsService.updateConversionMetrics(leadId, opportunityId);
      
      // Example: Trigger follow-up workflows
      // await this.workflowService.triggerOpportunityWorkflow(opportunityId);
      
    } catch (error) {
      this.logger.error(`Failed to handle lead conversion event for lead ${leadId}`, error);
    }
  }
}
