import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { Logger } from '@nestjs/common';
import { LeadAssignedEvent } from '../lead-assigned.event';

/**
 * Lead Assigned Event Handler
 * Handles the lead assigned event
 */
@EventsHandler(LeadAssignedEvent)
export class LeadAssignedHandler implements IEventHandler<LeadAssignedEvent> {
  private readonly logger = new Logger(LeadAssignedHandler.name);

  async handle(event: LeadAssignedEvent): Promise<void> {
    const { leadId, assignedUserId, assignedTeamId, assignedBy, assignedAt } = event;

    this.logger.log(`Lead ${leadId} assigned to user ${assignedUserId} and team ${assignedTeamId} by user ${assignedBy}`);

    try {
      // Example: Send assignment notification
      // await this.notificationService.sendAssignmentNotification(leadId, assignedUserId);
      
      // Example: Update workload metrics
      // await this.analyticsService.updateWorkloadMetrics(assignedUserId, assignedTeamId);
      
      // Example: Trigger assignment workflows
      // await this.workflowService.triggerAssignmentWorkflow(leadId, assignedUserId);
      
    } catch (error) {
      this.logger.error(`Failed to handle lead assignment event for lead ${leadId}`, error);
    }
  }
}
