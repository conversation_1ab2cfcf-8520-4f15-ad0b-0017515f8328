import { IEvent } from '@nestjs/cqrs';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';

/**
 * Lead Created Event
 * Published when a new lead is successfully created in the CRM system
 */
export class LeadCreatedEvent implements IEvent {
  constructor(
    public readonly leadId: number,
    public readonly lead: Lead,
    public readonly createdBy?: number,
    public readonly commandMetadata?: any,
    public readonly timestamp: Date = new Date(),
  ) {}

  /**
   * Get event metadata for logging and auditing
   */
  getMetadata() {
    return {
      eventType: 'LeadCreatedEvent',
      leadId: this.leadId,
      leadName: this.lead.name,
      leadType: this.lead.type.value,
      priority: this.lead.priority.value,
      source: this.lead.source,
      hasRevenue: !!this.lead.expectedRevenue,
      isAssigned: !!this.lead.assignedUserId,
      hasTeam: !!this.lead.teamId,
      createdBy: this.createdBy,
      timestamp: this.timestamp.toISOString(),
      commandMetadata: this.commandMetadata,
    };
  }

  /**
   * Get event payload for external systems
   */
  getPayload() {
    return {
      eventId: `lead-created-${this.leadId}-${this.timestamp.getTime()}`,
      eventType: 'lead.created',
      version: '1.0',
      timestamp: this.timestamp.toISOString(),
      data: {
        lead: this.lead.toPlainObject(),
        createdBy: this.createdBy,
        metadata: this.getMetadata(),
      },
    };
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject() {
    return {
      leadId: this.leadId,
      lead: this.lead.toPlainObject(),
      createdBy: this.createdBy,
      commandMetadata: this.commandMetadata,
      timestamp: this.timestamp,
      metadata: this.getMetadata(),
      payload: this.getPayload(),
    };
  }
}
