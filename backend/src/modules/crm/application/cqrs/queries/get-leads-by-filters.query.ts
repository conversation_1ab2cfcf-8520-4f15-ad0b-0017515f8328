/**
 * Get Leads by Filters Query
 * Query to retrieve leads based on filters
 */
export class GetLeadsByFiltersQuery {
  constructor(
    public readonly filters: LeadFilters,
  ) {}
}

/**
 * Lead Filters Interface
 */
export interface LeadFilters {
  status?: string;
  priority?: string;
  assignedUserId?: number;
  teamId?: number;
  source?: string;
  tags?: string[];
  dateFrom?: Date;
  dateTo?: Date;
  minRevenue?: number;
  maxRevenue?: number;
  limit?: number;
  offset?: number;
}
