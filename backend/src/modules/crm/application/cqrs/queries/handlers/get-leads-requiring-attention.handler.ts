import { Query<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Inject } from '@nestjs/common';
import { GetLeadsRequiringAttentionQuery } from '../get-leads-requiring-attention.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';

/**
 * Get Leads Requiring Attention Query Handler
 * Handles retrieving leads that require immediate attention
 */
@QueryHandler(GetLeadsRequiringAttentionQuery)
export class GetLeadsRequiringAttentionHandler implements IQueryHandler<GetLeadsRequiringAttentionQuery> {
  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  async execute(query: GetLeadsRequiringAttentionQuery): Promise<Lead[]> {
    const { teamId, userId } = query;
    
    // Build filters
    const filters: any = {};
    
    if (teamId) {
      filters.teamId = teamId;
    }
    
    if (userId) {
      filters.assignedUserId = userId;
    }
    
    // Get all leads
    const leads = await this.leadRepository.findByFilters(filters);
    
    // Filter leads requiring attention
    return leads.filter(lead => this.requiresAttention(lead));
  }

  private requiresAttention(lead: Lead): boolean {
    const now = new Date();
    
    // High priority leads (2=High, 3=Very High)
    if (lead.priority?.value >= 2) {
      return true;
    }
    
    // Overdue leads
    if (lead.dateDeadline && lead.dateDeadline < now) {
      return true;
    }
    
    // Leads due soon (within 24 hours)
    if (lead.dateDeadline) {
      const hoursUntilDeadline = (lead.dateDeadline.getTime() - now.getTime()) / (1000 * 60 * 60);
      if (hoursUntilDeadline <= 24 && hoursUntilDeadline > 0) {
        return true;
      }
    }
    
    // Leads not contacted for a long time (7 days)
    if (lead.createdAt) {
      const daysSinceCreated = (now.getTime() - lead.createdAt.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceCreated > 7 && !lead.assignedUserId) {
        return true;
      }
    }
    
    // High value leads without assignment
    if ((lead.expectedRevenue || 0) > 50000 && !lead.assignedUserId) {
      return true;
    }
    
    return false;
  }
}
