import { Query<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { GetLeadStatisticsQuery } from '../get-lead-statistics.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';

/**
 * Get Lead Statistics Query Handler
 * Handles retrieving lead statistics
 */
@Injectable()
@QueryHandler(GetLeadStatisticsQuery)
export class GetLeadStatisticsHandler implements IQueryHandler<GetLeadStatisticsQuery> {
  private readonly logger = new Logger(GetLeadStatisticsHandler.name);

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  async execute(query: GetLeadStatisticsQuery): Promise<LeadStatistics> {
    const { filters } = query;
    
    // Get all leads based on filters
    const leads = await this.leadRepository.findByFilters(filters);
    
    // Calculate statistics
    const totalLeads = leads.length;
    const qualifiedLeads = leads.filter(lead => lead.isQualified()).length;
    const convertedLeads = leads.filter(lead => lead.isConverted()).length;
    const totalRevenue = leads.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
    const averageRevenue = totalLeads > 0 ? totalRevenue / totalLeads : 0;
    const conversionRate = totalLeads > 0 ? (convertedLeads / totalLeads) * 100 : 0;
    
    // Group by status
    const statusDistribution = leads.reduce((acc, lead) => {
      const status = lead.status?.value || 'unknown';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // Group by source
    const sourceDistribution = leads.reduce((acc, lead) => {
      const source = lead.source || 'unknown';
      acc[source] = (acc[source] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalLeads,
      qualifiedLeads,
      convertedLeads,
      totalRevenue,
      averageRevenue,
      conversionRate,
      statusDistribution,
      sourceDistribution,
    };
  }
}

/**
 * Lead Statistics Interface
 */
export interface LeadStatistics {
  totalLeads: number;
  qualifiedLeads: number;
  convertedLeads: number;
  totalRevenue: number;
  averageRevenue: number;
  conversionRate: number;
  statusDistribution: Record<string, number>;
  sourceDistribution: Record<string, number>;
}
