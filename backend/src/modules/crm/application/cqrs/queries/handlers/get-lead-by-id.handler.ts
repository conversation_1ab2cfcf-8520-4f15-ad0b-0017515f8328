import { Query<PERSON><PERSON><PERSON>, <PERSON><PERSON>uery<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Inject } from '@nestjs/common';
import { GetLeadByIdQuery } from '../get-lead-by-id.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';

/**
 * Get Lead by ID Query Handler
 * Handles retrieving a lead by its ID
 */
@QueryHandler(GetLeadByIdQuery)
export class GetLeadByIdHandler implements IQueryHandler<GetLeadByIdQuery> {
  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  async execute(query: GetLeadByIdQuery): Promise<Lead | null> {
    const { leadId } = query;
    return this.leadRepository.findById(leadId);
  }
}
