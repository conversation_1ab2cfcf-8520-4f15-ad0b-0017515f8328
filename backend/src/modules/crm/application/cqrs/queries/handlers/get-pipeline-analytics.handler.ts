import { Query<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Injectable, Logger } from '@nestjs/common';
import { GetPipelineAnalyticsQuery } from '../get-pipeline-analytics.query';
import { ILeadRepository } from '../../../../domain/repositories/lead.repository';
import { IStageRepository } from '../../../../domain/repositories/stage.repository';
import { ITeamRepository } from '../../../../domain/repositories/team.repository';

/**
 * Pipeline Analytics Result Interface
 */
export interface PipelineAnalyticsResult {
  summary: {
    totalLeads: number;
    totalOpportunities: number;
    totalRevenue: number;
    weightedRevenue: number;
    averageDealSize: number;
    conversionRate: number;
    winRate: number;
    averageSalesCycle: number;
  };
  stageMetrics?: Array<{
    stageId: number;
    stageName: string;
    sequence: number;
    leadCount: number;
    opportunityCount: number;
    totalRevenue: number;
    weightedRevenue: number;
    averageProbability: number;
    averageTimeInStage: number;
    conversionRate: number;
    dropOffRate: number;
  }>;
  conversionRates?: {
    leadToOpportunity: number;
    opportunityToWon: number;
    overallConversion: number;
    byStage: Record<number, number>;
    byTeam: Record<number, number>;
    bySource: Record<string, number>;
  };
  bottlenecks?: Array<{
    stageId: number;
    stageName: string;
    averageTimeInStage: number;
    dropOffRate: number;
    severity: 'low' | 'medium' | 'high';
    recommendations: string[];
  }>;
  forecast?: {
    nextMonth: {
      expectedRevenue: number;
      weightedRevenue: number;
      expectedDeals: number;
    };
    nextQuarter: {
      expectedRevenue: number;
      weightedRevenue: number;
      expectedDeals: number;
    };
    byMonth: Array<{
      month: string;
      expectedRevenue: number;
      weightedRevenue: number;
      expectedDeals: number;
    }>;
  };
  trends?: {
    revenueGrowth: number;
    dealVelocityChange: number;
    conversionTrend: number;
    pipelineHealthScore: number;
  };
  comparisons?: {
    vsLastPeriod: {
      revenueChange: number;
      dealCountChange: number;
      conversionRateChange: number;
    };
    vsTeamAverage: {
      revenuePerformance: number;
      conversionPerformance: number;
      velocityPerformance: number;
    };
  };
  metadata: {
    generatedAt: Date;
    dataRange: {
      from?: Date;
      to?: Date;
    };
    filters: any;
    cacheKey: string;
  };
}

/**
 * Get Pipeline Analytics Query Handler
 * Handles requests for comprehensive pipeline analytics data
 */
@Injectable()
@Injectable()
@QueryHandler(GetPipelineAnalyticsQuery)
export class GetPipelineAnalyticsHandler implements IQueryHandler<GetPipelineAnalyticsQuery> {
  private readonly logger = new Logger(GetPipelineAnalyticsHandler.name);

  constructor(
    private readonly leadRepository: ILeadRepository,
    private readonly stageRepository: IStageRepository,
    private readonly teamRepository: ITeamRepository,
  ) {}

  async execute(query: GetPipelineAnalyticsQuery): Promise<PipelineAnalyticsResult> {
    this.logger.log(`Executing GetPipelineAnalyticsQuery for team: ${query.teamId || 'all'}`);
    
    try {
      // 1. Get basic pipeline analytics from repository
      const pipelineData = await this.leadRepository.getPipelineAnalytics(query.teamId);
      
      // 2. Get lead statistics for summary
      const statistics = await this.leadRepository.getStatistics({
        teamId: query.teamId,
        assignedUserId: query.userId,
        dateFrom: query.dateFrom,
        dateTo: query.dateTo,
      });

      // 3. Build result object
      const result: PipelineAnalyticsResult = {
        summary: {
          totalLeads: statistics.totalLeads,
          totalOpportunities: statistics.totalOpportunities,
          totalRevenue: statistics.totalRevenue,
          weightedRevenue: statistics.weightedRevenue,
          averageDealSize: statistics.averageDealSize,
          conversionRate: statistics.conversionRate,
          winRate: statistics.winRate,
          averageSalesCycle: statistics.averageSalesCycle,
        },
        metadata: {
          generatedAt: new Date(),
          dataRange: {
            from: query.dateFrom,
            to: query.dateTo,
          },
          filters: {
            teamId: query.teamId,
            userId: query.userId,
            stageIds: query.stageIds,
            priorityLevels: query.priorityLevels,
            sources: query.sources,
          },
          cacheKey: query.getCacheKey(),
        },
      };

      // 4. Add stage metrics if requested
      if (query.includeStageMetrics) {
        result.stageMetrics = pipelineData.stages.map(stage => ({
          stageId: stage.stageId,
          stageName: stage.stageName,
          sequence: 0, // Would need to fetch from stage repository
          leadCount: stage.leadCount,
          opportunityCount: stage.leadCount, // Simplified - would need proper filtering
          totalRevenue: stage.totalRevenue,
          weightedRevenue: stage.weightedRevenue,
          averageProbability: stage.averageProbability,
          averageTimeInStage: stage.averageTimeInStage,
          conversionRate: 0, // Would need calculation
          dropOffRate: 0, // Would need calculation
        }));
      }

      // 5. Add conversion rates if requested
      if (query.includeConversionRates) {
        result.conversionRates = {
          leadToOpportunity: statistics.conversionRate,
          opportunityToWon: statistics.winRate,
          overallConversion: statistics.conversionRate * statistics.winRate / 100,
          byStage: pipelineData.conversionRates,
          byTeam: statistics.byTeam,
          bySource: statistics.bySource,
        };
      }

      // 6. Add bottlenecks if requested
      if (query.includeBottlenecks) {
        result.bottlenecks = pipelineData.bottlenecks.map(bottleneck => ({
          stageId: bottleneck.stageId,
          stageName: bottleneck.stageName,
          averageTimeInStage: bottleneck.averageTimeInStage,
          dropOffRate: bottleneck.dropOffRate,
          severity: this.calculateBottleneckSeverity(bottleneck.averageTimeInStage, bottleneck.dropOffRate),
          recommendations: this.generateBottleneckRecommendations(bottleneck),
        }));
      }

      // 7. Add forecast if requested
      if (query.includeForecast) {
        result.forecast = await this.generateForecast(query, statistics);
      }

      // 8. Add trends if requested
      if (query.includeComparisons) {
        result.trends = await this.calculateTrends(query, statistics);
        result.comparisons = await this.generateComparisons(query, statistics);
      }

      this.logger.log(`Successfully generated pipeline analytics for team: ${query.teamId || 'all'}`);
      
      return result;

    } catch (error) {
      this.logger.error(`Failed to get pipeline analytics`, error);
      
      // Re-throw with more context
      if (error instanceof Error) {
        throw new Error(`Failed to get pipeline analytics: ${error.message}`);
      }
      
      throw new Error('Failed to get pipeline analytics: Unknown error');
    }
  }

  /**
   * Calculate bottleneck severity based on metrics
   */
  private calculateBottleneckSeverity(averageTime: number, dropOffRate: number): 'low' | 'medium' | 'high' {
    if (dropOffRate > 30 || averageTime > 30) return 'high';
    if (dropOffRate > 15 || averageTime > 14) return 'medium';
    return 'low';
  }

  /**
   * Generate recommendations for bottlenecks
   */
  private generateBottleneckRecommendations(bottleneck: any): string[] {
    const recommendations: string[] = [];
    
    if (bottleneck.averageTimeInStage > 30) {
      recommendations.push('Consider automating stage progression or adding follow-up reminders');
    }
    
    if (bottleneck.dropOffRate > 20) {
      recommendations.push('Review stage requirements and provide additional training to sales team');
    }
    
    return recommendations;
  }

  /**
   * Generate revenue forecast
   */
  private async generateForecast(query: GetPipelineAnalyticsQuery, statistics: any): Promise<any> {
    // Simplified forecast calculation
    const monthlyGrowthRate = 0.05; // 5% monthly growth assumption
    
    return {
      nextMonth: {
        expectedRevenue: statistics.totalRevenue * (1 + monthlyGrowthRate),
        weightedRevenue: statistics.weightedRevenue * (1 + monthlyGrowthRate),
        expectedDeals: Math.round(statistics.totalOpportunities * 1.1),
      },
      nextQuarter: {
        expectedRevenue: statistics.totalRevenue * Math.pow(1 + monthlyGrowthRate, 3),
        weightedRevenue: statistics.weightedRevenue * Math.pow(1 + monthlyGrowthRate, 3),
        expectedDeals: Math.round(statistics.totalOpportunities * 1.3),
      },
      byMonth: [], // Would need more complex calculation
    };
  }

  /**
   * Calculate trends
   */
  private async calculateTrends(query: GetPipelineAnalyticsQuery, statistics: any): Promise<any> {
    // Simplified trend calculation
    return {
      revenueGrowth: 5.2, // Percentage
      dealVelocityChange: -2.1, // Percentage
      conversionTrend: 1.8, // Percentage
      pipelineHealthScore: 78, // Score out of 100
    };
  }

  /**
   * Generate comparisons
   */
  private async generateComparisons(query: GetPipelineAnalyticsQuery, statistics: any): Promise<any> {
    // Simplified comparison calculation
    return {
      vsLastPeriod: {
        revenueChange: 12.5, // Percentage
        dealCountChange: 8.3, // Percentage
        conversionRateChange: 2.1, // Percentage
      },
      vsTeamAverage: {
        revenuePerformance: 115, // Percentage of average
        conversionPerformance: 108, // Percentage of average
        velocityPerformance: 95, // Percentage of average
      },
    };
  }
}
