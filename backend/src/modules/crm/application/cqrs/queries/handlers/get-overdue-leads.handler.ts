import { Query<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Inject } from '@nestjs/common';
import { GetOverdueLeadsQuery } from '../get-overdue-leads.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';

/**
 * Get Overdue Leads Query Handler
 * Handles retrieving leads that are overdue
 */
@QueryHandler(GetOverdueLeadsQuery)
export class GetOverdueLeadsHandler implements IQueryHandler<GetOverdueLeadsQuery> {
  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  async execute(query: GetOverdueLeadsQuery): Promise<Lead[]> {
    const { teamId, userId } = query;
    
    // Build filters for overdue leads
    const filters: any = {
      dateDeadlineBefore: new Date(), // Deadline is in the past
    };
    
    if (teamId) {
      filters.teamId = teamId;
    }
    
    if (userId) {
      filters.assignedUserId = userId;
    }
    
    // Get overdue leads
    const leads = await this.leadRepository.findByFilters(filters);
    
    // Filter only active leads (not won, lost, or cancelled)
    return leads.filter(lead => lead.status?.isActive());
  }
}
