import { Query<PERSON><PERSON><PERSON>, <PERSON><PERSON>uery<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { SearchLeadsQuery } from '../search-leads.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
import { CacheService, CacheKeyService, CACHE_CONSTANTS } from '@/shared/infrastructure/cache';

/**
 * Enhanced Search Leads Query Handler
 * Handles searching leads with caching, relevance scoring, and optimization
 */
@Injectable()
@QueryHandler(SearchLeadsQuery)
export class SearchLeadsHandler implements IQueryHandler<SearchLeadsQuery> {
  private readonly logger = new Logger(SearchLeadsHandler.name);

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
    private readonly cacheService: CacheService,
    private readonly cacheKeyService: CacheKeyService,
  ) {}

  async execute(query: SearchLeadsQuery): Promise<{
    leads: Lead[];
    searchTerm: string;
    totalResults: number;
    searchTime: number;
    suggestions?: string[];
    cached: boolean;
  }> {
    const startTime = Date.now();
    const { searchTerm, filters, limit } = query;

    this.logger.log(`Executing SearchLeadsQuery for term: "${searchTerm}"`);

    try {
      // Validate and sanitize search term
      const sanitizedSearchTerm = this.sanitizeSearchTerm(searchTerm);

      if (!sanitizedSearchTerm || sanitizedSearchTerm.length < 2) {
        return {
          leads: [],
          searchTerm,
          totalResults: 0,
          searchTime: Date.now() - startTime,
          cached: false,
        };
      }

      // Generate cache key for search
      const cacheKey = this.cacheKeyService.generateSearchKey(sanitizedSearchTerm, filters);

      // Use cache for search results
      const result = await this.cacheService.getOrSet(
        cacheKey,
        async () => {
          // Build search filters
          const searchFilters = {
            ...filters,
            searchTerm: sanitizedSearchTerm,
            limit: limit || 50,
          };

          // Execute search
          const leads = await this.leadRepository.search(searchTerm, searchFilters);

          // Generate search suggestions if no results
          const suggestions = leads.length === 0
            ? await this.generateSearchSuggestions(sanitizedSearchTerm)
            : undefined;

          return {
            leads,
            searchTerm,
            totalResults: leads.length,
            searchTime: Date.now() - startTime,
            suggestions,
            cached: false,
          };
        },
        CACHE_CONSTANTS.SEARCH_TTL, // 1 minute cache for search results
      );

      // Update search time and mark as cached
      const finalResult = {
        ...result,
        searchTime: Date.now() - startTime,
        cached: true,
      };

      this.logger.log(`Search completed: ${result.leads.length} results for "${searchTerm}"`);

      return finalResult;

    } catch (error) {
      this.logger.error(`Failed to execute SearchLeadsQuery: ${error.message}`, error.stack);

      // Fallback to direct search without cache
      const searchFilters = {
        ...filters,
        searchTerm,
        limit: limit || 50,
      };

      const leads = await this.leadRepository.search(searchTerm, searchFilters);

      return {
        leads,
        searchTerm,
        totalResults: leads.length,
        searchTime: Date.now() - startTime,
        cached: false,
      };
    }
  }

  /**
   * Sanitize search term to prevent injection and improve search quality
   */
  private sanitizeSearchTerm(searchTerm: string): string {
    if (!searchTerm) return '';

    return searchTerm
      .trim()
      .replace(/[<>\"'%;()&+]/g, '') // Remove potentially dangerous characters
      .replace(/\s+/g, ' ') // Normalize whitespace
      .substring(0, 100); // Limit length
  }

  /**
   * Generate search suggestions for empty results
   */
  private async generateSearchSuggestions(searchTerm: string): Promise<string[]> {
    try {
      // Simple suggestion logic - in production you might want more sophisticated algorithms
      const suggestions: string[] = [];

      // Common search terms that might be similar
      const commonTerms = ['lead', 'opportunity', 'prospect', 'customer', 'contact'];

      for (const term of commonTerms) {
        if (this.calculateSimilarity(searchTerm.toLowerCase(), term) > 0.5) {
          suggestions.push(term);
        }
      }

      // Add partial matches from recent searches (would need to implement search history)
      // For now, return basic suggestions
      if (suggestions.length === 0) {
        suggestions.push('Try searching by name, email, or company');
      }

      return suggestions.slice(0, 3); // Limit to 3 suggestions

    } catch (error) {
      this.logger.error('Failed to generate search suggestions:', error);
      return [];
    }
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private calculateSimilarity(str1: string, str2: string): number {
    const matrix: number[][] = [];
    const len1 = str1.length;
    const len2 = str2.length;

    if (len1 === 0) return len2 === 0 ? 1 : 0;
    if (len2 === 0) return 0;

    // Initialize matrix
    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j;
    }

    // Fill matrix
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost
        );
      }
    }

    const maxLen = Math.max(len1, len2);
    return (maxLen - matrix[len1][len2]) / maxLen;
  }

  /**
   * Log search analytics (for future implementation)
   */
  private async logSearchAnalytics(searchTerm: string, resultCount: number, searchTime: number): Promise<void> {
    try {
      // In production, you might want to log search analytics to a separate service
      this.logger.debug(`Search Analytics: term="${searchTerm}", results=${resultCount}, time=${searchTime}ms`);
    } catch (error) {
      this.logger.error('Failed to log search analytics:', error);
    }
  }
}
