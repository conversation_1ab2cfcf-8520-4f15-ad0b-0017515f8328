import { Query<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { Inject } from '@nestjs/common';
import { SearchLeadsQuery } from '../search-leads.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';

/**
 * Search Leads Query Handler
 * Handles searching leads based on text query
 */
@QueryHandler(SearchLeadsQuery)
export class <PERSON><PERSON>eadsHandler implements IQueryHandler<SearchLeadsQuery> {
  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  async execute(query: SearchLeadsQuery): Promise<Lead[]> {
    const { searchTerm, filters, limit } = query;
    
    // Build search filters
    const searchFilters = {
      ...filters,
      searchTerm,
      limit: limit || 50,
    };
    
    return this.leadRepository.search(searchFilters);
  }
}
