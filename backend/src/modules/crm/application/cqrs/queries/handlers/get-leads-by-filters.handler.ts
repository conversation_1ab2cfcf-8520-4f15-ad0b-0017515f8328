import { QueryHand<PERSON>, <PERSON><PERSON>ueryHand<PERSON> } from '@nestjs/cqrs';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { GetLeadsByFiltersQuery } from '../get-leads-by-filters.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';

/**
 * Get Leads by Filters Query Handler
 * Handles retrieving leads based on filters
 */
@Injectable()
@QueryHandler(GetLeadsByFiltersQuery)
export class GetLeadsByFiltersHandler implements IQueryHandler<GetLeadsByFiltersQuery> {
  private readonly logger = new Logger(GetLeadsByFiltersHandler.name);

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  async execute(query: GetLeadsByFiltersQuery): Promise<Lead[]> {
    const { filters } = query;
    return this.leadRepository.findByFilters(filters);
  }
}
