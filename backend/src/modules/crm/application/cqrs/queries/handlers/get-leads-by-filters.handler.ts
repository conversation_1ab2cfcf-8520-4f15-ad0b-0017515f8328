import { Query<PERSON>and<PERSON>, <PERSON><PERSON>ueryHand<PERSON> } from '@nestjs/cqrs';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { GetLeadsByFiltersQuery } from '../get-leads-by-filters.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
import { CacheService, CACHE_CONSTANTS } from '@/shared/infrastructure/cache';

/**
 * Enhanced Get Leads by Filters Query Handler
 * Handles retrieving leads based on filters with caching and optimization
 */
@Injectable()
@QueryHandler(GetLeadsByFiltersQuery)
export class GetLeadsByFiltersHandler implements IQueryHandler<GetLeadsByFiltersQuery> {
  private readonly logger = new Logger(GetLeadsByFiltersHandler.name);

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
    private readonly cacheService: CacheService,
  ) {}

  async execute(query: GetLeadsByFiltersQuery): Promise<{
    leads: Lead[];
    total: number;
    hasMore: boolean;
    filters: any;
    cached: boolean;
  }> {
    const { filters } = query;

    this.logger.log(`Executing GetLeadsByFiltersQuery with filters: ${JSON.stringify(filters)}`);

    try {
      // Use cache for query results
      const result = await this.cacheService.cacheQuery(
        'leads.findByFilters',
        filters,
        async () => {
          // Execute the actual query
          const leads = await this.leadRepository.findByFilters(filters);

          // Get total count for pagination
          const totalFilters = { ...filters };
          delete totalFilters.limit;
          delete totalFilters.offset;

          const allLeads = await this.leadRepository.findByFilters(totalFilters);
          const total = allLeads.length;

          // Calculate pagination info
          const limit = filters.limit || 100;
          const offset = filters.offset || 0;
          const hasMore = total > offset + leads.length;

          return {
            leads,
            total,
            hasMore,
            filters,
            cached: false,
          };
        },
        CACHE_CONSTANTS.LIST_TTL, // 2 minutes cache for list queries
      );

      this.logger.log(`Found ${result.leads.length} leads (total: ${result.total})`);

      return result;

    } catch (error) {
      this.logger.error(`Failed to execute GetLeadsByFiltersQuery: ${error.message}`, error.stack);

      // Fallback to direct repository call without cache
      const leads = await this.leadRepository.findByFilters(filters);

      return {
        leads,
        total: leads.length,
        hasMore: false,
        filters,
        cached: false,
      };
    }
  }

  /**
   * Validate and sanitize filters
   */
  private validateFilters(filters: any): any {
    const validatedFilters = { ...filters };

    // Ensure pagination limits
    if (validatedFilters.limit) {
      validatedFilters.limit = Math.min(Math.max(validatedFilters.limit, 1), 1000);
    }

    if (validatedFilters.offset) {
      validatedFilters.offset = Math.max(validatedFilters.offset, 0);
    }

    // Validate date ranges
    if (validatedFilters.dateFrom && validatedFilters.dateTo) {
      const dateFrom = new Date(validatedFilters.dateFrom);
      const dateTo = new Date(validatedFilters.dateTo);

      if (dateFrom > dateTo) {
        this.logger.warn('Invalid date range: dateFrom is after dateTo');
        delete validatedFilters.dateFrom;
        delete validatedFilters.dateTo;
      }
    }

    return validatedFilters;
  }

  /**
   * Build cache key for the query
   */
  private buildCacheKey(filters: any): string {
    // Sort filters for consistent cache keys
    const sortedFilters = Object.keys(filters)
      .sort()
      .reduce((acc, key) => {
        acc[key] = filters[key];
        return acc;
      }, {} as any);

    return `leads:filters:${JSON.stringify(sortedFilters)}`;
  }
}
