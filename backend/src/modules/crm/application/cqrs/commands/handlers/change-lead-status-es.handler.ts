import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Command<PERSON>and<PERSON>, EventBus } from '@nestjs/cqrs';
import { Injectable, Logger, Inject } from '@nestjs/common';
import { ChangeLeadStatusCommand } from '../change-lead-status.command';
import { LeadAggregate } from '@/modules/crm/domain/aggregates/lead.aggregate';
import { LeadStatus } from '@/modules/crm/domain/value-objects/lead-status.vo';
import { ILeadAggregateRepository } from '@/modules/crm/domain/repositories/lead-aggregate.repository';

/**
 * Change Lead Status Command Handler (Event Sourcing)
 * Handles lead status changes using Event Sourcing pattern
 */
@Injectable()
@CommandHandler(ChangeLeadStatusCommand)
export class ChangeLeadStatusESHandler implements ICommandHandler<ChangeLeadStatusCommand> {
  private readonly logger = new Logger(ChangeLeadStatusESHandler.name);

  constructor(
    @Inject('LEAD_AGGREGATE_REPOSITORY')
    private readonly leadAggregateRepository: ILeadAggregateRepository,
    private readonly eventBus: EventBus,
  ) {}

  async execute(command: ChangeLeadStatusCommand): Promise<{ aggregate: LeadAggregate }> {
    this.logger.log(`Executing ChangeLeadStatusCommand (ES) for lead: ${command.leadId} to status: ${command.newStatus}`);
    
    try {
      // 1. Load existing aggregate from event store
      const leadAggregate = await this.leadAggregateRepository.findById(command.leadId.toString());
      
      if (!leadAggregate) {
        throw new Error(`Lead not found: ${command.leadId}`);
      }

      if (leadAggregate.isDeleted) {
        throw new Error(`Cannot change status of deleted lead: ${command.leadId}`);
      }

      // 2. Create new status value object
      const newStatus = new LeadStatus(command.newStatus);

      // 3. Validate business rules
      this.validateBusinessRules(command, leadAggregate, newStatus);

      // 4. Apply status change to aggregate
      leadAggregate.changeStatus(
        newStatus,
        command.reason,
        command.changedBy,
        command.changedBy,
        command.tenantId,
      );

      // 5. Save aggregate (this will persist events to event store)
      await this.leadAggregateRepository.save(leadAggregate);

      // 6. Publish events to event bus
      const uncommittedEvents = leadAggregate.getUncommittedEvents();
      for (const event of uncommittedEvents) {
        await this.eventBus.publish(event);
      }

      // 7. Mark events as committed
      leadAggregate.commit();

      this.logger.log(`Successfully changed lead status: ${command.leadId} from ${leadAggregate.status.value} to ${command.newStatus}`);
      
      return {
        aggregate: leadAggregate,
      };

    } catch (error) {
      this.logger.error(`Failed to change lead status: ${command.leadId}`, error);
      throw error;
    }
  }

  /**
   * Validate business rules for status changes
   */
  private validateBusinessRules(
    command: ChangeLeadStatusCommand,
    aggregate: LeadAggregate,
    newStatus: LeadStatus,
  ): void {
    // Check if status transition is allowed
    if (!aggregate.status.canTransitionTo(newStatus)) {
      throw new Error(`Cannot transition from ${aggregate.status.value} to ${newStatus.value}`);
    }

    // Validate reason for certain status changes
    if (this.requiresReason(aggregate.status, newStatus) && !command.reason?.trim()) {
      throw new Error(`Reason is required when changing status to ${newStatus.value}`);
    }

    // Business rule: Cannot mark as won without expected revenue
    if (newStatus.isWon() && (!aggregate.expectedRevenue || aggregate.expectedRevenue <= 0)) {
      throw new Error('Cannot mark lead as won without expected revenue');
    }

    // Business rule: Lost leads must have a reason
    if (newStatus.isLost() && !command.reason?.trim()) {
      throw new Error('Reason is required when marking lead as lost');
    }

    // Business rule: Qualified leads should be assigned
    if (newStatus.isQualified() && !aggregate.assignedUserId) {
      throw new Error('Qualified leads should be assigned to a user');
    }

    // Business rule: Opportunities should have probability set
    if (newStatus.isOpportunity() && (!aggregate.probability || aggregate.probability <= 0)) {
      throw new Error('Opportunities should have probability greater than 0');
    }

    // Business rule: Cannot reopen won/lost leads without special permission
    if ((aggregate.status.isWon() || aggregate.status.isLost()) && 
        !newStatus.isWon() && !newStatus.isLost()) {
      throw new Error('Cannot reopen won/lost leads without special permission');
    }
  }

  /**
   * Check if status change requires a reason
   */
  private requiresReason(currentStatus: LeadStatus, newStatus: LeadStatus): boolean {
    // Require reason for these transitions
    const reasonRequiredTransitions = [
      'lost',
      'disqualified',
      'cancelled',
      'on_hold',
    ];

    return reasonRequiredTransitions.includes(newStatus.value) ||
           (currentStatus.isWon() && !newStatus.isWon()) ||
           (currentStatus.isLost() && !newStatus.isLost());
  }
}

/**
 * Change Lead Status Command Result (Event Sourcing)
 */
export interface ChangeLeadStatusESResult {
  aggregate: LeadAggregate;
  events: any[];
}
