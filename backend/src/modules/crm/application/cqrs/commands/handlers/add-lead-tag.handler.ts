import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { AddLeadTagCommand } from '../add-lead-tag.command';
import { ILeadRepository } from "@/modules/crm/domain/repositories/lead.repository";
import { LEAD_REPOSITORY_TOKEN } from "@/modules/crm/domain/repositories/injection-tokens";
/**
 * Add Lead Tag Command Handler
 * Handles adding a tag to a lead
 */
@Injectable()
@CommandHandler(AddLeadTagCommand)
export class AddLeadTagHandler implements ICommandHandler<AddLeadTagCommand> {
  private readonly logger = new Logger(AddLeadTagHandler.name);

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  async execute(command: AddLeadTagCommand): Promise<void> {
    const { leadId, tag } = command;

    // Get the lead
    const lead = await this.leadRepository.findById(leadId);
    if (!lead) {
      throw new Error(`Lead with ID ${leadId} not found`);
    }

    // Add the tag
    lead.addTag(tag);

    // Save the updated lead
    await this.leadRepository.update(leadId, lead);
  }
}
