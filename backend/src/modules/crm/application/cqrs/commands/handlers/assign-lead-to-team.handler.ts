import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { Inject } from '@nestjs/common';
import { AssignLeadToTeamCommand } from '../assign-lead-to-team.command';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';

/**
 * Assign Lead to Team Command Handler
 * Handles the assignment of a lead to a specific team
 */
@CommandHandler(AssignLeadToTeamCommand)
export class AssignLeadToTeamHandler implements ICommandHandler<AssignLeadToTeamCommand> {
  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  async execute(command: AssignLeadToTeamCommand): Promise<void> {
    const { leadId, teamId, assignedBy } = command;

    // Get the lead
    const lead = await this.leadRepository.findById(leadId);
    if (!lead) {
      throw new Error(`Lead with ID ${leadId} not found`);
    }

    // Assign the lead to team
    lead.assignToTeam(teamId, assignedBy);

    // Save the updated lead
    await this.leadRepository.update(leadId, lead);
  }
}
