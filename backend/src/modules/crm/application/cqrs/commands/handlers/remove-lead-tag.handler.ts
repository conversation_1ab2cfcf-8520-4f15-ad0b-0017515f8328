import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { Inject } from '@nestjs/common';
import { RemoveLeadTagCommand } from '../remove-lead-tag.command';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';

/**
 * Remove Lead Tag Command Handler
 * Handles removing a tag from a lead
 */
@CommandHandler(RemoveLeadTagCommand)
export class RemoveLeadTagHandler implements ICommandHandler<RemoveLeadTagCommand> {
  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  async execute(command: RemoveLeadTagCommand): Promise<void> {
    const { leadId, tag } = command;

    // Get the lead
    const lead = await this.leadRepository.findById(leadId);
    if (!lead) {
      throw new Error(`Lead with ID ${leadId} not found`);
    }

    // Remove the tag
    lead.removeTag(tag);

    // Save the updated lead
    await this.leadRepository.update(leadId, lead);
  }
}
