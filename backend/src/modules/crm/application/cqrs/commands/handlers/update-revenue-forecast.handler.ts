import { Command<PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { UpdateRevenueForecastCommand } from '../update-revenue-forecast.command';
import { ILeadRepository } from "@/modules/crm/domain/repositories/lead.repository";
import { LEAD_REPOSITORY_TOKEN } from "@/modules/crm/domain/repositories/injection-tokens";
/**
 * Update Revenue Forecast Command Handler
 * Handles updating the revenue forecast for a lead
 */
@CommandHandler(UpdateRevenueForecastCommand)
export class UpdateRevenueForecastHandler implements ICommandHandler<UpdateRevenueForecastCommand> {
  private readonly logger = new Logger(UpdateRevenueForecastHandler.name);

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  async execute(command: UpdateRevenueForecastCommand): Promise<void> {
    const { leadId, expectedRevenue, probability } = command;

    // Get the lead
    const lead = await this.leadRepository.findById(leadId);
    if (!lead) {
      throw new Error(`Lead with ID ${leadId} not found`);
    }

    // Update revenue forecast
    lead.updateRevenueForecast(expectedRevenue, probability);

    // Save the updated lead
    await this.leadRepository.update(leadId, lead);
  }
}
