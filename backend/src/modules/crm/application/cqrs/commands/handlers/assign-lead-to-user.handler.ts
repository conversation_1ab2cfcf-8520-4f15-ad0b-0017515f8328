import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { AssignLeadToUserCommand } from '../assign-lead-to-user.command';
import { ILeadRepository } from "@/modules/crm/domain/repositories/lead.repository";
import { LEAD_REPOSITORY_TOKEN } from "@/modules/crm/domain/repositories/injection-tokens";
/**
 * Assign Lead to User Command Handler
 * Handles the assignment of a lead to a specific user
 */
@Injectable()
@CommandHandler(AssignLeadToUserCommand)
export class AssignLeadToUserHandler implements ICommandHandler<AssignLeadToUserCommand> {
  private readonly logger = new Logger(AssignLeadToUserHandler.name);
  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  async execute(command: AssignLeadToUserCommand): Promise<void> {
    const { leadId, userId, assignedBy } = command;

    // Get the lead
    const lead = await this.leadRepository.findById(leadId);
    if (!lead) {
      throw new Error(`Lead with ID ${leadId} not found`);
    }

    // Assign the lead to user
    const updatedLead = lead.assignToUser(userId, assignedBy);

    // Save the updated lead
    await this.leadRepository.update(leadId, updatedLead);
  }
}
