import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>CommandHand<PERSON>, EventBus } from '@nestjs/cqrs';
import { Injectable, Logger, Inject } from '@nestjs/common';
import { UpdateLeadCommand } from '../update-lead.command';
import { LeadAggregate } from '@/modules/crm/domain/aggregates/lead.aggregate';
import { ContactInfo } from '@/modules/crm/domain/value-objects/contact-info.vo';
import { ILeadAggregateRepository } from '@/modules/crm/domain/repositories/lead-aggregate.repository';

/**
 * Update Lead Command Handler (Event Sourcing)
 * Handles updating lead information using Event Sourcing pattern
 */
@Injectable()
@CommandHandler(UpdateLeadCommand)
export class UpdateLeadESHandler implements ICommandHandler<UpdateLeadCommand> {
  private readonly logger = new Logger(UpdateLeadESHandler.name);

  constructor(
    @Inject('LEAD_AGGREGATE_REPOSITORY')
    private readonly leadAggregateRepository: ILeadAggregateRepository,
    private readonly eventBus: EventBus,
  ) {}

  async execute(command: UpdateLeadCommand): Promise<{ aggregate: LeadAggregate }> {
    this.logger.log(`Executing UpdateLeadCommand (ES) for lead: ${command.leadId}`);
    
    try {
      // 1. Load existing aggregate from event store
      const leadAggregate = await this.leadAggregateRepository.findById(command.leadId.toString());
      
      if (!leadAggregate) {
        throw new Error(`Lead not found: ${command.leadId}`);
      }

      if (leadAggregate.isDeleted) {
        throw new Error(`Cannot update deleted lead: ${command.leadId}`);
      }

      // 2. Prepare update data
      const updates: any = {};

      // Update basic fields
      if (command.updates.name !== undefined) updates.name = command.updates.name;
      if (command.updates.description !== undefined) updates.description = command.updates.description;
      if (command.updates.expectedRevenue !== undefined) updates.expectedRevenue = command.updates.expectedRevenue;
      if (command.updates.probability !== undefined) updates.probability = command.updates.probability;
      if (command.updates.tags !== undefined) updates.tags = command.updates.tags;
      if (command.updates.dateDeadline !== undefined) updates.dateDeadline = command.updates.dateDeadline;

      // Update contact info if provided
      if (this.hasContactInfoUpdates(command)) {
        const currentContactInfo = leadAggregate.contactInfo;
        const contactUpdates = command.updates.contactInfo || {};
        updates.contactInfo = new ContactInfo(
          contactUpdates.email ?? currentContactInfo.email,
          contactUpdates.phone ?? currentContactInfo.phone,
          contactUpdates.company ?? currentContactInfo.company,
          contactUpdates.website ?? currentContactInfo.website,
          currentContactInfo.address, // Not in command
          currentContactInfo.city, // Not in command
          currentContactInfo.country, // Not in command
        );
      }

      // 3. Validate business rules
      this.validateBusinessRules(command, leadAggregate, updates);

      // 4. Apply updates to aggregate
      if (Object.keys(updates).length > 0) {
        leadAggregate.update(
          updates,
          undefined, // updatedBy - not available in UpdateLeadCommand
          undefined, // tenantId - not available in UpdateLeadCommand
        );

        // 5. Save aggregate (this will persist events to event store)
        await this.leadAggregateRepository.save(leadAggregate);

        // 6. Publish events to event bus
        const uncommittedEvents = leadAggregate.getUncommittedEvents();
        for (const event of uncommittedEvents) {
          await this.eventBus.publish(event);
        }

        // 7. Mark events as committed
        leadAggregate.commit();

        this.logger.log(`Successfully updated lead aggregate: ${command.leadId}`);
      } else {
        this.logger.debug(`No updates provided for lead: ${command.leadId}`);
      }
      
      return {
        aggregate: leadAggregate,
      };

    } catch (error) {
      this.logger.error(`Failed to update lead: ${command.leadId}`, error);
      throw error;
    }
  }

  /**
   * Check if command has contact info updates
   */
  private hasContactInfoUpdates(command: UpdateLeadCommand): boolean {
    const contactInfo = command.updates.contactInfo;
    return contactInfo !== undefined && (
      contactInfo.email !== undefined ||
      contactInfo.phone !== undefined ||
      contactInfo.company !== undefined ||
      contactInfo.website !== undefined
    );
  }

  /**
   * Validate business rules for lead updates
   */
  private validateBusinessRules(
    command: UpdateLeadCommand,
    aggregate: LeadAggregate,
    updates: any,
  ): void {
    // Name validation
    if (updates.name !== undefined && !updates.name?.trim()) {
      throw new Error('Lead name cannot be empty');
    }

    // Revenue validation
    if (updates.expectedRevenue !== undefined && updates.expectedRevenue < 0) {
      throw new Error('Expected revenue cannot be negative');
    }

    // Probability validation
    if (updates.probability !== undefined) {
      if (updates.probability < 0 || updates.probability > 100) {
        throw new Error('Probability must be between 0 and 100');
      }
    }

    // Opportunity validation
    if (aggregate.type.isOpportunity()) {
      const finalRevenue = updates.expectedRevenue ?? aggregate.expectedRevenue;
      if (!finalRevenue || finalRevenue <= 0) {
        throw new Error('Opportunities must have positive expected revenue');
      }
    }

    // Email format validation
    if (updates.contactInfo?.email && !this.isValidEmail(updates.contactInfo.email)) {
      throw new Error('Invalid email format');
    }

    // Phone format validation
    if (updates.contactInfo?.phone && updates.contactInfo.phone.length < 10) {
      throw new Error('Phone number must be at least 10 digits');
    }

    // Date validation
    if (updates.dateDeadline && updates.dateDeadline < new Date()) {
      throw new Error('Deadline cannot be in the past');
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

/**
 * Update Lead Command Result (Event Sourcing)
 */
export interface UpdateLeadESResult {
  aggregate: LeadAggregate;
  events: any[];
}
