import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Command<PERSON>and<PERSON>, EventBus } from '@nestjs/cqrs';
import { Injectable, Logger, Inject } from '@nestjs/common';
import { CreateLeadCommand } from '../create-lead.command';
import { LeadAggregate } from '@/modules/crm/domain/aggregates/lead.aggregate';
import { ContactInfo } from '@/modules/crm/domain/value-objects/contact-info.vo';
import { ILeadAggregateRepository } from '@/modules/crm/domain/repositories/lead-aggregate.repository';
import { v4 as uuidv4 } from 'uuid';

/**
 * Create Lead Command Handler (Event Sourcing)
 * Handles the creation of new leads using Event Sourcing pattern
 */
@Injectable()
@CommandHandler(CreateLeadCommand)
export class CreateLeadESHandler implements ICommandHandler<CreateLeadCommand> {
  private readonly logger = new Logger(CreateLeadESHandler.name);

  constructor(
    @Inject('LEAD_AGGREGATE_REPOSITORY')
    private readonly leadAggregateRepository: ILeadAggregateRepository,
    private readonly eventBus: EventBus,
  ) {}

  async execute(command: CreateLeadCommand): Promise<{ leadId: string; aggregate: LeadAggregate }> {
    this.logger.log(`Executing CreateLeadCommand (ES) for: ${command.name}`);
    
    try {
      // 1. Generate unique ID for the aggregate
      const leadId = uuidv4();

      // 2. Create ContactInfo value object
      const contactInfo = new ContactInfo(
        command.email,
        command.phone,
        command.company,
        command.website,
        command.address,
        command.city,
        command.country,
      );

      // 3. Create Lead aggregate using factory method
      const leadAggregate = LeadAggregate.create(
        leadId,
        command.name,
        contactInfo,
        command.source,
        command.type,
        command.priority,
        command.expectedRevenue,
        command.teamId,
        command.assignedUserId,
        command.createdBy?.toString(),
        undefined, // tenantId - not available in CreateLeadCommand
      );

      // 4. Validate business rules
      this.validateBusinessRules(command, leadAggregate);

      // 5. Save aggregate (this will persist events to event store)
      await this.leadAggregateRepository.save(leadAggregate);

      // 6. Publish events to event bus for projections and side effects
      const uncommittedEvents = leadAggregate.getUncommittedEvents();
      for (const event of uncommittedEvents) {
        await this.eventBus.publish(event);
      }

      // 7. Mark events as committed
      leadAggregate.commit();

      this.logger.log(`Successfully created lead aggregate with ID: ${leadId}`);
      
      return {
        leadId,
        aggregate: leadAggregate,
      };

    } catch (error) {
      this.logger.error(`Failed to create lead: ${command.name}`, error);
      throw error;
    }
  }

  /**
   * Validate business rules for lead creation
   */
  private validateBusinessRules(command: CreateLeadCommand, aggregate: LeadAggregate): void {
    // Required field validations
    if (!command.name?.trim()) {
      throw new Error('Lead name is required');
    }

    if (!command.email && !command.phone) {
      throw new Error('Either email or phone is required');
    }

    if (!command.source?.trim()) {
      throw new Error('Lead source is required');
    }

    // Business logic validations
    if (command.expectedRevenue !== undefined && command.expectedRevenue < 0) {
      throw new Error('Expected revenue cannot be negative');
    }

    if (aggregate.type.isOpportunity() && !command.expectedRevenue) {
      throw new Error('Opportunities must have expected revenue');
    }

    // Email format validation
    if (command.email && !this.isValidEmail(command.email)) {
      throw new Error('Invalid email format');
    }

    // Phone format validation (basic)
    if (command.phone && command.phone.length < 10) {
      throw new Error('Phone number must be at least 10 digits');
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

/**
 * Create Lead Command Result (Event Sourcing)
 */
export interface CreateLeadESResult {
  leadId: string;
  aggregate: LeadAggregate;
  events: any[];
}
