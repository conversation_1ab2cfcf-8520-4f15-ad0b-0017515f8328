import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { DeleteLeadCommand } from '../delete-lead.command';
import { ILeadRepository, LEAD_REPOSITORY_TOKEN } from '../../../../domain/repositories/lead.repository';

/**
 * Delete Lead Command Handler
 * Handles the deletion of a lead
 */
@Injectable()
@CommandHandler(DeleteLeadCommand)
export class DeleteLeadHandler implements ICommandHandler<DeleteLeadCommand> {
  private readonly logger = new Logger(DeleteLeadHandler.name);
  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  async execute(command: DeleteLeadCommand): Promise<void> {
    const { leadId } = command;

    // Check if lead exists
    const lead = await this.leadRepository.findById(leadId);
    if (!lead) {
      throw new Error(`Lead with ID ${leadId} not found`);
    }

    // Delete the lead
    const deleted = await this.leadRepository.delete(leadId);
    if (!deleted) {
      throw new Error(`Failed to delete lead with ID ${leadId}`);
    }
  }
}
