import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { Inject } from '@nestjs/common';
import { DeleteLeadCommand } from '../delete-lead.command';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';

/**
 * Delete Lead Command Handler
 * Handles the deletion of a lead
 */
@CommandHandler(DeleteLeadCommand)
export class Delete<PERSON>eadHandler implements ICommandHandler<DeleteLeadCommand> {
  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  async execute(command: DeleteLeadCommand): Promise<void> {
    const { leadId } = command;

    // Check if lead exists
    const lead = await this.leadRepository.findById(leadId);
    if (!lead) {
      throw new Error(`Lead with ID ${leadId} not found`);
    }

    // Delete the lead
    const deleted = await this.leadRepository.delete(leadId);
    if (!deleted) {
      throw new Error(`Failed to delete lead with ID ${leadId}`);
    }
  }
}
