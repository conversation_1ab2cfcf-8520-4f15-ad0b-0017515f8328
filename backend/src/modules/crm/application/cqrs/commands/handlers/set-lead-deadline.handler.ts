import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { Inject } from '@nestjs/common';
import { SetLeadDeadlineCommand } from '../set-lead-deadline.command';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { LEAD_REPOSITORY_TOKEN } from '@/modules/crm/domain/repositories/injection-tokens';

/**
 * Set Lead Deadline Command Handler
 * Handles setting a deadline for a lead
 */
@CommandHandler(SetLeadDeadlineCommand)
export class SetLeadDeadlineHandler implements ICommandHandler<SetLeadDeadlineCommand> {
  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
  ) {}

  async execute(command: SetLeadDeadlineCommand): Promise<void> {
    const { leadId, deadline } = command;

    // Get the lead
    const lead = await this.leadRepository.findById(leadId);
    if (!lead) {
      throw new Error(`Lead with ID ${leadId} not found`);
    }

    // Set the deadline
    lead.setDeadline(deadline);

    // Save the updated lead
    await this.leadRepository.update(leadId, lead);
  }
}
