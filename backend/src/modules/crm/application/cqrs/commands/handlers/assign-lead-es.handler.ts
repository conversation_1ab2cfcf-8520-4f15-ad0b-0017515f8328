import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON>, EventBus } from '@nestjs/cqrs';
import { Injectable, Logger, Inject } from '@nestjs/common';
import { AssignLeadCommand } from '../assign-lead.command';
import { LeadAggregate } from '@/modules/crm/domain/aggregates/lead.aggregate';
import { ILeadAggregateRepository } from '@/modules/crm/domain/repositories/lead-aggregate.repository';

/**
 * Assign Lead Command Handler (Event Sourcing)
 * Handles lead assignment using Event Sourcing pattern
 */
@Injectable()
@CommandHandler(AssignLeadCommand)
export class AssignLeadESHandler implements ICommandHandler<AssignLeadCommand> {
  private readonly logger = new Logger(AssignLeadESHandler.name);

  constructor(
    @Inject('LEAD_AGGREGATE_REPOSITORY')
    private readonly leadAggregateRepository: ILeadAggregateRepository,
    private readonly eventBus: EventBus,
  ) {}

  async execute(command: AssignLeadCommand): Promise<{ aggregate: LeadAggregate }> {
    this.logger.log(`Executing AssignLeadCommand (ES) for lead: ${command.leadId}`);
    
    try {
      // 1. Load existing aggregate from event store
      const leadAggregate = await this.leadAggregateRepository.findById(command.leadId.toString());
      
      if (!leadAggregate) {
        throw new Error(`Lead not found: ${command.leadId}`);
      }

      if (leadAggregate.isDeleted) {
        throw new Error(`Cannot assign deleted lead: ${command.leadId}`);
      }

      // 2. Validate business rules
      this.validateBusinessRules(command, leadAggregate);

      // 3. Apply assignment to aggregate
      leadAggregate.assignTo(
        command.assignedUserId,
        command.teamId,
        command.assignmentReason,
        command.autoAssigned || false,
        command.assignedBy,
        command.tenantId,
      );

      // 4. Save aggregate (this will persist events to event store)
      await this.leadAggregateRepository.save(leadAggregate);

      // 5. Publish events to event bus
      const uncommittedEvents = leadAggregate.getUncommittedEvents();
      for (const event of uncommittedEvents) {
        await this.eventBus.publish(event);
      }

      // 6. Mark events as committed
      leadAggregate.commit();

      this.logger.log(`Successfully assigned lead: ${command.leadId} to user: ${command.assignedUserId}, team: ${command.teamId}`);
      
      return {
        aggregate: leadAggregate,
      };

    } catch (error) {
      this.logger.error(`Failed to assign lead: ${command.leadId}`, error);
      throw error;
    }
  }

  /**
   * Validate business rules for lead assignment
   */
  private validateBusinessRules(command: AssignLeadCommand, aggregate: LeadAggregate): void {
    // Must assign to either user or team (or both)
    if (!command.assignedUserId && !command.teamId) {
      throw new Error('Must assign lead to either a user or team');
    }

    // Cannot assign to the same user/team
    if (command.assignedUserId === aggregate.assignedUserId && 
        command.teamId === aggregate.teamId) {
      throw new Error('Lead is already assigned to this user/team');
    }

    // Validate assignment reason for manual assignments
    if (!command.autoAssigned && !command.assignmentReason?.trim()) {
      throw new Error('Assignment reason is required for manual assignments');
    }

    // Business rule: High priority leads should have assignment reason
    if (aggregate.priority.isHigh() && !command.assignmentReason?.trim()) {
      throw new Error('High priority leads require assignment reason');
    }

    // Business rule: Opportunities should be assigned to users, not just teams
    if (aggregate.type.isOpportunity() && !command.assignedUserId) {
      throw new Error('Opportunities must be assigned to a specific user');
    }
  }
}

/**
 * Assign Lead Command Result (Event Sourcing)
 */
export interface AssignLeadESResult {
  aggregate: LeadAggregate;
  events: any[];
}
