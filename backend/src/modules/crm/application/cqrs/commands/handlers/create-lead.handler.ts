import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ommand<PERSON>and<PERSON>, EventBus } from '@nestjs/cqrs';
import { Injectable, Logger, Inject } from '@nestjs/common';
import { CommandHandler, ICommandHandler, EventBus } from '@nestjs/cqrs';
import { CreateLeadCommand } from '../create-lead.command';
import { Lead } from '../../../../domain/entities/lead.entity';
import { ContactInfo } from '../../../../domain/value-objects/contact-info.vo';
import { LeadStatus } from '../../../../domain/value-objects/lead-status.vo';
import { ILeadRepository, LEAD_REPOSITORY_TOKEN } from '../../../../domain/repositories/lead.repository';
import { LeadCreatedEvent } from '../../events/lead-created.event';

/**
 * Create Lead Command Handler
 * Handles the creation of new leads in the CRM system
 */
@Injectable()
@CommandHandler(CreateLeadCommand)
export class CreateLeadHandler implements ICommandHandler<CreateLeadCommand> {
  private readonly logger = new Logger(CreateLeadHandler.name);

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN)
    private readonly leadRepository: ILeadRepository,
    private readonly eventBus: EventBus,
  ) {}

  async execute(command: CreateLeadCommand): Promise<{ leadId: number; lead: Lead }> {
    this.logger.log(`Executing CreateLeadCommand for: ${command.name}`);
    
    try {
      // 1. Create ContactInfo value object
      const contactInfo = new ContactInfo(
        command.email,
        command.phone,
        command.company,
        command.website,
        command.address,
        command.city,
        command.country,
      );

      // 2. Create Lead domain entity using factory method
      const lead = Lead.create(
        command.name,
        contactInfo,
        command.source,
        command.expectedRevenue,
        command.teamId,
        command.assignedUserId,
        command.priority,
        command.type,
      );

      // 3. Apply business rules and validation
      lead.validateBusinessRules();

      // 4. Set additional properties if provided
      if (command.description) {
        // Create updated lead with description
        const updatedLead = new Lead(
          lead.id,
          lead.name,
          lead.contactInfo,
          lead.status,
          lead.source,
          lead.type,
          lead.priority,
          lead.expectedRevenue,
          lead.probability,
          command.description,
          lead.assignedUserId,
          command.companyId || lead.companyId,
          command.tags,
          lead.partnerId,
          lead.stageId,
          lead.teamId,
          command.dateDeadline,
          lead.lostReasonId,
          command.campaignId,
          command.sourceId,
          command.mediumId,
          lead.createdAt,
          lead.updatedAt,
        );
        
        // 5. Save to repository (Odoo)
        const savedLead = await this.leadRepository.save(updatedLead);
        
        // 6. Publish domain event
        const event = new LeadCreatedEvent(
          savedLead.id,
          savedLead,
          command.createdBy,
          command.getMetadata(),
        );
        
        await this.eventBus.publish(event);
        
        this.logger.log(`Successfully created lead with ID: ${savedLead.id}`);
        
        return {
          leadId: savedLead.id,
          lead: savedLead,
        };
      }

      // 5. Save to repository (Odoo) - without description
      const savedLead = await this.leadRepository.save(lead);
      
      // 6. Publish domain event
      const event = new LeadCreatedEvent(
        savedLead.id,
        savedLead,
        command.createdBy,
        command.getMetadata(),
      );
      
      await this.eventBus.publish(event);
      
      this.logger.log(`Successfully created lead with ID: ${savedLead.id}`);
      
      return {
        leadId: savedLead.id,
        lead: savedLead,
      };

    } catch (error) {
      this.logger.error(`Failed to create lead: ${command.name}`, error);
      
      // Re-throw with more context
      if (error instanceof Error) {
        throw new Error(`Failed to create lead "${command.name}": ${error.message}`);
      }
      
      throw new Error(`Failed to create lead "${command.name}": Unknown error`);
    }
  }

  /**
   * Validate command before execution
   */
  private validateCommand(command: CreateLeadCommand): void {
    // Additional business validation can be added here
    // The command itself already validates basic requirements
    
    // Example: Check if team exists if teamId is provided
    // Example: Check if assigned user exists if assignedUserId is provided
    // These validations could be done via additional repository calls
  }
}
