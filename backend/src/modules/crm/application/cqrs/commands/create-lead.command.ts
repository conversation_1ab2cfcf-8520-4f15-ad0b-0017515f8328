import { ICommand } from '@nestjs/cqrs';
import { LeadPriority } from '@/modules/crm/domain/value-objects/lead-priority.vo';
import { LeadType } from '@/modules/crm/domain/value-objects/lead-type.vo';

/**
 * Create Lead Command
 * Represents the intent to create a new lead in the CRM system
 */
export class CreateLeadCommand implements ICommand {
  constructor(
    public readonly name: string,
    public readonly email?: string,
    public readonly phone?: string,
    public readonly company?: string,
    public readonly website?: string,
    public readonly address?: string,
    public readonly city?: string,
    public readonly country?: string,
    public readonly source: string = 'website',
    public readonly type: LeadType = LeadType.LEAD,
    public readonly priority: LeadPriority = LeadPriority.MEDIUM,
    public readonly expectedRevenue?: number,
    public readonly probability?: number,
    public readonly description?: string,
    public readonly assignedUserId?: number,
    public readonly teamId?: number,
    public readonly campaignId?: number,
    public readonly sourceId?: number,
    public readonly mediumId?: number,
    public readonly tags: string[] = [],
    public readonly dateDeadline?: Date,
    // Context information
    public readonly createdBy?: number,
    public readonly companyId?: number,
  ) {
    // Validate required fields
    if (!name || name.trim().length === 0) {
      throw new Error('Lead name is required');
    }

    // Validate email format if provided
    if (email && !this.isValidEmail(email)) {
      throw new Error('Invalid email format');
    }

    // Validate revenue and probability for opportunities
    if (type.isOpportunity()) {
      if (!expectedRevenue || expectedRevenue <= 0) {
        throw new Error('Expected revenue is required for opportunities and must be positive');
      }
      if (probability === undefined || probability < 0 || probability > 100) {
        throw new Error('Probability is required for opportunities and must be between 0 and 100');
      }
    }

    // Validate deadline is not in the past
    if (dateDeadline && dateDeadline < new Date()) {
      throw new Error('Deadline cannot be in the past');
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Get command metadata for logging and auditing
   */
  getMetadata() {
    return {
      commandType: 'CreateLeadCommand',
      leadName: this.name,
      leadType: this.type.value,
      priority: this.priority.value,
      source: this.source,
      hasRevenue: !!this.expectedRevenue,
      isAssigned: !!this.assignedUserId,
      hasTeam: !!this.teamId,
      hasDeadline: !!this.dateDeadline,
      tagCount: this.tags.length,
      createdBy: this.createdBy,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject() {
    return {
      name: this.name,
      email: this.email,
      phone: this.phone,
      company: this.company,
      website: this.website,
      address: this.address,
      city: this.city,
      country: this.country,
      source: this.source,
      type: this.type.toPlainObject(),
      priority: this.priority.toPlainObject(),
      expectedRevenue: this.expectedRevenue,
      probability: this.probability,
      description: this.description,
      assignedUserId: this.assignedUserId,
      teamId: this.teamId,
      campaignId: this.campaignId,
      sourceId: this.sourceId,
      mediumId: this.mediumId,
      tags: this.tags,
      dateDeadline: this.dateDeadline,
      createdBy: this.createdBy,
      companyId: this.companyId,
      metadata: this.getMetadata(),
    };
  }
}
