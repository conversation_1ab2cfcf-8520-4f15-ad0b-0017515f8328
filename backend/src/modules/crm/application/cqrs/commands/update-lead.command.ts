/**
 * Update Lead Command
 * Command to update an existing lead with new data
 */
export class UpdateLeadCommand {
  constructor(
    public readonly leadId: number,
    public readonly updates: {
      name?: string;
      description?: string;
      expectedRevenue?: number;
      probability?: number;
      priority?: number;
      stageId?: number;
      assignedUserId?: number;
      teamId?: number;
      tags?: string[];
      dateDeadline?: Date;
      contactInfo?: {
        email?: string;
        phone?: string;
        company?: string;
        website?: string;
      };
      // Saga-specific fields
      lastProcessed?: Date;
      opportunityId?: number;
      scoringProcessed?: boolean;
      assignmentProcessed?: boolean;
      enrichmentNotNeeded?: boolean;
      enrichmentProcessed?: boolean;
      complianceProcessed?: boolean;
    },
  ) {}
}
