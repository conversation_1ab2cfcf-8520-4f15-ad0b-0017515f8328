import { LeadAggregate } from '../aggregates/lead.aggregate';

/**
 * Lead Aggregate Repository Interface
 * Defines contract for persisting and retrieving Lead aggregates using Event Sourcing
 */
export interface ILeadAggregateRepository {
  /**
   * Save aggregate by persisting its uncommitted events
   */
  save(aggregate: LeadAggregate): Promise<void>;

  /**
   * Find aggregate by ID and reconstruct from events
   */
  findById(id: string): Promise<LeadAggregate | null>;

  /**
   * Check if aggregate exists
   */
  exists(id: string): Promise<boolean>;

  /**
   * Get aggregate version
   */
  getVersion(id: string): Promise<number>;

  /**
   * Find aggregates by criteria (using projections)
   */
  findByCriteria(criteria: LeadSearchCriteria): Promise<LeadAggregate[]>;

  /**
   * Delete aggregate (soft delete by applying delete event)
   */
  delete(id: string, reason?: string, userId?: string): Promise<void>;
}

/**
 * Lead Search Criteria Interface
 */
export interface LeadSearchCriteria {
  status?: string;
  assignedUserId?: number;
  teamId?: number;
  source?: string;
  type?: string;
  priority?: string;
  tags?: string[];
  createdAfter?: Date;
  createdBefore?: Date;
  isDeleted?: boolean;
  limit?: number;
  offset?: number;
}
