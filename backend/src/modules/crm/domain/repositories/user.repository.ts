/**
 * User Repository Interface
 * Defines the contract for user data access operations
 */
export interface IUserRepository {
  /**
   * Find user by ID
   */
  findById(id: string): Promise<User | null>;

  /**
   * Find users by team ID
   */
  findByTeamId(teamId: number, filters?: UserFilters): Promise<User[]>;

  /**
   * Find all users with optional filters
   */
  findAll(filters?: UserFilters): Promise<User[]>;

  /**
   * Create a new user
   */
  create(userData: CreateUserData): Promise<User>;

  /**
   * Update user
   */
  update(id: string, userData: UpdateUserData): Promise<User>;

  /**
   * Delete user
   */
  delete(id: string): Promise<boolean>;

  /**
   * Find users by role
   */
  findByRole(role: string): Promise<User[]>;

  /**
   * Find available users for assignment
   */
  findAvailableUsers(teamId?: number): Promise<User[]>;

  /**
   * Update user availability
   */
  updateAvailability(id: string, isAvailable: boolean): Promise<void>;

  /**
   * Get user workload statistics
   */
  getUserWorkloadStats(id: string): Promise<UserWorkloadStats>;

  /**
   * Find users by territory
   */
  findByTerritory(territory: string): Promise<User[]>;
}

/**
 * User Entity Interface
 */
export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  teamId?: number;
  seniority?: string;
  territories?: string[];
  skills?: string[];
  isActive: boolean;
  isAvailable: boolean;
  maxLeads?: number;
  timezone?: string;
  workingHours?: WorkingHours;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Supporting interfaces
 */
export interface UserFilters {
  isActive?: boolean;
  isAvailable?: boolean;
  role?: string;
  seniority?: string;
  territory?: string;
  skill?: string;
}

export interface CreateUserData {
  name: string;
  email: string;
  role: string;
  teamId?: number;
  seniority?: string;
  territories?: string[];
  skills?: string[];
  maxLeads?: number;
  timezone?: string;
  workingHours?: WorkingHours;
}

export interface UpdateUserData {
  name?: string;
  email?: string;
  role?: string;
  teamId?: number;
  seniority?: string;
  territories?: string[];
  skills?: string[];
  isActive?: boolean;
  isAvailable?: boolean;
  maxLeads?: number;
  timezone?: string;
  workingHours?: WorkingHours;
}

export interface WorkingHours {
  monday?: TimeSlot;
  tuesday?: TimeSlot;
  wednesday?: TimeSlot;
  thursday?: TimeSlot;
  friday?: TimeSlot;
  saturday?: TimeSlot;
  sunday?: TimeSlot;
}

export interface TimeSlot {
  start: string; // HH:mm format
  end: string;   // HH:mm format
}

export interface UserWorkloadStats {
  totalLeads: number;
  activeLeads: number;
  convertedLeads: number;
  totalValue: number;
  conversionRate: number;
  averageLeadAge: number;
  lastActivity: Date;
}
