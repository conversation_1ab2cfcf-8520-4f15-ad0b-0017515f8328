/**
 * Repository Injection Tokens
 * Defines tokens for dependency injection of repository interfaces
 */

export const LEAD_REPOSITORY_TOKEN = Symbol('ILeadRepository');
export const STAGE_REPOSITORY_TOKEN = Symbol('IStageRepository');
export const TEAM_REPOSITORY_TOKEN = Symbol('ITeamRepository');
export const USER_REPOSITORY_TOKEN = Symbol('IUserRepository');
export const OPPORTUNITY_REPOSITORY_TOKEN = Symbol('IOpportunityRepository');
export const CONTACT_REPOSITORY_TOKEN = Symbol('IContactRepository');
export const ACTIVITY_REPOSITORY_TOKEN = Symbol('IActivityRepository');
export const NOTE_REPOSITORY_TOKEN = Symbol('INoteRepository');
export const ATTACHMENT_REPOSITORY_TOKEN = Symbol('IAttachmentRepository');
export const PIPELINE_REPOSITORY_TOKEN = Symbol('IPipelineRepository');
