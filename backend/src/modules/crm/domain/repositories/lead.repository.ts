import { Lead } from '../entities/lead.entity';
import { Opportunity } from '../entities/opportunity.entity';
import { LeadStatus } from '../value-objects/lead-status.vo';
import { LeadType } from '../value-objects/lead-type.vo';
import { LeadPriority } from '../value-objects/lead-priority.vo';

/**
 * Lead Repository Interface
 * Defines the contract for lead data persistence with enhanced Odoo integration
 */
export interface ILeadRepository {
  /**
   * Save a lead (create or update)
   */
  save(lead: Lead): Promise<Lead>;

  /**
   * Find lead by ID
   */
  findById(id: number): Promise<Lead | null>;

  /**
   * Find lead by email
   */
  findByEmail(email: string): Promise<Lead | null>;

  /**
   * Find leads by stage
   */
  findByStage(stageId: number): Promise<Lead[]>;

  /**
   * Find leads by team
   */
  findByTeam(teamId: number): Promise<Lead[]>;

  /**
   * Find leads by type (lead or opportunity)
   */
  findByType(type: LeadType): Promise<Lead[]>;

  /**
   * Find leads by priority
   */
  findByPriority(priority: LeadPriority): Promise<Lead[]>;

  /**
   * Find leads by assigned user
   */
  findByAssignedUser(userId: number): Promise<Lead[]>;

  /**
   * Find overdue leads
   */
  findOverdue(): Promise<Lead[]>;

  /**
   * Find leads requiring immediate attention
   */
  findRequiringAttention(): Promise<Lead[]>;

  /**
   * Find leads with filters (enhanced)
   */
  findMany(filters: {
    status?: string | LeadStatus;
    source?: string;
    type?: LeadType;
    priority?: LeadPriority;
    assignedUserId?: number;
    teamId?: number;
    stageId?: number;
    partnerId?: number;
    campaignId?: number;
    sourceId?: number;
    mediumId?: number;
    minScore?: number;
    maxScore?: number;
    minRevenue?: number;
    maxRevenue?: number;
    minProbability?: number;
    maxProbability?: number;
    isOverdue?: boolean;
    requiresAttention?: boolean;
    dateFrom?: Date;
    dateTo?: Date;
    tags?: string[];
    offset?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{
    leads: Lead[];
    total: number;
    analytics: {
      averageScore: number;
      conversionRate: number;
      totalRevenue: number;
      weightedRevenue: number;
      averageProbability: number;
      topSources: Array<{ source: string; count: number }>;
      topTeams: Array<{ teamId: number; count: number }>;
      priorityDistribution: Array<{ priority: string; count: number }>;
      stageDistribution: Array<{ stageId: number; count: number }>;
    };
  }>;

  /**
   * Update lead status
   */
  updateStatus(id: number, status: LeadStatus): Promise<boolean>;

  /**
   * Update lead priority
   */
  updatePriority(id: number, priority: LeadPriority): Promise<boolean>;

  /**
   * Convert lead to opportunity
   */
  convertToOpportunity(leadId: number, partnerId?: number, stageId?: number): Promise<Opportunity>;

  /**
   * Assign lead to user
   */
  assignToUser(id: number, userId: number, teamId?: number): Promise<boolean>;

  /**
   * Assign lead to team
   */
  assignToTeam(id: number, teamId: number): Promise<boolean>;

  /**
   * Update revenue forecast
   */
  updateRevenueForecast(id: number, expectedRevenue: number, probability?: number): Promise<boolean>;

  /**
   * Set deadline
   */
  setDeadline(id: number, deadline: Date): Promise<boolean>;

  /**
   * Add tag to lead
   */
  addTag(id: number, tag: string): Promise<boolean>;

  /**
   * Remove tag from lead
   */
  removeTag(id: number, tag: string): Promise<boolean>;

  /**
   * Bulk update leads
   */
  bulkUpdate(ids: number[], updates: Partial<{
    status: LeadStatus;
    priority: LeadPriority;
    assignedUserId: number;
    teamId: number;
    stageId: number;
  }>): Promise<boolean>;

  /**
   * Delete lead (soft delete)
   */
  delete(id: number): Promise<boolean>;

  /**
   * Bulk delete leads
   */
  bulkDelete(ids: number[]): Promise<boolean>;

  /**
   * Get lead statistics (enhanced)
   */
  getStatistics(filters?: {
    teamId?: number;
    assignedUserId?: number;
    dateFrom?: Date;
    dateTo?: Date;
  }): Promise<{
    totalLeads: number;
    totalOpportunities: number;
    qualifiedLeads: number;
    convertedLeads: number;
    averageScore: number;
    conversionRate: number;
    totalRevenue: number;
    weightedRevenue: number;
    averageDealSize: number;
    averageSalesCycle: number;
    winRate: number;
    lossRate: number;
    pipelineVelocity: number;
    byPriority: Record<string, number>;
    byStage: Record<number, number>;
    byTeam: Record<number, number>;
    bySource: Record<string, number>;
  }>;

  /**
   * Get pipeline analytics
   */
  getPipelineAnalytics(teamId?: number): Promise<{
    stages: Array<{
      stageId: number;
      stageName: string;
      leadCount: number;
      totalRevenue: number;
      weightedRevenue: number;
      averageProbability: number;
      averageTimeInStage: number;
    }>;
    totalValue: number;
    totalWeightedValue: number;
    conversionRates: Record<number, number>;
    bottlenecks: Array<{
      stageId: number;
      stageName: string;
      averageTimeInStage: number;
      dropOffRate: number;
    }>;
  }>;

  /**
   * Search leads with full-text search
   */
  search(query: string, filters?: {
    type?: LeadType;
    teamId?: number;
    limit?: number;
  }): Promise<Lead[]>;

  /**
   * Find leads by filters
   */
  findByFilters(filters: any): Promise<Lead[]>;

  /**
   * Find leads by assigned user ID
   */
  findByAssignedUserId(userId: string): Promise<Lead[]>;

  /**
   * Search leads
   */
  search(searchFilters: any): Promise<Lead[]>;

  /**
   * Find leads by team ID
   */
  findByTeamId(teamId: number): Promise<Lead[]>;

  /**
   * Update lead
   */
  update(id: number, lead: Lead): Promise<Lead>;
}
