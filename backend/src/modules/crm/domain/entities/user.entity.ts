import { AggregateRoot } from '@nestjs/cqrs';
import { UserCreatedEvent } from '../events/user-created.event';
import { UserUpdatedEvent } from '../events/user-updated.event';
import { UserActivatedEvent } from '../events/user-activated.event';
import { UserDeactivatedEvent } from '../events/user-deactivated.event';

/**
 * User Entity
 * Represents a user in the CRM system with Odoo integration
 */
export class User extends AggregateRoot {
  constructor(
    public readonly id: string,
    public name: string,
    public email: string,
    public login: string,
    public role: string,
    public isActive: boolean = true,
    public teamId?: number,
    public seniority?: string,
    public territories?: string[],
    public skills?: string[],
    public isAvailable: boolean = true,
    public maxLeads?: number,
    public timezone?: string,
    public workingHours?: WorkingHours,
    public avatar?: string,
    public phone?: string,
    public language?: string,
    public odooUserId?: number,
    public permissions?: string[],
    public lastLogin?: Date,
    public readonly createdAt: Date = new Date(),
    public updatedAt: Date = new Date(),
  ) {
    super();
  }

  /**
   * Create a new user
   */
  static create(
    id: string,
    name: string,
    email: string,
    login: string,
    role: string,
    odooUserId: number,
  ): User {
    const user = new User(
      id,
      name,
      email,
      login,
      role,
      true, // isActive
      undefined, // teamId
      undefined, // seniority
      [], // territories
      [], // skills
      true, // isAvailable
      undefined, // maxLeads
      'UTC', // timezone
      undefined, // workingHours
      undefined, // avatar
      undefined, // phone
      'en_US', // language
      odooUserId,
      [], // permissions
      undefined, // lastLogin
      new Date(), // createdAt
      new Date(), // updatedAt
    );

    user.apply(new UserCreatedEvent(user.id, user.name, user.email, user.login, user.role));
    return user;
  }

  /**
   * Update user profile
   */
  updateProfile(updates: {
    name?: string;
    email?: string;
    phone?: string;
    avatar?: string;
    timezone?: string;
    language?: string;
  }): void {
    const oldData = {
      name: this.name,
      email: this.email,
      phone: this.phone,
      avatar: this.avatar,
      timezone: this.timezone,
      language: this.language,
    };

    if (updates.name) this.name = updates.name;
    if (updates.email) this.email = updates.email;
    if (updates.phone) this.phone = updates.phone;
    if (updates.avatar) this.avatar = updates.avatar;
    if (updates.timezone) this.timezone = updates.timezone;
    if (updates.language) this.language = updates.language;

    this.updatedAt = new Date();

    this.apply(new UserUpdatedEvent(this.id, oldData, {
      name: this.name,
      email: this.email,
      phone: this.phone,
      avatar: this.avatar,
      timezone: this.timezone,
      language: this.language,
    }));
  }

  /**
   * Assign user to team
   */
  assignToTeam(teamId: number): void {
    const oldTeamId = this.teamId;
    this.teamId = teamId;
    this.updatedAt = new Date();

    this.apply(new UserUpdatedEvent(this.id, { teamId: oldTeamId }, { teamId }));
  }

  /**
   * Update user role
   */
  updateRole(role: string): void {
    const oldRole = this.role;
    this.role = role;
    this.updatedAt = new Date();

    this.apply(new UserUpdatedEvent(this.id, { role: oldRole }, { role }));
  }

  /**
   * Update user permissions
   */
  updatePermissions(permissions: string[]): void {
    const oldPermissions = this.permissions;
    this.permissions = permissions;
    this.updatedAt = new Date();

    this.apply(new UserUpdatedEvent(this.id, { permissions: oldPermissions }, { permissions }));
  }

  /**
   * Set user availability
   */
  setAvailability(isAvailable: boolean): void {
    const oldAvailability = this.isAvailable;
    this.isAvailable = isAvailable;
    this.updatedAt = new Date();

    this.apply(new UserUpdatedEvent(this.id, { isAvailable: oldAvailability }, { isAvailable }));
  }

  /**
   * Activate user
   */
  activate(): void {
    if (!this.isActive) {
      this.isActive = true;
      this.updatedAt = new Date();
      this.apply(new UserActivatedEvent(this.id, this.name, this.email));
    }
  }

  /**
   * Deactivate user
   */
  deactivate(): void {
    if (this.isActive) {
      this.isActive = false;
      this.isAvailable = false;
      this.updatedAt = new Date();
      this.apply(new UserDeactivatedEvent(this.id, this.name, this.email));
    }
  }

  /**
   * Update last login time
   */
  updateLastLogin(): void {
    this.lastLogin = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Check if user has permission
   */
  hasPermission(permission: string): boolean {
    return this.permissions?.includes(permission) || false;
  }

  /**
   * Check if user is available for assignment
   */
  isAvailableForAssignment(): boolean {
    return this.isActive && this.isAvailable;
  }

  /**
   * Get user's full name with role
   */
  getDisplayName(): string {
    return `${this.name} (${this.role})`;
  }

  /**
   * Check if user is new (not persisted yet)
   */
  isNew(): boolean {
    return !this.id || this.id === '' || this.id === '0';
  }

  /**
   * Convert to plain object for serialization
   */
  toJSON(): any {
    return {
      id: this.id,
      name: this.name,
      email: this.email,
      login: this.login,
      role: this.role,
      isActive: this.isActive,
      teamId: this.teamId,
      seniority: this.seniority,
      territories: this.territories,
      skills: this.skills,
      isAvailable: this.isAvailable,
      maxLeads: this.maxLeads,
      timezone: this.timezone,
      workingHours: this.workingHours,
      avatar: this.avatar,
      phone: this.phone,
      language: this.language,
      odooUserId: this.odooUserId,
      permissions: this.permissions,
      lastLogin: this.lastLogin,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}

/**
 * Working Hours Interface
 */
export interface WorkingHours {
  monday?: TimeSlot;
  tuesday?: TimeSlot;
  wednesday?: TimeSlot;
  thursday?: TimeSlot;
  friday?: TimeSlot;
  saturday?: TimeSlot;
  sunday?: TimeSlot;
}

/**
 * Time Slot Interface
 */
export interface TimeSlot {
  start: string; // HH:mm format
  end: string;   // HH:mm format
}
