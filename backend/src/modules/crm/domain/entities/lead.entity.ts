import { OdooBaseModel } from '../../../../shared/domain/entities/odoo-base.entity';
import { LeadStatus } from '../value-objects/lead-status.vo';
import { ContactInfo } from '../value-objects/contact-info.vo';
import { LeadPriority } from '../value-objects/lead-priority.vo';
import { LeadType } from '../value-objects/lead-type.vo';
import { RevenueForecast } from '../value-objects/revenue-forecast.vo';

/**
 * Lead Domain Entity
 * Represents a potential customer in the CRM system with full Odoo mapping
 */
export class Lead extends OdooBaseModel {
  constructor(
    id: number,
    public readonly name: string,
    public readonly contactInfo: ContactInfo,
    public readonly status: LeadStatus,
    public readonly source: string,
    public readonly type: LeadType = LeadType.LEAD,
    public readonly priority: LeadPriority = LeadPriority.MEDIUM,
    public readonly expectedRevenue?: number,
    public readonly probability?: number,
    public readonly description?: string,
    public readonly assignedUserId?: number,
    public readonly companyId?: number,
    public readonly tags: string[] = [],
    // Additional Odoo fields
    public readonly partnerId?: number,           // res.partner
    public readonly stageId?: number,             // crm.stage
    public readonly teamId?: number,              // crm.team
    public readonly dateDeadline?: Date,          // Expected closing date
    public readonly lostReasonId?: number,        // crm.lost.reason
    public readonly campaignId?: number,          // utm.campaign
    public readonly sourceId?: number,            // utm.source
    public readonly mediumId?: number,            // utm.medium
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    super(id, createdAt, updatedAt);
    this.validateBusinessRules();
  }

  /**
   * Validate business rules for the lead
   */
  public validateBusinessRules(): void {
    // Type-specific validations
    if (this.type.isOpportunity()) {
      if (this.probability === undefined) {
        throw new Error('Opportunities must have a probability value');
      }
      if (this.expectedRevenue === undefined || this.expectedRevenue <= 0) {
        throw new Error('Opportunities must have a positive expected revenue');
      }
    }

    // Probability validation
    if (this.probability !== undefined && (this.probability < 0 || this.probability > 100)) {
      throw new Error('Probability must be between 0 and 100');
    }

    // Revenue validation
    if (this.expectedRevenue !== undefined && this.expectedRevenue < 0) {
      throw new Error('Expected revenue cannot be negative');
    }

    // Deadline validation
    if (this.dateDeadline && this.dateDeadline < new Date()) {
      // Allow past deadlines but log warning in real implementation
    }
  }

  /**
   * Business logic: Check if lead is qualified
   */
  isQualified(): boolean {
    return this.status.isQualified() &&
           this.contactInfo.isComplete() &&
           this.expectedRevenue !== undefined &&
           this.expectedRevenue > 0;
  }

  /**
   * Business logic: Check if lead is ready for conversion
   */
  canConvertToOpportunity(): boolean {
    return this.type.canConvertToOpportunity() &&
           this.isQualified() &&
           this.status.canConvert() &&
           this.assignedUserId !== undefined;
  }

  /**
   * Business logic: Check if lead is overdue
   */
  isOverdue(): boolean {
    if (!this.dateDeadline) return false;
    return this.dateDeadline < new Date() && !this.status.isTerminal();
  }

  /**
   * Business logic: Check if lead requires immediate attention
   */
  requiresImmediateAttention(): boolean {
    return this.priority.requiresImmediateAttention() ||
           this.isOverdue() ||
           (this.type.isOpportunity() && this.probability !== undefined && this.probability >= 80);
  }

  /**
   * Business logic: Get revenue forecast (for opportunities)
   */
  getRevenueForecast(): RevenueForecast | null {
    if (!this.type.canHaveRevenueForecast() || !this.expectedRevenue || this.probability === undefined) {
      return null;
    }
    return new RevenueForecast(this.expectedRevenue, this.probability);
  }

  /**
   * Business logic: Calculate weighted revenue
   */
  getWeightedRevenue(): number {
    const forecast = this.getRevenueForecast();
    return forecast ? forecast.weightedRevenue : 0;
  }

  /**
   * Business logic: Get days until deadline
   */
  getDaysUntilDeadline(): number | null {
    if (!this.dateDeadline) return null;
    const diffTime = this.dateDeadline.getTime() - new Date().getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Business logic: Calculate lead score
   */
  calculateScore(): number {
    let score = 0;

    // Base score from status (0-100 points)
    score += this.status.getScoreWeight();

    // Priority weight multiplier
    score *= this.priority.getScoreWeight();

    // Revenue potential (0-50 points)
    if (this.expectedRevenue) {
      score += Math.min(this.expectedRevenue / 1000, 50);
    }

    // Probability factor (0-30 points)
    if (this.probability) {
      score += this.probability * 0.3;
    }

    // Contact completeness (0-20 points)
    score += this.contactInfo.getCompletenessScore();

    // Source quality (0-20 points)
    score += this.getSourceScore();

    // Type bonus (opportunities get higher scores)
    if (this.type.isOpportunity()) {
      score += 10;
    }

    // Team assignment bonus
    if (this.teamId) {
      score += 5;
    }

    // Deadline urgency
    const daysUntilDeadline = this.getDaysUntilDeadline();
    if (daysUntilDeadline !== null) {
      if (daysUntilDeadline <= 7) {
        score += 15; // Urgent
      } else if (daysUntilDeadline <= 30) {
        score += 10; // Soon
      } else if (daysUntilDeadline <= 90) {
        score += 5; // Moderate
      }
    }

    return Math.min(Math.round(score), 100);
  }

  /**
   * Business logic: Update lead status
   */
  updateStatus(newStatus: LeadStatus, reason?: string): Lead {
    if (!this.status.canTransitionTo(newStatus)) {
      throw new Error(`Cannot transition from ${this.status.value} to ${newStatus.value}`);
    }

    return new Lead(
      this.id,
      this.name,
      this.contactInfo,
      newStatus,
      this.source,
      this.type,
      this.priority,
      this.expectedRevenue,
      this.probability,
      this.description,
      this.assignedUserId,
      this.companyId,
      this.tags,
      this.partnerId,
      this.stageId,
      this.teamId,
      this.dateDeadline,
      this.lostReasonId,
      this.campaignId,
      this.sourceId,
      this.mediumId,
      this.createdAt,
      new Date()
    );
  }

  /**
   * Business logic: Convert lead to opportunity
   */
  convertToOpportunity(partnerId?: number, stageId?: number): Lead {
    if (!this.canConvertToOpportunity()) {
      throw new Error('Lead cannot be converted to opportunity');
    }

    return new Lead(
      this.id,
      this.name,
      this.contactInfo,
      this.status,
      this.source,
      LeadType.OPPORTUNITY,
      this.priority,
      this.expectedRevenue,
      this.probability || LeadType.OPPORTUNITY.getDefaultProbability(),
      this.description,
      this.assignedUserId,
      this.companyId,
      this.tags,
      partnerId || this.partnerId,
      stageId || this.stageId,
      this.teamId,
      this.dateDeadline,
      this.lostReasonId,
      this.campaignId,
      this.sourceId,
      this.mediumId,
      this.createdAt,
      new Date()
    );
  }

  /**
   * Business logic: Update priority
   */
  updatePriority(newPriority: LeadPriority): Lead {
    return new Lead(
      this.id,
      this.name,
      this.contactInfo,
      this.status,
      this.source,
      this.type,
      newPriority,
      this.expectedRevenue,
      this.probability,
      this.description,
      this.assignedUserId,
      this.companyId,
      this.tags,
      this.partnerId,
      this.stageId,
      this.teamId,
      this.dateDeadline,
      this.lostReasonId,
      this.campaignId,
      this.sourceId,
      this.mediumId,
      this.createdAt,
      new Date()
    );
  }

  /**
   * Business logic: Update revenue forecast
   */
  updateRevenueForecast(expectedRevenue: number, probability?: number): Lead {
    if (!this.type.canHaveRevenueForecast()) {
      throw new Error('Only opportunities can have revenue forecasts');
    }

    return new Lead(
      this.id,
      this.name,
      this.contactInfo,
      this.status,
      this.source,
      this.type,
      this.priority,
      expectedRevenue,
      probability !== undefined ? probability : this.probability,
      this.description,
      this.assignedUserId,
      this.companyId,
      this.tags,
      this.partnerId,
      this.stageId,
      this.teamId,
      this.dateDeadline,
      this.lostReasonId,
      this.campaignId,
      this.sourceId,
      this.mediumId,
      this.createdAt,
      new Date()
    );
  }

  /**
   * Business logic: Set deadline
   */
  setDeadline(deadline: Date): Lead {
    if (deadline < new Date()) {
      throw new Error('Deadline cannot be in the past');
    }

    return new Lead(
      this.id,
      this.name,
      this.contactInfo,
      this.status,
      this.source,
      this.type,
      this.priority,
      this.expectedRevenue,
      this.probability,
      this.description,
      this.assignedUserId,
      this.companyId,
      this.tags,
      this.partnerId,
      this.stageId,
      this.teamId,
      deadline,
      this.lostReasonId,
      this.campaignId,
      this.sourceId,
      this.mediumId,
      this.createdAt,
      new Date()
    );
  }

  /**
   * Business logic: Add tag to lead
   */
  addTag(tag: string): Lead {
    if (this.tags.includes(tag)) {
      return this;
    }

    return new Lead(
      this.id,
      this.name,
      this.contactInfo,
      this.status,
      this.source,
      this.type,
      this.priority,
      this.expectedRevenue,
      this.probability,
      this.description,
      this.assignedUserId,
      this.companyId,
      [...this.tags, tag],
      this.partnerId,
      this.stageId,
      this.teamId,
      this.dateDeadline,
      this.lostReasonId,
      this.campaignId,
      this.sourceId,
      this.mediumId,
      this.createdAt,
      new Date()
    );
  }

  /**
   * Business logic: Assign lead to user
   */
  assignTo(userId: number, teamId?: number): Lead {
    return new Lead(
      this.id,
      this.name,
      this.contactInfo,
      this.status,
      this.source,
      this.type,
      this.priority,
      this.expectedRevenue,
      this.probability,
      this.description,
      userId,
      this.companyId,
      this.tags,
      this.partnerId,
      this.stageId,
      teamId || this.teamId,
      this.dateDeadline,
      this.lostReasonId,
      this.campaignId,
      this.sourceId,
      this.mediumId,
      this.createdAt,
      new Date()
    );
  }

  /**
   * Business logic: Assign to team
   */
  assignToTeam(teamId: number): Lead {
    return new Lead(
      this.id,
      this.name,
      this.contactInfo,
      this.status,
      this.source,
      this.type,
      this.priority,
      this.expectedRevenue,
      this.probability,
      this.description,
      this.assignedUserId,
      this.companyId,
      this.tags,
      this.partnerId,
      this.stageId,
      teamId,
      this.dateDeadline,
      this.lostReasonId,
      this.campaignId,
      this.sourceId,
      this.mediumId,
      this.createdAt,
      new Date()
    );
  }

  /**
   * Static factory method: Create new lead
   */
  static create(
    name: string,
    contactInfo: ContactInfo,
    source: string,
    expectedRevenue?: number,
    teamId?: number,
    assignedUserId?: number,
    priority: LeadPriority = LeadPriority.MEDIUM,
    type: LeadType = LeadType.LEAD,
  ): Lead {
    return new Lead(
      0, // New entity
      name,
      contactInfo,
      new LeadStatus('new'),
      source,
      type,
      priority,
      expectedRevenue,
      type.getDefaultProbability(),
      undefined, // description
      assignedUserId,
      undefined, // companyId
      [], // tags
      undefined, // partnerId
      undefined, // stageId
      teamId,
      undefined, // dateDeadline
      undefined, // lostReasonId
      undefined, // campaignId
      undefined, // sourceId
      undefined, // mediumId
      new Date(),
      new Date()
    );
  }

  /**
   * Private helper: Get score based on lead source
   */
  private getSourceScore(): number {
    const sourceScores: Record<string, number> = {
      'website': 15,
      'referral': 20,
      'social_media': 10,
      'email_campaign': 12,
      'cold_call': 8,
      'trade_show': 18,
      'partner': 16,
      'direct': 14,
    };

    return sourceScores[this.source.toLowerCase()] || 5;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject() {
    const revenueForecast = this.getRevenueForecast();
    const daysUntilDeadline = this.getDaysUntilDeadline();

    return {
      // Core fields
      id: this.id,
      name: this.name,
      contactInfo: this.contactInfo.toPlainObject(),
      status: this.status.value,
      source: this.source,
      type: this.type.toPlainObject(),
      priority: this.priority.toPlainObject(),

      // Revenue and probability
      expectedRevenue: this.expectedRevenue,
      probability: this.probability,
      revenueForecast: revenueForecast?.toPlainObject(),
      weightedRevenue: this.getWeightedRevenue(),

      // Description and metadata
      description: this.description,
      tags: this.tags,

      // Assignment and organization
      assignedUserId: this.assignedUserId,
      companyId: this.companyId,
      teamId: this.teamId,

      // Odoo-specific fields
      partnerId: this.partnerId,
      stageId: this.stageId,
      lostReasonId: this.lostReasonId,
      campaignId: this.campaignId,
      sourceId: this.sourceId,
      mediumId: this.mediumId,

      // Dates and deadlines
      dateDeadline: this.dateDeadline,
      daysUntilDeadline,
      isOverdue: this.isOverdue(),

      // Computed properties
      score: this.calculateScore(),
      isQualified: this.isQualified(),
      canConvert: this.canConvertToOpportunity(),
      requiresImmediateAttention: this.requiresImmediateAttention(),

      // Timestamps
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }











  /**
   * Remove tag
   */
  removeTag(tag: string): void {
    if (this.tags) {
      const index = this.tags.indexOf(tag);
      if (index > -1) {
        this.tags.splice(index, 1);
        this.updatedAt = new Date();
      }
    }
  }





  /**
   * Check if lead is converted
   */
  isConverted(): boolean {
    return this.status?.isConverted() || false;
  }
}
