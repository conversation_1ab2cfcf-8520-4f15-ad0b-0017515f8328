import { Lead } from './lead.entity';
import { ContactInfo } from '../value-objects/contact-info.vo';
import { LeadStatus } from '../value-objects/lead-status.vo';
import { LeadPriority } from '../value-objects/lead-priority.vo';
import { LeadType } from '../value-objects/lead-type.vo';
import { RevenueForecast } from '../value-objects/revenue-forecast.vo';

/**
 * Opportunity Domain Entity
 * Extends Lead with opportunity-specific business logic
 */
export class Opportunity extends Lead {
  constructor(
    id: number,
    name: string,
    contactInfo: ContactInfo,
    status: LeadStatus,
    source: string,
    priority: LeadPriority,
    expectedRevenue: number, // Required for opportunities
    probability: number,     // Required for opportunities
    description?: string,
    assignedUserId?: number,
    companyId?: number,
    tags: string[] = [],
    partnerId?: number,
    stageId?: number,
    teamId?: number,
    dateDeadline?: Date,
    lostReasonId?: number,
    campaignId?: number,
    sourceId?: number,
    mediumId?: number,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    super(
      id,
      name,
      contactInfo,
      status,
      source,
      LeadType.OPPORTUNITY, // Force opportunity type
      priority,
      expectedRevenue,
      probability,
      description,
      assignedUserId,
      companyId,
      tags,
      partnerId,
      stageId,
      teamId,
      dateDeadline,
      lostReasonId,
      campaignId,
      sourceId,
      mediumId,
      createdAt,
      updatedAt,
    );

    this.validateOpportunityRules();
  }

  /**
   * Validate opportunity-specific business rules
   */
  private validateOpportunityRules(): void {
    if (this.expectedRevenue === undefined || this.expectedRevenue <= 0) {
      throw new Error('Opportunities must have a positive expected revenue');
    }
    
    if (this.probability === undefined) {
      throw new Error('Opportunities must have a probability value');
    }

    if (this.probability < 0 || this.probability > 100) {
      throw new Error('Opportunity probability must be between 0 and 100');
    }
  }

  /**
   * Calculate weighted revenue with opportunity-specific logic
   */
  calculateWeightedRevenue(): number {
    const forecast = this.getRevenueForecast();
    if (!forecast) return 0;

    // Apply stage-based adjustments if stage is known
    let adjustment = 1.0;
    
    // These would typically come from stage configuration
    // For now, using probability-based adjustments
    if ((this.probability || 0) >= 90) {
      adjustment = 1.1; // Boost for very likely deals
    } else if ((this.probability || 0) <= 20) {
      adjustment = 0.9; // Reduce for unlikely deals
    }

    return forecast.weightedRevenue * adjustment;
  }

  /**
   * Check if opportunity can be closed (won or lost)
   */
  canBeClosed(): boolean {
    return this.assignedUserId !== undefined && 
           this.stageId !== undefined &&
           !this.status.isTerminal();
  }

  /**
   * Check if opportunity is in closing stage
   */
  isInClosingStage(): boolean {
    // This would typically check against stage configuration
    // For now, using probability as indicator
    return (this.probability || 0) >= 80;
  }

  /**
   * Get time spent in current stage (in days)
   */
  getTimeInStage(): number {
    // This would require stage change tracking
    // For now, return time since last update
    if (!this.updatedAt) return 0;
    
    const diffTime = new Date().getTime() - this.updatedAt.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Calculate sales velocity (revenue per day)
   */
  getSalesVelocity(): number {
    const ageInDays = this.getAgeInDays();
    if (ageInDays === 0) return 0;
    
    return this.calculateWeightedRevenue() / ageInDays;
  }

  /**
   * Check if opportunity is stale (no activity for too long)
   */
  isStale(staleDays: number = 30): boolean {
    const timeInStage = this.getTimeInStage();
    return timeInStage > staleDays && !this.status.isTerminal();
  }

  /**
   * Get opportunity health score (0-100)
   */
  getHealthScore(): number {
    let score = 50; // Base score

    // Probability factor (0-30 points)
    score += ((this.probability || 0) / 100) * 30;

    // Activity factor (0-20 points)
    const timeInStage = this.getTimeInStage();
    if (timeInStage <= 7) {
      score += 20; // Recent activity
    } else if (timeInStage <= 14) {
      score += 15;
    } else if (timeInStage <= 30) {
      score += 10;
    } else {
      score -= 10; // Stale opportunity
    }

    // Assignment factor (0-10 points)
    if (this.assignedUserId) {
      score += 10;
    }

    // Deadline factor (0-10 points)
    const daysUntilDeadline = this.getDaysUntilDeadline();
    if (daysUntilDeadline !== null) {
      if (daysUntilDeadline > 30) {
        score += 10; // Plenty of time
      } else if (daysUntilDeadline > 7) {
        score += 5;
      } else if (daysUntilDeadline < 0) {
        score -= 20; // Overdue
      }
    }

    // Contact completeness factor (0-10 points)
    if (this.contactInfo.isComplete()) {
      score += 10;
    }

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Get next best actions for this opportunity
   */
  getNextBestActions(): string[] {
    const actions: string[] = [];

    if (this.isOverdue()) {
      actions.push('Update deadline - opportunity is overdue');
    }

    if (this.isStale()) {
      actions.push('Schedule follow-up - no recent activity');
    }

    if (!this.contactInfo.isComplete()) {
      actions.push('Complete contact information');
    }

    if (!this.assignedUserId) {
      actions.push('Assign to sales representative');
    }

    if ((this.probability || 0) >= 80 && !this.isInClosingStage()) {
      actions.push('Move to closing stage');
    }

    if ((this.probability || 0) <= 20 && this.getTimeInStage() > 14) {
      actions.push('Consider marking as lost or nurturing');
    }

    if (this.requiresImmediateAttention()) {
      actions.push('Requires immediate attention - high priority');
    }

    return actions;
  }

  /**
   * Static factory method: Create opportunity from lead
   */
  static fromLead(
    lead: Lead,
    expectedRevenue: number,
    probability: number,
    partnerId?: number,
    stageId?: number,
  ): Opportunity {
    if (!lead.canConvertToOpportunity()) {
      throw new Error('Lead cannot be converted to opportunity');
    }

    return new Opportunity(
      lead.id,
      lead.name,
      lead.contactInfo,
      lead.status,
      lead.source,
      lead.priority,
      expectedRevenue,
      probability,
      lead.description,
      lead.assignedUserId,
      lead.companyId,
      lead.tags,
      partnerId || lead.partnerId,
      stageId || lead.stageId,
      lead.teamId,
      lead.dateDeadline,
      lead.lostReasonId,
      lead.campaignId,
      lead.sourceId,
      lead.mediumId,
      lead.createdAt,
      new Date(),
    );
  }

  /**
   * Static factory method: Create new opportunity
   */
  static create(
    name: string,
    contactInfo: ContactInfo,
    source: string,
    expectedRevenue: number = 0,
    teamId?: number,
    assignedUserId?: number,
    priority: LeadPriority = LeadPriority.MEDIUM,
  ): Opportunity {
    return new Opportunity(
      0, // New entity
      name,
      contactInfo,
      new LeadStatus('qualified'), // Opportunities start qualified
      source,
      priority,
      expectedRevenue,
      80, // Default probability for opportunities
      undefined, // description
      assignedUserId,
      undefined, // companyId
      [], // tags
      undefined, // partnerId
      undefined, // stageId
      teamId,
      undefined, // dateDeadline
      undefined, // lostReasonId
      undefined, // campaignId
      undefined, // sourceId
      undefined, // mediumId
      new Date(),
      new Date(),
    );
  }

  /**
   * Enhanced toPlainObject with opportunity-specific data
   */
  toPlainObject() {
    const baseObject = super.toPlainObject();
    
    return {
      ...baseObject,
      // Opportunity-specific computed fields
      weightedRevenue: this.calculateWeightedRevenue(),
      salesVelocity: this.getSalesVelocity(),
      timeInStage: this.getTimeInStage(),
      healthScore: this.getHealthScore(),
      isStale: this.isStale(),
      canBeClosed: this.canBeClosed(),
      isInClosingStage: this.isInClosingStage(),
      nextBestActions: this.getNextBestActions(),
    };
  }
}
