import { OdooBaseModel } from '@/shared/domain/entities/odoo-base.entity';

/**
 * Stage Domain Entity
 * Represents a stage in the CRM pipeline (crm.stage in Odoo)
 */
export class Stage extends OdooBaseModel {
  constructor(
    id: number,
    public readonly name: string,
    public readonly sequence: number,
    public readonly isWonStage: boolean = false,
    public readonly isLostStage: boolean = false,
    public readonly teamId?: number,
    public readonly requirements?: string,
    public readonly fold: boolean = false,
    public readonly probabilityMin?: number,
    public readonly probabilityMax?: number,
    public readonly onChange?: boolean,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    super(id, createdAt, updatedAt);
    this.validateBusinessRules();
  }

  /**
   * Validate stage business rules
   */
  private validateBusinessRules(): void {
    if (this.sequence < 0) {
      throw new Error('Stage sequence cannot be negative');
    }

    if (this.isWonStage && this.isLostStage) {
      throw new Error('Stage cannot be both won and lost');
    }

    if (this.probabilityMin !== undefined && this.probabilityMax !== undefined) {
      if (this.probabilityMin > this.probabilityMax) {
        throw new Error('Minimum probability cannot be greater than maximum probability');
      }
      if (this.probabilityMin < 0 || this.probabilityMax > 100) {
        throw new Error('Probability values must be between 0 and 100');
      }
    }
  }

  /**
   * Check if this is a terminal stage (won or lost)
   */
  isTerminal(): boolean {
    return this.isWonStage || this.isLostStage;
  }

  /**
   * Check if this is an active stage (not terminal and not folded)
   */
  isActive(): boolean {
    return !this.isTerminal() && !this.fold;
  }

  /**
   * Check if this stage is for a specific team
   */
  isTeamSpecific(): boolean {
    return this.teamId !== undefined;
  }

  /**
   * Check if this stage is global (not team-specific)
   */
  isGlobal(): boolean {
    return !this.isTeamSpecific();
  }

  /**
   * Get default probability for this stage
   */
  getDefaultProbability(): number {
    if (this.isWonStage) return 100;
    if (this.isLostStage) return 0;
    
    // Use average of min/max if available
    if (this.probabilityMin !== undefined && this.probabilityMax !== undefined) {
      return (this.probabilityMin + this.probabilityMax) / 2;
    }
    
    if (this.probabilityMin !== undefined) return this.probabilityMin;
    if (this.probabilityMax !== undefined) return this.probabilityMax;
    
    // Default based on sequence (early stages = lower probability)
    return Math.min(this.sequence * 20, 80);
  }

  /**
   * Check if a probability is valid for this stage
   */
  isProbabilityValid(probability: number): boolean {
    if (this.isWonStage) return probability === 100;
    if (this.isLostStage) return probability === 0;
    
    if (this.probabilityMin !== undefined && probability < this.probabilityMin) {
      return false;
    }
    
    if (this.probabilityMax !== undefined && probability > this.probabilityMax) {
      return false;
    }
    
    return true;
  }

  /**
   * Get stage color for UI display
   */
  getColor(): string {
    if (this.isWonStage) return '#28a745'; // Green
    if (this.isLostStage) return '#dc3545'; // Red
    if (this.fold) return '#6c757d'; // Gray
    
    // Color based on sequence
    const colors = [
      '#007bff', // Blue
      '#17a2b8', // Cyan
      '#ffc107', // Yellow
      '#fd7e14', // Orange
      '#6f42c1', // Purple
    ];
    
    return colors[this.sequence % colors.length] || '#007bff';
  }

  /**
   * Get CSS class for UI styling
   */
  getCssClass(): string {
    const classes = ['stage'];
    
    if (this.isWonStage) classes.push('stage-won');
    if (this.isLostStage) classes.push('stage-lost');
    if (this.fold) classes.push('stage-folded');
    if (this.isActive()) classes.push('stage-active');
    
    return classes.join(' ');
  }

  /**
   * Get icon for UI display
   */
  getIcon(): string {
    if (this.isWonStage) return 'check-circle';
    if (this.isLostStage) return 'x-circle';
    if (this.fold) return 'eye-slash';
    
    return 'circle';
  }

  /**
   * Compare stages by sequence
   */
  isBefore(other: Stage): boolean {
    return this.sequence < other.sequence;
  }

  /**
   * Compare stages by sequence
   */
  isAfter(other: Stage): boolean {
    return this.sequence > other.sequence;
  }

  /**
   * Get stage progression percentage (0-100)
   */
  getProgressionPercentage(totalStages: number): number {
    if (totalStages <= 1) return 100;
    return Math.round((this.sequence / (totalStages - 1)) * 100);
  }

  /**
   * Check if opportunities can move to this stage
   */
  canReceiveOpportunities(): boolean {
    return !this.fold && !this.isLostStage;
  }

  /**
   * Check if this stage requires specific actions
   */
  hasRequirements(): boolean {
    return !!(this.requirements && this.requirements.trim().length > 0);
  }

  /**
   * Get stage type for categorization
   */
  getStageType(): 'initial' | 'progress' | 'closing' | 'won' | 'lost' {
    if (this.isWonStage) return 'won';
    if (this.isLostStage) return 'lost';
    if (this.sequence === 0) return 'initial';
    
    const defaultProbability = this.getDefaultProbability();
    if (defaultProbability >= 80) return 'closing';
    
    return 'progress';
  }

  /**
   * Static factory method: Create new stage
   */
  static create(
    name: string,
    sequence: number,
    teamId?: number,
    isWonStage: boolean = false,
    isLostStage: boolean = false,
  ): Stage {
    return new Stage(
      0, // New entity
      name,
      sequence,
      isWonStage,
      isLostStage,
      teamId,
      undefined, // requirements
      false, // fold
      undefined, // probabilityMin
      undefined, // probabilityMax
      false, // onChange
      new Date(),
      new Date(),
    );
  }

  /**
   * Create a copy with updated properties
   */
  update(updates: Partial<{
    name: string;
    sequence: number;
    isWonStage: boolean;
    isLostStage: boolean;
    requirements: string;
    fold: boolean;
    probabilityMin: number;
    probabilityMax: number;
  }>): Stage {
    return new Stage(
      this.id,
      updates.name ?? this.name,
      updates.sequence ?? this.sequence,
      updates.isWonStage ?? this.isWonStage,
      updates.isLostStage ?? this.isLostStage,
      this.teamId,
      updates.requirements ?? this.requirements,
      updates.fold ?? this.fold,
      updates.probabilityMin ?? this.probabilityMin,
      updates.probabilityMax ?? this.probabilityMax,
      this.onChange,
      this.createdAt,
      new Date(),
    );
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject() {
    return {
      id: this.id,
      name: this.name,
      sequence: this.sequence,
      isWonStage: this.isWonStage,
      isLostStage: this.isLostStage,
      teamId: this.teamId,
      requirements: this.requirements,
      fold: this.fold,
      probabilityMin: this.probabilityMin,
      probabilityMax: this.probabilityMax,
      onChange: this.onChange,
      
      // Computed properties
      isTerminal: this.isTerminal(),
      isActive: this.isActive(),
      isTeamSpecific: this.isTeamSpecific(),
      isGlobal: this.isGlobal(),
      defaultProbability: this.getDefaultProbability(),
      color: this.getColor(),
      cssClass: this.getCssClass(),
      icon: this.getIcon(),
      stageType: this.getStageType(),
      canReceiveOpportunities: this.canReceiveOpportunities(),
      hasRequirements: this.hasRequirements(),
      
      // Timestamps
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
