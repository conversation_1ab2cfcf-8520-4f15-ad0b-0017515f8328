import { AggregateRoot } from '@nestjs/cqrs';
import { DomainEvent } from '../events/base/domain-event.base';
import { LeadStatus } from '../value-objects/lead-status.vo';
import { ContactInfo } from '../value-objects/contact-info.vo';
import { LeadPriority } from '../value-objects/lead-priority.vo';
import { LeadType } from '../value-objects/lead-type.vo';

// Import Lead Events
import { 
  LeadCreatedEvent,
  LeadStatusChangedEvent,
  LeadAssignedEvent,
  LeadUpdatedEvent,
  LeadDeletedEvent,
  LeadScoreChangedEvent,
  LeadActivityCreatedEvent
} from '../events/lead-events';

/**
 * Lead Aggregate Root
 * Implements Event Sourcing pattern for Lead domain entity
 * Encapsulates business logic and maintains consistency through events
 */
export class LeadAggregate extends AggregateRoot {
  private _id: string;
  private _name: string;
  private _contactInfo: ContactInfo;
  private _status: LeadStatus;
  private _source: string;
  private _type: LeadType;
  private _priority: LeadPriority;
  private _expectedRevenue?: number;
  private _probability?: number;
  private _description?: string;
  private _assignedUserId?: number;
  private _companyId?: number;
  private _tags: string[];
  private _partnerId?: number;
  private _stageId?: number;
  private _teamId?: number;
  private _dateDeadline?: Date;
  private _lostReasonId?: number;
  private _campaignId?: number;
  private _sourceId?: number;
  private _mediumId?: number;
  private _createdAt: Date;
  private _updatedAt: Date;
  private _version: number;
  private _isDeleted: boolean;

  constructor() {
    super();
    this._version = 0;
    this._isDeleted = false;
    this._tags = [];
    this._createdAt = new Date();
    this._updatedAt = new Date();
  }

  // Getters
  get id(): string { return this._id; }
  get name(): string { return this._name; }
  get contactInfo(): ContactInfo { return this._contactInfo; }
  get status(): LeadStatus { return this._status; }
  get source(): string { return this._source; }
  get type(): LeadType { return this._type; }
  get priority(): LeadPriority { return this._priority; }
  get expectedRevenue(): number | undefined { return this._expectedRevenue; }
  get probability(): number | undefined { return this._probability; }
  get description(): string | undefined { return this._description; }
  get assignedUserId(): number | undefined { return this._assignedUserId; }
  get companyId(): number | undefined { return this._companyId; }
  get tags(): string[] { return [...this._tags]; }
  get partnerId(): number | undefined { return this._partnerId; }
  get stageId(): number | undefined { return this._stageId; }
  get teamId(): number | undefined { return this._teamId; }
  get dateDeadline(): Date | undefined { return this._dateDeadline; }
  get lostReasonId(): number | undefined { return this._lostReasonId; }
  get campaignId(): number | undefined { return this._campaignId; }
  get sourceId(): number | undefined { return this._sourceId; }
  get mediumId(): number | undefined { return this._mediumId; }
  get createdAt(): Date { return this._createdAt; }
  get updatedAt(): Date { return this._updatedAt; }
  get version(): number { return this._version; }
  get isDeleted(): boolean { return this._isDeleted; }

  /**
   * Create a new lead (factory method)
   */
  static create(
    id: string,
    name: string,
    contactInfo: ContactInfo,
    source: string,
    type: LeadType = LeadType.LEAD,
    priority: LeadPriority = LeadPriority.MEDIUM,
    expectedRevenue?: number,
    teamId?: number,
    assignedUserId?: number,
    userId?: string,
    tenantId?: string,
  ): LeadAggregate {
    const lead = new LeadAggregate();
    
    // Create a temporary lead object for the event
    const tempLead = {
      id: parseInt(id),
      name,
      contactInfo,
      status: new LeadStatus('new'),
      source,
      type,
      priority,
      expectedRevenue,
      probability: type.getDefaultProbability(),
      description: undefined,
      assignedUserId,
      companyId: undefined,
      tags: [],
      partnerId: undefined,
      stageId: undefined,
      teamId,
      dateDeadline: undefined,
      lostReasonId: undefined,
      campaignId: undefined,
      sourceId: undefined,
      mediumId: undefined,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Apply creation event
    const event = new LeadCreatedEvent(
      tempLead,
      source,
      undefined, // campaign
      undefined, // causationId
      undefined, // correlationId
      userId,
      tenantId,
    );

    lead.apply(event);
    return lead;
  }

  /**
   * Update lead status
   */
  changeStatus(
    newStatus: LeadStatus,
    reason?: string,
    changedBy?: string,
    userId?: string,
    tenantId?: string,
  ): void {
    if (this._isDeleted) {
      throw new Error('Cannot change status of deleted lead');
    }

    if (!this._status.canTransitionTo(newStatus)) {
      throw new Error(`Cannot transition from ${this._status.value} to ${newStatus.value}`);
    }

    const event = new LeadStatusChangedEvent(
      parseInt(this._id),
      this._status,
      newStatus,
      reason,
      changedBy,
      undefined, // causationId
      undefined, // correlationId
      userId,
      tenantId,
    );

    this.apply(event);
  }

  /**
   * Assign lead to user/team
   */
  assignTo(
    assignedUserId?: number,
    assignedTeamId?: number,
    assignmentReason?: string,
    autoAssigned: boolean = false,
    userId?: string,
    tenantId?: string,
  ): void {
    if (this._isDeleted) {
      throw new Error('Cannot assign deleted lead');
    }

    const event = new LeadAssignedEvent(
      parseInt(this._id),
      assignedUserId,
      assignedTeamId,
      this._assignedUserId,
      this._teamId,
      assignmentReason,
      autoAssigned,
      undefined, // causationId
      undefined, // correlationId
      userId,
      tenantId,
    );

    this.apply(event);
  }

  /**
   * Update lead information
   */
  update(
    updates: Partial<LeadUpdateData>,
    userId?: string,
    tenantId?: string,
  ): void {
    if (this._isDeleted) {
      throw new Error('Cannot update deleted lead');
    }

    const event = new LeadUpdatedEvent(
      parseInt(this._id),
      updates,
      this.getCurrentState(),
      undefined, // causationId
      undefined, // correlationId
      userId,
      tenantId,
    );

    this.apply(event);
  }

  /**
   * Delete lead (soft delete)
   */
  delete(
    reason?: string,
    userId?: string,
    tenantId?: string,
  ): void {
    if (this._isDeleted) {
      throw new Error('Lead is already deleted');
    }

    const event = new LeadDeletedEvent(
      parseInt(this._id),
      reason,
      undefined, // causationId
      undefined, // correlationId
      userId,
      tenantId,
    );

    this.apply(event);
  }

  /**
   * Event Handlers - Apply events to update aggregate state
   * These methods are called automatically by the AggregateRoot when events are applied
   */

  // Handle LeadCreatedEvent
  onLeadCreatedEvent(event: LeadCreatedEvent): void {
    this._id = event.aggregateId;
    this._name = event.lead.name;
    this._contactInfo = event.lead.contactInfo;
    this._status = event.lead.status;
    this._source = event.source;
    this._type = event.lead.type;
    this._priority = event.lead.priority;
    this._expectedRevenue = event.lead.expectedRevenue;
    this._probability = event.lead.probability;
    this._description = event.lead.description;
    this._assignedUserId = event.lead.assignedUserId;
    this._companyId = event.lead.companyId;
    this._tags = [...(event.lead.tags || [])];
    this._partnerId = event.lead.partnerId;
    this._stageId = event.lead.stageId;
    this._teamId = event.lead.teamId;
    this._dateDeadline = event.lead.dateDeadline;
    this._lostReasonId = event.lead.lostReasonId;
    this._campaignId = event.lead.campaignId;
    this._sourceId = event.lead.sourceId;
    this._mediumId = event.lead.mediumId;
    this._createdAt = event.occurredAt;
    this._updatedAt = event.occurredAt;
    this._version = event.aggregateVersion;
  }

  // Handle LeadStatusChangedEvent
  onLeadStatusChangedEvent(event: LeadStatusChangedEvent): void {
    this._status = event.newStatus;
    this._updatedAt = event.occurredAt;
    this._version = event.aggregateVersion;
  }

  // Handle LeadAssignedEvent
  onLeadAssignedEvent(event: LeadAssignedEvent): void {
    this._assignedUserId = event.assignedUserId;
    this._teamId = event.assignedTeamId;
    this._updatedAt = event.occurredAt;
    this._version = event.aggregateVersion;
  }

  // Handle LeadUpdatedEvent
  onLeadUpdatedEvent(event: LeadUpdatedEvent): void {
    // Apply updates
    if (event.updates.name !== undefined) this._name = event.updates.name;
    if (event.updates.contactInfo !== undefined) this._contactInfo = event.updates.contactInfo;
    if (event.updates.expectedRevenue !== undefined) this._expectedRevenue = event.updates.expectedRevenue;
    if (event.updates.probability !== undefined) this._probability = event.updates.probability;
    if (event.updates.description !== undefined) this._description = event.updates.description;
    if (event.updates.tags !== undefined) this._tags = [...event.updates.tags];
    if (event.updates.dateDeadline !== undefined) this._dateDeadline = event.updates.dateDeadline;
    
    this._updatedAt = event.occurredAt;
    this._version = event.aggregateVersion;
  }

  // Handle LeadDeletedEvent
  onLeadDeletedEvent(event: LeadDeletedEvent): void {
    this._isDeleted = true;
    this._updatedAt = event.occurredAt;
    this._version = event.aggregateVersion;
  }

  /**
   * Get current state for updates
   */
  private getCurrentState(): any {
    return {
      id: this._id,
      name: this._name,
      contactInfo: this._contactInfo,
      status: this._status,
      source: this._source,
      type: this._type,
      priority: this._priority,
      expectedRevenue: this._expectedRevenue,
      probability: this._probability,
      description: this._description,
      assignedUserId: this._assignedUserId,
      companyId: this._companyId,
      tags: this._tags,
      partnerId: this._partnerId,
      stageId: this._stageId,
      teamId: this._teamId,
      dateDeadline: this._dateDeadline,
      lostReasonId: this._lostReasonId,
      campaignId: this._campaignId,
      sourceId: this._sourceId,
      mediumId: this._mediumId,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt,
      version: this._version,
      isDeleted: this._isDeleted,
    };
  }

  /**
   * Validate business rules
   */
  private validateBusinessRules(): void {
    if (this._type?.isOpportunity() && this._probability === undefined) {
      throw new Error('Opportunities must have a probability value');
    }

    if (this._expectedRevenue !== undefined && this._expectedRevenue < 0) {
      throw new Error('Expected revenue cannot be negative');
    }

    if (this._probability !== undefined && (this._probability < 0 || this._probability > 100)) {
      throw new Error('Probability must be between 0 and 100');
    }
  }

  /**
   * Override AggregateRoot methods for Event Sourcing
   */

  /**
   * Load aggregate from historical events
   */
  loadFromHistory(events: DomainEvent[]): void {
    for (const event of events) {
      this.applyEvent(event, false); // Don't add to uncommitted events
    }
  }

  /**
   * Apply event to aggregate
   */
  private applyEvent(event: DomainEvent, isNew: boolean = true): void {
    // Call the appropriate event handler based on event type
    switch (event.eventType) {
      case 'LeadCreated':
        this.onLeadCreatedEvent(event as LeadCreatedEvent);
        break;
      case 'LeadStatusChanged':
        this.onLeadStatusChangedEvent(event as LeadStatusChangedEvent);
        break;
      case 'LeadAssigned':
        this.onLeadAssignedEvent(event as LeadAssignedEvent);
        break;
      case 'LeadUpdated':
        this.onLeadUpdatedEvent(event as LeadUpdatedEvent);
        break;
      case 'LeadDeleted':
        this.onLeadDeletedEvent(event as LeadDeletedEvent);
        break;
      default:
        console.warn(`Unknown event type: ${event.eventType}`);
    }

    if (isNew) {
      this.apply(event); // Add to uncommitted events
    }
  }
}

/**
 * Lead Update Data Interface
 */
export interface LeadUpdateData {
  name?: string;
  contactInfo?: ContactInfo;
  expectedRevenue?: number;
  probability?: number;
  description?: string;
  tags?: string[];
  dateDeadline?: Date;
  partnerId?: number;
  stageId?: number;
  companyId?: number;
  lostReasonId?: number;
  campaignId?: number;
  sourceId?: number;
  mediumId?: number;
}
