/**
 * Notification Priority Enum
 * Defines priority levels for notifications
 */
export enum NotificationPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  CRITICAL = 4,
  URGENT = 5,
}

/**
 * Priority characteristics and behavior
 */
export const PRIORITY_CHARACTERISTICS = {
  [NotificationPriority.LOW]: {
    name: 'Low',
    description: 'Non-urgent informational notifications',
    deliveryDelay: 300, // 5 minutes
    retryAttempts: 1,
    retryDelay: 3600, // 1 hour
    respectQuietHours: true,
    batchingAllowed: true,
    channels: ['email', 'in_app'],
    color: '#6B7280', // Gray
    icon: 'info',
  },
  [NotificationPriority.NORMAL]: {
    name: 'Normal',
    description: 'Standard business notifications',
    deliveryDelay: 60, // 1 minute
    retryAttempts: 2,
    retryDelay: 1800, // 30 minutes
    respectQuietHours: true,
    batchingAllowed: true,
    channels: ['email', 'in_app', 'push'],
    color: '#3B82F6', // Blue
    icon: 'notification',
  },
  [NotificationPriority.HIGH]: {
    name: 'High',
    description: 'Important notifications requiring attention',
    deliveryDelay: 10, // 10 seconds
    retryAttempts: 3,
    retryDelay: 600, // 10 minutes
    respectQuietHours: false,
    batchingAllowed: false,
    channels: ['email', 'in_app', 'push', 'sms'],
    color: '#F59E0B', // Amber
    icon: 'warning',
  },
  [NotificationPriority.CRITICAL]: {
    name: 'Critical',
    description: 'Critical notifications requiring immediate attention',
    deliveryDelay: 0, // Immediate
    retryAttempts: 5,
    retryDelay: 300, // 5 minutes
    respectQuietHours: false,
    batchingAllowed: false,
    channels: ['email', 'in_app', 'push', 'sms', 'slack'],
    color: '#EF4444', // Red
    icon: 'alert',
  },
  [NotificationPriority.URGENT]: {
    name: 'Urgent',
    description: 'Emergency notifications requiring immediate action',
    deliveryDelay: 0, // Immediate
    retryAttempts: 10,
    retryDelay: 60, // 1 minute
    respectQuietHours: false,
    batchingAllowed: false,
    channels: ['email', 'in_app', 'push', 'sms', 'slack', 'webhook'],
    color: '#DC2626', // Dark Red
    icon: 'emergency',
  },
} as const;

/**
 * Get priority characteristics
 */
export function getPriorityCharacteristics(priority: NotificationPriority) {
  return PRIORITY_CHARACTERISTICS[priority];
}

/**
 * Get priority name
 */
export function getPriorityName(priority: NotificationPriority): string {
  return PRIORITY_CHARACTERISTICS[priority].name;
}

/**
 * Get priority color
 */
export function getPriorityColor(priority: NotificationPriority): string {
  return PRIORITY_CHARACTERISTICS[priority].color;
}

/**
 * Get delivery delay for priority
 */
export function getDeliveryDelay(priority: NotificationPriority): number {
  return PRIORITY_CHARACTERISTICS[priority].deliveryDelay;
}

/**
 * Get retry configuration for priority
 */
export function getRetryConfig(priority: NotificationPriority): {
  attempts: number;
  delay: number;
} {
  const characteristics = PRIORITY_CHARACTERISTICS[priority];
  return {
    attempts: characteristics.retryAttempts,
    delay: characteristics.retryDelay,
  };
}

/**
 * Check if priority respects quiet hours
 */
export function respectsQuietHours(priority: NotificationPriority): boolean {
  return PRIORITY_CHARACTERISTICS[priority].respectQuietHours;
}

/**
 * Check if priority allows batching
 */
export function allowsBatching(priority: NotificationPriority): boolean {
  return PRIORITY_CHARACTERISTICS[priority].batchingAllowed;
}

/**
 * Get recommended channels for priority
 */
export function getRecommendedChannelsForPriority(priority: NotificationPriority): string[] {
  return [...PRIORITY_CHARACTERISTICS[priority].channels];
}

/**
 * Compare priorities
 */
export function comparePriorities(a: NotificationPriority, b: NotificationPriority): number {
  return b - a; // Higher priority first
}

/**
 * Check if priority is high or above
 */
export function isHighPriority(priority: NotificationPriority): boolean {
  return priority >= NotificationPriority.HIGH;
}

/**
 * Check if priority is critical or above
 */
export function isCriticalPriority(priority: NotificationPriority): boolean {
  return priority >= NotificationPriority.CRITICAL;
}

/**
 * Get priority from string
 */
export function parsePriority(value: string): NotificationPriority {
  const upperValue = value.toUpperCase();
  
  switch (upperValue) {
    case 'LOW': return NotificationPriority.LOW;
    case 'NORMAL': return NotificationPriority.NORMAL;
    case 'HIGH': return NotificationPriority.HIGH;
    case 'CRITICAL': return NotificationPriority.CRITICAL;
    case 'URGENT': return NotificationPriority.URGENT;
    default: return NotificationPriority.NORMAL;
  }
}

/**
 * Get all priorities sorted by level
 */
export function getAllPriorities(): Array<{
  value: NotificationPriority;
  name: string;
  description: string;
  color: string;
}> {
  return Object.values(NotificationPriority)
    .filter(value => typeof value === 'number')
    .map(priority => {
      const characteristics = PRIORITY_CHARACTERISTICS[priority as NotificationPriority];
      return {
        value: priority as NotificationPriority,
        name: characteristics.name,
        description: characteristics.description,
        color: characteristics.color,
      };
    })
    .sort((a, b) => a.value - b.value);
}
