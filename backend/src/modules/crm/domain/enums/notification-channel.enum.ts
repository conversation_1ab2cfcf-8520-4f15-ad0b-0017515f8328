/**
 * Notification Channel Enum
 * Defines available notification delivery channels
 */
export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  IN_APP = 'in_app',
  SLACK = 'slack',
  WEBHOOK = 'webhook',
  TEAMS = 'teams',
  DISCORD = 'discord',
}

/**
 * Channel capabilities and characteristics
 */
export const CHANNEL_CAPABILITIES = {
  [NotificationChannel.EMAIL]: {
    supportsRichContent: true,
    supportsAttachments: true,
    supportsScheduling: true,
    maxContentLength: 100000,
    deliverySpeed: 'medium',
    reliability: 'high',
    cost: 'low',
  },
  [NotificationChannel.SMS]: {
    supportsRichContent: false,
    supportsAttachments: false,
    supportsScheduling: true,
    maxContentLength: 160,
    deliverySpeed: 'fast',
    reliability: 'high',
    cost: 'medium',
  },
  [NotificationChannel.PUSH]: {
    supportsRichContent: true,
    supportsAttachments: false,
    supportsScheduling: true,
    maxContentLength: 4000,
    deliverySpeed: 'fast',
    reliability: 'medium',
    cost: 'low',
  },
  [NotificationChannel.IN_APP]: {
    supportsRichContent: true,
    supportsAttachments: true,
    supportsScheduling: false,
    maxContentLength: 10000,
    deliverySpeed: 'instant',
    reliability: 'high',
    cost: 'free',
  },
  [NotificationChannel.SLACK]: {
    supportsRichContent: true,
    supportsAttachments: true,
    supportsScheduling: false,
    maxContentLength: 40000,
    deliverySpeed: 'fast',
    reliability: 'high',
    cost: 'free',
  },
  [NotificationChannel.WEBHOOK]: {
    supportsRichContent: true,
    supportsAttachments: true,
    supportsScheduling: true,
    maxContentLength: 1000000,
    deliverySpeed: 'fast',
    reliability: 'medium',
    cost: 'free',
  },
  [NotificationChannel.TEAMS]: {
    supportsRichContent: true,
    supportsAttachments: true,
    supportsScheduling: false,
    maxContentLength: 28000,
    deliverySpeed: 'fast',
    reliability: 'high',
    cost: 'free',
  },
  [NotificationChannel.DISCORD]: {
    supportsRichContent: true,
    supportsAttachments: true,
    supportsScheduling: false,
    maxContentLength: 2000,
    deliverySpeed: 'fast',
    reliability: 'high',
    cost: 'free',
  },
} as const;

/**
 * Get channel capabilities
 */
export function getChannelCapabilities(channel: NotificationChannel) {
  return CHANNEL_CAPABILITIES[channel];
}

/**
 * Check if channel supports rich content
 */
export function supportsRichContent(channel: NotificationChannel): boolean {
  return CHANNEL_CAPABILITIES[channel].supportsRichContent;
}

/**
 * Check if channel supports attachments
 */
export function supportsAttachments(channel: NotificationChannel): boolean {
  return CHANNEL_CAPABILITIES[channel].supportsAttachments;
}

/**
 * Get maximum content length for channel
 */
export function getMaxContentLength(channel: NotificationChannel): number {
  return CHANNEL_CAPABILITIES[channel].maxContentLength;
}

/**
 * Get recommended channels for different notification types
 */
export const RECOMMENDED_CHANNELS = {
  urgent: [NotificationChannel.SMS, NotificationChannel.PUSH, NotificationChannel.EMAIL],
  important: [NotificationChannel.EMAIL, NotificationChannel.IN_APP, NotificationChannel.PUSH],
  informational: [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
  marketing: [NotificationChannel.EMAIL, NotificationChannel.PUSH],
  system: [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
  team_collaboration: [NotificationChannel.SLACK, NotificationChannel.TEAMS, NotificationChannel.DISCORD],
  external_integration: [NotificationChannel.WEBHOOK],
} as const;

/**
 * Get recommended channels for notification type
 */
export function getRecommendedChannels(type: keyof typeof RECOMMENDED_CHANNELS): NotificationChannel[] {
  return RECOMMENDED_CHANNELS[type] || [NotificationChannel.EMAIL, NotificationChannel.IN_APP];
}
