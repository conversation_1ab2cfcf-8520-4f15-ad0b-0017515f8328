import { IEvent } from '@nestjs/cqrs';

/**
 * Lead Converted to Opportunity Event
 * Fired when a lead is converted to an opportunity
 */
export class LeadConvertedToOpportunityEvent implements IEvent {
  constructor(
    public readonly leadId: number,
    public readonly opportunityId: number,
    public readonly originalLeadData: any,
    public readonly opportunityData: any,
    public readonly convertedBy: number,
    public readonly metadata?: any,
    public readonly timestamp: Date = new Date(),
  ) {}
}
