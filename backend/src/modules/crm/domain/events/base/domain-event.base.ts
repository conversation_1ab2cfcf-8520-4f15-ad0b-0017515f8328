import { v4 as uuidv4 } from 'uuid';

/**
 * Base Domain Event
 * Provides rich metadata and standardized structure for all domain events
 */
export abstract class DomainEvent {
  public readonly eventId: string;
  public readonly eventType: string;
  public readonly aggregateId: string;
  public readonly aggregateType: string;
  public readonly aggregateVersion: number;
  public readonly occurredAt: Date;
  public readonly causationId?: string; // ID of the command that caused this event
  public readonly correlationId?: string; // ID to trace related events across boundaries
  public readonly userId?: string; // User who triggered the event
  public readonly tenantId?: string; // Multi-tenant support
  public readonly metadata: EventMetadata;

  constructor(
    aggregateId: string,
    aggregateType: string,
    aggregateVersion: number,
    eventType: string,
    metadata: Partial<EventMetadata> = {},
    causationId?: string,
    correlationId?: string,
    userId?: string,
    tenantId?: string,
  ) {
    this.eventId = uuidv4();
    this.eventType = eventType;
    this.aggregateId = aggregateId;
    this.aggregateType = aggregateType;
    this.aggregateVersion = aggregateVersion;
    this.occurredAt = new Date();
    this.causationId = causationId;
    this.correlationId = correlationId || uuidv4();
    this.userId = userId;
    this.tenantId = tenantId;
    this.metadata = {
      source: 'crm-service',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      ...metadata,
    };
  }

  /**
   * Get event payload (to be implemented by concrete events)
   */
  abstract getPayload(): Record<string, any>;

  /**
   * Serialize event for storage or transmission
   */
  toJSON(): SerializedDomainEvent {
    return {
      eventId: this.eventId,
      eventType: this.eventType,
      aggregateId: this.aggregateId,
      aggregateType: this.aggregateType,
      aggregateVersion: this.aggregateVersion,
      occurredAt: this.occurredAt.toISOString(),
      causationId: this.causationId,
      correlationId: this.correlationId,
      userId: this.userId,
      tenantId: this.tenantId,
      metadata: this.metadata,
      payload: this.getPayload(),
    };
  }

  /**
   * Create event from serialized data
   */
  static fromJSON(data: SerializedDomainEvent): DomainEvent {
    // This would be implemented by concrete event classes
    throw new Error('fromJSON must be implemented by concrete event classes');
  }

  /**
   * Check if this event is related to another event
   */
  isRelatedTo(other: DomainEvent): boolean {
    return this.correlationId === other.correlationId ||
           this.causationId === other.eventId ||
           other.causationId === this.eventId;
  }

  /**
   * Get event stream name for event sourcing
   */
  getStreamName(): string {
    return `${this.aggregateType}-${this.aggregateId}`;
  }

  /**
   * Get event routing key for message brokers
   */
  getRoutingKey(): string {
    return `${this.aggregateType}.${this.eventType}`;
  }

  /**
   * Check if event should be published externally
   */
  shouldPublishExternally(): boolean {
    return this.metadata.publishExternal !== false;
  }

  /**
   * Get event priority for processing
   */
  getPriority(): EventPriority {
    return this.metadata.priority || EventPriority.NORMAL;
  }

  /**
   * Get retry configuration
   */
  getRetryConfig(): RetryConfig {
    return this.metadata.retryConfig || {
      maxRetries: 3,
      backoffStrategy: 'exponential',
      initialDelay: 1000,
    };
  }
}

/**
 * Event metadata interface
 */
export interface EventMetadata {
  source?: string;
  version?: string;
  environment?: string;
  publishExternal?: boolean;
  priority?: EventPriority;
  retryConfig?: RetryConfig;
  tags?: string[];
  businessContext?: Record<string, any>;
  technicalContext?: Record<string, any>;
  [key: string]: any;
}

/**
 * Serialized domain event for storage/transmission
 */
export interface SerializedDomainEvent {
  eventId: string;
  eventType: string;
  aggregateId: string;
  aggregateType: string;
  aggregateVersion: number;
  occurredAt: string;
  causationId?: string;
  correlationId?: string;
  userId?: string;
  tenantId?: string;
  metadata: EventMetadata;
  payload: Record<string, any>;
}

/**
 * Event priority levels
 */
export enum EventPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  CRITICAL = 4,
}

/**
 * Retry configuration
 */
export interface RetryConfig {
  maxRetries: number;
  backoffStrategy: 'linear' | 'exponential' | 'fixed';
  initialDelay: number;
  maxDelay?: number;
  jitter?: boolean;
}

/**
 * Event store interface
 */
export interface EventStore {
  append(streamName: string, events: DomainEvent[], expectedVersion?: number): Promise<void>;
  getEvents(streamName: string, fromVersion?: number): Promise<DomainEvent[]>;
  getAllEvents(fromPosition?: number): Promise<DomainEvent[]>;
  getSnapshot(aggregateId: string, aggregateType: string): Promise<any>;
  saveSnapshot(aggregateId: string, aggregateType: string, snapshot: any, version: number): Promise<void>;
}

/**
 * Event publisher interface
 */
export interface EventPublisher {
  publish(event: DomainEvent): Promise<void>;
  publishBatch(events: DomainEvent[]): Promise<void>;
}

/**
 * Event handler interface
 */
export interface EventHandler<T extends DomainEvent = DomainEvent> {
  handle(event: T): Promise<void>;
  canHandle(event: DomainEvent): boolean;
  getHandledEventTypes(): string[];
}

/**
 * Event bus interface
 */
export interface EventBus {
  publish(event: DomainEvent): Promise<void>;
  publishBatch(events: DomainEvent[]): Promise<void>;
  subscribe<T extends DomainEvent>(eventType: string, handler: EventHandler<T>): void;
  unsubscribe(eventType: string, handler: EventHandler): void;
}

/**
 * Aggregate root interface for event sourcing
 */
export interface AggregateRoot {
  id: string;
  version: number;
  getUncommittedEvents(): DomainEvent[];
  markEventsAsCommitted(): void;
  loadFromHistory(events: DomainEvent[]): void;
}
