import { IEvent } from '@nestjs/cqrs';

/**
 * Lead Updated Event
 * Fired when a lead is updated
 */
export class LeadUpdatedEvent implements IEvent {
  constructor(
    public readonly leadId: number,
    public readonly oldData: any,
    public readonly newData: any,
    public readonly changes: string[],
    public readonly updatedBy: number,
    public readonly metadata?: any,
    public readonly timestamp: Date = new Date(),
  ) {}
}
