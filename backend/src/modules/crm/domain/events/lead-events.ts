import { DomainEvent, EventMetadata, EventPriority } from './base/domain-event.base';
import { Lead } from '../entities/lead.entity';
import { LeadStatus } from '../value-objects/lead-status.vo';
import { LeadPriority } from '../value-objects/lead-priority.vo';

/**
 * Enhanced Lead Created Event
 * Fired when a new lead is created in the system
 */
export class LeadCreatedEvent extends DomainEvent {
  constructor(
    public readonly lead: Lead,
    public readonly source: string,
    public readonly campaign?: string,
    causationId?: string,
    correlationId?: string,
    userId?: string,
    tenantId?: string,
  ) {
    super(
      lead.id.toString(),
      'Lead',
      1, // Initial version
      'LeadCreated',
      {
        priority: EventPriority.HIGH,
        publishExternal: true,
        tags: ['lead', 'creation', 'crm'],
        businessContext: {
          leadSource: source,
          campaign,
          leadType: lead.type?.value,
          leadPriority: lead.priority?.value,
          hasAssignment: !!lead.assignedUserId,
          hasTeam: !!lead.teamId,
          expectedRevenue: lead.expectedRevenue,
        },
        technicalContext: {
          createdVia: 'api',
          validationPassed: true,
          autoAssigned: !lead.assignedUserId,
        },
      },
      causationId,
      correlationId,
      userId,
      tenantId,
    );
  }

  getPayload(): Record<string, any> {
    return {
      leadId: this.lead.id,
      leadData: {
        name: this.lead.name,
        email: this.lead.contactInfo?.email,
        phone: this.lead.contactInfo?.phone,
        company: this.lead.contactInfo?.company,
        source: this.source,
        type: this.lead.type?.value,
        priority: this.lead.priority?.value,
        status: this.lead.status?.value,
        expectedRevenue: this.lead.expectedRevenue,
        probability: this.lead.probability,
        assignedUserId: this.lead.assignedUserId,
        teamId: this.lead.teamId,
        tags: this.lead.tags,
        dateDeadline: this.lead.dateDeadline?.toISOString(),
      },
      campaign: this.campaign,
      source: this.source,
    };
  }
}

/**
 * Lead Status Changed Event
 * Fired when a lead's status changes
 */
export class LeadStatusChangedEvent extends DomainEvent {
  constructor(
    public readonly leadId: number,
    public readonly previousStatus: LeadStatus,
    public readonly newStatus: LeadStatus,
    public readonly reason?: string,
    public readonly changedBy?: string,
    causationId?: string,
    correlationId?: string,
    userId?: string,
    tenantId?: string,
  ) {
    super(
      leadId.toString(),
      'Lead',
      1,
      'LeadStatusChanged',
      {
        priority: EventPriority.NORMAL,
        publishExternal: true,
        tags: ['lead', 'status-change', 'workflow'],
        businessContext: {
          previousStatus: previousStatus.value,
          newStatus: newStatus.value,
          reason,
          changedBy,
          isProgression: newStatus.isProgression(previousStatus),
          isRegression: newStatus.isRegression(previousStatus),
        },
      },
      causationId,
      correlationId,
      userId,
      tenantId,
    );
  }

  getPayload(): Record<string, any> {
    return {
      leadId: this.leadId,
      previousStatus: this.previousStatus.value,
      newStatus: this.newStatus.value,
      reason: this.reason,
      changedBy: this.changedBy,
      statusTransition: {
        from: this.previousStatus.value,
        to: this.newStatus.value,
        isProgression: this.newStatus.isProgression(this.previousStatus),
        isRegression: this.newStatus.isRegression(this.previousStatus),
      },
    };
  }
}

/**
 * Lead Assigned Event
 * Fired when a lead is assigned to a user or team
 */
export class LeadAssignedEvent extends DomainEvent {
  constructor(
    public readonly leadId: number,
    public readonly assignedUserId?: number,
    public readonly assignedTeamId?: number,
    public readonly previousAssignedUserId?: number,
    public readonly previousAssignedTeamId?: number,
    public readonly assignmentReason?: string,
    public readonly autoAssigned: boolean = false,
    causationId?: string,
    correlationId?: string,
    userId?: string,
    tenantId?: string,
  ) {
    super(
      leadId.toString(),
      'Lead',
      1,
      'LeadAssigned',
      {
        priority: EventPriority.HIGH,
        publishExternal: true,
        tags: ['lead', 'assignment', 'workflow'],
        businessContext: {
          assignedUserId,
          assignedTeamId,
          previousAssignedUserId,
          previousAssignedTeamId,
          assignmentReason,
          autoAssigned,
          isReassignment: !!(previousAssignedUserId || previousAssignedTeamId),
        },
      },
      causationId,
      correlationId,
      userId,
      tenantId,
    );
  }

  getPayload(): Record<string, any> {
    return {
      leadId: this.leadId,
      assignment: {
        userId: this.assignedUserId,
        teamId: this.assignedTeamId,
        reason: this.assignmentReason,
        autoAssigned: this.autoAssigned,
      },
      previousAssignment: {
        userId: this.previousAssignedUserId,
        teamId: this.previousAssignedTeamId,
      },
    };
  }
}

/**
 * Lead Priority Changed Event
 * Fired when a lead's priority changes
 */
export class LeadPriorityChangedEvent extends DomainEvent {
  constructor(
    public readonly leadId: number,
    public readonly previousPriority: LeadPriority,
    public readonly newPriority: LeadPriority,
    public readonly reason?: string,
    causationId?: string,
    correlationId?: string,
    userId?: string,
    tenantId?: string,
  ) {
    super(
      leadId.toString(),
      'Lead',
      1,
      'LeadPriorityChanged',
      {
        priority: EventPriority.NORMAL,
        publishExternal: true,
        tags: ['lead', 'priority-change'],
        businessContext: {
          previousPriority: previousPriority.value,
          newPriority: newPriority.value,
          reason,
          isEscalation: newPriority.value > previousPriority.value,
          isDeescalation: newPriority.value < previousPriority.value,
        },
      },
      causationId,
      correlationId,
      userId,
      tenantId,
    );
  }

  getPayload(): Record<string, any> {
    return {
      leadId: this.leadId,
      previousPriority: this.previousPriority.value,
      newPriority: this.newPriority.value,
      reason: this.reason,
      priorityChange: {
        from: this.previousPriority.value,
        to: this.newPriority.value,
        isEscalation: this.newPriority.value > this.previousPriority.value,
        isDeescalation: this.newPriority.value < this.previousPriority.value,
      },
    };
  }
}

/**
 * Lead Converted to Opportunity Event
 * Fired when a lead is successfully converted to an opportunity
 */
export class LeadConvertedToOpportunityEvent extends DomainEvent {
  constructor(
    public readonly leadId: number,
    public readonly opportunityId: number,
    public readonly conversionData: {
      expectedRevenue: number;
      probability: number;
      partnerId?: number;
      stageId?: number;
      dateDeadline?: Date;
      description?: string;
    },
    public readonly convertedBy: string,
    public readonly conversionReason?: string,
    causationId?: string,
    correlationId?: string,
    userId?: string,
    tenantId?: string,
  ) {
    super(
      leadId.toString(),
      'Lead',
      1,
      'LeadConvertedToOpportunity',
      {
        priority: EventPriority.HIGH,
        publishExternal: true,
        tags: ['lead', 'opportunity', 'conversion', 'sales'],
        businessContext: {
          opportunityId,
          expectedRevenue: conversionData.expectedRevenue,
          probability: conversionData.probability,
          convertedBy,
          conversionReason,
          hasPartner: !!conversionData.partnerId,
          hasDeadline: !!conversionData.dateDeadline,
        },
      },
      causationId,
      correlationId,
      userId,
      tenantId,
    );
  }

  getPayload(): Record<string, any> {
    return {
      leadId: this.leadId,
      opportunityId: this.opportunityId,
      conversionData: {
        ...this.conversionData,
        dateDeadline: this.conversionData.dateDeadline?.toISOString(),
      },
      convertedBy: this.convertedBy,
      conversionReason: this.conversionReason,
    };
  }
}

/**
 * Lead Score Changed Event
 * Fired when a lead's score is recalculated
 */
export class LeadScoreChangedEvent extends DomainEvent {
  constructor(
    public readonly leadId: number,
    public readonly previousScore: number,
    public readonly newScore: number,
    public readonly scoringFactors: Record<string, number>,
    public readonly scoringReason: string,
    causationId?: string,
    correlationId?: string,
    userId?: string,
    tenantId?: string,
  ) {
    super(
      leadId.toString(),
      'Lead',
      1,
      'LeadScoreChanged',
      {
        priority: EventPriority.NORMAL,
        publishExternal: false, // Internal scoring event
        tags: ['lead', 'scoring', 'analytics'],
        businessContext: {
          previousScore,
          newScore,
          scoringFactors,
          scoringReason,
          scoreIncrease: newScore > previousScore,
          scoreDecrease: newScore < previousScore,
          scoreDelta: newScore - previousScore,
        },
      },
      causationId,
      correlationId,
      userId,
      tenantId,
    );
  }

  getPayload(): Record<string, any> {
    return {
      leadId: this.leadId,
      previousScore: this.previousScore,
      newScore: this.newScore,
      scoringFactors: this.scoringFactors,
      scoringReason: this.scoringReason,
      scoreChange: {
        delta: this.newScore - this.previousScore,
        percentage: this.previousScore > 0 ? 
          ((this.newScore - this.previousScore) / this.previousScore) * 100 : 0,
      },
    };
  }
}

/**
 * Lead Activity Created Event
 * Fired when a new activity is created for a lead
 */
export class LeadActivityCreatedEvent extends DomainEvent {
  constructor(
    public readonly leadId: number,
    public readonly activityId: number,
    public readonly activityType: string,
    public readonly activityData: Record<string, any>,
    public readonly createdBy: string,
    causationId?: string,
    correlationId?: string,
    userId?: string,
    tenantId?: string,
  ) {
    super(
      leadId.toString(),
      'Lead',
      1,
      'LeadActivityCreated',
      {
        priority: EventPriority.LOW,
        publishExternal: false,
        tags: ['lead', 'activity', 'engagement'],
        businessContext: {
          activityType,
          createdBy,
          hasFollowUp: !!activityData.followUpDate,
        },
      },
      causationId,
      correlationId,
      userId,
      tenantId,
    );
  }

  getPayload(): Record<string, any> {
    return {
      leadId: this.leadId,
      activityId: this.activityId,
      activityType: this.activityType,
      activityData: this.activityData,
      createdBy: this.createdBy,
    };
  }
}

/**
 * Lead Updated Event
 * Fired when a lead's information is updated
 */
export class LeadUpdatedEvent extends DomainEvent {
  constructor(
    public readonly leadId: number,
    public readonly updates: Record<string, any>,
    public readonly previousState: Record<string, any>,
    causationId?: string,
    correlationId?: string,
    userId?: string,
    tenantId?: string,
  ) {
    super(
      leadId.toString(),
      'Lead',
      1,
      'LeadUpdated',
      {
        priority: EventPriority.NORMAL,
        publishExternal: true,
        tags: ['lead', 'update', 'modification'],
        businessContext: {
          updatedFields: Object.keys(updates),
          hasStatusChange: 'status' in updates,
          hasAssignmentChange: 'assignedUserId' in updates || 'teamId' in updates,
          hasRevenueChange: 'expectedRevenue' in updates,
        },
      },
      causationId,
      correlationId,
      userId,
      tenantId,
    );
  }

  getPayload(): Record<string, any> {
    return {
      updates: this.updates,
      previousState: this.previousState,
    };
  }
}

/**
 * Lead Deleted Event
 * Fired when a lead is deleted (soft delete)
 */
export class LeadDeletedEvent extends DomainEvent {
  constructor(
    public readonly leadId: number,
    public readonly reason?: string,
    causationId?: string,
    correlationId?: string,
    userId?: string,
    tenantId?: string,
  ) {
    super(
      leadId.toString(),
      'Lead',
      1,
      'LeadDeleted',
      {
        priority: EventPriority.HIGH,
        publishExternal: true,
        tags: ['lead', 'deletion', 'cleanup'],
        businessContext: {
          reason,
          softDelete: true,
        },
      },
      causationId,
      correlationId,
      userId,
      tenantId,
    );
  }

  getPayload(): Record<string, any> {
    return {
      reason: this.reason,
    };
  }
}
