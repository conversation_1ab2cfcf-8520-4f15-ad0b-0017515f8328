import { IEvent } from '@nestjs/cqrs';

/**
 * Lead Assigned Event
 * Fired when a lead is assigned to a user or team
 */
export class LeadAssignedEvent implements IEvent {
  constructor(
    public readonly leadId: number,
    public readonly assignedBy: number,
    public readonly previousUserId?: number,
    public readonly newUserId?: number,
    public readonly previousTeamId?: number,
    public readonly newTeamId?: number,
    public readonly metadata?: any,
    public readonly timestamp: Date = new Date(),
  ) {}
}
