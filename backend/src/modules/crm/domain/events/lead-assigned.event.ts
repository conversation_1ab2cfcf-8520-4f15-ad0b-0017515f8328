import { IEvent } from '@nestjs/cqrs';

/**
 * Lead Assigned Event
 * Fired when a lead is assigned to a user or team
 */
export class LeadAssignedEvent implements IEvent {
  constructor(
    public readonly leadId: string,
    public readonly assignedBy: string,
    public readonly previousUserId?: string,
    public readonly newUserId?: string,
    public readonly previousTeamId?: number,
    public readonly newTeamId?: number,
    public readonly metadata?: any,
    public readonly timestamp: Date = new Date(),
  ) {}
}
