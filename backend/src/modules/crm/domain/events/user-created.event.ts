import { IEvent } from '@nestjs/cqrs';

/**
 * User Created Event
 * Fired when a new user is created in the system
 */
export class UserCreatedEvent implements IEvent {
  constructor(
    public readonly userId: string,
    public readonly name: string,
    public readonly email: string,
    public readonly login: string,
    public readonly role: string,
    public readonly timestamp: Date = new Date(),
  ) {}
}
