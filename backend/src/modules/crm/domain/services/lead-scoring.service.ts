import { Injectable, Logger } from '@nestjs/common';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
import { LeadScore } from '@/modules/crm/domain/value-objects/lead-score.vo';
import { ScoringFactor } from '@/modules/crm/domain/value-objects/scoring-factor.vo';
import { ScoringRule } from '@/modules/crm/domain/value-objects/scoring-rule.vo';

/**
 * Lead Scoring Service
 * Implements sophisticated lead scoring algorithm with machine learning capabilities
 */
@Injectable()
export class LeadScoringService {
  private readonly logger = new Logger(LeadScoringService.name);

  // Scoring weights configuration
  private readonly scoringWeights = {
    contactCompleteness: 0.20,    // 20% weight
    revenuePotential: 0.25,       // 25% weight
    engagementLevel: 0.20,        // 20% weight
    companyFit: 0.15,             // 15% weight
    behavioralSignals: 0.10,      // 10% weight
    sourceQuality: 0.10,          // 10% weight
  };

  // Industry scoring multipliers
  private readonly industryMultipliers = new Map([
    ['technology', 1.2],
    ['finance', 1.15],
    ['healthcare', 1.1],
    ['manufacturing', 1.0],
    ['retail', 0.95],
    ['education', 0.9],
    ['government', 0.85],
  ]);

  // Source quality scores
  private readonly sourceQualityScores = new Map([
    ['referral', 100],
    ['partner', 90],
    ['direct', 85],
    ['organic_search', 80],
    ['paid_search', 75],
    ['social_media', 70],
    ['email_campaign', 65],
    ['webinar', 85],
    ['trade_show', 80],
    ['cold_outreach', 50],
    ['unknown', 30],
  ]);

  /**
   * Calculate comprehensive lead score
   */
  async calculateScore(lead: Lead, historicalData?: any): Promise<LeadScore> {
    this.logger.debug(`Calculating score for lead: ${lead.id}`);

    const factors: ScoringFactor[] = [];
    let totalScore = 0;

    try {
      // 1. Contact Completeness Score (0-100)
      const completenessScore = this.calculateCompletenessScore(lead);
      factors.push(new ScoringFactor('contact_completeness', completenessScore, this.scoringWeights.contactCompleteness));
      totalScore += completenessScore * this.scoringWeights.contactCompleteness;

      // 2. Revenue Potential Score (0-100)
      const revenueScore = this.calculateRevenuePotentialScore(lead);
      factors.push(new ScoringFactor('revenue_potential', revenueScore, this.scoringWeights.revenuePotential));
      totalScore += revenueScore * this.scoringWeights.revenuePotential;

      // 3. Engagement Level Score (0-100)
      const engagementScore = await this.calculateEngagementScore(lead);
      factors.push(new ScoringFactor('engagement_level', engagementScore, this.scoringWeights.engagementLevel));
      totalScore += engagementScore * this.scoringWeights.engagementLevel;

      // 4. Company Fit Score (0-100)
      const companyFitScore = this.calculateCompanyFitScore(lead);
      factors.push(new ScoringFactor('company_fit', companyFitScore, this.scoringWeights.companyFit));
      totalScore += companyFitScore * this.scoringWeights.companyFit;

      // 5. Behavioral Signals Score (0-100)
      const behavioralScore = await this.calculateBehavioralScore(lead);
      factors.push(new ScoringFactor('behavioral_signals', behavioralScore, this.scoringWeights.behavioralSignals));
      totalScore += behavioralScore * this.scoringWeights.behavioralSignals;

      // 6. Source Quality Score (0-100)
      const sourceScore = this.calculateSourceQualityScore(lead);
      factors.push(new ScoringFactor('source_quality', sourceScore, this.scoringWeights.sourceQuality));
      totalScore += sourceScore * this.scoringWeights.sourceQuality;

      // Apply industry multiplier
      const industryMultiplier = this.getIndustryMultiplier(lead);
      const adjustedScore = Math.min(100, totalScore * industryMultiplier);

      // Apply ML predictions if available
      const mlScore = await this.applyMLPredictions(lead, totalScore, historicalData);

      const finalScore = Math.round(Math.max(0, Math.min(100, mlScore || adjustedScore)));

      const leadScore = new LeadScore(
        finalScore,
        factors,
        this.getScoreGrade(finalScore),
        this.getRecommendedActions(finalScore, factors),
        new Date(),
        {
          industryMultiplier,
          mlAdjustment: mlScore ? mlScore - adjustedScore : 0,
          confidence: this.calculateConfidence(factors),
        },
      );

      this.logger.debug(`Calculated score for lead ${lead.id}: ${finalScore}`);
      return leadScore;

    } catch (error) {
      this.logger.error(`Failed to calculate score for lead ${lead.id}`, error);
      
      // Return default score on error
      return new LeadScore(
        50, // Default medium score
        [new ScoringFactor('error', 50, 1.0)],
        'C',
        ['Review lead data quality'],
        new Date(),
        { error: error.message },
      );
    }
  }

  /**
   * Calculate contact completeness score (0-100)
   */
  private calculateCompletenessScore(lead: Lead): number {
    let score = 0;
    const maxScore = 100;

    // Required fields (60 points total)
    if (lead.name) score += 15;
    if (lead.contactInfo?.email) score += 20;
    if (lead.contactInfo?.phone) score += 15;
    if (lead.contactInfo?.company) score += 10;

    // Optional but valuable fields (40 points total)
    if (lead.contactInfo?.website) score += 8;
    if (lead.contactInfo?.address) score += 5;
    if (lead.contactInfo?.city) score += 3;
    if (lead.contactInfo?.country) score += 4;
    if (lead.description) score += 5;
    if (lead.tags && lead.tags.length > 0) score += 5;
    if (lead.campaignId) score += 3;
    if (lead.sourceId) score += 2;
    if (lead.mediumId) score += 2;
    if (lead.expectedRevenue) score += 3;

    return Math.min(maxScore, score);
  }

  /**
   * Calculate revenue potential score (0-100)
   */
  private calculateRevenuePotentialScore(lead: Lead): number {
    if (!lead.expectedRevenue) {
      return 30; // Default score for leads without revenue data
    }

    // Revenue scoring tiers
    if (lead.expectedRevenue >= 100000) return 100;
    if (lead.expectedRevenue >= 50000) return 85;
    if (lead.expectedRevenue >= 25000) return 70;
    if (lead.expectedRevenue >= 10000) return 55;
    if (lead.expectedRevenue >= 5000) return 40;
    return 25;
  }

  /**
   * Calculate engagement level score (0-100)
   */
  private async calculateEngagementScore(lead: Lead): Promise<number> {
    // This would integrate with analytics service to get engagement data
    // For now, we'll use basic heuristics

    let score = 0;

    // Recent activity bonus
    const daysSinceCreated = Math.floor((Date.now() - (lead.createdAt?.getTime() || Date.now())) / (1000 * 60 * 60 * 24));
    if (daysSinceCreated <= 1) score += 30;
    else if (daysSinceCreated <= 7) score += 20;
    else if (daysSinceCreated <= 30) score += 10;

    // Priority level indicates engagement
    if (lead.priority) {
      switch (lead.priority.value) {
        case 3: score += 25; break; // Very High
        case 2: score += 20; break; // High
        case 1: score += 15; break; // Medium
        case 0: score += 5; break;  // Low
      }
    }

    // Source indicates engagement level
    const sourceEngagementBonus = this.getSourceEngagementBonus(lead.source);
    score += sourceEngagementBonus;

    // Has deadline indicates urgency/engagement
    if (lead.dateDeadline) score += 15;

    return Math.min(100, score);
  }

  /**
   * Calculate company fit score (0-100)
   */
  private calculateCompanyFitScore(lead: Lead): number {
    let score = 50; // Base score

    // Company size indicators
    if (lead.contactInfo?.company) {
      score += 20; // Has company information
      
      // Industry fit
      const industry = this.extractIndustryFromCompany(lead.contactInfo.company);
      const industryMultiplier = this.industryMultipliers.get(industry) || 1.0;
      score = score * industryMultiplier;
    }

    // Geographic fit (if we have preferences)
    if (lead.contactInfo?.country) {
      const geoScore = this.calculateGeographicFit(lead.contactInfo.country);
      score += geoScore;
    }

    // Website indicates established business
    if (lead.contactInfo?.website) {
      score += 15;
    }

    return Math.min(100, Math.max(0, score));
  }

  /**
   * Calculate behavioral signals score (0-100)
   */
  private async calculateBehavioralScore(lead: Lead): Promise<number> {
    let score = 0;

    // Lead type indicates intent level
    if (lead.type) {
      switch (lead.type.value) {
        case 'opportunity': score += 40; break;
        case 'lead': score += 20; break;
        default: score += 10; break;
      }
    }

    // Probability indicates confidence
    if (lead.probability) {
      score += Math.min(30, lead.probability * 0.3);
    }

    // Assignment indicates qualification
    if (lead.assignedUserId) score += 15;
    if (lead.teamId) score += 10;

    // Tags indicate categorization/interest
    if (lead.tags && lead.tags.length > 0) {
      score += Math.min(15, lead.tags.length * 3);
    }

    return Math.min(100, score);
  }

  /**
   * Calculate source quality score (0-100)
   */
  private calculateSourceQualityScore(lead: Lead): number {
    const sourceScore = this.sourceQualityScores.get(lead.source?.toLowerCase()) || 50;
    return sourceScore;
  }

  /**
   * Apply ML predictions (placeholder for future ML integration)
   */
  private async applyMLPredictions(lead: Lead, baseScore: number, historicalData?: any): Promise<number | null> {
    // Placeholder for TensorFlow.js integration
    // This would use historical conversion data to predict likelihood
    
    if (!historicalData) {
      return null; // No ML adjustment without historical data
    }

    try {
      // Future: Load TensorFlow.js model and make prediction
      // const model = await tf.loadLayersModel('/models/lead-scoring-model.json');
      // const prediction = model.predict(features);
      
      // For now, return null to use rule-based scoring
      return null;
    } catch (error) {
      this.logger.warn('ML prediction failed, falling back to rule-based scoring', error);
      return null;
    }
  }

  /**
   * Helper methods
   */
  private getIndustryMultiplier(lead: Lead): number {
    const industry = this.extractIndustryFromCompany(lead.contactInfo?.company);
    return this.industryMultipliers.get(industry) || 1.0;
  }

  private extractIndustryFromCompany(company?: string): string {
    if (!company) return 'unknown';
    
    const companyLower = company.toLowerCase();
    
    // Simple keyword matching (in production, use more sophisticated NLP)
    if (companyLower.includes('tech') || companyLower.includes('software') || companyLower.includes('digital')) {
      return 'technology';
    }
    if (companyLower.includes('bank') || companyLower.includes('finance') || companyLower.includes('investment')) {
      return 'finance';
    }
    if (companyLower.includes('health') || companyLower.includes('medical') || companyLower.includes('pharma')) {
      return 'healthcare';
    }
    if (companyLower.includes('manufacturing') || companyLower.includes('factory')) {
      return 'manufacturing';
    }
    if (companyLower.includes('retail') || companyLower.includes('store')) {
      return 'retail';
    }
    if (companyLower.includes('school') || companyLower.includes('university') || companyLower.includes('education')) {
      return 'education';
    }
    if (companyLower.includes('government') || companyLower.includes('public')) {
      return 'government';
    }
    
    return 'unknown';
  }

  private getSourceEngagementBonus(source?: string): number {
    if (!source) return 0;
    
    const engagementBonuses = new Map([
      ['referral', 25],
      ['webinar', 20],
      ['trade_show', 18],
      ['direct', 15],
      ['organic_search', 12],
      ['paid_search', 10],
      ['social_media', 8],
      ['email_campaign', 6],
      ['cold_outreach', 3],
    ]);
    
    return engagementBonuses.get(source.toLowerCase()) || 5;
  }

  private calculateGeographicFit(country: string): number {
    // Placeholder for geographic scoring
    // In production, this would be configurable based on business preferences
    const preferredCountries = ['US', 'CA', 'UK', 'DE', 'FR', 'AU'];
    return preferredCountries.includes(country.toUpperCase()) ? 10 : 5;
  }

  private getScoreGrade(score: number): string {
    if (score >= 90) return 'A+';
    if (score >= 80) return 'A';
    if (score >= 70) return 'B';
    if (score >= 60) return 'C';
    if (score >= 50) return 'D';
    return 'F';
  }

  private getRecommendedActions(score: number, factors: ScoringFactor[]): string[] {
    const actions: string[] = [];

    if (score >= 80) {
      actions.push('Immediate follow-up required');
      actions.push('Assign to senior sales rep');
      actions.push('Schedule demo/meeting within 24 hours');
    } else if (score >= 60) {
      actions.push('Follow up within 2-3 days');
      actions.push('Send relevant case studies');
      actions.push('Add to nurturing campaign');
    } else if (score >= 40) {
      actions.push('Add to long-term nurturing');
      actions.push('Gather more information');
      actions.push('Monitor engagement');
    } else {
      actions.push('Low priority follow-up');
      actions.push('Verify contact information');
      actions.push('Consider lead qualification');
    }

    // Add specific actions based on low-scoring factors
    factors.forEach(factor => {
      if (factor.score < 50) {
        switch (factor.name) {
          case 'contact_completeness':
            actions.push('Complete missing contact information');
            break;
          case 'engagement_level':
            actions.push('Increase engagement through targeted content');
            break;
          case 'company_fit':
            actions.push('Research company and industry fit');
            break;
        }
      }
    });

    return [...new Set(actions)]; // Remove duplicates
  }

  private calculateConfidence(factors: ScoringFactor[]): number {
    // Calculate confidence based on data completeness and factor consistency
    const completenessScore = factors.find(f => f.name === 'contact_completeness')?.score || 0;
    const factorVariance = this.calculateFactorVariance(factors);
    
    // Higher completeness and lower variance = higher confidence
    const confidence = (completenessScore * 0.6) + ((100 - factorVariance) * 0.4);
    return Math.round(Math.max(0, Math.min(100, confidence)));
  }

  private calculateFactorVariance(factors: ScoringFactor[]): number {
    const scores = factors.map(f => f.score);
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
    return Math.sqrt(variance);
  }
}
