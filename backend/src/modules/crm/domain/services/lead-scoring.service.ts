import { Injectable, Logger } from '@nestjs/common';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
import { LeadScore } from '@/modules/crm/domain/value-objects/lead-score.vo';
import { ScoringFactor } from '@/modules/crm/domain/value-objects/scoring-factor.vo';
import { ScoringRule } from '@/modules/crm/domain/value-objects/scoring-rule.vo';

/**
 * Lead Scoring Service
 * Implements sophisticated lead scoring algorithm
 */
@Injectable()
export class LeadScoringService {
  private readonly logger = new Logger(LeadScoringService.name);

  // Scoring weights configuration
  private readonly scoringWeights = {
    contactCompleteness: 0.20,    // 20% weight
    revenuePotential: 0.25,       // 25% weight
    engagementLevel: 0.20,        // 20% weight
    companyFit: 0.15,             // 15% weight
    behavioralSignals: 0.10,      // 10% weight
    sourceQuality: 0.10,          // 10% weight
  };

  // Industry scoring multipliers
  private readonly industryMultipliers = new Map([
    ['technology', 1.2],
    ['finance', 1.15],
    ['healthcare', 1.1],
    ['manufacturing', 1.0],
    ['retail', 0.95],
    ['education', 0.9],
    ['government', 0.85],
  ]);

  // Source quality scores
  private readonly sourceQualityScores = new Map([
    ['referral', 100],
    ['partner', 90],
    ['direct', 85],
    ['organic_search', 80],
    ['paid_search', 75],
    ['social_media', 70],
    ['email_campaign', 65],
    ['webinar', 85],
    ['trade_show', 80],
    ['cold_outreach', 50],
    ['unknown', 30],
  ]);

  /**
   * Calculate comprehensive lead score
   */
  async calculateScore(lead: Lead, historicalData?: any): Promise<LeadScore> {
    this.logger.debug(`Calculating score for lead: ${lead.id}`);

    // Simplified scoring - just basic calculation
    let totalScore = 50; // Base score

    // Contact completeness (0-25 points)
    if (lead.contactInfo.email) totalScore += 10;
    if (lead.contactInfo.phone) totalScore += 10;
    if (lead.contactInfo.company) totalScore += 5;

    // Source quality bonus
    const sourceScore = this.sourceQualityScores.get(lead.source?.toLowerCase()) || 50;
    totalScore += (sourceScore - 50) * 0.3; // Scale down impact

    // Industry multiplier
    const industryMultiplier = this.getIndustryMultiplier(lead);
    totalScore *= industryMultiplier;

    const finalScore = Math.round(Math.max(0, Math.min(100, totalScore)));

    // Create simplified factors
    const factors = [
      new ScoringFactor('contact_completeness', 70, 0.4, 'Contact information quality'),
      new ScoringFactor('source_quality', sourceScore, 0.3, 'Lead source quality'),
      new ScoringFactor('industry_fit', industryMultiplier * 50, 0.3, 'Industry alignment'),
    ];

    return new LeadScore(
      finalScore,
      factors,
      this.getScoreGrade(finalScore),
      this.getRecommendedActions(finalScore, factors),
      new Date(),
      {
        industryMultiplier,
        confidence: 75,
      },
    );
  }

  /**
   * Helper methods
   */
  private getIndustryMultiplier(lead: Lead): number {
    const industry = this.extractIndustryFromCompany(lead.contactInfo?.company);
    return this.industryMultipliers.get(industry) || 1.0;
  }

  private extractIndustryFromCompany(company?: string): string {
    if (!company) return 'unknown';
    
    const companyLower = company.toLowerCase();
    
    // Simple industry detection based on keywords
    if (companyLower.includes('tech') || companyLower.includes('software') || companyLower.includes('digital')) {
      return 'technology';
    }
    if (companyLower.includes('bank') || companyLower.includes('finance') || companyLower.includes('investment')) {
      return 'finance';
    }
    if (companyLower.includes('health') || companyLower.includes('medical') || companyLower.includes('pharma')) {
      return 'healthcare';
    }
    if (companyLower.includes('retail') || companyLower.includes('store') || companyLower.includes('shop')) {
      return 'retail';
    }
    if (companyLower.includes('manufact') || companyLower.includes('industrial')) {
      return 'manufacturing';
    }
    if (companyLower.includes('real estate') || companyLower.includes('property')) {
      return 'real_estate';
    }
    if (companyLower.includes('education') || companyLower.includes('school') || companyLower.includes('university')) {
      return 'education';
    }
    if (companyLower.includes('government') || companyLower.includes('public')) {
      return 'government';
    }
    
    return 'other';
  }

  private getSourceEngagementBonus(source?: string): number {
    if (!source) return 0;
    
    // Engagement bonuses based on source quality
    const engagementBonuses = new Map([
      ['referral', 15],
      ['direct', 12],
      ['linkedin', 10],
      ['email', 8],
      ['website', 5],
      ['social', 3],
      ['advertisement', 2],
    ]);
    
    return engagementBonuses.get(source.toLowerCase()) || 5;
  }

  private calculateGeographicFit(country: string): number {
    // Simplified geographic scoring
    const preferredCountries = ['US', 'CA', 'UK', 'AU', 'DE', 'FR'];
    return preferredCountries.includes(country.toUpperCase()) ? 10 : 5;
  }

  private getScoreGrade(score: number): string {
    if (score >= 90) return 'A+';
    if (score >= 80) return 'A';
    if (score >= 70) return 'B';
    if (score >= 60) return 'C';
    if (score >= 50) return 'D';
    return 'F';
  }

  private getRecommendedActions(score: number, factors: ScoringFactor[]): string[] {
    const actions: string[] = [];
    
    if (score >= 80) {
      actions.push('High priority - contact immediately');
      actions.push('Assign to senior sales rep');
    } else if (score >= 60) {
      actions.push('Medium priority - follow up within 24 hours');
      actions.push('Send personalized content');
    } else if (score >= 40) {
      actions.push('Low priority - nurture with automated campaigns');
      actions.push('Gather more information');
    } else {
      actions.push('Very low priority - minimal follow-up');
      actions.push('Consider disqualifying if no engagement');
    }
    
    // Add specific recommendations based on factors
    factors.forEach(factor => {
      if (factor.score < 30) {
        switch (factor.name) {
          case 'contact_completeness':
            actions.push('Gather missing contact information');
            break;
          case 'company_fit':
            actions.push('Research company better');
            break;
          case 'engagement_level':
            actions.push('Increase engagement through multiple channels');
            break;
        }
      }
    });
    
    return [...new Set(actions)]; // Remove duplicates
  }

  private calculateConfidence(factors: ScoringFactor[]): number {
    // Confidence based on data completeness and factor consistency
    const completenessScore = factors.find(f => f.name === 'contact_completeness')?.score || 0;
    const factorVariance = this.calculateFactorVariance(factors);
    
    // Higher completeness and lower variance = higher confidence
    const confidence = (completenessScore * 0.6) + ((100 - factorVariance) * 0.4);
    
    return Math.round(Math.max(0, Math.min(100, confidence)));
  }

  private calculateFactorVariance(factors: ScoringFactor[]): number {
    const scores = factors.map(f => f.score);
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
    return Math.sqrt(variance);
  }
}
