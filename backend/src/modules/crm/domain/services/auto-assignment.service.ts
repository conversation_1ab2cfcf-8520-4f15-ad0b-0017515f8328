import { Injectable, Logger, Inject } from '@nestjs/common';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
import { AssignmentRule } from '@/modules/crm/domain/value-objects/assignment-rule.vo';
import { AssignmentResult } from '@/modules/crm/domain/value-objects/assignment-result.vo';
import { WorkloadBalance } from '@/modules/crm/domain/value-objects/workload-balance.vo';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { ITeamRepository } from '@/modules/crm/domain/repositories/team.repository';
import { IUserRepository } from '@/modules/crm/domain/repositories/user.repository';
import {
  LEAD_REPOSITORY_TOKEN,
  TEAM_REPOSITORY_TOKEN,
  USER_REPOSITORY_TOKEN
} from '@/modules/crm/domain/repositories/injection-tokens';

/**
 * Auto-Assignment Service
 * Intelligent auto-assignment system with workload balancing and rule-based routing
 */
@Injectable()
export class AutoAssignmentService {
  private readonly logger = new Logger(AutoAssignmentService.name);

  // Assignment rules registry
  private readonly assignmentRules = new Map<string, AssignmentRule[]>();

  // Workload cache
  private readonly workloadCache = new Map<string, WorkloadBalance>();

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN) private readonly leadRepository: ILeadRepository,
    @Inject(TEAM_REPOSITORY_TOKEN) private readonly teamRepository: ITeamRepository,
    @Inject(USER_REPOSITORY_TOKEN) private readonly userRepository: IUserRepository,
  ) {
    this.initializeDefaultRules();
  }

  /**
   * Auto-assign lead to best available user
   */
  async autoAssignLead(lead: Lead, teamId?: number): Promise<AssignmentResult> {
    this.logger.debug(`Auto-assigning lead: ${lead.id} to team: ${teamId}`);

    try {
      // Get applicable assignment rules
      const rules = await this.getApplicableRules(lead, teamId);
      
      // Get available users
      const availableUsers = await this.getAvailableUsers(teamId);
      
      if (availableUsers.length === 0) {
        return new AssignmentResult(
          null,
          null,
          false,
          'No available users found',
          [],
          new Date(),
        );
      }

      // Apply assignment rules to filter and score users
      const scoredUsers = await this.scoreUsers(lead, availableUsers, rules);
      
      // Apply workload balancing
      const balancedUsers = await this.applyWorkloadBalancing(scoredUsers, teamId);
      
      // Select best user
      const selectedUser = this.selectBestUser(balancedUsers);
      
      if (!selectedUser) {
        return new AssignmentResult(
          null,
          null,
          false,
          'No suitable user found after applying rules',
          [],
          new Date(),
        );
      }

      // Update workload tracking
      await this.updateWorkloadTracking(selectedUser.userId, lead);

      this.logger.debug(`Lead ${lead.id} auto-assigned to user: ${selectedUser.userId}`);

      return new AssignmentResult(
        selectedUser.userId,
        teamId,
        true,
        'Successfully auto-assigned',
        this.generateAssignmentReasons(selectedUser, rules),
        new Date(),
        {
          score: selectedUser.score,
          workloadBefore: selectedUser.workloadBefore,
          workloadAfter: selectedUser.workloadAfter,
          rulesApplied: rules.map(r => r.name),
        },
      );

    } catch (error) {
      this.logger.error(`Failed to auto-assign lead: ${lead.id}`, error);
      
      return new AssignmentResult(
        null,
        null,
        false,
        `Assignment failed: ${error.message}`,
        [],
        new Date(),
      );
    }
  }

  /**
   * Bulk auto-assign multiple leads
   */
  async bulkAutoAssignLeads(leads: Lead[], teamId?: number): Promise<AssignmentResult[]> {
    this.logger.debug(`Bulk auto-assigning ${leads.length} leads to team: ${teamId}`);

    const results: AssignmentResult[] = [];

    // Process in batches to avoid overwhelming the system
    const batchSize = 10;
    for (let i = 0; i < leads.length; i += batchSize) {
      const batch = leads.slice(i, i + batchSize);
      
      const batchPromises = batch.map(lead => this.autoAssignLead(lead, teamId));
      const batchResults = await Promise.allSettled(batchPromises);

      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          this.logger.error(`Bulk assignment failed for lead ${batch[index].id}`, result.reason);
          results.push(new AssignmentResult(
            null,
            null,
            false,
            `Bulk assignment failed: ${result.reason}`,
            [],
            new Date(),
          ));
        }
      });

      // Small delay between batches
      if (i + batchSize < leads.length) {
        await this.delay(100);
      }
    }

    this.logger.debug(`Bulk auto-assignment completed: ${results.filter(r => r.success).length}/${leads.length} successful`);
    return results;
  }

  /**
   * Reassign leads for workload balancing
   */
  async rebalanceWorkload(teamId: number): Promise<RebalanceResult> {
    this.logger.debug(`Rebalancing workload for team: ${teamId}`);

    try {
      // Get current workload distribution
      const workloadDistribution = await this.getWorkloadDistribution(teamId);
      
      // Identify imbalanced assignments
      const imbalancedLeads = await this.identifyImbalancedLeads(workloadDistribution);
      
      if (imbalancedLeads.length === 0) {
        return {
          success: true,
          message: 'Workload is already balanced',
          reassignments: [],
          workloadBefore: workloadDistribution,
          workloadAfter: workloadDistribution,
        };
      }

      // Perform reassignments
      const reassignments: Reassignment[] = [];
      
      for (const lead of imbalancedLeads) {
        const assignmentResult = await this.autoAssignLead(lead, teamId);
        
        if (assignmentResult.success && assignmentResult.assignedUserId !== lead.assignedUserId) {
          reassignments.push({
            leadId: lead.id,
            fromUserId: lead.assignedUserId,
            toUserId: assignmentResult.assignedUserId,
            reason: 'Workload rebalancing',
          });
        }
      }

      // Get updated workload distribution
      const newWorkloadDistribution = await this.getWorkloadDistribution(teamId);

      this.logger.debug(`Workload rebalancing completed: ${reassignments.length} reassignments`);

      return {
        success: true,
        message: `Successfully rebalanced workload with ${reassignments.length} reassignments`,
        reassignments,
        workloadBefore: workloadDistribution,
        workloadAfter: newWorkloadDistribution,
      };

    } catch (error) {
      this.logger.error(`Failed to rebalance workload for team: ${teamId}`, error);
      
      return {
        success: false,
        message: `Rebalancing failed: ${error.message}`,
        reassignments: [],
        workloadBefore: {},
        workloadAfter: {},
      };
    }
  }

  /**
   * Add assignment rule
   */
  addAssignmentRule(teamId: string, rule: AssignmentRule): void {
    if (!this.assignmentRules.has(teamId)) {
      this.assignmentRules.set(teamId, []);
    }
    
    const rules = this.assignmentRules.get(teamId)!;
    rules.push(rule);
    
    // Sort by priority (higher priority first)
    rules.sort((a, b) => b.priority - a.priority);
    
    this.logger.debug(`Added assignment rule: ${rule.name} for team: ${teamId}`);
  }

  /**
   * Remove assignment rule
   */
  removeAssignmentRule(teamId: string, ruleId: string): boolean {
    const rules = this.assignmentRules.get(teamId);
    if (!rules) return false;
    
    const index = rules.findIndex(rule => rule.id === ruleId);
    if (index === -1) return false;
    
    rules.splice(index, 1);
    this.logger.debug(`Removed assignment rule: ${ruleId} from team: ${teamId}`);
    return true;
  }

  /**
   * Get assignment statistics
   */
  async getAssignmentStats(teamId?: number, dateFrom?: Date, dateTo?: Date): Promise<AssignmentStats> {
    // This would query the database for assignment statistics
    return {
      totalAssignments: 0,
      autoAssignments: 0,
      manualAssignments: 0,
      autoAssignmentRate: 0,
      averageAssignmentTime: 0,
      workloadBalance: 0,
      userStats: {},
    };
  }

  /**
   * Private helper methods
   */
  private async getApplicableRules(lead: Lead, teamId?: number): Promise<AssignmentRule[]> {
    const teamKey = teamId?.toString() || 'default';
    const allRules = this.assignmentRules.get(teamKey) || [];
    
    return allRules.filter(rule => rule.appliesTo(lead));
  }

  private async getAvailableUsers(teamId?: number): Promise<User[]> {
    if (teamId) {
      return this.userRepository.findByTeamId(teamId, { isActive: true, isAvailable: true });
    } else {
      return this.userRepository.findAll({ isActive: true, isAvailable: true });
    }
  }

  private async scoreUsers(lead: Lead, users: User[], rules: AssignmentRule[]): Promise<ScoredUser[]> {
    const scoredUsers: ScoredUser[] = [];

    for (const user of users) {
      let score = 50; // Base score
      const appliedRules: string[] = [];

      // Apply each rule
      for (const rule of rules) {
        if (rule.appliesTo(lead) && rule.appliesToUser(user)) {
          score += rule.getScoreAdjustment(lead, user);
          appliedRules.push(rule.name);
        }
      }

      // Get current workload
      const workload = await this.getUserWorkload(user.id);

      scoredUsers.push({
        userId: user.id,
        user,
        score: Math.max(0, Math.min(100, score)),
        workloadBefore: workload,
        workloadAfter: workload, // Will be updated later
        appliedRules,
      });
    }

    return scoredUsers.sort((a, b) => b.score - a.score);
  }

  private async applyWorkloadBalancing(scoredUsers: ScoredUser[], teamId?: number): Promise<ScoredUser[]> {
    // Get team workload statistics
    const teamWorkload = await this.getTeamWorkloadStats(teamId);
    
    // Adjust scores based on workload
    return scoredUsers.map(user => {
      const workloadRatio = user.workloadBefore.totalLeads / (teamWorkload.averageLeads || 1);
      
      // Penalty for overloaded users
      let workloadAdjustment = 0;
      if (workloadRatio > 1.5) {
        workloadAdjustment = -20; // Heavy penalty
      } else if (workloadRatio > 1.2) {
        workloadAdjustment = -10; // Moderate penalty
      } else if (workloadRatio < 0.8) {
        workloadAdjustment = 10; // Bonus for underloaded users
      }

      return {
        ...user,
        score: Math.max(0, Math.min(100, user.score + workloadAdjustment)),
        workloadAfter: {
          ...user.workloadBefore,
          totalLeads: user.workloadBefore.totalLeads + 1,
        },
      };
    }).sort((a, b) => b.score - a.score);
  }

  private selectBestUser(scoredUsers: ScoredUser[]): ScoredUser | null {
    if (scoredUsers.length === 0) return null;
    
    // Filter users with acceptable scores
    const acceptableUsers = scoredUsers.filter(user => user.score >= 30);
    
    if (acceptableUsers.length === 0) return null;
    
    // Return the highest scoring user
    return acceptableUsers[0];
  }

  private async updateWorkloadTracking(userId: string, lead: Lead): Promise<void> {
    const currentWorkload = await this.getUserWorkload(userId);
    
    const updatedWorkload = new WorkloadBalance(
      userId,
      currentWorkload.totalLeads + 1,
      currentWorkload.activeLeads + 1,
      currentWorkload.totalValue + (lead.expectedRevenue || 0),
      currentWorkload.averageLeadAge,
      new Date(),
    );

    this.workloadCache.set(userId, updatedWorkload);
  }

  private generateAssignmentReasons(user: ScoredUser, rules: AssignmentRule[]): string[] {
    const reasons: string[] = [];
    
    reasons.push(`User scored ${user.score}/100 points`);
    
    if (user.appliedRules.length > 0) {
      reasons.push(`Applied rules: ${user.appliedRules.join(', ')}`);
    }
    
    if (user.workloadBefore.totalLeads < 10) {
      reasons.push('User has low workload');
    }
    
    return reasons;
  }

  private async getUserWorkload(userId: string): Promise<WorkloadBalance> {
    // Check cache first
    let workload = this.workloadCache.get(userId);
    
    if (!workload) {
      // Calculate from database
      const userLeads = await this.leadRepository.findByAssignedUserId(userId);
      const totalLeads = userLeads.length;
      const activeLeads = userLeads.filter(lead => !lead.isConverted() && !lead.isLost()).length;
      const totalValue = userLeads.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
      const averageAge = this.calculateAverageLeadAge(userLeads);
      
      workload = new WorkloadBalance(userId, totalLeads, activeLeads, totalValue, averageAge, new Date());
      this.workloadCache.set(userId, workload);
    }
    
    return workload;
  }

  private async getWorkloadDistribution(teamId: number): Promise<Record<string, WorkloadBalance>> {
    const teamUsers = await this.userRepository.findByTeamId(teamId);
    const distribution: Record<string, WorkloadBalance> = {};
    
    for (const user of teamUsers) {
      distribution[user.id] = await this.getUserWorkload(user.id);
    }
    
    return distribution;
  }

  private async identifyImbalancedLeads(workloadDistribution: Record<string, WorkloadBalance>): Promise<Lead[]> {
    // Identify users with excessive workload
    const workloads = Object.values(workloadDistribution);
    const averageWorkload = workloads.reduce((sum, w) => sum + w.totalLeads, 0) / workloads.length;
    
    const overloadedUsers = Object.entries(workloadDistribution)
      .filter(([_, workload]) => workload.totalLeads > averageWorkload * 1.5)
      .map(([userId, _]) => userId);
    
    // Get leads from overloaded users
    const imbalancedLeads: Lead[] = [];
    
    for (const userId of overloadedUsers) {
      const userLeads = await this.leadRepository.findByAssignedUserId(userId);
      // Take the newest leads for reassignment
      const leadsToReassign = userLeads
        .filter(lead => !lead.isConverted() && !lead.isLost())
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, Math.floor((userLeads.length - averageWorkload) / 2));
      
      imbalancedLeads.push(...leadsToReassign);
    }
    
    return imbalancedLeads;
  }

  private async getTeamWorkloadStats(teamId?: number): Promise<TeamWorkloadStats> {
    // This would calculate team-wide workload statistics
    return {
      averageLeads: 10,
      totalLeads: 100,
      activeUsers: 10,
    };
  }

  private calculateAverageLeadAge(leads: Lead[]): number {
    if (leads.length === 0) return 0;
    
    const totalAge = leads.reduce((sum, lead) => {
      const ageInDays = Math.floor((Date.now() - lead.createdAt.getTime()) / (1000 * 60 * 60 * 24));
      return sum + ageInDays;
    }, 0);
    
    return totalAge / leads.length;
  }

  private initializeDefaultRules(): void {
    // Initialize default assignment rules
    const defaultRules = [
      new AssignmentRule(
        'high-value-leads',
        'High Value Leads',
        'Assign high-value leads to senior reps',
        (lead) => (lead.expectedRevenue || 0) > 50000,
        (user) => user.seniority === 'senior',
        20,
        1,
        true,
      ),
      new AssignmentRule(
        'geographic-routing',
        'Geographic Routing',
        'Assign leads based on geographic location',
        (lead) => !!lead.contactInfo?.country,
        (user) => !!user.territories,
        15,
        2,
        true,
      ),
    ];

    defaultRules.forEach(rule => this.addAssignmentRule('default', rule));
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Supporting interfaces
 */
interface User {
  id: string;
  name: string;
  email: string;
  teamId?: number;
  seniority?: string;
  territories?: string[];
  isActive: boolean;
  isAvailable: boolean;
}

interface ScoredUser {
  userId: string;
  user: User;
  score: number;
  workloadBefore: WorkloadBalance;
  workloadAfter: WorkloadBalance;
  appliedRules: string[];
}

interface TeamWorkloadStats {
  averageLeads: number;
  totalLeads: number;
  activeUsers: number;
}

interface Reassignment {
  leadId: number;
  fromUserId?: number;
  toUserId?: number;
  reason: string;
}

interface RebalanceResult {
  success: boolean;
  message: string;
  reassignments: Reassignment[];
  workloadBefore: Record<string, WorkloadBalance>;
  workloadAfter: Record<string, WorkloadBalance>;
}

interface AssignmentStats {
  totalAssignments: number;
  autoAssignments: number;
  manualAssignments: number;
  autoAssignmentRate: number;
  averageAssignmentTime: number;
  workloadBalance: number;
  userStats: Record<string, any>;
}
