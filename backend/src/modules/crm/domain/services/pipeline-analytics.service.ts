import { Injectable, Logger, Inject } from '@nestjs/common';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { IStageRepository } from '@/modules/crm/domain/repositories/stage.repository';
import { ITeamRepository } from '@/modules/crm/domain/repositories/team.repository';
import { PipelineAnalytics } from '@/modules/crm/domain/value-objects/pipeline-analytics.vo';
import { PipelineMetrics } from '@/modules/crm/domain/value-objects/pipeline-metrics.vo';
import { ConversionFunnel } from '@/modules/crm/domain/value-objects/conversion-funnel.vo';
import { PipelineForecast } from '@/modules/crm/domain/value-objects/pipeline-forecast.vo';
import { BottleneckAnalysis } from '@/modules/crm/domain/value-objects/bottleneck-analysis.vo';
import {
  LEAD_REPOSITORY_TOKEN,
  STAGE_REPOSITORY_TOKEN,
  TEAM_REPOSITORY_TOKEN
} from '@/modules/crm/domain/repositories/injection-tokens';

/**
 * Pipeline Analytics Service
 * Provides comprehensive analytics engine with forecasting, bottleneck detection, and performance metrics
 */
@Injectable()
export class PipelineAnalyticsService {
  private readonly logger = new Logger(PipelineAnalyticsService.name);

  constructor(
    @Inject(LEAD_REPOSITORY_TOKEN) private readonly leadRepository: ILeadRepository,
    @Inject(STAGE_REPOSITORY_TOKEN) private readonly stageRepository: IStageRepository,
    @Inject(TEAM_REPOSITORY_TOKEN) private readonly teamRepository: ITeamRepository,
  ) {}

  /**
   * Generate comprehensive pipeline analytics
   */
  async generatePipelineAnalytics(filters: AnalyticsFilters): Promise<PipelineAnalytics> {
    this.logger.debug('Generating pipeline analytics', filters);

    try {
      // Execute analytics calculations in parallel for better performance
      const [
        metrics,
        conversionFunnel,
        forecast,
        bottlenecks,
        trends,
        comparisons,
      ] = await Promise.all([
        this.calculatePipelineMetrics(filters),
        this.analyzeConversionFunnel(filters),
        this.generateForecast(filters),
        this.detectBottlenecks(filters),
        this.analyzeTrends(filters),
        this.generateComparisons(filters),
      ]);

      const analytics = new PipelineAnalytics(
        metrics,
        conversionFunnel,
        forecast,
        bottlenecks,
        trends,
        comparisons,
        new Date(),
        {
          filters,
          generatedBy: 'pipeline-analytics-service',
          version: '1.0.0',
        },
      );

      this.logger.debug('Pipeline analytics generated successfully');
      return analytics;

    } catch (error) {
      this.logger.error('Failed to generate pipeline analytics', error);
      throw error;
    }
  }

  /**
   * Calculate comprehensive pipeline metrics
   */
  async calculatePipelineMetrics(filters: AnalyticsFilters): Promise<PipelineMetrics> {
    const leads = await this.getFilteredLeads(filters);
    const stages = await this.stageRepository.findAll();

    // Basic metrics
    const totalLeads = leads.length;
    const qualifiedLeads = leads.filter(lead => lead.isQualified()).length;
    const convertedLeads = leads.filter(lead => lead.isConverted()).length;
    const totalValue = leads.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
    const weightedValue = leads.reduce((sum, lead) => sum + lead.getWeightedRevenue(), 0);

    // Conversion rates
    const conversionRate = totalLeads > 0 ? (convertedLeads / totalLeads) * 100 : 0;
    const qualificationRate = totalLeads > 0 ? (qualifiedLeads / totalLeads) * 100 : 0;

    // Velocity metrics
    const averageSalesCycle = this.calculateAverageSalesCycle(leads);
    const pipelineVelocity = this.calculatePipelineVelocity(leads);

    // Stage distribution
    const stageDistribution = this.calculateStageDistribution(leads, stages);

    // Source performance
    const sourcePerformance = this.calculateSourcePerformance(leads);

    // Team performance
    const teamPerformance = await this.calculateTeamPerformance(leads, filters);

    // Win/Loss analysis
    const winLossAnalysis = this.calculateWinLossAnalysis(leads);

    return new PipelineMetrics(
      totalLeads,
      qualifiedLeads,
      convertedLeads,
      totalValue,
      weightedValue,
      conversionRate,
      qualificationRate,
      averageSalesCycle,
      pipelineVelocity,
      stageDistribution,
      sourcePerformance,
      teamPerformance,
      winLossAnalysis,
      new Date(),
    );
  }

  /**
   * Analyze conversion funnel
   */
  async analyzeConversionFunnel(filters: AnalyticsFilters): Promise<ConversionFunnel> {
    const leads = await this.getFilteredLeads(filters);
    const stages = await this.stageRepository.findAll();

    const funnelSteps = stages.map(stage => {
      const leadsInStage = leads.filter(lead => lead.stageId === stage.id);
      const leadsCount = leadsInStage.length;
      const value = leadsInStage.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
      
      return {
        stageId: stage.id,
        stageName: stage.name,
        leadsCount,
        value,
        conversionRate: 0, // Will be calculated below
        dropOffRate: 0,
        averageTimeInStage: this.calculateAverageTimeInStage(leadsInStage),
      };
    });

    // Calculate conversion rates between stages
    for (let i = 0; i < funnelSteps.length - 1; i++) {
      const currentStep = funnelSteps[i];
      const nextStep = funnelSteps[i + 1];
      
      if (currentStep.leadsCount > 0) {
        currentStep.conversionRate = (nextStep.leadsCount / currentStep.leadsCount) * 100;
        currentStep.dropOffRate = 100 - currentStep.conversionRate;
      }
    }

    // Overall funnel metrics
    const totalEntries = funnelSteps[0]?.leadsCount || 0;
    const totalConversions = funnelSteps[funnelSteps.length - 1]?.leadsCount || 0;
    const overallConversionRate = totalEntries > 0 ? (totalConversions / totalEntries) * 100 : 0;

    return new ConversionFunnel(
      funnelSteps,
      overallConversionRate,
      this.identifyFunnelBottlenecks(funnelSteps),
      this.generateFunnelRecommendations(funnelSteps),
      new Date(),
    );
  }

  /**
   * Generate pipeline forecast
   */
  async generateForecast(filters: AnalyticsFilters): Promise<PipelineForecast> {
    const leads = await this.getFilteredLeads(filters);
    const historicalData = await this.getHistoricalData(filters);

    // Current pipeline value
    const currentPipelineValue = leads.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
    const weightedPipelineValue = leads.reduce((sum, lead) => sum + lead.getWeightedRevenue(), 0);

    // Forecast periods (30, 60, 90 days)
    const forecasts = [30, 60, 90].map(days => {
      const periodEnd = new Date();
      periodEnd.setDate(periodEnd.getDate() + days);

      const expectedClosures = this.predictClosuresInPeriod(leads, periodEnd);
      const expectedValue = expectedClosures.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
      const confidence = this.calculateForecastConfidence(expectedClosures, historicalData);

      return {
        period: `${days} days`,
        periodEnd,
        expectedClosures: expectedClosures.length,
        expectedValue,
        confidence,
        scenarios: this.generateForecastScenarios(expectedValue, confidence),
      };
    });

    // Trend analysis
    const trendAnalysis = this.analyzeForecastTrends(historicalData);

    // Risk factors
    const riskFactors = this.identifyForecastRisks(leads, historicalData);

    return new PipelineForecast(
      currentPipelineValue,
      weightedPipelineValue,
      forecasts,
      trendAnalysis,
      riskFactors,
      new Date(),
    );
  }

  /**
   * Detect pipeline bottlenecks
   */
  async detectBottlenecks(filters: AnalyticsFilters): Promise<BottleneckAnalysis> {
    const leads = await this.getFilteredLeads(filters);
    const stages = await this.stageRepository.findAll();

    const bottlenecks = [];

    // Stage-based bottlenecks
    for (const stage of stages) {
      const leadsInStage = leads.filter(lead => lead.stageId === stage.id);
      const averageTimeInStage = this.calculateAverageTimeInStage(leadsInStage);
      const expectedTime = stage.expectedDuration || 7; // Default 7 days

      if (averageTimeInStage > expectedTime * 1.5) {
        bottlenecks.push({
          type: 'stage_delay',
          stageId: stage.id,
          stageName: stage.name,
          severity: this.calculateBottleneckSeverity(averageTimeInStage, expectedTime),
          affectedLeads: leadsInStage.length,
          averageDelay: averageTimeInStage - expectedTime,
          recommendations: this.generateStageBottleneckRecommendations(stage, leadsInStage),
        });
      }
    }

    // Team-based bottlenecks
    const teamBottlenecks = await this.detectTeamBottlenecks(leads);
    bottlenecks.push(...teamBottlenecks);

    // Source-based bottlenecks
    const sourceBottlenecks = this.detectSourceBottlenecks(leads);
    bottlenecks.push(...sourceBottlenecks);

    // Overall impact assessment
    const totalImpact = this.calculateBottleneckImpact(bottlenecks, leads);

    return new BottleneckAnalysis(
      bottlenecks,
      totalImpact,
      this.prioritizeBottlenecks(bottlenecks),
      this.generateBottleneckActionPlan(bottlenecks),
      new Date(),
    );
  }

  /**
   * Analyze trends
   */
  async analyzeTrends(filters: AnalyticsFilters): Promise<any> {
    const historicalData = await this.getHistoricalData(filters);
    
    return {
      conversionTrends: this.analyzeConversionTrends(historicalData),
      velocityTrends: this.analyzeVelocityTrends(historicalData),
      volumeTrends: this.analyzeVolumeTrends(historicalData),
      valueTrends: this.analyzeValueTrends(historicalData),
    };
  }

  /**
   * Generate comparisons
   */
  async generateComparisons(filters: AnalyticsFilters): Promise<any> {
    const currentPeriod = await this.calculatePipelineMetrics(filters);
    
    // Previous period comparison
    const previousFilters = { ...filters };
    if (filters.dateFrom && filters.dateTo) {
      const periodLength = filters.dateTo.getTime() - filters.dateFrom.getTime();
      previousFilters.dateFrom = new Date(filters.dateFrom.getTime() - periodLength);
      previousFilters.dateTo = new Date(filters.dateTo.getTime() - periodLength);
    }
    
    const previousPeriod = await this.calculatePipelineMetrics(previousFilters);

    return {
      periodOverPeriod: this.comparePeriods(currentPeriod, previousPeriod),
      teamComparisons: await this.compareTeams(filters),
      sourceComparisons: this.compareSources(filters),
      benchmarkComparisons: this.compareToBenchmarks(currentPeriod),
    };
  }

  /**
   * Helper methods (simplified implementations)
   */
  private async getFilteredLeads(filters: AnalyticsFilters): Promise<any[]> {
    // This would use the lead repository with proper filtering
    return this.leadRepository.findByFilters(filters);
  }

  private async getHistoricalData(filters: AnalyticsFilters): Promise<any[]> {
    // This would fetch historical analytics data
    return [];
  }

  private calculateAverageSalesCycle(leads: any[]): number {
    const convertedLeads = leads.filter(lead => lead.isConverted());
    if (convertedLeads.length === 0) return 0;

    const totalDays = convertedLeads.reduce((sum, lead) => {
      const createdDate = new Date(lead.createdAt);
      const convertedDate = new Date(lead.convertedAt || Date.now());
      const days = Math.floor((convertedDate.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24));
      return sum + days;
    }, 0);

    return totalDays / convertedLeads.length;
  }

  private calculatePipelineVelocity(leads: any[]): number {
    // Simplified velocity calculation
    const totalValue = leads.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
    const averageCycle = this.calculateAverageSalesCycle(leads);
    return averageCycle > 0 ? totalValue / averageCycle : 0;
  }

  private calculateStageDistribution(leads: any[], stages: any[]): any {
    return stages.map(stage => ({
      stageId: stage.id,
      stageName: stage.name,
      count: leads.filter(lead => lead.stageId === stage.id).length,
      value: leads.filter(lead => lead.stageId === stage.id)
        .reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0),
    }));
  }

  private calculateSourcePerformance(leads: any[]): any {
    const sources = [...new Set(leads.map(lead => lead.source))];
    return sources.map(source => {
      const sourceLeads = leads.filter(lead => lead.source === source);
      return {
        source,
        count: sourceLeads.length,
        conversionRate: this.calculateConversionRate(sourceLeads),
        averageValue: this.calculateAverageValue(sourceLeads),
      };
    });
  }

  private async calculateTeamPerformance(leads: any[], filters: AnalyticsFilters): Promise<any> {
    // This would calculate team-specific performance metrics
    return {};
  }

  private calculateWinLossAnalysis(leads: any[]): any {
    const wonLeads = leads.filter(lead => lead.status === 'won');
    const lostLeads = leads.filter(lead => lead.status === 'lost');
    
    return {
      winRate: leads.length > 0 ? (wonLeads.length / leads.length) * 100 : 0,
      lossRate: leads.length > 0 ? (lostLeads.length / leads.length) * 100 : 0,
      winReasons: this.analyzeWinReasons(wonLeads),
      lossReasons: this.analyzeLossReasons(lostLeads),
    };
  }

  private calculateAverageTimeInStage(leads: any[]): number {
    // Simplified calculation
    return 7; // Default 7 days
  }

  private identifyFunnelBottlenecks(funnelSteps: any[]): any[] {
    return funnelSteps.filter(step => step.dropOffRate > 50);
  }

  private generateFunnelRecommendations(funnelSteps: any[]): string[] {
    return ['Optimize high drop-off stages', 'Improve lead qualification'];
  }

  private predictClosuresInPeriod(leads: any[], periodEnd: Date): any[] {
    return leads.filter(lead => {
      const deadline = new Date(lead.dateDeadline || Date.now());
      return deadline <= periodEnd && lead.probability > 50;
    });
  }

  private calculateForecastConfidence(leads: any[], historicalData: any[]): number {
    // Simplified confidence calculation
    return 75; // 75% confidence
  }

  private generateForecastScenarios(expectedValue: number, confidence: number): any {
    return {
      optimistic: expectedValue * 1.2,
      realistic: expectedValue,
      pessimistic: expectedValue * 0.8,
    };
  }

  private analyzeForecastTrends(historicalData: any[]): any {
    return { trend: 'stable', growth: 0 };
  }

  private identifyForecastRisks(leads: any[], historicalData: any[]): string[] {
    return ['Market uncertainty', 'Seasonal variations'];
  }

  private calculateBottleneckSeverity(actual: number, expected: number): 'low' | 'medium' | 'high' {
    const ratio = actual / expected;
    if (ratio > 3) return 'high';
    if (ratio > 2) return 'medium';
    return 'low';
  }

  private generateStageBottleneckRecommendations(stage: any, leads: any[]): string[] {
    return [`Review ${stage.name} process`, 'Provide additional training'];
  }

  private async detectTeamBottlenecks(leads: any[]): Promise<any[]> {
    return [];
  }

  private detectSourceBottlenecks(leads: any[]): any[] {
    return [];
  }

  private calculateBottleneckImpact(bottlenecks: any[], leads: any[]): any {
    return { delayedDeals: 0, lostRevenue: 0 };
  }

  private prioritizeBottlenecks(bottlenecks: any[]): any[] {
    return bottlenecks.sort((a, b) => b.affectedLeads - a.affectedLeads);
  }

  private generateBottleneckActionPlan(bottlenecks: any[]): any[] {
    return bottlenecks.map(bottleneck => ({
      bottleneck: bottleneck.type,
      action: 'Address bottleneck',
      priority: bottleneck.severity,
      timeline: '2 weeks',
    }));
  }

  private analyzeConversionTrends(historicalData: any[]): any {
    return { trend: 'improving', rate: 5 };
  }

  private analyzeVelocityTrends(historicalData: any[]): any {
    return { trend: 'stable', change: 0 };
  }

  private analyzeVolumeTrends(historicalData: any[]): any {
    return { trend: 'increasing', change: 10 };
  }

  private analyzeValueTrends(historicalData: any[]): any {
    return { trend: 'increasing', change: 15 };
  }

  private comparePeriods(current: any, previous: any): any {
    return {
      conversionRateChange: current.conversionRate - previous.conversionRate,
      valueChange: current.totalValue - previous.totalValue,
      velocityChange: current.pipelineVelocity - previous.pipelineVelocity,
    };
  }

  private async compareTeams(filters: AnalyticsFilters): Promise<any> {
    return {};
  }

  private compareSources(filters: AnalyticsFilters): any {
    return {};
  }

  private compareToBenchmarks(metrics: any): any {
    return {
      industryBenchmark: 'Above average',
      sizeBenchmark: 'On par',
    };
  }

  private calculateConversionRate(leads: any[]): number {
    const converted = leads.filter(lead => lead.isConverted()).length;
    return leads.length > 0 ? (converted / leads.length) * 100 : 0;
  }

  private calculateAverageValue(leads: any[]): number {
    const total = leads.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
    return leads.length > 0 ? total / leads.length : 0;
  }

  private analyzeWinReasons(wonLeads: any[]): any[] {
    return [{ reason: 'Good fit', count: wonLeads.length }];
  }

  private analyzeLossReasons(lostLeads: any[]): any[] {
    return [{ reason: 'Price', count: lostLeads.length }];
  }
}

/**
 * Analytics filters interface
 */
export interface AnalyticsFilters {
  teamId?: number;
  userId?: number;
  dateFrom?: Date;
  dateTo?: Date;
  stageIds?: number[];
  priorityLevels?: string[];
  sources?: string[];
  [key: string]: any;
}
