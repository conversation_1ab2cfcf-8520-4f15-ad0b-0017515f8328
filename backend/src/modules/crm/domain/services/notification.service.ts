import { Injectable, Logger } from '@nestjs/common';
import { NotificationTemplate } from '@/modules/crm/domain/value-objects/notification-template.vo';
import { NotificationPreference } from '@/modules/crm/domain/value-objects/notification-preference.vo';
import { NotificationDelivery } from '@/modules/crm/domain/value-objects/notification-delivery.vo';
import { NotificationChannel } from '@/modules/crm/domain/enums/notification-channel.enum';
import { NotificationPriority } from '@/modules/crm/domain/enums/notification-priority.enum';

/**
 * Notification Service
 * Multi-channel notification system with templates, preferences, and delivery tracking
 */
@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  // Template registry
  private readonly templates = new Map<string, NotificationTemplate>();

  // User preferences cache
  private readonly userPreferences = new Map<string, NotificationPreference[]>();

  constructor() {
    this.initializeDefaultTemplates();
  }

  /**
   * Send notification to user(s)
   */
  async sendNotification(request: NotificationRequest): Promise<NotificationDelivery[]> {
    this.logger.debug(`Sending notification: ${request.type} to ${request.recipients.length} recipients`);

    try {
      const template = this.getTemplate(request.type);
      const deliveries: NotificationDelivery[] = [];

      for (const recipient of request.recipients) {
        const userPreferences = await this.getUserPreferences(recipient.userId);
        const enabledChannels = this.getEnabledChannels(userPreferences, request.type, request.priority);

        for (const channel of enabledChannels) {
          const delivery = await this.sendToChannel(
            channel,
            recipient,
            template,
            request.data,
            request.priority,
          );
          deliveries.push(delivery);
        }
      }

      this.logger.debug(`Notification sent successfully: ${deliveries.length} deliveries`);
      return deliveries;

    } catch (error) {
      this.logger.error(`Failed to send notification: ${request.type}`, error);
      throw error;
    }
  }

  /**
   * Send bulk notifications
   */
  async sendBulkNotifications(requests: NotificationRequest[]): Promise<NotificationDelivery[]> {
    this.logger.debug(`Sending bulk notifications: ${requests.length} requests`);

    const allDeliveries: NotificationDelivery[] = [];

    // Process in batches to avoid overwhelming the system
    const batchSize = 50;
    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize);
      
      const batchPromises = batch.map(request => this.sendNotification(request));
      const batchResults = await Promise.allSettled(batchPromises);

      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          allDeliveries.push(...result.value);
        } else {
          this.logger.error(`Bulk notification failed for request ${i + index}`, result.reason);
        }
      });

      // Small delay between batches
      if (i + batchSize < requests.length) {
        await this.delay(100);
      }
    }

    this.logger.debug(`Bulk notifications completed: ${allDeliveries.length} total deliveries`);
    return allDeliveries;
  }

  /**
   * Send lead assignment notification
   */
  async sendLeadAssignmentNotification(data: {
    leadId: number;
    leadName: string;
    assigneeId: string;
    assigneeName: string;
    assigneeEmail: string;
    assignerName: string;
    priority: string;
    dueDate?: Date;
  }): Promise<NotificationDelivery[]> {
    return this.sendNotification({
      type: 'lead_assigned',
      recipients: [{
        userId: data.assigneeId,
        email: data.assigneeEmail,
        name: data.assigneeName,
      }],
      data: {
        leadId: data.leadId,
        leadName: data.leadName,
        assignerName: data.assignerName,
        priority: data.priority,
        dueDate: data.dueDate?.toISOString(),
        dashboardUrl: `${process.env.FRONTEND_URL}/leads/${data.leadId}`,
      },
      priority: data.priority === 'high' ? NotificationPriority.HIGH : NotificationPriority.NORMAL,
    });
  }

  /**
   * Send opportunity update notification
   */
  async sendOpportunityUpdateNotification(data: {
    opportunityId: number;
    opportunityName: string;
    recipientId: string;
    recipientName: string;
    recipientEmail: string;
    updateType: 'stage_change' | 'value_change' | 'won' | 'lost';
    newStage?: string;
    newValue?: number;
    probability?: number;
  }): Promise<NotificationDelivery[]> {
    const priority = data.updateType === 'won' || data.updateType === 'lost' 
      ? NotificationPriority.CRITICAL 
      : NotificationPriority.NORMAL;

    return this.sendNotification({
      type: 'opportunity_updated',
      recipients: [{
        userId: data.recipientId,
        email: data.recipientEmail,
        name: data.recipientName,
      }],
      data: {
        opportunityId: data.opportunityId,
        opportunityName: data.opportunityName,
        updateType: data.updateType,
        newStage: data.newStage,
        newValue: data.newValue,
        probability: data.probability,
        dashboardUrl: `${process.env.FRONTEND_URL}/opportunities/${data.opportunityId}`,
      },
      priority,
    });
  }

  /**
   * Send pipeline alert notification
   */
  async sendPipelineAlertNotification(data: {
    alertType: 'bottleneck' | 'forecast_risk' | 'target_miss' | 'conversion_drop';
    severity: 'low' | 'medium' | 'high';
    message: string;
    details: Record<string, any>;
    recipients: Array<{
      userId: string;
      email: string;
      name: string;
      role: string;
    }>;
  }): Promise<NotificationDelivery[]> {
    const priority = data.severity === 'high' 
      ? NotificationPriority.CRITICAL 
      : data.severity === 'medium' 
        ? NotificationPriority.HIGH 
        : NotificationPriority.NORMAL;

    return this.sendNotification({
      type: 'pipeline_alert',
      recipients: data.recipients,
      data: {
        alertType: data.alertType,
        severity: data.severity,
        message: data.message,
        details: data.details,
        dashboardUrl: `${process.env.FRONTEND_URL}/analytics/pipeline`,
      },
      priority,
    });
  }

  /**
   * Send scheduled report notification
   */
  async sendScheduledReportNotification(data: {
    reportType: string;
    reportName: string;
    recipients: Array<{
      userId: string;
      email: string;
      name: string;
    }>;
    reportUrl: string;
    generatedAt: Date;
    summary: Record<string, any>;
  }): Promise<NotificationDelivery[]> {
    return this.sendNotification({
      type: 'scheduled_report',
      recipients: data.recipients,
      data: {
        reportType: data.reportType,
        reportName: data.reportName,
        reportUrl: data.reportUrl,
        generatedAt: data.generatedAt.toISOString(),
        summary: data.summary,
      },
      priority: NotificationPriority.LOW,
    });
  }

  /**
   * Register notification template
   */
  registerTemplate(template: NotificationTemplate): void {
    this.templates.set(template.type, template);
    this.logger.debug(`Registered notification template: ${template.type}`);
  }

  /**
   * Update user notification preferences
   */
  async updateUserPreferences(userId: string, preferences: NotificationPreference[]): Promise<void> {
    this.userPreferences.set(userId, preferences);
    // In production, this would persist to database
    this.logger.debug(`Updated notification preferences for user: ${userId}`);
  }

  /**
   * Get user notification preferences
   */
  async getUserPreferences(userId: string): Promise<NotificationPreference[]> {
    let preferences = this.userPreferences.get(userId);
    
    if (!preferences) {
      // Load from database or create defaults
      preferences = this.createDefaultPreferences();
      this.userPreferences.set(userId, preferences);
    }
    
    return preferences;
  }

  /**
   * Get notification delivery status
   */
  async getDeliveryStatus(deliveryId: string): Promise<NotificationDelivery | null> {
    // In production, this would query the database
    return null;
  }

  /**
   * Get notification statistics
   */
  async getNotificationStats(filters: {
    userId?: string;
    dateFrom?: Date;
    dateTo?: Date;
    type?: string;
    channel?: NotificationChannel;
  }): Promise<NotificationStats> {
    // In production, this would query the database
    return {
      totalSent: 0,
      totalDelivered: 0,
      totalFailed: 0,
      deliveryRate: 0,
      channelBreakdown: {},
      typeBreakdown: {},
    };
  }

  /**
   * Private helper methods
   */
  private getTemplate(type: string): NotificationTemplate {
    const template = this.templates.get(type);
    if (!template) {
      throw new Error(`Notification template not found: ${type}`);
    }
    return template;
  }

  private getEnabledChannels(
    preferences: NotificationPreference[],
    notificationType: string,
    priority: NotificationPriority,
  ): NotificationChannel[] {
    const enabledChannels: NotificationChannel[] = [];

    preferences.forEach(pref => {
      if (pref.notificationType === notificationType || pref.notificationType === 'all') {
        if (pref.enabled) {
          enabledChannels.push(...pref.channels);
        }
      }
    });

    // For critical notifications, always include email if not explicitly disabled
    if (priority === NotificationPriority.CRITICAL) {
      if (!enabledChannels.includes(NotificationChannel.EMAIL)) {
        const emailDisabled = preferences.some(pref => 
          pref.notificationType === notificationType && 
          !pref.channels.includes(NotificationChannel.EMAIL)
        );
        
        if (!emailDisabled) {
          enabledChannels.push(NotificationChannel.EMAIL);
        }
      }
    }

    return [...new Set(enabledChannels)]; // Remove duplicates
  }

  private async sendToChannel(
    channel: NotificationChannel,
    recipient: NotificationRecipient,
    template: NotificationTemplate,
    data: Record<string, any>,
    priority: NotificationPriority,
  ): Promise<NotificationDelivery> {
    const deliveryId = this.generateDeliveryId();
    
    try {
      // Render template with data
      const content = template.render(data);
      
      // Send via specific channel
      let success = false;
      let error: string | undefined;

      switch (channel) {
        case NotificationChannel.EMAIL:
          success = await this.sendEmail(recipient, content, priority);
          break;
        
        case NotificationChannel.SMS:
          success = await this.sendSMS(recipient, content, priority);
          break;
        
        case NotificationChannel.PUSH:
          success = await this.sendPushNotification(recipient, content, priority);
          break;
        
        case NotificationChannel.IN_APP:
          success = await this.sendInAppNotification(recipient, content, priority);
          break;
        
        case NotificationChannel.SLACK:
          success = await this.sendSlackNotification(recipient, content, priority);
          break;
        
        default:
          throw new Error(`Unsupported notification channel: ${channel}`);
      }

      return new NotificationDelivery(
        deliveryId,
        recipient.userId,
        template.type,
        channel,
        success ? 'delivered' : 'failed',
        new Date(),
        success ? undefined : error,
        {
          priority,
          retryCount: 0,
        },
      );

    } catch (err) {
      this.logger.error(`Failed to send notification via ${channel}`, err);
      
      return new NotificationDelivery(
        deliveryId,
        recipient.userId,
        template.type,
        channel,
        'failed',
        new Date(),
        err instanceof Error ? err.message : 'Unknown error',
        {
          priority,
          retryCount: 0,
        },
      );
    }
  }

  private async sendEmail(recipient: NotificationRecipient, content: any, priority: NotificationPriority): Promise<boolean> {
    // Integrate with email service (SendGrid, AWS SES, etc.)
    this.logger.debug(`Sending email to: ${recipient.email}`);
    return true; // Placeholder
  }

  private async sendSMS(recipient: NotificationRecipient, content: any, priority: NotificationPriority): Promise<boolean> {
    // Integrate with SMS service (Twilio, AWS SNS, etc.)
    this.logger.debug(`Sending SMS to: ${recipient.phone}`);
    return true; // Placeholder
  }

  private async sendPushNotification(recipient: NotificationRecipient, content: any, priority: NotificationPriority): Promise<boolean> {
    // Integrate with push notification service (Firebase, OneSignal, etc.)
    this.logger.debug(`Sending push notification to: ${recipient.userId}`);
    return true; // Placeholder
  }

  private async sendInAppNotification(recipient: NotificationRecipient, content: any, priority: NotificationPriority): Promise<boolean> {
    // Store in database for in-app display
    this.logger.debug(`Sending in-app notification to: ${recipient.userId}`);
    return true; // Placeholder
  }

  private async sendSlackNotification(recipient: NotificationRecipient, content: any, priority: NotificationPriority): Promise<boolean> {
    // Integrate with Slack API
    this.logger.debug(`Sending Slack notification to: ${recipient.slackUserId}`);
    return true; // Placeholder
  }

  private generateDeliveryId(): string {
    return `delivery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private createDefaultPreferences(): NotificationPreference[] {
    return [
      new NotificationPreference(
        'all',
        true,
        [NotificationChannel.EMAIL, NotificationChannel.IN_APP],
        { quietHours: { start: '22:00', end: '08:00' } },
      ),
    ];
  }

  private initializeDefaultTemplates(): void {
    // Initialize default notification templates
    this.registerTemplate(new NotificationTemplate(
      'lead_assigned',
      'Lead Assigned',
      'You have been assigned a new lead: {{leadName}}',
      'A new lead "{{leadName}}" has been assigned to you by {{assignerName}}. Priority: {{priority}}. View details: {{dashboardUrl}}',
      {
        email: {
          subject: 'New Lead Assignment: {{leadName}}',
          template: 'lead-assignment-email',
        },
        sms: {
          template: 'New lead assigned: {{leadName}}. Priority: {{priority}}. Check your dashboard.',
        },
        push: {
          title: 'New Lead Assignment',
          body: '{{leadName}} assigned by {{assignerName}}',
        },
      },
    ));

    // Add more default templates...
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Supporting interfaces
 */
export interface NotificationRequest {
  type: string;
  recipients: NotificationRecipient[];
  data: Record<string, any>;
  priority: NotificationPriority;
  scheduledAt?: Date;
}

export interface NotificationRecipient {
  userId: string;
  email?: string;
  phone?: string;
  name?: string;
  slackUserId?: string;
  pushTokens?: string[];
}

export interface NotificationStats {
  totalSent: number;
  totalDelivered: number;
  totalFailed: number;
  deliveryRate: number;
  channelBreakdown: Record<string, number>;
  typeBreakdown: Record<string, number>;
}
