/**
 * Workload Balance Value Object
 * Represents the workload balance for a user
 */
export class WorkloadBalance {
  constructor(
    public readonly userId: string,
    public readonly totalLeads: number,
    public readonly activeLeads: number,
    public readonly totalValue: number,
    public readonly averageLeadAge: number,
    public readonly lastUpdated: Date,
    public readonly metadata: Record<string, any> = {},
  ) {
    this.validateWorkload();
  }

  private validateWorkload(): void {
    if (!this.userId || this.userId.trim().length === 0) {
      throw new Error('Workload balance must have a user ID');
    }
    
    if (this.totalLeads < 0) {
      throw new Error('Total leads cannot be negative');
    }
    
    if (this.activeLeads < 0) {
      throw new Error('Active leads cannot be negative');
    }
    
    if (this.activeLeads > this.totalLeads) {
      throw new Error('Active leads cannot exceed total leads');
    }
  }

  /**
   * Get workload intensity score (0-100)
   */
  getWorkloadIntensity(): number {
    // Base calculation on number of active leads
    let intensity = Math.min(this.activeLeads * 5, 100);
    
    // Adjust for lead age (older leads are more intensive)
    if (this.averageLeadAge > 30) {
      intensity += 10;
    } else if (this.averageLeadAge > 14) {
      intensity += 5;
    }
    
    return Math.min(100, intensity);
  }

  /**
   * Get workload level
   */
  getWorkloadLevel(): 'light' | 'moderate' | 'heavy' | 'overloaded' {
    const intensity = this.getWorkloadIntensity();
    
    if (intensity >= 80) return 'overloaded';
    if (intensity >= 60) return 'heavy';
    if (intensity >= 30) return 'moderate';
    return 'light';
  }

  /**
   * Check if user is overloaded
   */
  isOverloaded(): boolean {
    return this.getWorkloadLevel() === 'overloaded';
  }

  /**
   * Check if user can take more leads
   */
  canTakeMoreLeads(maxLeads: number = 20): boolean {
    return this.activeLeads < maxLeads && !this.isOverloaded();
  }

  /**
   * Get average value per lead
   */
  getAverageValuePerLead(): number {
    return this.totalLeads > 0 ? this.totalValue / this.totalLeads : 0;
  }

  /**
   * Get conversion rate (if available in metadata)
   */
  getConversionRate(): number {
    return this.metadata.conversionRate || 0;
  }

  /**
   * Compare with another workload balance
   */
  compareWith(other: WorkloadBalance): WorkloadComparison {
    return {
      totalLeadsDiff: this.totalLeads - other.totalLeads,
      activeLeadsDiff: this.activeLeads - other.activeLeads,
      totalValueDiff: this.totalValue - other.totalValue,
      intensityDiff: this.getWorkloadIntensity() - other.getWorkloadIntensity(),
      isMoreBalanced: this.getWorkloadIntensity() < other.getWorkloadIntensity(),
    };
  }

  /**
   * Get workload summary
   */
  getSummary(): WorkloadSummary {
    return {
      userId: this.userId,
      totalLeads: this.totalLeads,
      activeLeads: this.activeLeads,
      totalValue: this.totalValue,
      averageLeadAge: this.averageLeadAge,
      workloadLevel: this.getWorkloadLevel(),
      workloadIntensity: this.getWorkloadIntensity(),
      averageValuePerLead: this.getAverageValuePerLead(),
      canTakeMoreLeads: this.canTakeMoreLeads(),
      lastUpdated: this.lastUpdated,
    };
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      userId: this.userId,
      totalLeads: this.totalLeads,
      activeLeads: this.activeLeads,
      totalValue: this.totalValue,
      averageLeadAge: this.averageLeadAge,
      lastUpdated: this.lastUpdated.toISOString(),
      metadata: this.metadata,
      workloadIntensity: this.getWorkloadIntensity(),
      workloadLevel: this.getWorkloadLevel(),
      averageValuePerLead: this.getAverageValuePerLead(),
      canTakeMoreLeads: this.canTakeMoreLeads(),
      summary: this.getSummary(),
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): WorkloadBalance {
    return new WorkloadBalance(
      data.userId,
      data.totalLeads,
      data.activeLeads,
      data.totalValue,
      data.averageLeadAge,
      new Date(data.lastUpdated),
      data.metadata,
    );
  }

  /**
   * Create empty workload
   */
  static createEmpty(userId: string): WorkloadBalance {
    return new WorkloadBalance(
      userId,
      0,
      0,
      0,
      0,
      new Date(),
    );
  }
}

/**
 * Supporting interfaces
 */
export interface WorkloadComparison {
  totalLeadsDiff: number;
  activeLeadsDiff: number;
  totalValueDiff: number;
  intensityDiff: number;
  isMoreBalanced: boolean;
}

export interface WorkloadSummary {
  userId: string;
  totalLeads: number;
  activeLeads: number;
  totalValue: number;
  averageLeadAge: number;
  workloadLevel: 'light' | 'moderate' | 'heavy' | 'overloaded';
  workloadIntensity: number;
  averageValuePerLead: number;
  canTakeMoreLeads: boolean;
  lastUpdated: Date;
}
