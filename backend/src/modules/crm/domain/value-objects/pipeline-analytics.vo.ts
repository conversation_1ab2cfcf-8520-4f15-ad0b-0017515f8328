import { PipelineMetrics } from './pipeline-metrics.vo';
import { ConversionFunnel } from './conversion-funnel.vo';
import { PipelineForecast } from './pipeline-forecast.vo';
import { BottleneckAnalysis } from './bottleneck-analysis.vo';

/**
 * Pipeline Analytics Value Object
 * Comprehensive analytics data for pipeline performance
 */
export class PipelineAnalytics {
  constructor(
    public readonly metrics: PipelineMetrics,
    public readonly conversionFunnel: ConversionFunnel,
    public readonly forecast: PipelineForecast,
    public readonly bottlenecks: BottleneckAnalysis,
    public readonly trends: any,
    public readonly comparisons: any,
    public readonly generatedAt: Date,
    public readonly metadata: Record<string, any> = {},
  ) {
    this.validateAnalytics();
  }

  private validateAnalytics(): void {
    if (!this.metrics) {
      throw new Error('Pipeline analytics must include metrics');
    }
    
    if (!this.conversionFunnel) {
      throw new Error('Pipeline analytics must include conversion funnel');
    }
    
    if (!this.forecast) {
      throw new Error('Pipeline analytics must include forecast');
    }
    
    if (!this.bottlenecks) {
      throw new Error('Pipeline analytics must include bottleneck analysis');
    }
  }

  /**
   * Get overall pipeline health score (0-100)
   */
  getHealthScore(): number {
    const metricsScore = this.calculateMetricsScore();
    const funnelScore = this.calculateFunnelScore();
    const forecastScore = this.calculateForecastScore();
    const bottleneckScore = this.calculateBottleneckScore();

    // Weighted average
    const healthScore = (
      metricsScore * 0.3 +
      funnelScore * 0.25 +
      forecastScore * 0.25 +
      bottleneckScore * 0.2
    );

    return Math.round(Math.max(0, Math.min(100, healthScore)));
  }

  /**
   * Get pipeline health grade
   */
  getHealthGrade(): string {
    const score = this.getHealthScore();
    if (score >= 90) return 'A+';
    if (score >= 80) return 'A';
    if (score >= 70) return 'B';
    if (score >= 60) return 'C';
    if (score >= 50) return 'D';
    return 'F';
  }

  /**
   * Get key insights from the analytics
   */
  getKeyInsights(): string[] {
    const insights: string[] = [];

    // Conversion rate insights
    if (this.metrics.conversionRate > 20) {
      insights.push('Excellent conversion rate performance');
    } else if (this.metrics.conversionRate < 10) {
      insights.push('Conversion rate needs improvement');
    }

    // Pipeline velocity insights
    if (this.metrics.pipelineVelocity > 10000) {
      insights.push('Strong pipeline velocity');
    } else if (this.metrics.pipelineVelocity < 5000) {
      insights.push('Pipeline velocity is below target');
    }

    // Bottleneck insights
    const criticalBottlenecks = this.bottlenecks.bottlenecks.filter(b => b.severity === 'high');
    if (criticalBottlenecks.length > 0) {
      insights.push(`${criticalBottlenecks.length} critical bottlenecks detected`);
    }

    // Forecast insights
    const forecastTrend = this.forecast.trendAnalysis?.trend;
    if (forecastTrend === 'improving') {
      insights.push('Pipeline forecast shows positive trend');
    } else if (forecastTrend === 'declining') {
      insights.push('Pipeline forecast shows concerning decline');
    }

    return insights;
  }

  /**
   * Get recommended actions
   */
  getRecommendedActions(): string[] {
    const actions: string[] = [];

    // Based on conversion funnel
    const funnelBottlenecks = this.conversionFunnel.bottlenecks;
    if (funnelBottlenecks.length > 0) {
      actions.push('Address conversion funnel bottlenecks');
    }

    // Based on pipeline bottlenecks
    const prioritizedBottlenecks = this.bottlenecks.prioritizedBottlenecks;
    if (prioritizedBottlenecks.length > 0) {
      actions.push(`Focus on ${prioritizedBottlenecks[0].type} bottleneck`);
    }

    // Based on metrics
    if (this.metrics.conversionRate < 15) {
      actions.push('Improve lead qualification process');
    }

    if (this.metrics.averageSalesCycle > 60) {
      actions.push('Optimize sales cycle duration');
    }

    // Based on forecast risks
    if (this.forecast.riskFactors.length > 0) {
      actions.push('Mitigate identified forecast risks');
    }

    return [...new Set(actions)]; // Remove duplicates
  }

  /**
   * Get performance indicators
   */
  getPerformanceIndicators(): Record<string, any> {
    return {
      healthScore: this.getHealthScore(),
      healthGrade: this.getHealthGrade(),
      conversionRate: this.metrics.conversionRate,
      pipelineVelocity: this.metrics.pipelineVelocity,
      forecastAccuracy: this.forecast.forecasts[0]?.confidence || 0,
      bottleneckCount: this.bottlenecks.bottlenecks.length,
      trendDirection: this.trends?.conversionTrends?.trend || 'stable',
    };
  }

  /**
   * Check if analytics data is fresh
   */
  isFresh(maxAgeHours: number = 24): boolean {
    const ageHours = (Date.now() - this.generatedAt.getTime()) / (1000 * 60 * 60);
    return ageHours <= maxAgeHours;
  }

  /**
   * Get analytics summary for dashboard
   */
  getDashboardSummary(): Record<string, any> {
    return {
      overview: {
        totalLeads: this.metrics.totalLeads,
        conversionRate: this.metrics.conversionRate,
        pipelineValue: this.metrics.totalValue,
        healthScore: this.getHealthScore(),
      },
      forecast: {
        next30Days: this.forecast.forecasts.find(f => f.period === '30 days'),
        trend: this.trends?.conversionTrends?.trend,
      },
      alerts: {
        criticalBottlenecks: this.bottlenecks.bottlenecks.filter(b => b.severity === 'high').length,
        riskFactors: this.forecast.riskFactors.length,
      },
      recommendations: this.getRecommendedActions().slice(0, 3), // Top 3 recommendations
    };
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      metrics: this.metrics.toPlainObject(),
      conversionFunnel: this.conversionFunnel.toPlainObject(),
      forecast: this.forecast.toPlainObject(),
      bottlenecks: this.bottlenecks.toPlainObject(),
      trends: this.trends,
      comparisons: this.comparisons,
      healthScore: this.getHealthScore(),
      healthGrade: this.getHealthGrade(),
      keyInsights: this.getKeyInsights(),
      recommendedActions: this.getRecommendedActions(),
      performanceIndicators: this.getPerformanceIndicators(),
      dashboardSummary: this.getDashboardSummary(),
      generatedAt: this.generatedAt.toISOString(),
      metadata: this.metadata,
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): PipelineAnalytics {
    return new PipelineAnalytics(
      PipelineMetrics.fromPlainObject(data.metrics),
      ConversionFunnel.fromPlainObject(data.conversionFunnel),
      PipelineForecast.fromPlainObject(data.forecast),
      BottleneckAnalysis.fromPlainObject(data.bottlenecks),
      data.trends,
      data.comparisons,
      new Date(data.generatedAt),
      data.metadata,
    );
  }

  /**
   * Private helper methods for health score calculation
   */
  private calculateMetricsScore(): number {
    let score = 50; // Base score

    // Conversion rate scoring
    if (this.metrics.conversionRate >= 25) score += 20;
    else if (this.metrics.conversionRate >= 15) score += 10;
    else if (this.metrics.conversionRate < 5) score -= 20;

    // Pipeline velocity scoring
    if (this.metrics.pipelineVelocity >= 15000) score += 15;
    else if (this.metrics.pipelineVelocity >= 8000) score += 8;
    else if (this.metrics.pipelineVelocity < 3000) score -= 15;

    // Sales cycle scoring
    if (this.metrics.averageSalesCycle <= 30) score += 10;
    else if (this.metrics.averageSalesCycle <= 60) score += 5;
    else if (this.metrics.averageSalesCycle > 120) score -= 15;

    // Lead volume scoring
    if (this.metrics.totalLeads >= 100) score += 5;
    else if (this.metrics.totalLeads < 20) score -= 10;

    return Math.max(0, Math.min(100, score));
  }

  private calculateFunnelScore(): number {
    let score = 50; // Base score

    // Overall conversion rate
    if (this.conversionFunnel.overallConversionRate >= 20) score += 25;
    else if (this.conversionFunnel.overallConversionRate >= 10) score += 10;
    else if (this.conversionFunnel.overallConversionRate < 5) score -= 25;

    // Bottleneck penalty
    const bottleneckPenalty = this.conversionFunnel.bottlenecks.length * 5;
    score -= bottleneckPenalty;

    return Math.max(0, Math.min(100, score));
  }

  private calculateForecastScore(): number {
    let score = 50; // Base score

    // Forecast confidence
    const avgConfidence = this.forecast.forecasts.reduce((sum, f) => sum + f.confidence, 0) / this.forecast.forecasts.length;
    if (avgConfidence >= 80) score += 25;
    else if (avgConfidence >= 60) score += 10;
    else if (avgConfidence < 40) score -= 20;

    // Risk factor penalty
    const riskPenalty = this.forecast.riskFactors.length * 5;
    score -= riskPenalty;

    return Math.max(0, Math.min(100, score));
  }

  private calculateBottleneckScore(): number {
    let score = 100; // Start with perfect score

    // Penalty for each bottleneck based on severity
    this.bottlenecks.bottlenecks.forEach(bottleneck => {
      switch (bottleneck.severity) {
        case 'high': score -= 20; break;
        case 'medium': score -= 10; break;
        case 'low': score -= 5; break;
      }
    });

    return Math.max(0, score);
  }
}
