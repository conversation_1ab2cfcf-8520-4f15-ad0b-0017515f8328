/**
 * Pipeline Metrics Value Object
 * Comprehensive metrics for pipeline performance analysis
 */
export class PipelineMetrics {
  constructor(
    public readonly totalLeads: number,
    public readonly qualifiedLeads: number,
    public readonly convertedLeads: number,
    public readonly totalValue: number,
    public readonly weightedValue: number,
    public readonly conversionRate: number,
    public readonly qualificationRate: number,
    public readonly averageSalesCycle: number,
    public readonly pipelineVelocity: number,
    public readonly stageDistribution: StageDistribution[],
    public readonly sourcePerformance: SourcePerformance[],
    public readonly teamPerformance: TeamPerformance,
    public readonly winLossAnalysis: WinLossAnalysis,
    public readonly calculatedAt: Date,
  ) {
    this.validateMetrics();
  }

  private validateMetrics(): void {
    if (this.totalLeads < 0) {
      throw new Error('Total leads cannot be negative');
    }
    
    if (this.conversionRate < 0 || this.conversionRate > 100) {
      throw new Error('Conversion rate must be between 0 and 100');
    }
    
    if (this.qualificationRate < 0 || this.qualificationRate > 100) {
      throw new Error('Qualification rate must be between 0 and 100');
    }
  }

  /**
   * Get pipeline efficiency score (0-100)
   */
  getEfficiencyScore(): number {
    let score = 0;

    // Conversion rate component (40%)
    score += Math.min(this.conversionRate * 2, 40);

    // Qualification rate component (30%)
    score += Math.min(this.qualificationRate * 0.3, 30);

    // Sales cycle component (20%) - shorter is better
    const cycleScore = Math.max(0, 20 - (this.averageSalesCycle / 10));
    score += cycleScore;

    // Pipeline velocity component (10%)
    const velocityScore = Math.min(this.pipelineVelocity / 1000, 10);
    score += velocityScore;

    return Math.round(Math.max(0, Math.min(100, score)));
  }

  /**
   * Get average deal size
   */
  getAverageDealSize(): number {
    return this.totalLeads > 0 ? this.totalValue / this.totalLeads : 0;
  }

  /**
   * Get weighted average deal size
   */
  getWeightedAverageDealSize(): number {
    return this.totalLeads > 0 ? this.weightedValue / this.totalLeads : 0;
  }

  /**
   * Get pipeline coverage ratio
   */
  getPipelineCoverageRatio(): number {
    // Ratio of pipeline value to target (assuming target is needed revenue)
    // This would typically be calculated against a target value
    return this.weightedValue; // Simplified
  }

  /**
   * Get top performing source
   */
  getTopPerformingSource(): SourcePerformance | null {
    if (this.sourcePerformance.length === 0) return null;
    
    return this.sourcePerformance.reduce((top, current) => 
      current.conversionRate > top.conversionRate ? current : top
    );
  }

  /**
   * Get worst performing source
   */
  getWorstPerformingSource(): SourcePerformance | null {
    if (this.sourcePerformance.length === 0) return null;
    
    return this.sourcePerformance.reduce((worst, current) => 
      current.conversionRate < worst.conversionRate ? current : worst
    );
  }

  /**
   * Get stage with most leads
   */
  getMostPopulatedStage(): StageDistribution | null {
    if (this.stageDistribution.length === 0) return null;
    
    return this.stageDistribution.reduce((max, current) => 
      current.count > max.count ? current : max
    );
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): Record<string, any> {
    return {
      efficiency: this.getEfficiencyScore(),
      averageDealSize: this.getAverageDealSize(),
      weightedAverageDealSize: this.getWeightedAverageDealSize(),
      topSource: this.getTopPerformingSource()?.source,
      worstSource: this.getWorstPerformingSource()?.source,
      mostPopulatedStage: this.getMostPopulatedStage()?.stageName,
      winRate: this.winLossAnalysis.winRate,
      lossRate: this.winLossAnalysis.lossRate,
    };
  }

  /**
   * Compare with previous metrics
   */
  compareWith(previousMetrics: PipelineMetrics): MetricsComparison {
    return {
      totalLeadsChange: this.totalLeads - previousMetrics.totalLeads,
      conversionRateChange: this.conversionRate - previousMetrics.conversionRate,
      qualificationRateChange: this.qualificationRate - previousMetrics.qualificationRate,
      averageSalesCycleChange: this.averageSalesCycle - previousMetrics.averageSalesCycle,
      pipelineVelocityChange: this.pipelineVelocity - previousMetrics.pipelineVelocity,
      totalValueChange: this.totalValue - previousMetrics.totalValue,
      weightedValueChange: this.weightedValue - previousMetrics.weightedValue,
      efficiencyScoreChange: this.getEfficiencyScore() - previousMetrics.getEfficiencyScore(),
    };
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      totalLeads: this.totalLeads,
      qualifiedLeads: this.qualifiedLeads,
      convertedLeads: this.convertedLeads,
      totalValue: this.totalValue,
      weightedValue: this.weightedValue,
      conversionRate: this.conversionRate,
      qualificationRate: this.qualificationRate,
      averageSalesCycle: this.averageSalesCycle,
      pipelineVelocity: this.pipelineVelocity,
      stageDistribution: this.stageDistribution,
      sourcePerformance: this.sourcePerformance,
      teamPerformance: this.teamPerformance,
      winLossAnalysis: this.winLossAnalysis,
      efficiencyScore: this.getEfficiencyScore(),
      averageDealSize: this.getAverageDealSize(),
      weightedAverageDealSize: this.getWeightedAverageDealSize(),
      performanceSummary: this.getPerformanceSummary(),
      calculatedAt: this.calculatedAt.toISOString(),
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): PipelineMetrics {
    return new PipelineMetrics(
      data.totalLeads,
      data.qualifiedLeads,
      data.convertedLeads,
      data.totalValue,
      data.weightedValue,
      data.conversionRate,
      data.qualificationRate,
      data.averageSalesCycle,
      data.pipelineVelocity,
      data.stageDistribution,
      data.sourcePerformance,
      data.teamPerformance,
      data.winLossAnalysis,
      new Date(data.calculatedAt),
    );
  }

  /**
   * Create default metrics
   */
  static createDefault(): PipelineMetrics {
    return new PipelineMetrics(
      0, 0, 0, 0, 0, 0, 0, 0, 0,
      [], [], {}, { winRate: 0, lossRate: 0, winReasons: [], lossReasons: [] },
      new Date(),
    );
  }
}

/**
 * Supporting interfaces
 */
export interface StageDistribution {
  stageId: number;
  stageName: string;
  count: number;
  value: number;
  percentage?: number;
}

export interface SourcePerformance {
  source: string;
  count: number;
  conversionRate: number;
  averageValue: number;
  totalValue?: number;
}

export interface TeamPerformance {
  [teamId: string]: {
    teamName: string;
    leadCount: number;
    conversionRate: number;
    averageValue: number;
    totalValue: number;
  };
}

export interface WinLossAnalysis {
  winRate: number;
  lossRate: number;
  winReasons: Array<{ reason: string; count: number; percentage?: number }>;
  lossReasons: Array<{ reason: string; count: number; percentage?: number }>;
}

export interface MetricsComparison {
  totalLeadsChange: number;
  conversionRateChange: number;
  qualificationRateChange: number;
  averageSalesCycleChange: number;
  pipelineVelocityChange: number;
  totalValueChange: number;
  weightedValueChange: number;
  efficiencyScoreChange: number;
}
