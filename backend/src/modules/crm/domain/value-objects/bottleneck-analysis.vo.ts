/**
 * Bottleneck Analysis Value Object
 * Represents comprehensive analysis of pipeline bottlenecks and their impact
 */
export class BottleneckAnalysis {
  constructor(
    public readonly bottlenecks: Bottleneck[],
    public readonly totalImpact: BottleneckImpact,
    public readonly prioritizedBottlenecks: Bottleneck[],
    public readonly actionPlan: ActionPlan[],
    public readonly analyzedAt: Date,
  ) {
    this.validateAnalysis();
  }

  private validateAnalysis(): void {
    if (!Array.isArray(this.bottlenecks)) {
      throw new Error('Bottlenecks must be an array');
    }
    
    if (!this.totalImpact) {
      throw new Error('Total impact is required');
    }
  }

  /**
   * Get critical bottlenecks (high severity)
   */
  getCriticalBottlenecks(): Bottleneck[] {
    return this.bottlenecks.filter(bottleneck => bottleneck.severity === 'high');
  }

  /**
   * Get bottlenecks by type
   */
  getBottlenecksByType(type: string): Bottleneck[] {
    return this.bottlenecks.filter(bottleneck => bottleneck.type === type);
  }

  /**
   * Get overall bottleneck severity score (0-100)
   */
  getSeverityScore(): number {
    if (this.bottlenecks.length === 0) return 0;
    
    const severityWeights = { low: 1, medium: 3, high: 5 };
    const totalSeverity = this.bottlenecks.reduce((sum, bottleneck) => {
      return sum + severityWeights[bottleneck.severity];
    }, 0);
    
    // Normalize to 0-100 scale (assuming max 20 bottlenecks with high severity)
    const maxPossibleSeverity = 20 * severityWeights.high;
    return Math.min(100, (totalSeverity / maxPossibleSeverity) * 100);
  }

  /**
   * Get bottleneck health grade
   */
  getHealthGrade(): string {
    const score = this.getSeverityScore();
    if (score <= 20) return 'A';
    if (score <= 40) return 'B';
    if (score <= 60) return 'C';
    if (score <= 80) return 'D';
    return 'F';
  }

  /**
   * Get most impactful bottleneck
   */
  getMostImpactfulBottleneck(): Bottleneck | null {
    if (this.bottlenecks.length === 0) return null;
    
    return this.bottlenecks.reduce((most, current) => 
      current.affectedLeads > most.affectedLeads ? current : most
    );
  }

  /**
   * Get bottlenecks requiring immediate attention
   */
  getUrgentBottlenecks(): Bottleneck[] {
    return this.bottlenecks.filter(bottleneck => 
      bottleneck.severity === 'high' || bottleneck.affectedLeads > 50
    );
  }

  /**
   * Get estimated resolution time
   */
  getEstimatedResolutionTime(): ResolutionTimeEstimate {
    const urgentCount = this.getUrgentBottlenecks().length;
    const totalCount = this.bottlenecks.length;
    
    // Estimate based on bottleneck count and severity
    let weeks = 0;
    this.bottlenecks.forEach(bottleneck => {
      switch (bottleneck.severity) {
        case 'high': weeks += 3; break;
        case 'medium': weeks += 2; break;
        case 'low': weeks += 1; break;
      }
    });
    
    return {
      totalWeeks: Math.min(weeks, 52), // Cap at 1 year
      urgentBottlenecks: urgentCount,
      parallelResolution: Math.ceil(weeks / 2), // Assuming some parallel work
      phases: this.generateResolutionPhases(),
    };
  }

  /**
   * Get bottleneck trends
   */
  getBottleneckTrends(): BottleneckTrends {
    // This would typically compare with historical data
    return {
      newBottlenecks: this.bottlenecks.filter(b => b.isNew).length,
      resolvedBottlenecks: 0, // Would come from historical comparison
      worsening: this.bottlenecks.filter(b => b.trend === 'worsening').length,
      improving: this.bottlenecks.filter(b => b.trend === 'improving').length,
      overallTrend: this.calculateOverallTrend(),
    };
  }

  /**
   * Get ROI of fixing bottlenecks
   */
  getResolutionROI(): ResolutionROI {
    const potentialRevenue = this.bottlenecks.reduce((sum, bottleneck) => {
      return sum + (bottleneck.potentialRevenue || 0);
    }, 0);
    
    const estimatedCost = this.bottlenecks.reduce((sum, bottleneck) => {
      return sum + (bottleneck.resolutionCost || 0);
    }, 0);
    
    const roi = estimatedCost > 0 ? ((potentialRevenue - estimatedCost) / estimatedCost) * 100 : 0;
    
    return {
      potentialRevenue,
      estimatedCost,
      roi,
      paybackPeriod: this.calculatePaybackPeriod(potentialRevenue, estimatedCost),
      priority: roi > 200 ? 'high' : roi > 100 ? 'medium' : 'low',
    };
  }

  /**
   * Get bottleneck summary for dashboard
   */
  getBottleneckSummary(): BottleneckSummary {
    const critical = this.getCriticalBottlenecks();
    const urgent = this.getUrgentBottlenecks();
    const mostImpactful = this.getMostImpactfulBottleneck();
    
    return {
      totalBottlenecks: this.bottlenecks.length,
      criticalCount: critical.length,
      urgentCount: urgent.length,
      severityScore: this.getSeverityScore(),
      healthGrade: this.getHealthGrade(),
      mostImpactfulType: mostImpactful?.type,
      totalAffectedLeads: this.totalImpact.delayedDeals,
      estimatedRevenueLoss: this.totalImpact.lostRevenue,
      estimatedResolutionWeeks: this.getEstimatedResolutionTime().parallelResolution,
    };
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      bottlenecks: this.bottlenecks,
      totalImpact: this.totalImpact,
      prioritizedBottlenecks: this.prioritizedBottlenecks,
      actionPlan: this.actionPlan,
      severityScore: this.getSeverityScore(),
      healthGrade: this.getHealthGrade(),
      criticalBottlenecks: this.getCriticalBottlenecks(),
      urgentBottlenecks: this.getUrgentBottlenecks(),
      mostImpactfulBottleneck: this.getMostImpactfulBottleneck(),
      resolutionTimeEstimate: this.getEstimatedResolutionTime(),
      bottleneckTrends: this.getBottleneckTrends(),
      resolutionROI: this.getResolutionROI(),
      bottleneckSummary: this.getBottleneckSummary(),
      analyzedAt: this.analyzedAt.toISOString(),
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): BottleneckAnalysis {
    return new BottleneckAnalysis(
      data.bottlenecks,
      data.totalImpact,
      data.prioritizedBottlenecks,
      data.actionPlan,
      new Date(data.analyzedAt),
    );
  }

  /**
   * Private helper methods
   */
  private generateResolutionPhases(): ResolutionPhase[] {
    const phases: ResolutionPhase[] = [];
    
    // Phase 1: Critical bottlenecks
    const critical = this.getCriticalBottlenecks();
    if (critical.length > 0) {
      phases.push({
        phase: 1,
        name: 'Critical Issues',
        bottlenecks: critical.map(b => b.type),
        estimatedWeeks: critical.length * 2,
        priority: 'high',
      });
    }
    
    // Phase 2: High-impact bottlenecks
    const highImpact = this.bottlenecks.filter(b => 
      b.severity === 'medium' && b.affectedLeads > 20
    );
    if (highImpact.length > 0) {
      phases.push({
        phase: 2,
        name: 'High Impact Issues',
        bottlenecks: highImpact.map(b => b.type),
        estimatedWeeks: highImpact.length * 1.5,
        priority: 'medium',
      });
    }
    
    // Phase 3: Remaining bottlenecks
    const remaining = this.bottlenecks.filter(b => 
      b.severity === 'low' || (b.severity === 'medium' && b.affectedLeads <= 20)
    );
    if (remaining.length > 0) {
      phases.push({
        phase: 3,
        name: 'Optimization',
        bottlenecks: remaining.map(b => b.type),
        estimatedWeeks: remaining.length,
        priority: 'low',
      });
    }
    
    return phases;
  }

  private calculateOverallTrend(): 'improving' | 'stable' | 'worsening' {
    const worsening = this.bottlenecks.filter(b => b.trend === 'worsening').length;
    const improving = this.bottlenecks.filter(b => b.trend === 'improving').length;
    
    if (worsening > improving) return 'worsening';
    if (improving > worsening) return 'improving';
    return 'stable';
  }

  private calculatePaybackPeriod(revenue: number, cost: number): number {
    // Simplified payback calculation (months)
    if (cost === 0 || revenue === 0) return 0;
    const monthlyBenefit = revenue / 12; // Assume annual revenue spread over 12 months
    return cost / monthlyBenefit;
  }
}

/**
 * Supporting interfaces
 */
export interface Bottleneck {
  type: string;
  stageId?: number;
  stageName?: string;
  severity: 'low' | 'medium' | 'high';
  affectedLeads: number;
  averageDelay?: number;
  recommendations: string[];
  potentialRevenue?: number;
  resolutionCost?: number;
  isNew?: boolean;
  trend?: 'improving' | 'stable' | 'worsening';
}

export interface BottleneckImpact {
  delayedDeals: number;
  lostRevenue: number;
  additionalMetrics?: Record<string, number>;
}

export interface ActionPlan {
  bottleneck: string;
  action: string;
  priority: 'low' | 'medium' | 'high';
  timeline: string;
  owner?: string;
  resources?: string[];
}

export interface ResolutionTimeEstimate {
  totalWeeks: number;
  urgentBottlenecks: number;
  parallelResolution: number;
  phases: ResolutionPhase[];
}

export interface ResolutionPhase {
  phase: number;
  name: string;
  bottlenecks: string[];
  estimatedWeeks: number;
  priority: 'low' | 'medium' | 'high';
}

export interface BottleneckTrends {
  newBottlenecks: number;
  resolvedBottlenecks: number;
  worsening: number;
  improving: number;
  overallTrend: 'improving' | 'stable' | 'worsening';
}

export interface ResolutionROI {
  potentialRevenue: number;
  estimatedCost: number;
  roi: number;
  paybackPeriod: number;
  priority: 'low' | 'medium' | 'high';
}

export interface BottleneckSummary {
  totalBottlenecks: number;
  criticalCount: number;
  urgentCount: number;
  severityScore: number;
  healthGrade: string;
  mostImpactfulType?: string;
  totalAffectedLeads: number;
  estimatedRevenueLoss: number;
  estimatedResolutionWeeks: number;
}
