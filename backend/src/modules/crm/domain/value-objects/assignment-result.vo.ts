/**
 * Assignment Result Value Object
 * Represents the result of an auto-assignment operation
 */
export class AssignmentResult {
  constructor(
    public readonly assignedUserId: number | null,
    public readonly teamId: number | null,
    public readonly success: boolean,
    public readonly message: string,
    public readonly reasons: string[],
    public readonly assignedAt: Date,
    public readonly metadata: Record<string, any> = {},
  ) {
    this.validateResult();
  }

  private validateResult(): void {
    if (!this.message || this.message.trim().length === 0) {
      throw new Error('Assignment result must have a message');
    }
    
    if (!Array.isArray(this.reasons)) {
      throw new Error('Assignment result reasons must be an array');
    }
  }

  /**
   * Check if assignment was successful
   */
  isSuccessful(): boolean {
    return this.success && this.assignedUserId !== null;
  }

  /**
   * Get assignment confidence score
   */
  getConfidenceScore(): number {
    return this.metadata.score || 0;
  }

  /**
   * Get primary reason for assignment
   */
  getPrimaryReason(): string | null {
    return this.reasons.length > 0 ? this.reasons[0] : null;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      assignedUserId: this.assignedUserId,
      teamId: this.teamId,
      success: this.success,
      message: this.message,
      reasons: this.reasons,
      assignedAt: this.assignedAt.toISOString(),
      metadata: this.metadata,
      isSuccessful: this.isSuccessful(),
      confidenceScore: this.getConfidenceScore(),
      primaryReason: this.getPrimaryReason(),
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): AssignmentResult {
    return new AssignmentResult(
      data.assignedUserId,
      data.teamId,
      data.success,
      data.message,
      data.reasons,
      new Date(data.assignedAt),
      data.metadata,
    );
  }

  /**
   * Create successful result
   */
  static success(
    assignedUserId: number,
    teamId: number | null,
    reasons: string[],
    metadata: Record<string, any> = {},
  ): AssignmentResult {
    return new AssignmentResult(
      assignedUserId,
      teamId,
      true,
      'Successfully assigned',
      reasons,
      new Date(),
      metadata,
    );
  }

  /**
   * Create failed result
   */
  static failure(
    message: string,
    reasons: string[] = [],
    metadata: Record<string, any> = {},
  ): AssignmentResult {
    return new AssignmentResult(
      null,
      null,
      false,
      message,
      reasons,
      new Date(),
      metadata,
    );
  }
}
