/**
 * Lead Status Value Object
 * Represents the status of a lead in the CRM system
 */
export class LeadStatus {
  private static readonly VALID_STATUSES = [
    'new',
    'qualified',
    'proposition',
    'won',
    'lost',
    'cancelled',
  ] as const;

  private static readonly STATUS_HIERARCHY = {
    'new': 1,
    'qualified': 2,
    'proposition': 3,
    'won': 4,
    'lost': 0,
    'cancelled': 0,
  };

  constructor(public readonly value: string) {
    this.validateStatus();
  }

  private validateStatus(): void {
    if (!LeadStatus.VALID_STATUSES.includes(this.value as any)) {
      throw new Error(`Invalid lead status: ${this.value}`);
    }
  }

  /**
   * Check if this status represents a progression from another status
   */
  isProgression(previousStatus: LeadStatus): boolean {
    const currentLevel = LeadStatus.STATUS_HIERARCHY[this.value];
    const previousLevel = LeadStatus.STATUS_HIERARCHY[previousStatus.value];
    
    return currentLevel > previousLevel;
  }

  /**
   * Check if this status represents a regression from another status
   */
  isRegression(previousStatus: LeadStatus): boolean {
    const currentLevel = LeadStatus.STATUS_HIERARCHY[this.value];
    const previousLevel = LeadStatus.STATUS_HIERARCHY[previousStatus.value];
    
    return currentLevel < previousLevel && currentLevel > 0 && previousLevel > 0;
  }

  /**
   * Check if lead is active (not won, lost, or cancelled)
   */
  isActive(): boolean {
    return !['won', 'lost', 'cancelled'].includes(this.value);
  }

  /**
   * Check if lead is closed (won or lost)
   */
  isClosed(): boolean {
    return ['won', 'lost'].includes(this.value);
  }

  /**
   * Check if lead is won
   */
  isWon(): boolean {
    return this.value === 'won';
  }

  /**
   * Check if lead is lost
   */
  isLost(): boolean {
    return this.value === 'lost';
  }

  /**
   * Check if lead is cancelled
   */
  isCancelled(): boolean {
    return this.value === 'cancelled';
  }

  /**
   * Check if lead is qualified
   */
  isQualified(): boolean {
    return ['qualified', 'proposition', 'won'].includes(this.value);
  }

  /**
   * Check if lead is converted
   */
  isConverted(): boolean {
    return this.value === 'won';
  }

  /**
   * Check if lead can be converted to opportunity
   */
  canConvert(): boolean {
    return ['qualified', 'proposition'].includes(this.value);
  }

  /**
   * Check if status is terminal (no further transitions possible)
   */
  isTerminal(): boolean {
    return ['won', 'lost', 'cancelled'].includes(this.value);
  }

  /**
   * Get score weight for lead scoring algorithm
   */
  getScoreWeight(): number {
    const weights = {
      'new': 1,
      'qualified': 1.5,
      'proposition': 2,
      'won': 3,
      'lost': 0,
      'cancelled': 0,
    };

    return weights[this.value] || 1;
  }

  /**
   * Get status display name
   */
  getDisplayName(): string {
    const displayNames = {
      'new': 'New',
      'qualified': 'Qualified',
      'proposition': 'Proposition',
      'won': 'Won',
      'lost': 'Lost',
      'cancelled': 'Cancelled',
    };
    
    return displayNames[this.value] || this.value;
  }

  /**
   * Get status color for UI
   */
  getColor(): string {
    const colors = {
      'new': '#6B7280',      // Gray
      'qualified': '#3B82F6', // Blue
      'proposition': '#F59E0B', // Amber
      'won': '#10B981',       // Green
      'lost': '#EF4444',      // Red
      'cancelled': '#6B7280', // Gray
    };
    
    return colors[this.value] || '#6B7280';
  }

  /**
   * Get next possible statuses
   */
  getNextPossibleStatuses(): LeadStatus[] {
    const transitions = {
      'new': ['qualified', 'lost', 'cancelled'],
      'qualified': ['proposition', 'lost', 'cancelled'],
      'proposition': ['won', 'lost', 'cancelled'],
      'won': [],
      'lost': [],
      'cancelled': [],
    };
    
    return (transitions[this.value] || []).map(status => new LeadStatus(status));
  }

  /**
   * Check if transition to another status is valid
   */
  canTransitionTo(newStatus: LeadStatus): boolean {
    const possibleStatuses = this.getNextPossibleStatuses();
    return possibleStatuses.some(status => status.value === newStatus.value);
  }

  /**
   * Get status priority for sorting
   */
  getPriority(): number {
    return LeadStatus.STATUS_HIERARCHY[this.value] || 0;
  }

  /**
   * Compare with another status
   */
  equals(other: LeadStatus): boolean {
    return this.value === other.value;
  }

  /**
   * Convert to string
   */
  toString(): string {
    return this.value;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      value: this.value,
      displayName: this.getDisplayName(),
      color: this.getColor(),
      priority: this.getPriority(),
      isActive: this.isActive(),
      isClosed: this.isClosed(),
      isWon: this.isWon(),
      isLost: this.isLost(),
      isCancelled: this.isCancelled(),
      nextPossibleStatuses: this.getNextPossibleStatuses().map(s => s.value),
    };
  }

  /**
   * Create from string value
   */
  static fromString(value: string): LeadStatus {
    return new LeadStatus(value);
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): LeadStatus {
    return new LeadStatus(data.value);
  }

  /**
   * Get all valid statuses
   */
  static getAllStatuses(): LeadStatus[] {
    return LeadStatus.VALID_STATUSES.map(status => new LeadStatus(status));
  }

  /**
   * Create new status
   */
  static new(): LeadStatus {
    return new LeadStatus('new');
  }

  /**
   * Create qualified status
   */
  static qualified(): LeadStatus {
    return new LeadStatus('qualified');
  }

  /**
   * Create proposition status
   */
  static proposition(): LeadStatus {
    return new LeadStatus('proposition');
  }

  /**
   * Create won status
   */
  static won(): LeadStatus {
    return new LeadStatus('won');
  }

  /**
   * Create lost status
   */
  static lost(): LeadStatus {
    return new LeadStatus('lost');
  }

  /**
   * Create cancelled status
   */
  static cancelled(): LeadStatus {
    return new LeadStatus('cancelled');
  }
}
