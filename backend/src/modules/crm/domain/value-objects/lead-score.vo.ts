import { ScoringFactor } from './scoring-factor.vo';

/**
 * Lead Score Value Object
 * Represents a comprehensive lead score with all contributing factors
 */
export class LeadScore {
  constructor(
    public readonly score: number,
    public readonly factors: ScoringFactor[],
    public readonly grade: string,
    public readonly recommendedActions: string[],
    public readonly calculatedAt: Date,
    public readonly metadata: Record<string, any> = {},
  ) {
    this.validateScore();
  }

  private validateScore(): void {
    if (this.score < 0 || this.score > 100) {
      throw new Error('Lead score must be between 0 and 100');
    }
    
    if (!this.factors || this.factors.length === 0) {
      throw new Error('Lead score must have at least one scoring factor');
    }
    
    if (!this.grade) {
      throw new Error('Lead score must have a grade');
    }
  }

  /**
   * Check if this is a high-priority lead
   */
  isHighPriority(): boolean {
    return this.score >= 80;
  }

  /**
   * Check if this is a medium-priority lead
   */
  isMediumPriority(): boolean {
    return this.score >= 60 && this.score < 80;
  }

  /**
   * Check if this is a low-priority lead
   */
  isLowPriority(): boolean {
    return this.score < 60;
  }

  /**
   * Get the confidence level of this score
   */
  getConfidence(): number {
    return this.metadata.confidence || 50;
  }

  /**
   * Check if this score is reliable (high confidence)
   */
  isReliable(): boolean {
    return this.getConfidence() >= 70;
  }

  /**
   * Get the primary factor contributing to the score
   */
  getPrimaryFactor(): ScoringFactor | null {
    if (this.factors.length === 0) return null;
    
    return this.factors.reduce((max, factor) => 
      (factor.weightedScore > max.weightedScore) ? factor : max
    );
  }

  /**
   * Get factors that are below threshold (need improvement)
   */
  getWeakFactors(threshold: number = 50): ScoringFactor[] {
    return this.factors.filter(factor => factor.score < threshold);
  }

  /**
   * Get factors that are above threshold (strengths)
   */
  getStrongFactors(threshold: number = 70): ScoringFactor[] {
    return this.factors.filter(factor => factor.score >= threshold);
  }

  /**
   * Calculate the improvement potential
   */
  getImprovementPotential(): number {
    const weakFactors = this.getWeakFactors();
    if (weakFactors.length === 0) return 0;
    
    // Calculate potential score if weak factors were improved to 70
    let potentialIncrease = 0;
    weakFactors.forEach(factor => {
      const improvement = Math.max(0, 70 - factor.score);
      potentialIncrease += improvement * factor.weight;
    });
    
    return Math.min(100 - this.score, potentialIncrease);
  }

  /**
   * Get urgency level based on score and metadata
   */
  getUrgencyLevel(): 'critical' | 'high' | 'medium' | 'low' {
    if (this.score >= 90) return 'critical';
    if (this.score >= 80) return 'high';
    if (this.score >= 60) return 'medium';
    return 'low';
  }

  /**
   * Get recommended follow-up timeframe
   */
  getRecommendedFollowUpTime(): string {
    switch (this.getUrgencyLevel()) {
      case 'critical': return 'within 1 hour';
      case 'high': return 'within 24 hours';
      case 'medium': return 'within 3 days';
      case 'low': return 'within 1 week';
      default: return 'within 1 week';
    }
  }

  /**
   * Check if score has changed significantly from previous score
   */
  hasSignificantChange(previousScore?: LeadScore): boolean {
    if (!previousScore) return true;
    
    const scoreDifference = Math.abs(this.score - previousScore.score);
    return scoreDifference >= 10; // 10+ point change is significant
  }

  /**
   * Get score trend compared to previous score
   */
  getTrend(previousScore?: LeadScore): 'up' | 'down' | 'stable' | 'new' {
    if (!previousScore) return 'new';
    
    const difference = this.score - previousScore.score;
    if (difference > 5) return 'up';
    if (difference < -5) return 'down';
    return 'stable';
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      score: this.score,
      grade: this.grade,
      urgencyLevel: this.getUrgencyLevel(),
      confidence: this.getConfidence(),
      isHighPriority: this.isHighPriority(),
      recommendedActions: this.recommendedActions,
      recommendedFollowUpTime: this.getRecommendedFollowUpTime(),
      improvementPotential: this.getImprovementPotential(),
      factors: this.factors.map(factor => factor.toPlainObject()),
      calculatedAt: this.calculatedAt.toISOString(),
      metadata: this.metadata,
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): LeadScore {
    return new LeadScore(
      data.score,
      data.factors.map((f: any) => ScoringFactor.fromPlainObject(f)),
      data.grade,
      data.recommendedActions,
      new Date(data.calculatedAt),
      data.metadata,
    );
  }

  /**
   * Compare two lead scores
   */
  static compare(a: LeadScore, b: LeadScore): number {
    // Primary sort by score (descending)
    if (a.score !== b.score) {
      return b.score - a.score;
    }
    
    // Secondary sort by confidence (descending)
    const aConfidence = a.getConfidence();
    const bConfidence = b.getConfidence();
    if (aConfidence !== bConfidence) {
      return bConfidence - aConfidence;
    }
    
    // Tertiary sort by calculation time (most recent first)
    return b.calculatedAt.getTime() - a.calculatedAt.getTime();
  }

  /**
   * Create a default/placeholder score
   */
  static createDefault(): LeadScore {
    return new LeadScore(
      50,
      [new ScoringFactor('default', 50, 1.0)],
      'C',
      ['Review and complete lead information'],
      new Date(),
      { isDefault: true },
    );
  }

  /**
   * Validate score data
   */
  static isValid(data: any): boolean {
    return (
      typeof data.score === 'number' &&
      data.score >= 0 &&
      data.score <= 100 &&
      Array.isArray(data.factors) &&
      data.factors.length > 0 &&
      typeof data.grade === 'string' &&
      Array.isArray(data.recommendedActions) &&
      data.calculatedAt instanceof Date
    );
  }
}
