/**
 * Pipeline Forecast Value Object
 * Represents pipeline forecasting with multiple scenarios and confidence levels
 */
export class PipelineForecast {
  constructor(
    public readonly currentPipelineValue: number,
    public readonly weightedPipelineValue: number,
    public readonly forecasts: ForecastPeriod[],
    public readonly trendAnalysis: TrendAnalysis,
    public readonly riskFactors: string[],
    public readonly generatedAt: Date,
  ) {
    this.validateForecast();
  }

  private validateForecast(): void {
    if (this.currentPipelineValue < 0) {
      throw new Error('Current pipeline value cannot be negative');
    }
    
    if (this.weightedPipelineValue < 0) {
      throw new Error('Weighted pipeline value cannot be negative');
    }
    
    if (!this.forecasts || this.forecasts.length === 0) {
      throw new Error('Forecast must include at least one period');
    }
  }

  /**
   * Get forecast for specific period
   */
  getForecastForPeriod(days: number): ForecastPeriod | null {
    return this.forecasts.find(f => f.period === `${days} days`) || null;
  }

  /**
   * Get next 30-day forecast
   */
  getNext30DayForecast(): ForecastPeriod | null {
    return this.getForecastForPeriod(30);
  }

  /**
   * Get next quarter forecast
   */
  getNextQuarterForecast(): ForecastPeriod | null {
    return this.getForecastForPeriod(90);
  }

  /**
   * Get overall forecast confidence
   */
  getOverallConfidence(): number {
    if (this.forecasts.length === 0) return 0;
    
    const totalConfidence = this.forecasts.reduce((sum, f) => sum + f.confidence, 0);
    return Math.round(totalConfidence / this.forecasts.length);
  }

  /**
   * Get forecast accuracy grade
   */
  getAccuracyGrade(): string {
    const confidence = this.getOverallConfidence();
    if (confidence >= 90) return 'A+';
    if (confidence >= 80) return 'A';
    if (confidence >= 70) return 'B';
    if (confidence >= 60) return 'C';
    if (confidence >= 50) return 'D';
    return 'F';
  }

  /**
   * Get growth projection
   */
  getGrowthProjection(): GrowthProjection {
    const next30 = this.getNext30DayForecast();
    const next90 = this.getNextQuarterForecast();
    
    if (!next30 || !next90) {
      return {
        monthlyGrowth: 0,
        quarterlyGrowth: 0,
        trend: 'stable',
      };
    }
    
    const monthlyGrowth = this.currentPipelineValue > 0 
      ? ((next30.expectedValue - this.currentPipelineValue) / this.currentPipelineValue) * 100
      : 0;
    
    const quarterlyGrowth = this.currentPipelineValue > 0
      ? ((next90.expectedValue - this.currentPipelineValue) / this.currentPipelineValue) * 100
      : 0;
    
    let trend: 'growing' | 'declining' | 'stable' = 'stable';
    if (quarterlyGrowth > 5) trend = 'growing';
    else if (quarterlyGrowth < -5) trend = 'declining';
    
    return {
      monthlyGrowth,
      quarterlyGrowth,
      trend,
    };
  }

  /**
   * Get risk assessment
   */
  getRiskAssessment(): RiskAssessment {
    const riskLevel = this.calculateRiskLevel();
    const confidence = this.getOverallConfidence();
    
    return {
      level: riskLevel,
      factors: this.riskFactors,
      confidence,
      mitigation: this.generateMitigationStrategies(),
    };
  }

  /**
   * Get scenario analysis
   */
  getScenarioAnalysis(): ScenarioAnalysis {
    const scenarios: Record<string, number> = {};
    
    this.forecasts.forEach(forecast => {
      const period = forecast.period;
      scenarios[`${period}_optimistic`] = forecast.scenarios.optimistic;
      scenarios[`${period}_realistic`] = forecast.scenarios.realistic;
      scenarios[`${period}_pessimistic`] = forecast.scenarios.pessimistic;
    });
    
    return {
      scenarios,
      mostLikely: this.getMostLikelyScenario(),
      bestCase: this.getBestCaseScenario(),
      worstCase: this.getWorstCaseScenario(),
    };
  }

  /**
   * Get forecast summary for dashboard
   */
  getForecastSummary(): ForecastSummary {
    const next30 = this.getNext30DayForecast();
    const growth = this.getGrowthProjection();
    const risk = this.getRiskAssessment();
    
    return {
      currentValue: this.currentPipelineValue,
      weightedValue: this.weightedPipelineValue,
      next30DayValue: next30?.expectedValue || 0,
      next30DayClosures: next30?.expectedClosures || 0,
      confidence: this.getOverallConfidence(),
      accuracyGrade: this.getAccuracyGrade(),
      trend: growth.trend,
      monthlyGrowth: growth.monthlyGrowth,
      riskLevel: risk.level,
      riskFactorCount: this.riskFactors.length,
    };
  }

  /**
   * Check if forecast is reliable
   */
  isReliable(): boolean {
    return this.getOverallConfidence() >= 70 && this.riskFactors.length <= 3;
  }

  /**
   * Get forecast alerts
   */
  getForecastAlerts(): ForecastAlert[] {
    const alerts: ForecastAlert[] = [];
    
    // Low confidence alert
    if (this.getOverallConfidence() < 60) {
      alerts.push({
        type: 'low_confidence',
        severity: 'warning',
        message: 'Forecast confidence is below 60%',
        recommendation: 'Review pipeline data quality and update probabilities',
      });
    }
    
    // High risk alert
    if (this.riskFactors.length > 5) {
      alerts.push({
        type: 'high_risk',
        severity: 'error',
        message: `${this.riskFactors.length} risk factors identified`,
        recommendation: 'Address critical risk factors to improve forecast reliability',
      });
    }
    
    // Declining trend alert
    const growth = this.getGrowthProjection();
    if (growth.trend === 'declining' && growth.quarterlyGrowth < -10) {
      alerts.push({
        type: 'declining_trend',
        severity: 'warning',
        message: 'Pipeline showing declining trend',
        recommendation: 'Increase lead generation and improve conversion rates',
      });
    }
    
    return alerts;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      currentPipelineValue: this.currentPipelineValue,
      weightedPipelineValue: this.weightedPipelineValue,
      forecasts: this.forecasts,
      trendAnalysis: this.trendAnalysis,
      riskFactors: this.riskFactors,
      overallConfidence: this.getOverallConfidence(),
      accuracyGrade: this.getAccuracyGrade(),
      growthProjection: this.getGrowthProjection(),
      riskAssessment: this.getRiskAssessment(),
      scenarioAnalysis: this.getScenarioAnalysis(),
      forecastSummary: this.getForecastSummary(),
      forecastAlerts: this.getForecastAlerts(),
      isReliable: this.isReliable(),
      generatedAt: this.generatedAt.toISOString(),
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): PipelineForecast {
    return new PipelineForecast(
      data.currentPipelineValue,
      data.weightedPipelineValue,
      data.forecasts,
      data.trendAnalysis,
      data.riskFactors,
      new Date(data.generatedAt),
    );
  }

  /**
   * Private helper methods
   */
  private calculateRiskLevel(): 'low' | 'medium' | 'high' {
    const riskCount = this.riskFactors.length;
    const confidence = this.getOverallConfidence();
    
    if (riskCount > 5 || confidence < 50) return 'high';
    if (riskCount > 2 || confidence < 70) return 'medium';
    return 'low';
  }

  private generateMitigationStrategies(): string[] {
    const strategies: string[] = [];
    
    if (this.riskFactors.includes('Market uncertainty')) {
      strategies.push('Diversify target markets and customer segments');
    }
    
    if (this.riskFactors.includes('Seasonal variations')) {
      strategies.push('Develop counter-seasonal products or services');
    }
    
    if (this.getOverallConfidence() < 70) {
      strategies.push('Improve data quality and update lead probabilities');
    }
    
    return strategies;
  }

  private getMostLikelyScenario(): string {
    const next30 = this.getNext30DayForecast();
    return next30 ? `${next30.scenarios.realistic} in 30 days` : 'No forecast available';
  }

  private getBestCaseScenario(): string {
    const next30 = this.getNext30DayForecast();
    return next30 ? `${next30.scenarios.optimistic} in 30 days` : 'No forecast available';
  }

  private getWorstCaseScenario(): string {
    const next30 = this.getNext30DayForecast();
    return next30 ? `${next30.scenarios.pessimistic} in 30 days` : 'No forecast available';
  }
}

/**
 * Supporting interfaces
 */
export interface ForecastPeriod {
  period: string;
  periodEnd: Date;
  expectedClosures: number;
  expectedValue: number;
  confidence: number;
  scenarios: {
    optimistic: number;
    realistic: number;
    pessimistic: number;
  };
}

export interface TrendAnalysis {
  trend: 'improving' | 'stable' | 'declining';
  growth: number;
  seasonality?: {
    pattern: string;
    impact: number;
  };
}

export interface GrowthProjection {
  monthlyGrowth: number;
  quarterlyGrowth: number;
  trend: 'growing' | 'declining' | 'stable';
}

export interface RiskAssessment {
  level: 'low' | 'medium' | 'high';
  factors: string[];
  confidence: number;
  mitigation: string[];
}

export interface ScenarioAnalysis {
  scenarios: Record<string, number>;
  mostLikely: string;
  bestCase: string;
  worstCase: string;
}

export interface ForecastSummary {
  currentValue: number;
  weightedValue: number;
  next30DayValue: number;
  next30DayClosures: number;
  confidence: number;
  accuracyGrade: string;
  trend: 'growing' | 'declining' | 'stable';
  monthlyGrowth: number;
  riskLevel: 'low' | 'medium' | 'high';
  riskFactorCount: number;
}

export interface ForecastAlert {
  type: 'low_confidence' | 'high_risk' | 'declining_trend' | 'data_quality';
  severity: 'info' | 'warning' | 'error';
  message: string;
  recommendation: string;
}
