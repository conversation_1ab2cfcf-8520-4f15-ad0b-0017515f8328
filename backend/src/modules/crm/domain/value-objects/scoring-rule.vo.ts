/**
 * Scoring Rule Value Object
 * Represents a configurable rule for lead scoring
 */
export class ScoringRule {
  constructor(
    public readonly id: string,
    public readonly name: string,
    public readonly description: string,
    public readonly factor: string,
    public readonly condition: ScoringCondition,
    public readonly scoreAdjustment: number,
    public readonly isActive: boolean = true,
    public readonly priority: number = 0,
    public readonly metadata: Record<string, any> = {},
  ) {
    this.validateRule();
  }

  private validateRule(): void {
    if (!this.id || this.id.trim().length === 0) {
      throw new Error('Scoring rule must have an ID');
    }
    
    if (!this.name || this.name.trim().length === 0) {
      throw new Error('Scoring rule must have a name');
    }
    
    if (!this.factor || this.factor.trim().length === 0) {
      throw new Error('Scoring rule must specify a factor');
    }
    
    if (this.scoreAdjustment < -100 || this.scoreAdjustment > 100) {
      throw new Error('Score adjustment must be between -100 and 100');
    }
  }

  /**
   * Check if this rule applies to the given lead data
   */
  appliesTo(leadData: Record<string, any>): boolean {
    if (!this.isActive) return false;
    
    return this.condition.evaluate(leadData);
  }

  /**
   * Apply this rule to get score adjustment
   */
  apply(leadData: Record<string, any>): number {
    if (!this.appliesTo(leadData)) return 0;
    
    return this.scoreAdjustment;
  }

  /**
   * Get rule type based on score adjustment
   */
  getRuleType(): 'bonus' | 'penalty' | 'neutral' {
    if (this.scoreAdjustment > 0) return 'bonus';
    if (this.scoreAdjustment < 0) return 'penalty';
    return 'neutral';
  }

  /**
   * Get impact level based on score adjustment magnitude
   */
  getImpactLevel(): 'high' | 'medium' | 'low' {
    const magnitude = Math.abs(this.scoreAdjustment);
    if (magnitude >= 20) return 'high';
    if (magnitude >= 10) return 'medium';
    return 'low';
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      factor: this.factor,
      condition: this.condition.toPlainObject(),
      scoreAdjustment: this.scoreAdjustment,
      isActive: this.isActive,
      priority: this.priority,
      ruleType: this.getRuleType(),
      impactLevel: this.getImpactLevel(),
      metadata: this.metadata,
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): ScoringRule {
    return new ScoringRule(
      data.id,
      data.name,
      data.description,
      data.factor,
      ScoringCondition.fromPlainObject(data.condition),
      data.scoreAdjustment,
      data.isActive,
      data.priority,
      data.metadata,
    );
  }

  /**
   * Create predefined rules
   */
  static createPredefinedRules(): ScoringRule[] {
    return [
      // High-value opportunity bonus
      new ScoringRule(
        'high-revenue-bonus',
        'High Revenue Bonus',
        'Bonus for leads with high expected revenue',
        'revenue_potential',
        new ScoringCondition('expectedRevenue', 'gte', 50000),
        15,
        true,
        1,
      ),

      // Referral source bonus
      new ScoringRule(
        'referral-source-bonus',
        'Referral Source Bonus',
        'Bonus for leads from referral sources',
        'source_quality',
        new ScoringCondition('source', 'equals', 'referral'),
        10,
        true,
        2,
      ),

      // Technology industry bonus
      new ScoringRule(
        'tech-industry-bonus',
        'Technology Industry Bonus',
        'Bonus for technology industry leads',
        'company_fit',
        new ScoringCondition('industry', 'equals', 'technology'),
        8,
        true,
        3,
      ),

      // Missing email penalty
      new ScoringRule(
        'missing-email-penalty',
        'Missing Email Penalty',
        'Penalty for leads without email',
        'contact_completeness',
        new ScoringCondition('email', 'empty', null),
        -20,
        true,
        4,
      ),

      // Recent activity bonus
      new ScoringRule(
        'recent-activity-bonus',
        'Recent Activity Bonus',
        'Bonus for recently created leads',
        'engagement_level',
        new ScoringCondition('daysSinceCreated', 'lte', 1),
        12,
        true,
        5,
      ),
    ];
  }
}

/**
 * Scoring Condition Value Object
 * Represents a condition that can be evaluated against lead data
 */
export class ScoringCondition {
  constructor(
    public readonly field: string,
    public readonly operator: ConditionOperator,
    public readonly value: any,
    public readonly logicalOperator?: 'and' | 'or',
    public readonly subConditions?: ScoringCondition[],
  ) {
    this.validateCondition();
  }

  private validateCondition(): void {
    if (!this.field || this.field.trim().length === 0) {
      throw new Error('Scoring condition must have a field');
    }
    
    if (!this.operator) {
      throw new Error('Scoring condition must have an operator');
    }
  }

  /**
   * Evaluate this condition against lead data
   */
  evaluate(leadData: Record<string, any>): boolean {
    const fieldValue = this.getFieldValue(leadData, this.field);
    const result = this.evaluateOperator(fieldValue, this.operator, this.value);

    // Handle sub-conditions if present
    if (this.subConditions && this.subConditions.length > 0) {
      const subResults = this.subConditions.map(condition => condition.evaluate(leadData));
      
      if (this.logicalOperator === 'or') {
        return result || subResults.some(r => r);
      } else {
        return result && subResults.every(r => r);
      }
    }

    return result;
  }

  private getFieldValue(data: Record<string, any>, field: string): any {
    // Support nested field access with dot notation
    const parts = field.split('.');
    let value = data;
    
    for (const part of parts) {
      if (value && typeof value === 'object') {
        value = value[part];
      } else {
        return undefined;
      }
    }
    
    return value;
  }

  private evaluateOperator(fieldValue: any, operator: ConditionOperator, compareValue: any): boolean {
    switch (operator) {
      case 'equals':
        return fieldValue === compareValue;
      
      case 'not_equals':
        return fieldValue !== compareValue;
      
      case 'gt':
        return typeof fieldValue === 'number' && fieldValue > compareValue;
      
      case 'gte':
        return typeof fieldValue === 'number' && fieldValue >= compareValue;
      
      case 'lt':
        return typeof fieldValue === 'number' && fieldValue < compareValue;
      
      case 'lte':
        return typeof fieldValue === 'number' && fieldValue <= compareValue;
      
      case 'contains':
        return typeof fieldValue === 'string' && fieldValue.toLowerCase().includes(compareValue.toLowerCase());
      
      case 'not_contains':
        return typeof fieldValue === 'string' && !fieldValue.toLowerCase().includes(compareValue.toLowerCase());
      
      case 'starts_with':
        return typeof fieldValue === 'string' && fieldValue.toLowerCase().startsWith(compareValue.toLowerCase());
      
      case 'ends_with':
        return typeof fieldValue === 'string' && fieldValue.toLowerCase().endsWith(compareValue.toLowerCase());
      
      case 'empty':
        return !fieldValue || fieldValue === '' || (Array.isArray(fieldValue) && fieldValue.length === 0);
      
      case 'not_empty':
        return !!fieldValue && fieldValue !== '' && (!Array.isArray(fieldValue) || fieldValue.length > 0);
      
      case 'in':
        return Array.isArray(compareValue) && compareValue.includes(fieldValue);
      
      case 'not_in':
        return Array.isArray(compareValue) && !compareValue.includes(fieldValue);
      
      case 'regex':
        return typeof fieldValue === 'string' && new RegExp(compareValue).test(fieldValue);
      
      default:
        throw new Error(`Unsupported operator: ${operator}`);
    }
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      field: this.field,
      operator: this.operator,
      value: this.value,
      logicalOperator: this.logicalOperator,
      subConditions: this.subConditions?.map(c => c.toPlainObject()),
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): ScoringCondition {
    return new ScoringCondition(
      data.field,
      data.operator,
      data.value,
      data.logicalOperator,
      data.subConditions?.map((c: any) => ScoringCondition.fromPlainObject(c)),
    );
  }
}

/**
 * Supported condition operators
 */
export type ConditionOperator = 
  | 'equals'
  | 'not_equals'
  | 'gt'
  | 'gte'
  | 'lt'
  | 'lte'
  | 'contains'
  | 'not_contains'
  | 'starts_with'
  | 'ends_with'
  | 'empty'
  | 'not_empty'
  | 'in'
  | 'not_in'
  | 'regex';
