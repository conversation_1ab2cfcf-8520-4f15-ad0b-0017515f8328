/**
 * Contact Info Value Object
 * Represents contact information for a lead or customer
 */
export class ContactInfo {
  constructor(
    public readonly email?: string,
    public readonly phone?: string,
    public readonly mobile?: string,
    public readonly website?: string,
    public readonly company?: string,
    public readonly jobTitle?: string,
    public readonly address?: string,
    public readonly city?: string,
    public readonly state?: string,
    public readonly country?: string,
    public readonly postalCode?: string,
    public readonly socialMedia?: SocialMediaInfo,
  ) {
    this.validateContactInfo();
  }

  private validateContactInfo(): void {
    if (this.email && !this.isValidEmail(this.email)) {
      throw new Error('Invalid email format');
    }
    
    if (this.website && !this.isValidWebsite(this.website)) {
      throw new Error('Invalid website format');
    }
  }

  /**
   * Check if contact has any information
   */
  hasAnyInfo(): boolean {
    return !!(
      this.email ||
      this.phone ||
      this.mobile ||
      this.website ||
      this.company ||
      this.jobTitle ||
      this.address ||
      this.city ||
      this.state ||
      this.country ||
      this.postalCode ||
      this.socialMedia
    );
  }

  /**
   * Check if contact has complete address
   */
  hasCompleteAddress(): boolean {
    return !!(this.address && this.city && this.country);
  }

  /**
   * Check if contact has business information
   */
  hasBusinessInfo(): boolean {
    return !!(this.company || this.jobTitle || this.website);
  }

  /**
   * Get completeness score (0-100)
   */
  getCompletenessScore(): number {
    let score = 0;
    const fields = [
      this.email,
      this.phone,
      this.mobile,
      this.website,
      this.company,
      this.jobTitle,
      this.address,
      this.city,
      this.state,
      this.country,
      this.postalCode,
    ];

    const filledFields = fields.filter(field => field && field.trim().length > 0).length;
    score = (filledFields / fields.length) * 100;

    // Bonus for social media
    if (this.socialMedia && Object.keys(this.socialMedia).length > 0) {
      score += 5;
    }

    return Math.min(100, Math.round(score));
  }

  /**
   * Get primary contact method
   */
  getPrimaryContactMethod(): 'email' | 'phone' | 'mobile' | 'website' | null {
    if (this.email) return 'email';
    if (this.mobile) return 'mobile';
    if (this.phone) return 'phone';
    if (this.website) return 'website';
    return null;
  }

  /**
   * Get formatted address
   */
  getFormattedAddress(): string {
    const parts = [
      this.address,
      this.city,
      this.state,
      this.postalCode,
      this.country,
    ].filter(part => part && part.trim().length > 0);

    return parts.join(', ');
  }

  /**
   * Get display name for company or contact
   */
  getDisplayName(): string {
    if (this.company) {
      return this.jobTitle ? `${this.company} (${this.jobTitle})` : this.company;
    }
    
    if (this.jobTitle) {
      return this.jobTitle;
    }
    
    return this.email || this.phone || this.mobile || 'Unknown Contact';
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      email: this.email,
      phone: this.phone,
      mobile: this.mobile,
      website: this.website,
      company: this.company,
      jobTitle: this.jobTitle,
      address: this.address,
      city: this.city,
      state: this.state,
      country: this.country,
      postalCode: this.postalCode,
      socialMedia: this.socialMedia,
      hasAnyInfo: this.hasAnyInfo(),
      hasCompleteAddress: this.hasCompleteAddress(),
      hasBusinessInfo: this.hasBusinessInfo(),
      completenessScore: this.getCompletenessScore(),
      primaryContactMethod: this.getPrimaryContactMethod(),
      formattedAddress: this.getFormattedAddress(),
      displayName: this.getDisplayName(),
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): ContactInfo {
    return new ContactInfo(
      data.email,
      data.phone,
      data.mobile,
      data.website,
      data.company,
      data.jobTitle,
      data.address,
      data.city,
      data.state,
      data.country,
      data.postalCode,
      data.socialMedia,
    );
  }

  /**
   * Create empty contact info
   */
  static createEmpty(): ContactInfo {
    return new ContactInfo();
  }

  /**
   * Merge with another contact info
   */
  mergeWith(other: ContactInfo): ContactInfo {
    return new ContactInfo(
      this.email || other.email,
      this.phone || other.phone,
      this.mobile || other.mobile,
      this.website || other.website,
      this.company || other.company,
      this.jobTitle || other.jobTitle,
      this.address || other.address,
      this.city || other.city,
      this.state || other.state,
      this.country || other.country,
      this.postalCode || other.postalCode,
      { ...other.socialMedia, ...this.socialMedia },
    );
  }

  /**
   * Private validation methods
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidWebsite(website: string): boolean {
    try {
      new URL(website.startsWith('http') ? website : `https://${website}`);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * Social Media Info Interface
 */
export interface SocialMediaInfo {
  linkedin?: string;
  twitter?: string;
  facebook?: string;
  instagram?: string;
  youtube?: string;
  github?: string;
  [platform: string]: string | undefined;
}
