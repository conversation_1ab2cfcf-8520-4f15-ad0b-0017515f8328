/**
 * Conversion Funnel Value Object
 * Represents the conversion funnel analysis with stage-by-stage breakdown
 */
export class ConversionFunnel {
  constructor(
    public readonly steps: FunnelStep[],
    public readonly overallConversionRate: number,
    public readonly bottlenecks: FunnelBottleneck[],
    public readonly recommendations: string[],
    public readonly analyzedAt: Date,
  ) {
    this.validateFunnel();
  }

  private validateFunnel(): void {
    if (!this.steps || this.steps.length === 0) {
      throw new Error('Conversion funnel must have at least one step');
    }
    
    if (this.overallConversionRate < 0 || this.overallConversionRate > 100) {
      throw new Error('Overall conversion rate must be between 0 and 100');
    }
  }

  /**
   * Get the step with the highest drop-off rate
   */
  getWorstPerformingStep(): FunnelStep | null {
    if (this.steps.length === 0) return null;
    
    return this.steps.reduce((worst, current) => 
      current.dropOffRate > worst.dropOffRate ? current : worst
    );
  }

  /**
   * Get the step with the best conversion rate
   */
  getBestPerformingStep(): FunnelStep | null {
    if (this.steps.length === 0) return null;
    
    return this.steps.reduce((best, current) => 
      current.conversionRate > best.conversionRate ? current : best
    );
  }

  /**
   * Get total leads at the top of the funnel
   */
  getTotalLeadsAtTop(): number {
    return this.steps.length > 0 ? this.steps[0].leadsCount : 0;
  }

  /**
   * Get total conversions at the bottom of the funnel
   */
  getTotalConversions(): number {
    return this.steps.length > 0 ? this.steps[this.steps.length - 1].leadsCount : 0;
  }

  /**
   * Get total value at each stage
   */
  getTotalValueProgression(): number[] {
    return this.steps.map(step => step.value);
  }

  /**
   * Get average time in funnel
   */
  getAverageTimeInFunnel(): number {
    if (this.steps.length === 0) return 0;
    
    const totalTime = this.steps.reduce((sum, step) => sum + step.averageTimeInStage, 0);
    return totalTime;
  }

  /**
   * Get funnel efficiency score (0-100)
   */
  getEfficiencyScore(): number {
    if (this.steps.length === 0) return 0;
    
    // Base score from overall conversion rate
    let score = this.overallConversionRate;
    
    // Penalty for bottlenecks
    const bottleneckPenalty = this.bottlenecks.length * 5;
    score -= bottleneckPenalty;
    
    // Bonus for consistent conversion rates (low variance)
    const conversionRates = this.steps.map(step => step.conversionRate).filter(rate => rate > 0);
    if (conversionRates.length > 1) {
      const variance = this.calculateVariance(conversionRates);
      const consistencyBonus = Math.max(0, 10 - variance / 10);
      score += consistencyBonus;
    }
    
    return Math.round(Math.max(0, Math.min(100, score)));
  }

  /**
   * Get stage-by-stage analysis
   */
  getStageAnalysis(): StageAnalysis[] {
    return this.steps.map((step, index) => ({
      stageId: step.stageId,
      stageName: step.stageName,
      position: index + 1,
      leadsCount: step.leadsCount,
      value: step.value,
      conversionRate: step.conversionRate,
      dropOffRate: step.dropOffRate,
      averageTimeInStage: step.averageTimeInStage,
      performance: this.getStagePerformance(step),
      recommendations: this.getStageRecommendations(step),
    }));
  }

  /**
   * Get funnel health indicators
   */
  getHealthIndicators(): FunnelHealthIndicators {
    const worstStep = this.getWorstPerformingStep();
    const bestStep = this.getBestPerformingStep();
    
    return {
      overallHealth: this.getEfficiencyScore() >= 70 ? 'good' : this.getEfficiencyScore() >= 50 ? 'fair' : 'poor',
      criticalBottlenecks: this.bottlenecks.filter(b => b.severity === 'high').length,
      worstStage: worstStep?.stageName,
      bestStage: bestStep?.stageName,
      averageDropOff: this.calculateAverageDropOff(),
      timeToConvert: this.getAverageTimeInFunnel(),
    };
  }

  /**
   * Get improvement opportunities
   */
  getImprovementOpportunities(): ImprovementOpportunity[] {
    const opportunities: ImprovementOpportunity[] = [];
    
    // High drop-off stages
    this.steps.forEach(step => {
      if (step.dropOffRate > 50) {
        opportunities.push({
          type: 'high_dropoff',
          stage: step.stageName,
          impact: 'high',
          description: `${step.stageName} has ${step.dropOffRate.toFixed(1)}% drop-off rate`,
          recommendation: `Optimize ${step.stageName} process to reduce drop-off`,
          potentialImprovement: this.calculatePotentialImprovement(step),
        });
      }
    });
    
    // Slow stages
    this.steps.forEach(step => {
      if (step.averageTimeInStage > 14) { // More than 2 weeks
        opportunities.push({
          type: 'slow_stage',
          stage: step.stageName,
          impact: 'medium',
          description: `${step.stageName} takes ${step.averageTimeInStage} days on average`,
          recommendation: `Streamline ${step.stageName} to reduce time`,
          potentialImprovement: 'Faster pipeline velocity',
        });
      }
    });
    
    return opportunities;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      steps: this.steps,
      overallConversionRate: this.overallConversionRate,
      bottlenecks: this.bottlenecks,
      recommendations: this.recommendations,
      efficiencyScore: this.getEfficiencyScore(),
      healthIndicators: this.getHealthIndicators(),
      stageAnalysis: this.getStageAnalysis(),
      improvementOpportunities: this.getImprovementOpportunities(),
      totalLeadsAtTop: this.getTotalLeadsAtTop(),
      totalConversions: this.getTotalConversions(),
      averageTimeInFunnel: this.getAverageTimeInFunnel(),
      analyzedAt: this.analyzedAt.toISOString(),
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): ConversionFunnel {
    return new ConversionFunnel(
      data.steps,
      data.overallConversionRate,
      data.bottlenecks,
      data.recommendations,
      new Date(data.analyzedAt),
    );
  }

  /**
   * Private helper methods
   */
  private calculateVariance(values: number[]): number {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
  }

  private getStagePerformance(step: FunnelStep): 'excellent' | 'good' | 'fair' | 'poor' {
    if (step.conversionRate >= 80) return 'excellent';
    if (step.conversionRate >= 60) return 'good';
    if (step.conversionRate >= 40) return 'fair';
    return 'poor';
  }

  private getStageRecommendations(step: FunnelStep): string[] {
    const recommendations: string[] = [];
    
    if (step.dropOffRate > 50) {
      recommendations.push('High drop-off rate - review stage requirements');
    }
    
    if (step.averageTimeInStage > 14) {
      recommendations.push('Long stage duration - streamline process');
    }
    
    if (step.conversionRate < 40) {
      recommendations.push('Low conversion rate - improve qualification criteria');
    }
    
    return recommendations;
  }

  private calculateAverageDropOff(): number {
    const dropOffs = this.steps.map(step => step.dropOffRate).filter(rate => rate > 0);
    return dropOffs.length > 0 ? dropOffs.reduce((sum, rate) => sum + rate, 0) / dropOffs.length : 0;
  }

  private calculatePotentialImprovement(step: FunnelStep): string {
    const improvement = step.dropOffRate * 0.3; // Assume 30% improvement possible
    return `Potential ${improvement.toFixed(1)}% improvement in overall conversion`;
  }
}

/**
 * Supporting interfaces
 */
export interface FunnelStep {
  stageId: number;
  stageName: string;
  leadsCount: number;
  value: number;
  conversionRate: number;
  dropOffRate: number;
  averageTimeInStage: number;
}

export interface FunnelBottleneck {
  stageId: number;
  stageName: string;
  severity: 'low' | 'medium' | 'high';
  dropOffRate: number;
  impact: string;
  recommendation: string;
}

export interface StageAnalysis {
  stageId: number;
  stageName: string;
  position: number;
  leadsCount: number;
  value: number;
  conversionRate: number;
  dropOffRate: number;
  averageTimeInStage: number;
  performance: 'excellent' | 'good' | 'fair' | 'poor';
  recommendations: string[];
}

export interface FunnelHealthIndicators {
  overallHealth: 'good' | 'fair' | 'poor';
  criticalBottlenecks: number;
  worstStage?: string;
  bestStage?: string;
  averageDropOff: number;
  timeToConvert: number;
}

export interface ImprovementOpportunity {
  type: 'high_dropoff' | 'slow_stage' | 'low_conversion';
  stage: string;
  impact: 'high' | 'medium' | 'low';
  description: string;
  recommendation: string;
  potentialImprovement: string;
}
