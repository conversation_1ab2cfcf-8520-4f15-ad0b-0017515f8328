import { Lead } from '../entities/lead.entity';

/**
 * Assignment Rule Value Object
 * Represents a rule for automatic lead assignment
 */
export class AssignmentRule {
  constructor(
    public readonly id: string,
    public readonly name: string,
    public readonly description: string,
    public readonly leadCondition: (lead: Lead) => boolean,
    public readonly userCondition: (user: any) => boolean,
    public readonly scoreAdjustment: number,
    public readonly priority: number,
    public readonly isActive: boolean = true,
  ) {
    this.validateRule();
  }

  private validateRule(): void {
    if (!this.id || this.id.trim().length === 0) {
      throw new Error('Assignment rule must have an ID');
    }
    
    if (!this.name || this.name.trim().length === 0) {
      throw new Error('Assignment rule must have a name');
    }
    
    if (this.scoreAdjustment < -100 || this.scoreAdjustment > 100) {
      throw new Error('Score adjustment must be between -100 and 100');
    }
  }

  /**
   * Check if this rule applies to the given lead
   */
  appliesTo(lead: Lead): boolean {
    if (!this.isActive) return false;
    
    try {
      return this.leadCondition(lead);
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if this rule applies to the given user
   */
  appliesToUser(user: any): boolean {
    if (!this.isActive) return false;
    
    try {
      return this.userCondition(user);
    } catch (error) {
      return false;
    }
  }

  /**
   * Get score adjustment for this rule
   */
  getScoreAdjustment(lead: Lead, user: any): number {
    if (!this.appliesTo(lead) || !this.appliesToUser(user)) {
      return 0;
    }
    
    return this.scoreAdjustment;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      scoreAdjustment: this.scoreAdjustment,
      priority: this.priority,
      isActive: this.isActive,
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): AssignmentRule {
    return new AssignmentRule(
      data.id,
      data.name,
      data.description,
      () => true, // Default condition
      () => true, // Default condition
      data.scoreAdjustment,
      data.priority,
      data.isActive,
    );
  }
}
