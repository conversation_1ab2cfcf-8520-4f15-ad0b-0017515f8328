/**
 * Scoring Factor Value Object
 * Represents an individual factor that contributes to the overall lead score
 */
export class ScoringFactor {
  public readonly weightedScore: number;

  constructor(
    public readonly name: string,
    public readonly score: number,
    public readonly weight: number,
    public readonly description?: string,
    public readonly metadata: Record<string, any> = {},
  ) {
    this.validateFactor();
    this.weightedScore = this.score * this.weight;
  }

  private validateFactor(): void {
    if (!this.name || this.name.trim().length === 0) {
      throw new Error('Scoring factor must have a name');
    }
    
    if (this.score < 0 || this.score > 100) {
      throw new Error('Scoring factor score must be between 0 and 100');
    }
    
    if (this.weight < 0 || this.weight > 1) {
      throw new Error('Scoring factor weight must be between 0 and 1');
    }
  }

  /**
   * Check if this factor is a strength (high score)
   */
  isStrength(threshold: number = 70): boolean {
    return this.score >= threshold;
  }

  /**
   * Check if this factor is a weakness (low score)
   */
  isWeakness(threshold: number = 50): boolean {
    return this.score < threshold;
  }

  /**
   * Get the impact level of this factor
   */
  getImpactLevel(): 'high' | 'medium' | 'low' {
    if (this.weight >= 0.2) return 'high';
    if (this.weight >= 0.1) return 'medium';
    return 'low';
  }

  /**
   * Get the performance level of this factor
   */
  getPerformanceLevel(): 'excellent' | 'good' | 'fair' | 'poor' {
    if (this.score >= 80) return 'excellent';
    if (this.score >= 60) return 'good';
    if (this.score >= 40) return 'fair';
    return 'poor';
  }

  /**
   * Calculate the contribution to total score
   */
  getContribution(): number {
    return this.weightedScore;
  }

  /**
   * Get improvement potential for this factor
   */
  getImprovementPotential(targetScore: number = 80): number {
    if (this.score >= targetScore) return 0;
    return (targetScore - this.score) * this.weight;
  }

  /**
   * Get human-readable description
   */
  getDisplayName(): string {
    const nameMap = new Map([
      ['contact_completeness', 'Contact Completeness'],
      ['revenue_potential', 'Revenue Potential'],
      ['engagement_level', 'Engagement Level'],
      ['company_fit', 'Company Fit'],
      ['behavioral_signals', 'Behavioral Signals'],
      ['source_quality', 'Source Quality'],
      ['industry_fit', 'Industry Fit'],
      ['geographic_fit', 'Geographic Fit'],
      ['timing_signals', 'Timing Signals'],
      ['technology_fit', 'Technology Fit'],
    ]);

    return nameMap.get(this.name) || this.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Get factor-specific recommendations
   */
  getRecommendations(): string[] {
    const recommendations: string[] = [];

    if (this.isWeakness()) {
      switch (this.name) {
        case 'contact_completeness':
          recommendations.push('Complete missing contact information');
          recommendations.push('Verify email and phone number');
          recommendations.push('Add company details and website');
          break;
        
        case 'revenue_potential':
          recommendations.push('Qualify budget and timeline');
          recommendations.push('Understand decision-making process');
          recommendations.push('Identify economic buyer');
          break;
        
        case 'engagement_level':
          recommendations.push('Send relevant content and resources');
          recommendations.push('Schedule a discovery call');
          recommendations.push('Invite to webinar or demo');
          break;
        
        case 'company_fit':
          recommendations.push('Research company and industry');
          recommendations.push('Identify use cases and pain points');
          recommendations.push('Find mutual connections');
          break;
        
        case 'behavioral_signals':
          recommendations.push('Monitor website activity');
          recommendations.push('Track email engagement');
          recommendations.push('Note content preferences');
          break;
        
        case 'source_quality':
          recommendations.push('Verify lead source accuracy');
          recommendations.push('Improve lead capture forms');
          recommendations.push('Focus on higher-quality channels');
          break;
        
        default:
          recommendations.push(`Improve ${this.getDisplayName().toLowerCase()}`);
          break;
      }
    }

    return recommendations;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      name: this.name,
      displayName: this.getDisplayName(),
      score: this.score,
      weight: this.weight,
      weightedScore: this.weightedScore,
      description: this.description,
      impactLevel: this.getImpactLevel(),
      performanceLevel: this.getPerformanceLevel(),
      isStrength: this.isStrength(),
      isWeakness: this.isWeakness(),
      recommendations: this.getRecommendations(),
      metadata: this.metadata,
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): ScoringFactor {
    return new ScoringFactor(
      data.name,
      data.score,
      data.weight,
      data.description,
      data.metadata,
    );
  }

  /**
   * Compare two scoring factors by weighted score
   */
  static compare(a: ScoringFactor, b: ScoringFactor): number {
    return b.weightedScore - a.weightedScore;
  }

  /**
   * Create a collection of default factors
   */
  static createDefaults(): ScoringFactor[] {
    return [
      new ScoringFactor('contact_completeness', 50, 0.20, 'Completeness of contact information'),
      new ScoringFactor('revenue_potential', 50, 0.25, 'Potential revenue value'),
      new ScoringFactor('engagement_level', 50, 0.20, 'Level of engagement with content'),
      new ScoringFactor('company_fit', 50, 0.15, 'Fit with ideal customer profile'),
      new ScoringFactor('behavioral_signals', 50, 0.10, 'Behavioral indicators of interest'),
      new ScoringFactor('source_quality', 50, 0.10, 'Quality of lead source'),
    ];
  }

  /**
   * Validate factor data
   */
  static isValid(data: any): boolean {
    return (
      typeof data.name === 'string' &&
      data.name.trim().length > 0 &&
      typeof data.score === 'number' &&
      data.score >= 0 &&
      data.score <= 100 &&
      typeof data.weight === 'number' &&
      data.weight >= 0 &&
      data.weight <= 1
    );
  }

  /**
   * Calculate total weight of a collection of factors
   */
  static getTotalWeight(factors: ScoringFactor[]): number {
    return factors.reduce((total, factor) => total + factor.weight, 0);
  }

  /**
   * Normalize weights to sum to 1.0
   */
  static normalizeWeights(factors: ScoringFactor[]): ScoringFactor[] {
    const totalWeight = ScoringFactor.getTotalWeight(factors);
    
    if (totalWeight === 0) {
      throw new Error('Cannot normalize factors with zero total weight');
    }
    
    return factors.map(factor => 
      new ScoringFactor(
        factor.name,
        factor.score,
        factor.weight / totalWeight,
        factor.description,
        factor.metadata,
      )
    );
  }
}
