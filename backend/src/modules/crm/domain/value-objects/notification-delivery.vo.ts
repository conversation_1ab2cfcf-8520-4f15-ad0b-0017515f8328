import { NotificationChannel } from '@/modules/crm/domain/enums/notification-channel.enum';

/**
 * Notification Delivery Value Object
 * Represents the delivery status and details of a notification
 */
export class NotificationDelivery {
  constructor(
    public readonly id: string,
    public readonly userId: string,
    public readonly notificationType: string,
    public readonly channel: NotificationChannel,
    public readonly status: DeliveryStatus,
    public readonly sentAt: Date,
    public readonly error?: string,
    public readonly metadata: Record<string, any> = {},
  ) {
    this.validateDelivery();
  }

  private validateDelivery(): void {
    if (!this.id || this.id.trim().length === 0) {
      throw new Error('Notification delivery must have an ID');
    }
    
    if (!this.userId || this.userId.trim().length === 0) {
      throw new Error('Notification delivery must have a user ID');
    }
    
    if (!this.notificationType || this.notificationType.trim().length === 0) {
      throw new Error('Notification delivery must have a type');
    }
  }

  /**
   * Check if delivery was successful
   */
  isSuccessful(): boolean {
    return this.status === 'delivered';
  }

  /**
   * Check if delivery failed
   */
  isFailed(): boolean {
    return this.status === 'failed';
  }

  /**
   * Check if delivery is pending
   */
  isPending(): boolean {
    return this.status === 'pending';
  }

  /**
   * Get delivery duration in milliseconds
   */
  getDeliveryDuration(): number {
    const deliveredAt = this.metadata.deliveredAt;
    if (!deliveredAt) return 0;
    
    return new Date(deliveredAt).getTime() - this.sentAt.getTime();
  }

  /**
   * Get retry count
   */
  getRetryCount(): number {
    return this.metadata.retryCount || 0;
  }

  /**
   * Get priority level
   */
  getPriority(): string {
    return this.metadata.priority || 'normal';
  }

  /**
   * Check if delivery can be retried
   */
  canRetry(): boolean {
    return this.isFailed() && this.getRetryCount() < 3;
  }

  /**
   * Get delivery summary
   */
  getSummary(): DeliverySummary {
    return {
      id: this.id,
      userId: this.userId,
      notificationType: this.notificationType,
      channel: this.channel,
      status: this.status,
      isSuccessful: this.isSuccessful(),
      sentAt: this.sentAt,
      deliveryDuration: this.getDeliveryDuration(),
      retryCount: this.getRetryCount(),
      priority: this.getPriority(),
      error: this.error,
    };
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      id: this.id,
      userId: this.userId,
      notificationType: this.notificationType,
      channel: this.channel,
      status: this.status,
      sentAt: this.sentAt.toISOString(),
      error: this.error,
      metadata: this.metadata,
      isSuccessful: this.isSuccessful(),
      deliveryDuration: this.getDeliveryDuration(),
      retryCount: this.getRetryCount(),
      priority: this.getPriority(),
      canRetry: this.canRetry(),
      summary: this.getSummary(),
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): NotificationDelivery {
    return new NotificationDelivery(
      data.id,
      data.userId,
      data.notificationType,
      data.channel,
      data.status,
      new Date(data.sentAt),
      data.error,
      data.metadata,
    );
  }

  /**
   * Create successful delivery
   */
  static success(
    id: string,
    userId: string,
    notificationType: string,
    channel: NotificationChannel,
    metadata: Record<string, any> = {},
  ): NotificationDelivery {
    return new NotificationDelivery(
      id,
      userId,
      notificationType,
      channel,
      'delivered',
      new Date(),
      undefined,
      {
        ...metadata,
        deliveredAt: new Date().toISOString(),
      },
    );
  }

  /**
   * Create failed delivery
   */
  static failure(
    id: string,
    userId: string,
    notificationType: string,
    channel: NotificationChannel,
    error: string,
    metadata: Record<string, any> = {},
  ): NotificationDelivery {
    return new NotificationDelivery(
      id,
      userId,
      notificationType,
      channel,
      'failed',
      new Date(),
      error,
      metadata,
    );
  }

  /**
   * Create pending delivery
   */
  static pending(
    id: string,
    userId: string,
    notificationType: string,
    channel: NotificationChannel,
    metadata: Record<string, any> = {},
  ): NotificationDelivery {
    return new NotificationDelivery(
      id,
      userId,
      notificationType,
      channel,
      'pending',
      new Date(),
      undefined,
      metadata,
    );
  }
}

/**
 * Supporting types and interfaces
 */
export type DeliveryStatus = 'pending' | 'delivered' | 'failed' | 'retrying';

export interface DeliverySummary {
  id: string;
  userId: string;
  notificationType: string;
  channel: NotificationChannel;
  status: DeliveryStatus;
  isSuccessful: boolean;
  sentAt: Date;
  deliveryDuration: number;
  retryCount: number;
  priority: string;
  error?: string;
}
