import { NotificationChannel } from '../enums/notification-channel.enum';

/**
 * Notification Template Value Object
 * Represents a template for generating notifications across different channels
 */
export class NotificationTemplate {
  constructor(
    public readonly type: string,
    public readonly name: string,
    public readonly shortDescription: string,
    public readonly longDescription: string,
    public readonly channelTemplates: Record<string, ChannelTemplate>,
    public readonly variables: TemplateVariable[] = [],
    public readonly metadata: Record<string, any> = {},
  ) {
    this.validateTemplate();
  }

  private validateTemplate(): void {
    if (!this.type || this.type.trim().length === 0) {
      throw new Error('Notification template must have a type');
    }
    
    if (!this.name || this.name.trim().length === 0) {
      throw new Error('Notification template must have a name');
    }
    
    if (!this.channelTemplates || Object.keys(this.channelTemplates).length === 0) {
      throw new Error('Notification template must have at least one channel template');
    }
  }

  /**
   * Render template for all channels with provided data
   */
  render(data: Record<string, any>): RenderedNotification {
    const renderedChannels: Record<string, any> = {};

    Object.entries(this.channelTemplates).forEach(([channel, template]) => {
      try {
        renderedChannels[channel] = this.renderChannel(channel, template, data);
      } catch (error) {
        throw new Error(`Failed to render template for channel ${channel}: ${error.message}`);
      }
    });

    return {
      type: this.type,
      channels: renderedChannels,
      renderedAt: new Date(),
      data,
    };
  }

  /**
   * Render template for specific channel
   */
  renderForChannel(channel: string, data: Record<string, any>): any {
    const template = this.channelTemplates[channel];
    if (!template) {
      throw new Error(`Template not found for channel: ${channel}`);
    }

    return this.renderChannel(channel, template, data);
  }

  /**
   * Get supported channels
   */
  getSupportedChannels(): string[] {
    return Object.keys(this.channelTemplates);
  }

  /**
   * Check if template supports channel
   */
  supportsChannel(channel: string): boolean {
    return this.channelTemplates.hasOwnProperty(channel);
  }

  /**
   * Get template variables
   */
  getVariables(): TemplateVariable[] {
    return this.variables;
  }

  /**
   * Get required variables
   */
  getRequiredVariables(): TemplateVariable[] {
    return this.variables.filter(variable => variable.required);
  }

  /**
   * Validate data against template variables
   */
  validateData(data: Record<string, any>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check required variables
    this.getRequiredVariables().forEach(variable => {
      if (!(variable.name in data) || data[variable.name] === null || data[variable.name] === undefined) {
        errors.push(`Required variable '${variable.name}' is missing`);
      }
    });

    // Check variable types
    this.variables.forEach(variable => {
      if (variable.name in data) {
        const value = data[variable.name];
        if (!this.isValidType(value, variable.type)) {
          errors.push(`Variable '${variable.name}' has invalid type. Expected: ${variable.type}`);
        }
      }
    });

    // Check for unused variables
    Object.keys(data).forEach(key => {
      if (!this.variables.some(variable => variable.name === key)) {
        warnings.push(`Unused variable '${key}' provided`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Get template preview with sample data
   */
  getPreview(): RenderedNotification {
    const sampleData = this.generateSampleData();
    return this.render(sampleData);
  }

  /**
   * Clone template with modifications
   */
  clone(modifications: Partial<{
    type: string;
    name: string;
    shortDescription: string;
    longDescription: string;
    channelTemplates: Record<string, ChannelTemplate>;
    variables: TemplateVariable[];
    metadata: Record<string, any>;
  }>): NotificationTemplate {
    return new NotificationTemplate(
      modifications.type || this.type,
      modifications.name || this.name,
      modifications.shortDescription || this.shortDescription,
      modifications.longDescription || this.longDescription,
      modifications.channelTemplates || this.channelTemplates,
      modifications.variables || this.variables,
      { ...this.metadata, ...modifications.metadata },
    );
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      type: this.type,
      name: this.name,
      shortDescription: this.shortDescription,
      longDescription: this.longDescription,
      channelTemplates: this.channelTemplates,
      variables: this.variables,
      metadata: this.metadata,
      supportedChannels: this.getSupportedChannels(),
      requiredVariables: this.getRequiredVariables().map(v => v.name),
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): NotificationTemplate {
    return new NotificationTemplate(
      data.type,
      data.name,
      data.shortDescription,
      data.longDescription,
      data.channelTemplates,
      data.variables,
      data.metadata,
    );
  }

  /**
   * Private helper methods
   */
  private renderChannel(channel: string, template: ChannelTemplate, data: Record<string, any>): any {
    const rendered: any = {};

    // Render each property of the channel template
    Object.entries(template).forEach(([key, value]) => {
      if (typeof value === 'string') {
        rendered[key] = this.interpolateString(value, data);
      } else {
        rendered[key] = value;
      }
    });

    return rendered;
  }

  private interpolateString(template: string, data: Record<string, any>): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, variable) => {
      const value = data[variable];
      return value !== undefined ? String(value) : match;
    });
  }

  private isValidType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string': return typeof value === 'string';
      case 'number': return typeof value === 'number';
      case 'boolean': return typeof value === 'boolean';
      case 'date': return value instanceof Date || !isNaN(Date.parse(value));
      case 'array': return Array.isArray(value);
      case 'object': return typeof value === 'object' && value !== null;
      case 'any': return true;
      default: return true;
    }
  }

  private generateSampleData(): Record<string, any> {
    const sampleData: Record<string, any> = {};

    this.variables.forEach(variable => {
      switch (variable.type) {
        case 'string':
          sampleData[variable.name] = variable.defaultValue || `Sample ${variable.name}`;
          break;
        case 'number':
          sampleData[variable.name] = variable.defaultValue || 123;
          break;
        case 'boolean':
          sampleData[variable.name] = variable.defaultValue || true;
          break;
        case 'date':
          sampleData[variable.name] = variable.defaultValue || new Date().toISOString();
          break;
        case 'array':
          sampleData[variable.name] = variable.defaultValue || ['Sample', 'Array'];
          break;
        case 'object':
          sampleData[variable.name] = variable.defaultValue || { sample: 'object' };
          break;
        default:
          sampleData[variable.name] = variable.defaultValue || `Sample ${variable.name}`;
      }
    });

    return sampleData;
  }
}

/**
 * Supporting interfaces
 */
export interface ChannelTemplate {
  subject?: string;
  title?: string;
  body?: string;
  template?: string;
  html?: string;
  text?: string;
  [key: string]: any;
}

export interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object' | 'any';
  description: string;
  required: boolean;
  defaultValue?: any;
  validation?: {
    pattern?: string;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
  };
}

export interface RenderedNotification {
  type: string;
  channels: Record<string, any>;
  renderedAt: Date;
  data: Record<string, any>;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
