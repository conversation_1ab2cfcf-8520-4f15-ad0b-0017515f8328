import { NotificationChannel } from '@/modules/crm/domain/enums/notification-channel.enum';

/**
 * Notification Preference Value Object
 * Represents user preferences for notifications
 */
export class NotificationPreference {
  constructor(
    public readonly notificationType: string,
    public readonly enabled: boolean,
    public readonly channels: NotificationChannel[],
    public readonly settings: NotificationSettings = {},
  ) {
    this.validatePreference();
  }

  private validatePreference(): void {
    if (!this.notificationType || this.notificationType.trim().length === 0) {
      throw new Error('Notification preference must have a type');
    }
    
    if (!Array.isArray(this.channels)) {
      throw new Error('Notification preference channels must be an array');
    }
  }

  /**
   * Check if channel is enabled for this preference
   */
  isChannelEnabled(channel: NotificationChannel): boolean {
    return this.enabled && this.channels.includes(channel);
  }

  /**
   * Check if preference applies to notification type
   */
  appliesTo(notificationType: string): boolean {
    return this.notificationType === 'all' || this.notificationType === notificationType;
  }

  /**
   * Check if notification should be sent during quiet hours
   */
  respectsQuietHours(): boolean {
    return this.settings.quietHours !== undefined;
  }

  /**
   * Check if current time is within quiet hours
   */
  isQuietTime(): boolean {
    if (!this.settings.quietHours) return false;
    
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    const { start, end } = this.settings.quietHours;
    
    // Handle overnight quiet hours (e.g., 22:00 to 08:00)
    if (start > end) {
      return currentTime >= start || currentTime <= end;
    }
    
    return currentTime >= start && currentTime <= end;
  }

  /**
   * Get frequency setting for this preference
   */
  getFrequency(): 'immediate' | 'batched' | 'daily' | 'weekly' {
    return this.settings.frequency || 'immediate';
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject(): Record<string, any> {
    return {
      notificationType: this.notificationType,
      enabled: this.enabled,
      channels: this.channels,
      settings: this.settings,
      respectsQuietHours: this.respectsQuietHours(),
      frequency: this.getFrequency(),
    };
  }

  /**
   * Create from plain object
   */
  static fromPlainObject(data: any): NotificationPreference {
    return new NotificationPreference(
      data.notificationType,
      data.enabled,
      data.channels,
      data.settings,
    );
  }

  /**
   * Create default preferences
   */
  static createDefaults(): NotificationPreference[] {
    return [
      new NotificationPreference(
        'all',
        true,
        [NotificationChannel.EMAIL, NotificationChannel.IN_APP],
        {
          quietHours: { start: '22:00', end: '08:00' },
          frequency: 'immediate',
        },
      ),
      new NotificationPreference(
        'lead_assigned',
        true,
        [NotificationChannel.EMAIL, NotificationChannel.IN_APP, NotificationChannel.PUSH],
        { frequency: 'immediate' },
      ),
      new NotificationPreference(
        'opportunity_updated',
        true,
        [NotificationChannel.EMAIL, NotificationChannel.IN_APP],
        { frequency: 'immediate' },
      ),
      new NotificationPreference(
        'pipeline_alert',
        true,
        [NotificationChannel.EMAIL, NotificationChannel.SLACK],
        { frequency: 'immediate' },
      ),
      new NotificationPreference(
        'scheduled_report',
        true,
        [NotificationChannel.EMAIL],
        { frequency: 'batched' },
      ),
    ];
  }
}

/**
 * Notification Settings Interface
 */
export interface NotificationSettings {
  quietHours?: {
    start: string; // HH:mm format
    end: string;   // HH:mm format
  };
  frequency?: 'immediate' | 'batched' | 'daily' | 'weekly';
  maxPerDay?: number;
  timezone?: string;
  language?: string;
  customSettings?: Record<string, any>;
}
