import { Injectable, Logger } from '@nestjs/common';
import { IUserRepository } from '../../domain/repositories/user.repository';
import { User, WorkingHours } from '../../domain/entities/user.entity';
import { OdooConnectionUseCase } from '../../../../shared/application/use-cases/odoo-connection.use-case';

/**
 * Odoo User Repository Implementation
 * Implements user data persistence using Odoo backend (res.users model)
 */
@Injectable()
export class OdooUserRepository implements IUserRepository {
  private readonly logger = new Logger(OdooUserRepository.name);

  // Odoo field mapping for res.users model
  private readonly USER_FIELDS = [
    'id',
    'name',
    'email',
    'login',
    'active',
    'partner_id',
    'groups_id',
    'company_id',
    'company_ids',
    'tz',
    'lang',
    'image_1920',
    'phone',
    'mobile',
    'create_date',
    'write_date',
    'login_date',
    // Custom fields for CRM
    'x_crm_role',
    'x_crm_team_id',
    'x_crm_seniority',
    'x_crm_territories',
    'x_crm_skills',
    'x_crm_is_available',
    'x_crm_max_leads',
    'x_crm_working_hours',
  ];

  constructor(
    private readonly odooConnection: OdooConnectionUseCase,
  ) {}

  async save(user: User): Promise<User> {
    try {
      const odooData = this.mapUserToOdoo(user);
      
      if (user.isNew()) {
        const id = await this.odooConnection.create('res.users', odooData);
        this.logger.log(`Created new user with ID: ${id}`);
        
        const createdUser = await this.findById(id.toString());
        if (!createdUser) {
          throw new Error(`Failed to fetch created user with ID: ${id}`);
        }
        return createdUser;
      } else {
        const success = await this.odooConnection.update('res.users', [parseInt(user.id)], odooData);
        if (!success) {
          throw new Error(`Failed to update user with ID: ${user.id}`);
        }
        
        const updatedUser = await this.findById(user.id);
        if (!updatedUser) {
          throw new Error(`Failed to fetch updated user with ID: ${user.id}`);
        }
        return updatedUser;
      }
    } catch (error) {
      this.logger.error(`Failed to save user: ${user.name}`, error);
      throw error;
    }
  }

  async findById(id: string): Promise<User | null> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'res.users',
        [['id', '=', parseInt(id)]],
        { fields: this.USER_FIELDS }
      );

      if (records.length === 0) {
        return null;
      }

      return this.mapOdooToUser(records[0]);
    } catch (error) {
      this.logger.error(`Failed to find user by ID: ${id}`, error);
      throw error;
    }
  }

  async findByEmail(email: string): Promise<User | null> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'res.users',
        [['email', '=', email]],
        { fields: this.USER_FIELDS }
      );

      if (records.length === 0) {
        return null;
      }

      return this.mapOdooToUser(records[0]);
    } catch (error) {
      this.logger.error(`Failed to find user by email: ${email}`, error);
      throw error;
    }
  }

  async findByLogin(login: string): Promise<User | null> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'res.users',
        [['login', '=', login]],
        { fields: this.USER_FIELDS }
      );

      if (records.length === 0) {
        return null;
      }

      return this.mapOdooToUser(records[0]);
    } catch (error) {
      this.logger.error(`Failed to find user by login: ${login}`, error);
      throw error;
    }
  }

  async findByTeamId(teamId: number, filters?: any): Promise<User[]> {
    try {
      const domain = [
        ['x_crm_team_id', '=', teamId],
        ['active', '=', true]
      ];

      if (filters?.isAvailable) {
        domain.push(['x_crm_is_available', '=', filters.isAvailable]);
      }

      const records = await this.odooConnection.searchRead<any>(
        'res.users',
        domain,
        { fields: this.USER_FIELDS }
      );

      return records.map(record => this.mapOdooToUser(record));
    } catch (error) {
      this.logger.error(`Failed to find users by team ID: ${teamId}`, error);
      throw error;
    }
  }

  async findAll(filters?: any): Promise<User[]> {
    try {
      const domain = [['active', '=', true]];

      if (filters?.role) {
        domain.push(['x_crm_role', '=', filters.role]);
      }

      if (filters?.isAvailable !== undefined) {
        domain.push(['x_crm_is_available', '=', filters.isAvailable]);
      }

      const records = await this.odooConnection.searchRead<any>(
        'res.users',
        domain,
        { 
          fields: this.USER_FIELDS,
          limit: filters?.limit || 100,
          offset: filters?.offset || 0
        }
      );

      return records.map(record => this.mapOdooToUser(record));
    } catch (error) {
      this.logger.error('Failed to find all users', error);
      throw error;
    }
  }

  async create(userData: any): Promise<User> {
    try {
      const odooData = this.mapCreateDataToOdoo(userData);
      const id = await this.odooConnection.create('res.users', odooData);
      
      const createdUser = await this.findById(id.toString());
      if (!createdUser) {
        throw new Error(`Failed to fetch created user with ID: ${id}`);
      }
      
      return createdUser;
    } catch (error) {
      this.logger.error('Failed to create user', error);
      throw error;
    }
  }

  async update(id: string, userData: any): Promise<User> {
    try {
      const odooData = this.mapUpdateDataToOdoo(userData);
      const success = await this.odooConnection.update('res.users', [parseInt(id)], odooData);

      if (!success) {
        throw new Error(`Failed to update user with ID: ${id}`);
      }

      const updatedUser = await this.findById(id);
      if (!updatedUser) {
        throw new Error(`Failed to fetch updated user with ID: ${id}`);
      }

      return updatedUser;
    } catch (error) {
      this.logger.error(`Failed to update user with ID: ${id}`, error);
      throw error;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      // Soft delete by setting active = false
      const success = await this.odooConnection.update('res.users', [parseInt(id)], { active: false });
      return success;
    } catch (error) {
      this.logger.error(`Failed to delete user with ID: ${id}`, error);
      throw error;
    }
  }

  async findByRole(role: string): Promise<User[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'res.users',
        [
          ['x_crm_role', '=', role],
          ['active', '=', true]
        ],
        { fields: this.USER_FIELDS }
      );

      return records.map(record => this.mapOdooToUser(record));
    } catch (error) {
      this.logger.error(`Failed to find users by role: ${role}`, error);
      throw error;
    }
  }

  async findAvailableUsers(teamId?: number): Promise<User[]> {
    try {
      const domain: any[] = [
        ['active', '=', true],
        ['x_crm_is_available', '=', true]
      ];

      if (teamId) {
        domain.push(['x_crm_team_id', '=', teamId]);
      }

      const records = await this.odooConnection.searchRead<any>(
        'res.users',
        domain,
        { fields: this.USER_FIELDS }
      );

      return records.map(record => this.mapOdooToUser(record));
    } catch (error) {
      this.logger.error('Failed to find available users', error);
      throw error;
    }
  }

  async updateAvailability(id: string, isAvailable: boolean): Promise<void> {
    try {
      await this.odooConnection.update('res.users', [parseInt(id)], {
        x_crm_is_available: isAvailable
      });
    } catch (error) {
      this.logger.error(`Failed to update availability for user ID: ${id}`, error);
      throw error;
    }
  }

  async getUserWorkloadStats(id: string): Promise<any> {
    try {
      // This would require complex queries to CRM leads
      // For now, return basic stats
      const leadRecords = await this.odooConnection.searchRead<any>('crm.lead', [
        ['user_id', '=', parseInt(id)],
        ['active', '=', true]
      ], { fields: ['id'] });

      const leadCount = leadRecords.length;

      return {
        totalLeads: leadCount,
        activeLeads: leadCount,
        convertedLeads: 0,
        totalValue: 0,
        conversionRate: 0,
        averageLeadAge: 0,
        lastActivity: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to get workload stats for user ID: ${id}`, error);
      throw error;
    }
  }

  async findByTerritory(territory: string): Promise<User[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'res.users',
        [
          ['x_crm_territories', 'ilike', territory],
          ['active', '=', true]
        ],
        { fields: this.USER_FIELDS }
      );

      return records.map(record => this.mapOdooToUser(record));
    } catch (error) {
      this.logger.error(`Failed to find users by territory: ${territory}`, error);
      throw error;
    }
  }

  async authenticate(login: string, password: string): Promise<User | null> {
    try {
      // For now, just find user by login and return it
      // In a real implementation, you would verify the password
      const user = await this.findByLogin(login);

      if (user && user.isActive) {
        return user;
      }

      return null;
    } catch (error) {
      this.logger.error(`Authentication failed for login: ${login}`, error);
      return null;
    }
  }

  async refreshFromOdoo(userId: string): Promise<User> {
    try {
      const user = await this.findById(userId);
      if (!user) {
        throw new Error(`User not found with ID: ${userId}`);
      }
      return user;
    } catch (error) {
      this.logger.error(`Failed to refresh user from Odoo: ${userId}`, error);
      throw error;
    }
  }

  async syncWithOdoo(user: User): Promise<boolean> {
    try {
      await this.save(user);
      return true;
    } catch (error) {
      this.logger.error(`Failed to sync user with Odoo: ${user.id}`, error);
      return false;
    }
  }

  async getPermissions(userId: string): Promise<string[]> {
    try {
      // Get user's groups and extract permissions
      const records = await this.odooConnection.searchRead<any>(
        'res.users',
        [['id', '=', parseInt(userId)]],
        { fields: ['groups_id'] }
      );

      if (records.length === 0) {
        return [];
      }

      const groupIds = records[0].groups_id;
      if (!groupIds || groupIds.length === 0) {
        return [];
      }

      // Get group permissions
      const groups = await this.odooConnection.searchRead<any>(
        'res.groups',
        [['id', 'in', groupIds]],
        { fields: ['name', 'category_id'] }
      );

      return groups.map(group => group.name);
    } catch (error) {
      this.logger.error(`Failed to get permissions for user: ${userId}`, error);
      return [];
    }
  }

  async hasPermission(userId: string, permission: string): Promise<boolean> {
    try {
      const permissions = await this.getPermissions(userId);
      return permissions.includes(permission);
    } catch (error) {
      this.logger.error(`Failed to check permission for user: ${userId}`, error);
      return false;
    }
  }

  /**
   * Map Odoo record to User entity
   */
  private mapOdooToUser(record: any): User {
    return new User(
      record.id.toString(),
      record.name || '',
      record.email || '',
      record.login || '',
      record.x_crm_role || 'user',
      record.active || false,
      record.x_crm_team_id?.[0] || undefined,
      record.x_crm_seniority || undefined,
      this.parseJsonField(record.x_crm_territories) || [],
      this.parseJsonField(record.x_crm_skills) || [],
      record.x_crm_is_available || true,
      record.x_crm_max_leads || undefined,
      record.tz || 'UTC',
      this.parseJsonField(record.x_crm_working_hours) || undefined,
      record.image_1920 || undefined,
      record.phone || record.mobile || undefined,
      record.lang || 'en_US',
      record.id, // odooUserId same as id
      [], // permissions will be loaded separately
      record.login_date ? new Date(record.login_date) : undefined,
      record.create_date ? new Date(record.create_date) : new Date(),
      record.write_date ? new Date(record.write_date) : new Date(),
    );
  }

  /**
   * Map User entity to Odoo record
   */
  private mapUserToOdoo(user: User): any {
    return {
      name: user.name,
      email: user.email,
      login: user.login,
      active: user.isActive,
      tz: user.timezone,
      lang: user.language,
      phone: user.phone,
      x_crm_role: user.role,
      x_crm_team_id: user.teamId,
      x_crm_seniority: user.seniority,
      x_crm_territories: this.stringifyJsonField(user.territories),
      x_crm_skills: this.stringifyJsonField(user.skills),
      x_crm_is_available: user.isAvailable,
      x_crm_max_leads: user.maxLeads,
      x_crm_working_hours: this.stringifyJsonField(user.workingHours),
    };
  }

  /**
   * Map create data to Odoo format
   */
  private mapCreateDataToOdoo(userData: any): any {
    return {
      name: userData.name,
      email: userData.email,
      login: userData.email, // Use email as login by default
      active: true,
      tz: userData.timezone || 'UTC',
      lang: userData.language || 'en_US',
      phone: userData.phone,
      x_crm_role: userData.role || 'user',
      x_crm_team_id: userData.teamId,
      x_crm_seniority: userData.seniority,
      x_crm_territories: this.stringifyJsonField(userData.territories),
      x_crm_skills: this.stringifyJsonField(userData.skills),
      x_crm_is_available: userData.isAvailable !== false,
      x_crm_max_leads: userData.maxLeads,
      x_crm_working_hours: this.stringifyJsonField(userData.workingHours),
    };
  }

  /**
   * Map update data to Odoo format
   */
  private mapUpdateDataToOdoo(userData: any): any {
    const data: any = {};

    if (userData.name !== undefined) data.name = userData.name;
    if (userData.email !== undefined) data.email = userData.email;
    if (userData.phone !== undefined) data.phone = userData.phone;
    if (userData.timezone !== undefined) data.tz = userData.timezone;
    if (userData.language !== undefined) data.lang = userData.language;
    if (userData.role !== undefined) data.x_crm_role = userData.role;
    if (userData.teamId !== undefined) data.x_crm_team_id = userData.teamId;
    if (userData.seniority !== undefined) data.x_crm_seniority = userData.seniority;
    if (userData.territories !== undefined) data.x_crm_territories = this.stringifyJsonField(userData.territories);
    if (userData.skills !== undefined) data.x_crm_skills = this.stringifyJsonField(userData.skills);
    if (userData.isActive !== undefined) data.active = userData.isActive;
    if (userData.isAvailable !== undefined) data.x_crm_is_available = userData.isAvailable;
    if (userData.maxLeads !== undefined) data.x_crm_max_leads = userData.maxLeads;
    if (userData.workingHours !== undefined) data.x_crm_working_hours = this.stringifyJsonField(userData.workingHours);

    return data;
  }

  /**
   * Parse JSON field from Odoo
   */
  private parseJsonField(value: any): any {
    if (!value) return null;
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return null;
      }
    }
    return value;
  }

  /**
   * Stringify JSON field for Odoo
   */
  private stringifyJsonField(value: any): string | null {
    if (!value) return null;
    if (typeof value === 'string') return value;
    try {
      return JSON.stringify(value);
    } catch {
      return null;
    }
  }
}
