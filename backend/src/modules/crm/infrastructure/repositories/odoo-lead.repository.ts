import { Injectable, Logger } from '@nestjs/common';
import { ILeadRepository } from '../../domain/repositories/lead.repository';
import { Lead } from '../../domain/entities/lead.entity';
import { Opportunity } from '../../domain/entities/opportunity.entity';
import { ContactInfo } from '../../domain/value-objects/contact-info.vo';
import { LeadStatus } from '../../domain/value-objects/lead-status.vo';
import { LeadPriority } from '../../domain/value-objects/lead-priority.vo';
import { LeadType } from '../../domain/value-objects/lead-type.vo';
import { OdooConnectionUseCase } from '../../../../shared/application/use-cases/odoo-connection.use-case';

/**
 * Odoo Lead Repository Implementation
 * Implements lead data persistence using Odoo CRM backend
 */
@Injectable()
export class OdooLeadRepository implements ILeadRepository {
  private readonly logger = new Logger(OdooLeadRepository.name);

  // Odoo field mapping for crm.lead model
  private readonly LEAD_FIELDS = [
    'id',
    'name',
    'email_from',
    'phone',
    'partner_name',
    'contact_name',
    'street',
    'city',
    'country_id',
    'website',
    'stage_id',
    'type',
    'priority',
    'expected_revenue',
    'probability',
    'description',
    'user_id',
    'team_id',
    'company_id',
    'partner_id',
    'date_deadline',
    'lost_reason_id',
    'campaign_id',
    'source_id',
    'medium_id',
    'tag_ids',
    'create_date',
    'write_date',
    'active',
  ];

  constructor(
    private readonly odooConnection: OdooConnectionUseCase,
  ) {}

  async save(lead: Lead): Promise<Lead> {
    try {
      const odooData = this.mapLeadToOdoo(lead);
      
      if (lead.isNew()) {
        // Create new lead
        const id = await this.odooConnection.create('crm.lead', odooData);
        this.logger.log(`Created new lead with ID: ${id}`);
        
        // Fetch the created lead to get all fields
        const createdLead = await this.findById(id);
        if (!createdLead) {
          throw new Error(`Failed to fetch created lead with ID: ${id}`);
        }
        return createdLead;
      } else {
        // Update existing lead
        const success = await this.odooConnection.update('crm.lead', [lead.id], odooData);
        if (!success) {
          throw new Error(`Failed to update lead with ID: ${lead.id}`);
        }
        
        // Fetch the updated lead
        const updatedLead = await this.findById(lead.id);
        if (!updatedLead) {
          throw new Error(`Failed to fetch updated lead with ID: ${lead.id}`);
        }
        return updatedLead;
      }
    } catch (error) {
      this.logger.error(`Failed to save lead: ${lead.name}`, error);
      throw error;
    }
  }

  async findById(id: number): Promise<Lead | null> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.lead',
        [['id', '=', id]],
        { fields: this.LEAD_FIELDS }
      );
      
      return records.length > 0 ? this.mapOdooToLead(records[0]) : null;
    } catch (error) {
      this.logger.error(`Failed to find lead by ID: ${id}`, error);
      throw error;
    }
  }

  async findByEmail(email: string): Promise<Lead | null> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.lead',
        [['email_from', '=', email]],
        { fields: this.LEAD_FIELDS, limit: 1 }
      );
      
      return records.length > 0 ? this.mapOdooToLead(records[0]) : null;
    } catch (error) {
      this.logger.error(`Failed to find lead by email: ${email}`, error);
      throw error;
    }
  }

  async findByStage(stageId: number): Promise<Lead[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.lead',
        [['stage_id', '=', stageId]],
        { fields: this.LEAD_FIELDS }
      );
      
      return records.map(record => this.mapOdooToLead(record));
    } catch (error) {
      this.logger.error(`Failed to find leads by stage: ${stageId}`, error);
      throw error;
    }
  }

  async findByTeam(teamId: number): Promise<Lead[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.lead',
        [['team_id', '=', teamId]],
        { fields: this.LEAD_FIELDS }
      );
      
      return records.map(record => this.mapOdooToLead(record));
    } catch (error) {
      this.logger.error(`Failed to find leads by team: ${teamId}`, error);
      throw error;
    }
  }

  async findByType(type: LeadType): Promise<Lead[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.lead',
        [['type', '=', type.value]],
        { fields: this.LEAD_FIELDS }
      );
      
      return records.map(record => this.mapOdooToLead(record));
    } catch (error) {
      this.logger.error(`Failed to find leads by type: ${type.value}`, error);
      throw error;
    }
  }

  async findByPriority(priority: LeadPriority): Promise<Lead[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.lead',
        [['priority', '=', priority.value.toString()]],
        { fields: this.LEAD_FIELDS }
      );
      
      return records.map(record => this.mapOdooToLead(record));
    } catch (error) {
      this.logger.error(`Failed to find leads by priority: ${priority.value}`, error);
      throw error;
    }
  }

  async findByAssignedUser(userId: number): Promise<Lead[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.lead',
        [['user_id', '=', userId]],
        { fields: this.LEAD_FIELDS }
      );
      
      return records.map(record => this.mapOdooToLead(record));
    } catch (error) {
      this.logger.error(`Failed to find leads by assigned user: ${userId}`, error);
      throw error;
    }
  }

  async findOverdue(): Promise<Lead[]> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const records = await this.odooConnection.searchRead<any>(
        'crm.lead',
        [
          ['date_deadline', '<', today],
          ['stage_id.is_won', '=', false],
          ['active', '=', true]
        ],
        { fields: this.LEAD_FIELDS }
      );
      
      return records.map(record => this.mapOdooToLead(record));
    } catch (error) {
      this.logger.error('Failed to find overdue leads', error);
      throw error;
    }
  }

  async findRequiringAttention(): Promise<Lead[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.lead',
        [
          '|',
          ['priority', 'in', ['2', '3']], // High or Very High priority
          '&',
          ['type', '=', 'opportunity'],
          ['probability', '>=', 80]
        ],
        { fields: this.LEAD_FIELDS }
      );
      
      return records.map(record => this.mapOdooToLead(record));
    } catch (error) {
      this.logger.error('Failed to find leads requiring attention', error);
      throw error;
    }
  }

  /**
   * Map Lead domain entity to Odoo record format
   */
  private mapLeadToOdoo(lead: Lead): any {
    const odooData: any = {
      name: lead.name,
      email_from: lead.contactInfo.email,
      phone: lead.contactInfo.phone,
      partner_name: lead.contactInfo.company,
      contact_name: lead.contactInfo.company,
      street: lead.contactInfo.address,
      city: lead.contactInfo.city,
      website: lead.contactInfo.website,
      type: lead.type.value,
      priority: lead.priority.value.toString(),
      description: lead.description,
      user_id: lead.assignedUserId,
      team_id: lead.teamId,
      company_id: lead.companyId,
      partner_id: lead.partnerId,
      stage_id: lead.stageId,
      lost_reason_id: lead.lostReasonId,
      campaign_id: lead.campaignId,
      source_id: lead.sourceId,
      medium_id: lead.mediumId,
    };

    // Only set revenue and probability for opportunities
    if (lead.type.isOpportunity()) {
      odooData.expected_revenue = lead.expectedRevenue;
      odooData.probability = lead.probability;
    }

    // Set deadline if provided
    if (lead.dateDeadline) {
      odooData.date_deadline = lead.dateDeadline.toISOString().split('T')[0];
    }

    // Handle tags (many2many field)
    if (lead.tags.length > 0) {
      // This would require tag ID mapping in a real implementation
      // For now, we'll skip tag handling
    }

    // Remove undefined values
    Object.keys(odooData).forEach(key => {
      if (odooData[key] === undefined) {
        delete odooData[key];
      }
    });

    return odooData;
  }

  /**
   * Map Odoo record to Lead domain entity
   */
  private mapOdooToLead(odooRecord: any): Lead {
    // Map Odoo stage to our LeadStatus
    const status = this.mapOdooStageToStatus(odooRecord.stage_id);
    
    // Create ContactInfo value object
    const contactInfo = new ContactInfo(
      odooRecord.email_from,
      odooRecord.phone,
      odooRecord.partner_name || odooRecord.contact_name,
      odooRecord.website,
      odooRecord.street,
      odooRecord.city,
      odooRecord.country_id?.[1] // country_id is [id, name] tuple
    );

    // Map priority
    const priority = LeadPriority.fromValue(parseInt(odooRecord.priority || '1'));
    
    // Map type
    const type = LeadType.fromValue(odooRecord.type || 'lead');

    return new Lead(
      odooRecord.id,
      odooRecord.name,
      contactInfo,
      status,
      'odoo', // source - would need proper mapping
      type,
      priority,
      odooRecord.expected_revenue,
      odooRecord.probability,
      odooRecord.description,
      odooRecord.user_id?.[0], // user_id is [id, name] tuple
      odooRecord.company_id?.[0],
      [], // tags - would need proper mapping
      odooRecord.partner_id?.[0],
      odooRecord.stage_id?.[0],
      odooRecord.team_id?.[0],
      odooRecord.date_deadline ? new Date(odooRecord.date_deadline) : undefined,
      odooRecord.lost_reason_id?.[0],
      odooRecord.campaign_id?.[0],
      odooRecord.source_id?.[0],
      odooRecord.medium_id?.[0],
      new Date(odooRecord.create_date),
      new Date(odooRecord.write_date)
    );
  }

  async findMany(filters: any): Promise<any> {
    try {
      // Build Odoo domain from filters
      const domain = this.buildOdooDomain(filters);

      // Set up search options
      const options: any = {
        fields: this.LEAD_FIELDS,
        offset: filters.offset || 0,
        limit: filters.limit || 100,
      };

      // Add sorting
      if (filters.sortBy) {
        const order = `${filters.sortBy} ${filters.sortOrder || 'asc'}`;
        options.order = order;
      }

      const records = await this.odooConnection.searchRead<any>('crm.lead', domain, options);
      const leads = records.map(record => this.mapOdooToLead(record));

      // Get total count
      const totalCount = await this.odooConnection.searchRead<any>(
        'crm.lead',
        domain,
        { fields: ['id'] }
      );

      // Calculate analytics (simplified)
      const analytics = this.calculateAnalytics(leads);

      return {
        leads,
        total: totalCount.length,
        analytics,
      };
    } catch (error) {
      this.logger.error('Failed to find leads with filters', error);
      throw error;
    }
  }

  async updateStatus(id: number, status: LeadStatus): Promise<boolean> {
    try {
      // In a real implementation, you'd need to map status to stage_id
      const success = await this.odooConnection.update('crm.lead', [id], {
        // This is simplified - you'd need proper stage mapping
      });
      return success;
    } catch (error) {
      this.logger.error(`Failed to update status for lead: ${id}`, error);
      throw error;
    }
  }

  async updatePriority(id: number, priority: LeadPriority): Promise<boolean> {
    try {
      const success = await this.odooConnection.update('crm.lead', [id], {
        priority: priority.value.toString(),
      });
      return success;
    } catch (error) {
      this.logger.error(`Failed to update priority for lead: ${id}`, error);
      throw error;
    }
  }

  async convertToOpportunity(leadId: number, partnerId?: number, stageId?: number): Promise<Opportunity> {
    try {
      // Use Odoo's convert_opportunity method
      const result = await this.odooConnection.execute(
        'crm.lead',
        'convert_opportunity',
        [leadId],
        {
          partner_id: partnerId,
          stage_id: stageId,
        }
      );

      // Fetch the converted opportunity
      const opportunity = await this.findById(leadId);
      if (!opportunity || !opportunity.type.isOpportunity()) {
        throw new Error('Failed to convert lead to opportunity');
      }

      return Opportunity.fromLead(opportunity, opportunity.expectedRevenue || 0, opportunity.probability || 0);
    } catch (error) {
      this.logger.error(`Failed to convert lead to opportunity: ${leadId}`, error);
      throw error;
    }
  }

  async assignToUser(id: number, userId: number, teamId?: number): Promise<boolean> {
    try {
      const updateData: any = { user_id: userId };
      if (teamId) {
        updateData.team_id = teamId;
      }

      const success = await this.odooConnection.update('crm.lead', [id], updateData);
      return success;
    } catch (error) {
      this.logger.error(`Failed to assign lead to user: ${id}`, error);
      throw error;
    }
  }

  async assignToTeam(id: number, teamId: number): Promise<boolean> {
    try {
      const success = await this.odooConnection.update('crm.lead', [id], {
        team_id: teamId,
      });
      return success;
    } catch (error) {
      this.logger.error(`Failed to assign lead to team: ${id}`, error);
      throw error;
    }
  }

  async updateRevenueForecast(id: number, expectedRevenue: number, probability?: number): Promise<boolean> {
    try {
      const updateData: any = { expected_revenue: expectedRevenue };
      if (probability !== undefined) {
        updateData.probability = probability;
      }

      const success = await this.odooConnection.update('crm.lead', [id], updateData);
      return success;
    } catch (error) {
      this.logger.error(`Failed to update revenue forecast for lead: ${id}`, error);
      throw error;
    }
  }

  async setDeadline(id: number, deadline: Date): Promise<boolean> {
    try {
      const success = await this.odooConnection.update('crm.lead', [id], {
        date_deadline: deadline.toISOString().split('T')[0],
      });
      return success;
    } catch (error) {
      this.logger.error(`Failed to set deadline for lead: ${id}`, error);
      throw error;
    }
  }

  async addTag(id: number, tag: string): Promise<boolean> {
    try {
      // This would require proper tag handling in Odoo
      // For now, return true as placeholder
      return true;
    } catch (error) {
      this.logger.error(`Failed to add tag to lead: ${id}`, error);
      throw error;
    }
  }

  async removeTag(id: number, tag: string): Promise<boolean> {
    try {
      // This would require proper tag handling in Odoo
      // For now, return true as placeholder
      return true;
    } catch (error) {
      this.logger.error(`Failed to remove tag from lead: ${id}`, error);
      throw error;
    }
  }

  async bulkUpdate(ids: number[], updates: any): Promise<boolean> {
    try {
      const odooUpdates = this.mapBulkUpdatesToOdoo(updates);
      const success = await this.odooConnection.update('crm.lead', ids, odooUpdates);
      return success;
    } catch (error) {
      this.logger.error('Failed to bulk update leads', error);
      throw error;
    }
  }

  async delete(id: number): Promise<boolean> {
    try {
      // Soft delete by setting active = false
      const success = await this.odooConnection.update('crm.lead', [id], {
        active: false,
      });
      return success;
    } catch (error) {
      this.logger.error(`Failed to delete lead: ${id}`, error);
      throw error;
    }
  }

  async bulkDelete(ids: number[]): Promise<boolean> {
    try {
      // Soft delete by setting active = false
      const success = await this.odooConnection.update('crm.lead', ids, {
        active: false,
      });
      return success;
    } catch (error) {
      this.logger.error('Failed to bulk delete leads', error);
      throw error;
    }
  }

  // Placeholder implementations for remaining methods
  async getStatistics(filters?: any): Promise<any> {
    // Implementation would involve complex Odoo queries
    return {
      totalLeads: 0,
      totalOpportunities: 0,
      qualifiedLeads: 0,
      convertedLeads: 0,
      averageScore: 0,
      conversionRate: 0,
      totalRevenue: 0,
      weightedRevenue: 0,
      averageDealSize: 0,
      averageSalesCycle: 0,
      winRate: 0,
      lossRate: 0,
      pipelineVelocity: 0,
      byPriority: {},
      byStage: {},
      byTeam: {},
      bySource: {},
    };
  }

  async getPipelineAnalytics(teamId?: number): Promise<any> {
    // Implementation would involve complex Odoo queries
    return {
      stages: [],
      totalValue: 0,
      totalWeightedValue: 0,
      conversionRates: {},
      bottlenecks: [],
    };
  }

  async search(query: string, filters?: any): Promise<Lead[]> {
    try {
      const domain = [
        '|', '|', '|',
        ['name', 'ilike', query],
        ['email_from', 'ilike', query],
        ['phone', 'ilike', query],
        ['partner_name', 'ilike', query],
      ];

      if (filters?.type) {
        domain.push(['type', '=', filters.type.value]);
      }
      if (filters?.teamId) {
        domain.push(['team_id', '=', filters.teamId]);
      }

      const records = await this.odooConnection.searchRead<any>(
        'crm.lead',
        domain,
        {
          fields: this.LEAD_FIELDS,
          limit: filters?.limit || 50,
        }
      );

      return records.map(record => this.mapOdooToLead(record));
    } catch (error) {
      this.logger.error(`Failed to search leads: ${query}`, error);
      throw error;
    }
  }

  /**
   * Build Odoo domain from filters
   */
  private buildOdooDomain(filters: any): any[] {
    const domain: any[] = [['active', '=', true]];

    if (filters.status) {
      // Would need proper stage mapping
    }
    if (filters.type) {
      domain.push(['type', '=', filters.type.value]);
    }
    if (filters.priority) {
      domain.push(['priority', '=', filters.priority.value.toString()]);
    }
    if (filters.assignedUserId) {
      domain.push(['user_id', '=', filters.assignedUserId]);
    }
    if (filters.teamId) {
      domain.push(['team_id', '=', filters.teamId]);
    }
    if (filters.stageId) {
      domain.push(['stage_id', '=', filters.stageId]);
    }
    if (filters.partnerId) {
      domain.push(['partner_id', '=', filters.partnerId]);
    }
    if (filters.minRevenue) {
      domain.push(['expected_revenue', '>=', filters.minRevenue]);
    }
    if (filters.maxRevenue) {
      domain.push(['expected_revenue', '<=', filters.maxRevenue]);
    }
    if (filters.minProbability) {
      domain.push(['probability', '>=', filters.minProbability]);
    }
    if (filters.maxProbability) {
      domain.push(['probability', '<=', filters.maxProbability]);
    }
    if (filters.dateFrom) {
      domain.push(['create_date', '>=', filters.dateFrom.toISOString()]);
    }
    if (filters.dateTo) {
      domain.push(['create_date', '<=', filters.dateTo.toISOString()]);
    }

    return domain;
  }

  /**
   * Calculate analytics from leads
   */
  private calculateAnalytics(leads: Lead[]): any {
    const totalLeads = leads.length;
    const qualifiedLeads = leads.filter(lead => lead.isQualified()).length;
    const opportunities = leads.filter(lead => lead.type.isOpportunity());

    return {
      averageScore: totalLeads > 0 ? leads.reduce((sum, lead) => sum + lead.calculateScore(), 0) / totalLeads : 0,
      conversionRate: totalLeads > 0 ? (qualifiedLeads / totalLeads) * 100 : 0,
      totalRevenue: opportunities.reduce((sum, opp) => sum + (opp.expectedRevenue || 0), 0),
      weightedRevenue: opportunities.reduce((sum, opp) => sum + opp.getWeightedRevenue(), 0),
      topSources: [],
      topTeams: [],
      priorityDistribution: [],
      stageDistribution: [],
    };
  }

  /**
   * Map bulk updates to Odoo format
   */
  private mapBulkUpdatesToOdoo(updates: any): any {
    const odooUpdates: any = {};

    if (updates.status) {
      // Would need proper stage mapping
    }
    if (updates.priority) {
      odooUpdates.priority = updates.priority.value.toString();
    }
    if (updates.assignedUserId) {
      odooUpdates.user_id = updates.assignedUserId;
    }
    if (updates.teamId) {
      odooUpdates.team_id = updates.teamId;
    }
    if (updates.stageId) {
      odooUpdates.stage_id = updates.stageId;
    }

    return odooUpdates;
  }

  /**
   * Map Odoo stage to LeadStatus
   * This is a simplified mapping - in reality, you'd need to fetch stage details
   */
  private mapOdooStageToStatus(stageData: any): LeadStatus {
    if (!stageData) return new LeadStatus('new');

    // stageData is typically [id, name] tuple
    const stageName = stageData[1]?.toLowerCase() || 'new';

    // Simple mapping based on stage name
    if (stageName.includes('new') || stageName.includes('draft')) return new LeadStatus('new');
    if (stageName.includes('contact') || stageName.includes('call')) return new LeadStatus('contacted');
    if (stageName.includes('qualif')) return new LeadStatus('qualified');
    if (stageName.includes('proposal') || stageName.includes('quote')) return new LeadStatus('proposal');
    if (stageName.includes('negotiat') || stageName.includes('closing')) return new LeadStatus('negotiation');
    if (stageName.includes('won') || stageName.includes('closed')) return new LeadStatus('won');
    if (stageName.includes('lost') || stageName.includes('cancel')) return new LeadStatus('lost');

    return new LeadStatus('new');
  }

  // Missing methods - placeholder implementations
  async findByFilters(filters: any): Promise<Lead[]> {
    // TODO: Implement with Odoo search
    return [];
  }

  async findByAssignedUserId(userId: string): Promise<Lead[]> {
    // TODO: Implement with Odoo search
    return [];
  }

  async findByTeamId(teamId: number): Promise<Lead[]> {
    // TODO: Implement with Odoo search
    return [];
  }

  async update(id: number, lead: Lead): Promise<Lead> {
    // TODO: Implement with Odoo write
    return lead;
  }
}
