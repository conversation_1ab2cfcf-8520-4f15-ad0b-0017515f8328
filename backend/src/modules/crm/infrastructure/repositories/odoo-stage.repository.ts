import { Injectable, Logger } from '@nestjs/common';
import { IStageRepository } from '../../domain/repositories/stage.repository';
import { Stage } from '../../domain/entities/stage.entity';
import { OdooConnectionUseCase } from '../../../../shared/application/use-cases/odoo-connection.use-case';

/**
 * Odoo Stage Repository Implementation
 * Implements stage data persistence using Odoo CRM backend
 */
@Injectable()
export class OdooStageRepository implements IStageRepository {
  private readonly logger = new Logger(OdooStageRepository.name);

  // Odoo field mapping for crm.stage model
  private readonly STAGE_FIELDS = [
    'id',
    'name',
    'sequence',
    'is_won',
    'fold',
    'team_id',
    'requirements',
    'on_change',
    'probability',
    'create_date',
    'write_date',
  ];

  constructor(
    private readonly odooConnection: OdooConnectionUseCase,
  ) {}

  async save(stage: Stage): Promise<Stage> {
    try {
      const odooData = this.mapStageToOdoo(stage);
      
      if (stage.isNew()) {
        const id = await this.odooConnection.create('crm.stage', odooData);
        this.logger.log(`Created new stage with ID: ${id}`);
        
        const createdStage = await this.findById(id);
        if (!createdStage) {
          throw new Error(`Failed to fetch created stage with ID: ${id}`);
        }
        return createdStage;
      } else {
        const success = await this.odooConnection.update('crm.stage', [stage.id], odooData);
        if (!success) {
          throw new Error(`Failed to update stage with ID: ${stage.id}`);
        }
        
        const updatedStage = await this.findById(stage.id);
        if (!updatedStage) {
          throw new Error(`Failed to fetch updated stage with ID: ${stage.id}`);
        }
        return updatedStage;
      }
    } catch (error) {
      this.logger.error(`Failed to save stage: ${stage.name}`, error);
      throw error;
    }
  }

  async findById(id: number): Promise<Stage | null> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.stage',
        [['id', '=', id]],
        { fields: this.STAGE_FIELDS }
      );
      
      return records.length > 0 ? this.mapOdooToStage(records[0]) : null;
    } catch (error) {
      this.logger.error(`Failed to find stage by ID: ${id}`, error);
      throw error;
    }
  }

  async findAll(): Promise<Stage[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.stage',
        [],
        { fields: this.STAGE_FIELDS, order: 'sequence asc' }
      );
      
      return records.map(record => this.mapOdooToStage(record));
    } catch (error) {
      this.logger.error('Failed to find all stages', error);
      throw error;
    }
  }

  async findByTeam(teamId: number): Promise<Stage[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.stage',
        [['team_id', '=', teamId]],
        { fields: this.STAGE_FIELDS, order: 'sequence asc' }
      );
      
      return records.map(record => this.mapOdooToStage(record));
    } catch (error) {
      this.logger.error(`Failed to find stages by team: ${teamId}`, error);
      throw error;
    }
  }

  async findGlobal(): Promise<Stage[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.stage',
        [['team_id', '=', false]],
        { fields: this.STAGE_FIELDS, order: 'sequence asc' }
      );
      
      return records.map(record => this.mapOdooToStage(record));
    } catch (error) {
      this.logger.error('Failed to find global stages', error);
      throw error;
    }
  }

  async findActive(teamId?: number): Promise<Stage[]> {
    try {
      const domain = [['fold', '=', false]];
      if (teamId) {
        domain.push(['team_id', '=', teamId.toString()]);
      }

      const records = await this.odooConnection.searchRead<any>(
        'crm.stage',
        domain,
        { fields: this.STAGE_FIELDS, order: 'sequence asc' }
      );
      
      return records.map(record => this.mapOdooToStage(record));
    } catch (error) {
      this.logger.error('Failed to find active stages', error);
      throw error;
    }
  }

  async findTerminal(teamId?: number): Promise<Stage[]> {
    try {
      const domain = [['is_won', '=', true]];
      if (teamId) {
        domain.push(['team_id', '=', teamId.toString()]);
      }

      const records = await this.odooConnection.searchRead<any>(
        'crm.stage',
        domain,
        { fields: this.STAGE_FIELDS, order: 'sequence asc' }
      );
      
      return records.map(record => this.mapOdooToStage(record));
    } catch (error) {
      this.logger.error('Failed to find terminal stages', error);
      throw error;
    }
  }

  async findWonStages(teamId?: number): Promise<Stage[]> {
    try {
      const domain = [['is_won', '=', true]];
      if (teamId) {
        domain.push(['team_id', '=', teamId.toString()]);
      }

      const records = await this.odooConnection.searchRead<any>(
        'crm.stage',
        domain,
        { fields: this.STAGE_FIELDS, order: 'sequence asc' }
      );
      
      return records.map(record => this.mapOdooToStage(record));
    } catch (error) {
      this.logger.error('Failed to find won stages', error);
      throw error;
    }
  }

  async findLostStages(teamId?: number): Promise<Stage[]> {
    try {
      // In Odoo, lost stages are typically identified by fold=true and is_won=false
      const domain = [['fold', '=', true], ['is_won', '=', false]];
      if (teamId) {
        domain.push(['team_id', '=', teamId.toString()]);
      }

      const records = await this.odooConnection.searchRead<any>(
        'crm.stage',
        domain,
        { fields: this.STAGE_FIELDS, order: 'sequence asc' }
      );
      
      return records.map(record => this.mapOdooToStage(record));
    } catch (error) {
      this.logger.error('Failed to find lost stages', error);
      throw error;
    }
  }

  async findOrderedBySequence(teamId?: number): Promise<Stage[]> {
    try {
      const domain = teamId ? [['team_id', '=', teamId]] : [];

      const records = await this.odooConnection.searchRead<any>(
        'crm.stage',
        domain,
        { fields: this.STAGE_FIELDS, order: 'sequence asc' }
      );
      
      return records.map(record => this.mapOdooToStage(record));
    } catch (error) {
      this.logger.error('Failed to find stages ordered by sequence', error);
      throw error;
    }
  }

  // Placeholder implementations for remaining methods
  async findMany(filters: any): Promise<any> {
    try {
      const domain = this.buildOdooDomain(filters);
      const options: any = {
        fields: this.STAGE_FIELDS,
        offset: filters.offset || 0,
        limit: filters.limit || 100,
        order: 'sequence asc',
      };

      const records = await this.odooConnection.searchRead<any>('crm.stage', domain, options);
      const stages = records.map(record => this.mapOdooToStage(record));

      const totalCount = await this.odooConnection.searchRead<any>(
        'crm.stage',
        domain,
        { fields: ['id'] }
      );

      return {
        stages,
        total: totalCount.length,
      };
    } catch (error) {
      this.logger.error('Failed to find stages with filters', error);
      throw error;
    }
  }

  async updateSequence(id: number, newSequence: number): Promise<boolean> {
    try {
      const success = await this.odooConnection.update('crm.stage', [id], {
        sequence: newSequence,
      });
      return success;
    } catch (error) {
      this.logger.error(`Failed to update sequence for stage: ${id}`, error);
      throw error;
    }
  }

  async updateStage(id: number, updates: any): Promise<boolean> {
    try {
      const odooUpdates = this.mapStageUpdatesToOdoo(updates);
      const success = await this.odooConnection.update('crm.stage', [id], odooUpdates);
      return success;
    } catch (error) {
      this.logger.error(`Failed to update stage: ${id}`, error);
      throw error;
    }
  }

  async reorderStages(stageSequences: Array<{ id: number; sequence: number }>): Promise<boolean> {
    try {
      for (const { id, sequence } of stageSequences) {
        await this.updateSequence(id, sequence);
      }
      return true;
    } catch (error) {
      this.logger.error('Failed to reorder stages', error);
      throw error;
    }
  }

  async toggleFold(id: number): Promise<boolean> {
    try {
      const stage = await this.findById(id);
      if (!stage) {
        throw new Error(`Stage not found: ${id}`);
      }

      const success = await this.odooConnection.update('crm.stage', [id], {
        fold: !stage.fold,
      });
      return success;
    } catch (error) {
      this.logger.error(`Failed to toggle fold for stage: ${id}`, error);
      throw error;
    }
  }

  async delete(id: number): Promise<boolean> {
    try {
      const success = await this.odooConnection.unlink('crm.stage', [id]);
      return success;
    } catch (error) {
      this.logger.error(`Failed to delete stage: ${id}`, error);
      throw error;
    }
  }

  // Placeholder implementations for complex methods
  async getStatistics(): Promise<any> {
    return {
      totalStages: 0,
      activeStages: 0,
      foldedStages: 0,
      wonStages: 0,
      lostStages: 0,
      averageLeadsPerStage: 0,
      averageRevenuePerStage: 0,
      stageUtilization: [],
    };
  }

  async getPerformanceMetrics(): Promise<any> {
    return {
      stageMetrics: [],
      bottlenecks: [],
      recommendations: [],
    };
  }

  async getNextStage(): Promise<Stage | null> {
    return null;
  }

  async getPreviousStage(): Promise<Stage | null> {
    return null;
  }

  async canDelete(): Promise<boolean> {
    return true;
  }

  async getProgressionPath(): Promise<any[]> {
    return [];
  }

  async validateSequences(): Promise<any> {
    return {
      isValid: true,
      issues: [],
      suggestions: [],
    };
  }

  async search(query: string): Promise<Stage[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.stage',
        [['name', 'ilike', query]],
        { fields: this.STAGE_FIELDS, order: 'sequence asc' }
      );
      
      return records.map(record => this.mapOdooToStage(record));
    } catch (error) {
      this.logger.error(`Failed to search stages: ${query}`, error);
      throw error;
    }
  }

  async clone(): Promise<Stage> {
    throw new Error('Clone method not implemented');
  }

  async getTemplates(): Promise<any[]> {
    return [];
  }

  async applyTemplate(): Promise<Stage[]> {
    return [];
  }

  /**
   * Map Stage domain entity to Odoo record format
   */
  private mapStageToOdoo(stage: Stage): any {
    return {
      name: stage.name,
      sequence: stage.sequence,
      is_won: stage.isWonStage,
      fold: stage.fold,
      team_id: stage.teamId || false,
      requirements: stage.requirements,
      on_change: stage.onChange,
    };
  }

  /**
   * Map Odoo record to Stage domain entity
   */
  private mapOdooToStage(odooRecord: any): Stage {
    return new Stage(
      odooRecord.id,
      odooRecord.name,
      odooRecord.sequence,
      odooRecord.is_won || false,
      false, // isLostStage - would need custom logic
      odooRecord.team_id?.[0],
      odooRecord.requirements,
      odooRecord.fold || false,
      undefined, // probabilityMin
      undefined, // probabilityMax
      odooRecord.on_change || false,
      new Date(odooRecord.create_date),
      new Date(odooRecord.write_date)
    );
  }

  /**
   * Build Odoo domain from filters
   */
  private buildOdooDomain(filters: any): any[] {
    const domain: any[] = [];

    if (filters.teamId !== undefined) {
      domain.push(['team_id', '=', filters.teamId || false]);
    }
    if (filters.isWonStage !== undefined) {
      domain.push(['is_won', '=', filters.isWonStage]);
    }
    if (filters.fold !== undefined) {
      domain.push(['fold', '=', filters.fold]);
    }
    if (filters.minSequence !== undefined) {
      domain.push(['sequence', '>=', filters.minSequence]);
    }
    if (filters.maxSequence !== undefined) {
      domain.push(['sequence', '<=', filters.maxSequence]);
    }

    return domain;
  }

  /**
   * Map stage updates to Odoo format
   */
  private mapStageUpdatesToOdoo(updates: any): any {
    const odooUpdates: any = {};

    if (updates.name !== undefined) {
      odooUpdates.name = updates.name;
    }
    if (updates.sequence !== undefined) {
      odooUpdates.sequence = updates.sequence;
    }
    if (updates.isWonStage !== undefined) {
      odooUpdates.is_won = updates.isWonStage;
    }
    if (updates.fold !== undefined) {
      odooUpdates.fold = updates.fold;
    }
    if (updates.requirements !== undefined) {
      odooUpdates.requirements = updates.requirements;
    }

    return odooUpdates;
  }
}
