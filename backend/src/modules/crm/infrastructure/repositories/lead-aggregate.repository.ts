import { Injectable, Logger, Inject } from '@nestjs/common';
import { ILeadAggregateRepository, LeadSearchCriteria } from '../../domain/repositories/lead-aggregate.repository';
import { LeadAggregate } from '../../domain/aggregates/lead.aggregate';
import { EventStore, DomainEvent } from '../../domain/events/base/domain-event.base';
import { SnapshotService, AggregateSnapshot } from '../event-sourcing/services/snapshot.service';

/**
 * Lead Aggregate Repository Implementation
 * Implements Event Sourcing pattern for Lead aggregates
 */
@Injectable()
export class LeadAggregateRepository implements ILeadAggregateRepository {
  private readonly logger = new Logger(LeadAggregateRepository.name);
  private readonly AGGREGATE_TYPE = 'Lead';
  private readonly SNAPSHOT_FREQUENCY = 10; // Take snapshot every 10 events

  constructor(
    @Inject('EVENT_STORE') private readonly eventStore: EventStore,
    private readonly snapshotService: SnapshotService,
  ) {}

  /**
   * Save aggregate by persisting its uncommitted events
   */
  async save(aggregate: LeadAggregate): Promise<void> {
    this.logger.debug(`Saving Lead aggregate: ${aggregate.id}`);

    try {
      const uncommittedEvents = aggregate.getUncommittedEvents() as DomainEvent[];

      if (uncommittedEvents.length === 0) {
        this.logger.debug(`No uncommitted events for aggregate: ${aggregate.id}`);
        return;
      }

      // Get stream name
      const streamName = `${this.AGGREGATE_TYPE}-${aggregate.id}`;

      // Persist events to event store
      await this.eventStore.append(streamName, uncommittedEvents, aggregate.version - uncommittedEvents.length);

      // Mark events as committed
      aggregate.commit();

      // Check if we should create a snapshot
      if (this.snapshotService.shouldCreateSnapshot(aggregate.version, 0, this.SNAPSHOT_FREQUENCY)) {
        await this.createSnapshot(aggregate);
      }

      this.logger.debug(`Successfully saved Lead aggregate: ${aggregate.id} with ${uncommittedEvents.length} events`);
    } catch (error) {
      this.logger.error(`Failed to save Lead aggregate: ${aggregate.id}`, error);
      throw error;
    }
  }

  /**
   * Find aggregate by ID and reconstruct from events
   */
  async findById(id: string): Promise<LeadAggregate | null> {
    this.logger.debug(`Finding Lead aggregate by ID: ${id}`);

    try {
      const streamName = `${this.AGGREGATE_TYPE}-${id}`;
      
      // Try to load from snapshot first
      let aggregate: LeadAggregate | null = null;
      let fromVersion = 0;

      const snapshot = await this.snapshotService.getSnapshot(id, this.AGGREGATE_TYPE);
      if (snapshot) {
        aggregate = this.restoreFromSnapshot(snapshot);
        fromVersion = snapshot.version + 1;
        this.logger.debug(`Loaded Lead aggregate from snapshot: ${id} at version: ${snapshot.version}`);
      }

      // Load events since snapshot (or all events if no snapshot)
      const events = await this.eventStore.getEvents(streamName, fromVersion);
      
      if (events.length === 0 && !snapshot) {
        this.logger.debug(`No events found for Lead aggregate: ${id}`);
        return null;
      }

      // Create new aggregate if no snapshot
      if (!aggregate) {
        aggregate = new LeadAggregate();
      }

      // Apply events to reconstruct state
      if (events.length > 0) {
        aggregate.loadFromHistory(events);
        this.logger.debug(`Reconstructed Lead aggregate: ${id} from ${events.length} events`);
      }

      return aggregate;
    } catch (error) {
      this.logger.error(`Failed to find Lead aggregate: ${id}`, error);
      throw error;
    }
  }

  /**
   * Check if aggregate exists
   */
  async exists(id: string): Promise<boolean> {
    try {
      const aggregate = await this.findById(id);
      return aggregate !== null && !aggregate.isDeleted;
    } catch (error) {
      this.logger.error(`Failed to check if Lead aggregate exists: ${id}`, error);
      return false;
    }
  }

  /**
   * Get aggregate version
   */
  async getVersion(id: string): Promise<number> {
    try {
      const aggregate = await this.findById(id);
      return aggregate ? aggregate.version : 0;
    } catch (error) {
      this.logger.error(`Failed to get version for Lead aggregate: ${id}`, error);
      return 0;
    }
  }

  /**
   * Find aggregates by criteria (using projections)
   * Note: This would typically query read models/projections for performance
   */
  async findByCriteria(criteria: LeadSearchCriteria): Promise<LeadAggregate[]> {
    this.logger.debug(`Finding Lead aggregates by criteria`, criteria);

    try {
      // In a real implementation, this would query read models/projections
      // For now, we'll return an empty array as this is complex to implement
      // without proper projection infrastructure
      
      this.logger.warn('findByCriteria not fully implemented - requires projection infrastructure');
      return [];
    } catch (error) {
      this.logger.error(`Failed to find Lead aggregates by criteria`, error);
      throw error;
    }
  }

  /**
   * Delete aggregate (soft delete by applying delete event)
   */
  async delete(id: string, reason?: string, userId?: string): Promise<void> {
    this.logger.debug(`Deleting Lead aggregate: ${id}`);

    try {
      const aggregate = await this.findById(id);
      
      if (!aggregate) {
        throw new Error(`Lead aggregate not found: ${id}`);
      }

      if (aggregate.isDeleted) {
        this.logger.debug(`Lead aggregate already deleted: ${id}`);
        return;
      }

      // Apply delete event
      aggregate.delete(reason, userId);

      // Save the aggregate with the delete event
      await this.save(aggregate);

      this.logger.debug(`Successfully deleted Lead aggregate: ${id}`);
    } catch (error) {
      this.logger.error(`Failed to delete Lead aggregate: ${id}`, error);
      throw error;
    }
  }

  /**
   * Create snapshot of aggregate
   */
  private async createSnapshot(aggregate: LeadAggregate): Promise<void> {
    try {
      const snapshotData = {
        id: aggregate.id,
        name: aggregate.name,
        contactInfo: aggregate.contactInfo,
        status: aggregate.status,
        source: aggregate.source,
        type: aggregate.type,
        priority: aggregate.priority,
        expectedRevenue: aggregate.expectedRevenue,
        probability: aggregate.probability,
        description: aggregate.description,
        assignedUserId: aggregate.assignedUserId,
        companyId: aggregate.companyId,
        tags: aggregate.tags,
        partnerId: aggregate.partnerId,
        stageId: aggregate.stageId,
        teamId: aggregate.teamId,
        dateDeadline: aggregate.dateDeadline,
        lostReasonId: aggregate.lostReasonId,
        campaignId: aggregate.campaignId,
        sourceId: aggregate.sourceId,
        mediumId: aggregate.mediumId,
        createdAt: aggregate.createdAt,
        updatedAt: aggregate.updatedAt,
        isDeleted: aggregate.isDeleted,
      };

      await this.snapshotService.createSnapshot(
        aggregate.id,
        this.AGGREGATE_TYPE,
        snapshotData,
        aggregate.version
      );

      this.logger.debug(`Created snapshot for Lead aggregate: ${aggregate.id} at version: ${aggregate.version}`);
    } catch (error) {
      this.logger.error(`Failed to create snapshot for Lead aggregate: ${aggregate.id}`, error);
      // Don't throw - snapshot creation failure shouldn't fail the save operation
    }
  }

  /**
   * Restore aggregate from snapshot
   */
  private restoreFromSnapshot(snapshot: AggregateSnapshot): LeadAggregate {
    const aggregate = new LeadAggregate();
    
    // Restore state from snapshot data
    // This would need to be implemented based on the snapshot structure
    // For now, we'll create a basic implementation
    
    this.logger.debug(`Restoring Lead aggregate from snapshot: ${snapshot.aggregateId}`);
    
    return aggregate;
  }
}
