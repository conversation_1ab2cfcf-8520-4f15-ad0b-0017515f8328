import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule, ConfigService } from '@nestjs/config';

// Queue processors
import { EmailQueueProcessor } from './processors/email-queue.processor';
import { NotificationQueueProcessor } from './processors/notification-queue.processor';
import { AnalyticsQueueProcessor } from './processors/analytics-queue.processor';
import { IntegrationQueueProcessor } from './processors/integration-queue.processor';
import { WorkflowQueueProcessor } from './processors/workflow-queue.processor';

// Queue producers
import { EmailQueueProducer } from './producers/email-queue.producer';
import { NotificationQueueProducer } from './producers/notification-queue.producer';
import { AnalyticsQueueProducer } from './producers/analytics-queue.producer';
import { IntegrationQueueProducer } from './producers/integration-queue.producer';
import { WorkflowQueueProducer } from './producers/workflow-queue.producer';

// Queue names
export const QUEUE_NAMES = {
  EMAIL: 'crm-email-queue',
  NOTIFICATION: 'crm-notification-queue',
  ANALYTICS: 'crm-analytics-queue',
  INTEGRATION: 'crm-integration-queue',
  WORKFLOW: 'crm-workflow-queue',
} as const;

/**
 * Queue Module for CRM
 * Configures BullMQ queues for async processing
 */
@Module({
  imports: [
    ConfigModule,
    
    // Configure BullMQ with Redis connection
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        connection: {
          host: configService.get('REDIS_HOST', 'localhost'),
          port: configService.get<number>('REDIS_PORT', 6379),
          password: configService.get('REDIS_PASSWORD'),
          db: configService.get<number>('REDIS_DB', 0),
          maxRetriesPerRequest: null,
          retryDelayOnFailover: 100,
          enableReadyCheck: false,
        },
        defaultJobOptions: {
          removeOnComplete: 100, // Keep last 100 completed jobs
          removeOnFail: 50, // Keep last 50 failed jobs
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      }),
      inject: [ConfigService],
    }),

    // Register individual queues
    BullModule.registerQueue(
      {
        name: QUEUE_NAMES.EMAIL,
        defaultJobOptions: {
          removeOnComplete: 50,
          removeOnFail: 25,
          attempts: 5,
          backoff: {
            type: 'exponential',
            delay: 3000,
          },
        },
      },
      {
        name: QUEUE_NAMES.NOTIFICATION,
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'fixed',
            delay: 1000,
          },
        },
      },
      {
        name: QUEUE_NAMES.ANALYTICS,
        defaultJobOptions: {
          removeOnComplete: 200,
          removeOnFail: 100,
          attempts: 2,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
        },
      },
      {
        name: QUEUE_NAMES.INTEGRATION,
        defaultJobOptions: {
          removeOnComplete: 50,
          removeOnFail: 25,
          attempts: 5,
          backoff: {
            type: 'exponential',
            delay: 10000,
          },
        },
      },
      {
        name: QUEUE_NAMES.WORKFLOW,
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      },
    ),
  ],
  providers: [
    // Queue processors
    EmailQueueProcessor,
    NotificationQueueProcessor,
    AnalyticsQueueProcessor,
    IntegrationQueueProcessor,
    WorkflowQueueProcessor,

    // Queue producers
    EmailQueueProducer,
    NotificationQueueProducer,
    AnalyticsQueueProducer,
    IntegrationQueueProducer,
    WorkflowQueueProducer,
  ],
  exports: [
    // Export producers for use in other modules
    EmailQueueProducer,
    NotificationQueueProducer,
    AnalyticsQueueProducer,
    IntegrationQueueProducer,
    WorkflowQueueProducer,
  ],
})
export class QueueModule {}

/**
 * Queue job types
 */
export const JOB_TYPES = {
  // Email jobs
  SEND_WELCOME_EMAIL: 'send-welcome-email',
  SEND_LEAD_NOTIFICATION: 'send-lead-notification',
  SEND_OPPORTUNITY_UPDATE: 'send-opportunity-update',
  SEND_BULK_EMAIL: 'send-bulk-email',

  // Notification jobs
  PUSH_NOTIFICATION: 'push-notification',
  SMS_NOTIFICATION: 'sms-notification',
  IN_APP_NOTIFICATION: 'in-app-notification',
  SLACK_NOTIFICATION: 'slack-notification',

  // Analytics jobs
  CALCULATE_LEAD_SCORE: 'calculate-lead-score',
  UPDATE_PIPELINE_METRICS: 'update-pipeline-metrics',
  GENERATE_REPORT: 'generate-report',
  SYNC_ANALYTICS_DATA: 'sync-analytics-data',

  // Integration jobs
  SYNC_TO_ODOO: 'sync-to-odoo',
  SYNC_FROM_ODOO: 'sync-from-odoo',
  WEBHOOK_DELIVERY: 'webhook-delivery',
  EXTERNAL_API_CALL: 'external-api-call',

  // Workflow jobs
  LEAD_NURTURING_STEP: 'lead-nurturing-step',
  OPPORTUNITY_FOLLOW_UP: 'opportunity-follow-up',
  AUTO_ASSIGNMENT: 'auto-assignment',
  DATA_ENRICHMENT: 'data-enrichment',
} as const;

/**
 * Job priorities
 */
export enum JobPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  CRITICAL = 4,
}

/**
 * Common job options interface
 */
export interface BaseJobOptions {
  priority?: JobPriority;
  delay?: number;
  repeat?: {
    pattern?: string; // cron pattern
    every?: number; // milliseconds
    limit?: number;
  };
  attempts?: number;
  backoff?: {
    type: 'fixed' | 'exponential';
    delay: number;
  };
  removeOnComplete?: number | boolean;
  removeOnFail?: number | boolean;
}

/**
 * Job data interfaces
 */
export interface EmailJobData {
  to: string | string[];
  subject: string;
  template: string;
  data: Record<string, any>;
  from?: string;
  cc?: string[];
  bcc?: string[];
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

export interface NotificationJobData {
  userId: string | string[];
  type: 'push' | 'sms' | 'in-app' | 'slack';
  title: string;
  message: string;
  data?: Record<string, any>;
  channels?: string[];
}

export interface AnalyticsJobData {
  type: 'lead-score' | 'pipeline-metrics' | 'report' | 'sync';
  entityId?: string;
  entityType?: string;
  parameters?: Record<string, any>;
  reportType?: string;
  dateRange?: {
    from: Date;
    to: Date;
  };
}

export interface IntegrationJobData {
  type: 'sync-to-odoo' | 'sync-from-odoo' | 'webhook' | 'api-call';
  entityId?: string;
  entityType?: string;
  endpoint?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  payload?: Record<string, any>;
  headers?: Record<string, string>;
  retryConfig?: {
    maxRetries: number;
    backoffStrategy: 'linear' | 'exponential';
  };
}

export interface WorkflowJobData {
  type: 'nurturing' | 'follow-up' | 'assignment' | 'enrichment';
  leadId?: number;
  opportunityId?: number;
  userId?: number;
  teamId?: number;
  stepId?: string;
  workflowId?: string;
  parameters?: Record<string, any>;
}
