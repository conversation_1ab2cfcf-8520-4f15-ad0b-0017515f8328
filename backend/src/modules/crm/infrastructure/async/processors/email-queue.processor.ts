import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QUEUE_NAMES, JOB_TYPES, EmailJobData } from '../queue.module';

/**
 * Email Queue Processor
 * Processes email jobs from the queue
 */
@Injectable()
@Processor(QUEUE_NAMES.EMAIL)
export class EmailQueueProcessor extends WorkerHost {
  private readonly logger = new Logger(EmailQueueProcessor.name);

  /**
   * Process email jobs
   */
  async process(job: Job<EmailJobData, any, string>): Promise<any> {
    this.logger.debug(`Processing email job: ${job.name} (${job.id})`);

    try {
      // Update job progress
      await job.updateProgress(10);

      switch (job.name) {
        case JOB_TYPES.SEND_WELCOME_EMAIL:
          return await this.processWelcomeEmail(job);

        case JOB_TYPES.SEND_LEAD_NOTIFICATION:
          return await this.processLeadNotification(job);

        case JOB_TYPES.SEND_OPPORTUNITY_UPDATE:
          return await this.processOpportunityUpdate(job);

        case JOB_TYPES.SEND_BULK_EMAIL:
          return await this.processBulkEmail(job);

        default:
          throw new Error(`Unknown email job type: ${job.name}`);
      }
    } catch (error) {
      this.logger.error(`Failed to process email job: ${job.name} (${job.id})`, error);
      throw error;
    }
  }

  /**
   * Process welcome email
   */
  private async processWelcomeEmail(job: Job<EmailJobData>): Promise<any> {
    const { to, subject, template, data } = job.data;

    this.logger.log(`Sending welcome email to: ${to}`);

    await job.updateProgress(30);

    // Simulate email template rendering
    const renderedContent = await this.renderEmailTemplate(template, data);

    await job.updateProgress(60);

    // Simulate email sending
    const result = await this.sendEmail({
      to,
      subject,
      html: renderedContent.html,
      text: renderedContent.text,
    });

    await job.updateProgress(100);

    this.logger.log(`Welcome email sent successfully to: ${to}`);

    return {
      messageId: result.messageId,
      recipient: to,
      template,
      sentAt: new Date().toISOString(),
    };
  }

  /**
   * Process lead notification email
   */
  private async processLeadNotification(job: Job<EmailJobData>): Promise<any> {
    const { to, subject, template, data } = job.data;

    this.logger.log(`Sending lead notification to: ${to}`);

    await job.updateProgress(30);

    const renderedContent = await this.renderEmailTemplate(template, data);

    await job.updateProgress(60);

    const result = await this.sendEmail({
      to,
      subject,
      html: renderedContent.html,
      text: renderedContent.text,
    });

    await job.updateProgress(100);

    this.logger.log(`Lead notification sent successfully to: ${to}`);

    return {
      messageId: result.messageId,
      recipient: to,
      leadId: data.leadId,
      template,
      sentAt: new Date().toISOString(),
    };
  }

  /**
   * Process opportunity update email
   */
  private async processOpportunityUpdate(job: Job<EmailJobData>): Promise<any> {
    const { to, subject, template, data } = job.data;

    this.logger.log(`Sending opportunity update to: ${to}`);

    await job.updateProgress(30);

    const renderedContent = await this.renderEmailTemplate(template, data);

    await job.updateProgress(60);

    const result = await this.sendEmail({
      to,
      subject,
      html: renderedContent.html,
      text: renderedContent.text,
    });

    await job.updateProgress(100);

    this.logger.log(`Opportunity update sent successfully to: ${to}`);

    return {
      messageId: result.messageId,
      recipient: to,
      opportunityId: data.opportunityId,
      updateType: data.updateType,
      template,
      sentAt: new Date().toISOString(),
    };
  }

  /**
   * Process bulk email
   */
  private async processBulkEmail(job: Job<EmailJobData>): Promise<any> {
    const { to, subject, template, data } = job.data;

    this.logger.log(`Sending bulk email to: ${to}`);

    await job.updateProgress(30);

    const renderedContent = await this.renderEmailTemplate(template, data);

    await job.updateProgress(60);

    const result = await this.sendEmail({
      to,
      subject,
      html: renderedContent.html,
      text: renderedContent.text,
    });

    await job.updateProgress(100);

    this.logger.log(`Bulk email sent successfully to: ${to}`);

    return {
      messageId: result.messageId,
      recipient: to,
      campaignId: data.campaignId,
      template,
      sentAt: new Date().toISOString(),
    };
  }

  /**
   * Render email template
   */
  private async renderEmailTemplate(
    template: string,
    data: Record<string, any>,
  ): Promise<{ html: string; text: string }> {
    // Simulate template rendering
    // In a real implementation, you would use a template engine like Handlebars, Mustache, etc.
    
    const html = `
      <html>
        <body>
          <h1>Email Template: ${template}</h1>
          <p>Data: ${JSON.stringify(data, null, 2)}</p>
        </body>
      </html>
    `;

    const text = `Email Template: ${template}\nData: ${JSON.stringify(data, null, 2)}`;

    return { html, text };
  }

  /**
   * Send email
   */
  private async sendEmail(emailData: {
    to: string | string[];
    subject: string;
    html: string;
    text: string;
  }): Promise<{ messageId: string }> {
    // Simulate email sending
    // In a real implementation, you would use an email service like SendGrid, AWS SES, etc.
    
    this.logger.debug(`Simulating email send to: ${emailData.to}`);
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simulate occasional failures for testing retry logic
    if (Math.random() < 0.05) { // 5% failure rate
      throw new Error('Simulated email service failure');
    }

    return {
      messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  /**
   * Handle job completion
   */
  @OnWorkerEvent('completed')
  onCompleted(job: Job<EmailJobData>, result: any) {
    this.logger.log(`Email job completed: ${job.name} (${job.id})`);
    
    // You could emit events here for analytics, notifications, etc.
    // this.eventEmitter.emit('email.sent', {
    //   jobId: job.id,
    //   jobName: job.name,
    //   recipient: job.data.to,
    //   result,
    // });
  }

  /**
   * Handle job failure
   */
  @OnWorkerEvent('failed')
  onFailed(job: Job<EmailJobData>, error: Error) {
    this.logger.error(`Email job failed: ${job.name} (${job.id})`, error);
    
    // You could emit events here for error tracking, alerts, etc.
    // this.eventEmitter.emit('email.failed', {
    //   jobId: job.id,
    //   jobName: job.name,
    //   recipient: job.data.to,
    //   error: error.message,
    //   attemptsMade: job.attemptsMade,
    // });
  }

  /**
   * Handle job progress
   */
  @OnWorkerEvent('progress')
  onProgress(job: Job<EmailJobData>, progress: number | object) {
    this.logger.debug(`Email job progress: ${job.name} (${job.id}) - ${progress}%`);
  }

  /**
   * Handle worker error
   */
  @OnWorkerEvent('error')
  onError(error: Error) {
    this.logger.error('Email worker error:', error);
  }

  /**
   * Handle worker stalled
   */
  @OnWorkerEvent('stalled')
  onStalled(jobId: string) {
    this.logger.warn(`Email job stalled: ${jobId}`);
  }
}
