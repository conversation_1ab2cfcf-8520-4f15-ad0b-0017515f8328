import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class NotificationQueueProducer {
  private readonly logger = new Logger(NotificationQueueProducer.name);

  async sendAssignmentNotification(data: any): Promise<void> {
    this.logger.log('NotificationQueueProducer - sendAssignmentNotification - Placeholder');
  }

  async sendTeamNotification(data: any): Promise<void> {
    this.logger.log('NotificationQueueProducer - sendTeamNotification - Placeholder');
  }
}
