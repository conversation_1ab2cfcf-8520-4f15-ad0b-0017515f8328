import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class IntegrationQueueProducer {
  private readonly logger = new Logger(IntegrationQueueProducer.name);

  async syncToOdoo(data: any): Promise<void> {
    this.logger.log('IntegrationQueueProducer - syncToOdoo - Placeholder');
  }

  async sendWebhook(data: any): Promise<void> {
    this.logger.log('IntegrationQueueProducer - sendWebhook - Placeholder');
  }
}
