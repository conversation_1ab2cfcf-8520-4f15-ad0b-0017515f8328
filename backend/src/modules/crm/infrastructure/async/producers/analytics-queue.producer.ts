import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { QUEUE_NAMES, JOB_TYPES, JobPriority, AnalyticsJobData, BaseJobOptions } from '@/modules/crm/infrastructure/async/queue.module';

/**
 * Analytics Queue Producer
 * Handles adding analytics jobs to the queue for async processing
 */
@Injectable()
export class AnalyticsQueueProducer {
  private readonly logger = new Logger(AnalyticsQueueProducer.name);

  constructor(
    @InjectQueue(QUEUE_NAMES.ANALYTICS) private readonly analyticsQueue: Queue,
  ) {}

  /**
   * Calculate lead score
   */
  async calculateLeadScore(data: {
    leadId: string;
    factors?: Record<string, any>;
    historicalData?: any;
    priority?: JobPriority;
  }, options: BaseJobOptions = {}): Promise<void> {
    const jobData: AnalyticsJobData = {
      type: 'lead-score',
      entityId: data.leadId,
      entityType: 'lead',
      parameters: {
        factors: data.factors,
        historicalData: data.historicalData,
      },
    };

    await this.addAnalyticsJob(
      JOB_TYPES.CALCULATE_LEAD_SCORE,
      jobData,
      {
        priority: data.priority || JobPriority.NORMAL,
        ...options,
      },
    );

    this.logger.log(`Queued lead scoring for lead: ${data.leadId}`);
  }

  /**
   * Update pipeline metrics
   */
  async updatePipelineMetrics(data: {
    type: 'lead_created' | 'lead_qualified' | 'lead_converted' | 'opportunity_won' | 'opportunity_lost';
    leadId?: string;
    opportunityId?: string;
    teamId?: string;
    source?: string;
    timestamp?: Date;
    value?: number;
  }, options: BaseJobOptions = {}): Promise<void> {
    const jobData: AnalyticsJobData = {
      type: 'pipeline-metrics',
      entityId: data.leadId || data.opportunityId,
      entityType: data.leadId ? 'lead' : 'opportunity',
      parameters: {
        eventType: data.type,
        teamId: data.teamId,
        source: data.source,
        timestamp: data.timestamp || new Date(),
        value: data.value,
      },
    };

    await this.addAnalyticsJob(
      JOB_TYPES.UPDATE_PIPELINE_METRICS,
      jobData,
      {
        priority: JobPriority.LOW,
        ...options,
      },
    );

    this.logger.log(`Queued pipeline metrics update for type: ${data.type}`);
  }

  /**
   * Generate analytics report
   */
  async generateReport(data: {
    reportType: 'lead_scoring_summary' | 'pipeline_performance' | 'conversion_analysis' | 'team_performance';
    dateRange?: {
      from: Date;
      to: Date;
    };
    filters?: Record<string, any>;
    teamId?: string;
    userId?: string;
    format?: 'json' | 'csv' | 'pdf';
  }, options: BaseJobOptions = {}): Promise<void> {
    const jobData: AnalyticsJobData = {
      type: 'report',
      reportType: data.reportType,
      parameters: {
        dateRange: data.dateRange,
        filters: data.filters,
        teamId: data.teamId,
        userId: data.userId,
        format: data.format || 'json',
      },
    };

    await this.addAnalyticsJob(
      JOB_TYPES.GENERATE_REPORT,
      jobData,
      {
        priority: JobPriority.LOW,
        ...options,
      },
    );

    this.logger.log(`Queued report generation: ${data.reportType}`);
  }

  /**
   * Sync analytics data with external systems
   */
  async syncAnalyticsData(data: {
    type: 'lead_scores' | 'pipeline_metrics' | 'conversion_data' | 'team_performance';
    entityIds?: string[];
    dateRange?: {
      from: Date;
      to: Date;
    };
    destination?: string;
    batchSize?: number;
  }, options: BaseJobOptions = {}): Promise<void> {
    const jobData: AnalyticsJobData = {
      type: 'sync',
      parameters: {
        syncType: data.type,
        entityIds: data.entityIds,
        dateRange: data.dateRange,
        destination: data.destination,
        batchSize: data.batchSize || 100,
      },
    };

    await this.addAnalyticsJob(
      JOB_TYPES.SYNC_ANALYTICS_DATA,
      jobData,
      {
        priority: JobPriority.LOW,
        ...options,
      },
    );

    this.logger.log(`Queued analytics data sync for type: ${data.type}`);
  }

  /**
   * Batch calculate lead scores for multiple leads
   */
  async batchCalculateLeadScores(data: {
    leadIds: string[];
    batchSize?: number;
    priority?: JobPriority;
  }, options: BaseJobOptions = {}): Promise<void> {
    const batchSize = data.batchSize || 10;
    const batches = this.chunkArray(data.leadIds, batchSize);

    const jobs = batches.map((batch, index) => ({
      name: JOB_TYPES.CALCULATE_LEAD_SCORE,
      data: {
        type: 'lead-score',
        parameters: {
          leadIds: batch,
          batchIndex: index,
          totalBatches: batches.length,
        },
      } as AnalyticsJobData,
      opts: {
        priority: data.priority || JobPriority.NORMAL,
        delay: index * 1000, // Stagger batches by 1 second
        ...options,
      },
    }));

    await this.analyticsQueue.addBulk(jobs);

    this.logger.log(`Queued ${batches.length} batch lead scoring jobs for ${data.leadIds.length} leads`);
  }

  /**
   * Schedule recurring analytics tasks
   */
  async scheduleRecurringAnalytics(data: {
    taskType: 'daily_metrics' | 'weekly_report' | 'monthly_summary';
    schedule: string; // cron pattern
    parameters?: Record<string, any>;
  }, options: BaseJobOptions = {}): Promise<void> {
    const jobData: AnalyticsJobData = {
      type: 'sync',
      parameters: {
        taskType: data.taskType,
        ...data.parameters,
      },
    };

    await this.addAnalyticsJob(
      `recurring-${data.taskType}`,
      jobData,
      {
        priority: JobPriority.LOW,
        repeat: {
          pattern: data.schedule,
        },
        ...options,
      },
    );

    this.logger.log(`Scheduled recurring analytics task: ${data.taskType} with pattern: ${data.schedule}`);
  }

  /**
   * Add analytics job to queue
   */
  private async addAnalyticsJob(
    jobName: string,
    jobData: AnalyticsJobData,
    options: BaseJobOptions = {},
  ): Promise<void> {
    try {
      await this.analyticsQueue.add(jobName, jobData, {
        priority: options.priority || JobPriority.NORMAL,
        delay: options.delay,
        repeat: options.repeat,
        attempts: options.attempts || 3,
        backoff: options.backoff || {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: options.removeOnComplete || 100,
        removeOnFail: options.removeOnFail || 50,
      });
    } catch (error) {
      this.logger.error(`Failed to add analytics job: ${jobName}`, error);
      throw error;
    }
  }

  /**
   * Utility method to chunk array into smaller arrays
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
  }> {
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      this.analyticsQueue.getWaiting(),
      this.analyticsQueue.getActive(),
      this.analyticsQueue.getCompleted(),
      this.analyticsQueue.getFailed(),
      this.analyticsQueue.getDelayed(),
    ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
    };
  }

  /**
   * Clean completed jobs
   */
  async cleanCompletedJobs(olderThan: number = 24 * 60 * 60 * 1000): Promise<number> {
    const cleaned = await this.analyticsQueue.clean(olderThan, 100, 'completed');
    this.logger.log(`Cleaned ${cleaned.length} completed analytics jobs`);
    return cleaned.length;
  }

  /**
   * Clean failed jobs
   */
  async cleanFailedJobs(olderThan: number = 7 * 24 * 60 * 60 * 1000): Promise<number> {
    const cleaned = await this.analyticsQueue.clean(olderThan, 50, 'failed');
    this.logger.log(`Cleaned ${cleaned.length} failed analytics jobs`);
    return cleaned.length;
  }
}
