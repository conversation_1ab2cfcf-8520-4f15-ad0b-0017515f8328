import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { QUEUE_NAMES, JOB_TYPES, JobPriority, EmailJobData, BaseJobOptions } from '../queue.module';

/**
 * Email Queue Producer
 * Handles adding email jobs to the queue for async processing
 */
@Injectable()
export class EmailQueueProducer {
  private readonly logger = new Logger(EmailQueueProducer.name);

  constructor(
    @InjectQueue(QUEUE_NAMES.EMAIL) private readonly emailQueue: Queue,
  ) {}

  /**
   * Send welcome email to new lead
   */
  async sendWelcomeEmail(
    leadData: {
      email: string;
      name: string;
      company?: string;
      source?: string;
    },
    options: BaseJobOptions = {},
  ): Promise<void> {
    const jobData: EmailJobData = {
      to: leadData.email,
      subject: 'Welcome to our CRM system',
      template: 'welcome-lead',
      data: {
        name: leadData.name,
        company: leadData.company,
        source: leadData.source,
      },
    };

    await this.addEmailJob(
      JOB_TYPES.SEND_WELCOME_EMAIL,
      jobData,
      {
        priority: JobPriority.HIGH,
        ...options,
      },
    );

    this.logger.log(`Queued welcome email for lead: ${leadData.email}`);
  }

  /**
   * Send lead assignment notification
   */
  async sendLeadAssignmentNotification(
    assigneeData: {
      email: string;
      name: string;
    },
    leadData: {
      id: number;
      name: string;
      company?: string;
      priority: string;
    },
    options: BaseJobOptions = {},
  ): Promise<void> {
    const jobData: EmailJobData = {
      to: assigneeData.email,
      subject: `New Lead Assigned: ${leadData.name}`,
      template: 'lead-assignment',
      data: {
        assigneeName: assigneeData.name,
        leadId: leadData.id,
        leadName: leadData.name,
        leadCompany: leadData.company,
        leadPriority: leadData.priority,
        dashboardUrl: `${process.env.FRONTEND_URL}/leads/${leadData.id}`,
      },
    };

    await this.addEmailJob(
      JOB_TYPES.SEND_LEAD_NOTIFICATION,
      jobData,
      {
        priority: JobPriority.HIGH,
        ...options,
      },
    );

    this.logger.log(`Queued lead assignment notification for: ${assigneeData.email}`);
  }

  /**
   * Send opportunity update notification
   */
  async sendOpportunityUpdateNotification(
    recipientData: {
      email: string;
      name: string;
    },
    opportunityData: {
      id: number;
      name: string;
      stage: string;
      value: number;
      probability: number;
    },
    updateType: 'stage_change' | 'value_change' | 'won' | 'lost',
    options: BaseJobOptions = {},
  ): Promise<void> {
    const jobData: EmailJobData = {
      to: recipientData.email,
      subject: `Opportunity Update: ${opportunityData.name}`,
      template: 'opportunity-update',
      data: {
        recipientName: recipientData.name,
        opportunityId: opportunityData.id,
        opportunityName: opportunityData.name,
        stage: opportunityData.stage,
        value: opportunityData.value,
        probability: opportunityData.probability,
        updateType,
        dashboardUrl: `${process.env.FRONTEND_URL}/opportunities/${opportunityData.id}`,
      },
    };

    const priority = updateType === 'won' || updateType === 'lost' 
      ? JobPriority.CRITICAL 
      : JobPriority.NORMAL;

    await this.addEmailJob(
      JOB_TYPES.SEND_OPPORTUNITY_UPDATE,
      jobData,
      {
        priority,
        ...options,
      },
    );

    this.logger.log(`Queued opportunity update notification for: ${recipientData.email}`);
  }

  /**
   * Send bulk email campaign
   */
  async sendBulkEmailCampaign(
    campaignData: {
      id: string;
      name: string;
      subject: string;
      template: string;
      recipients: Array<{
        email: string;
        name: string;
        customData?: Record<string, any>;
      }>;
      globalData?: Record<string, any>;
    },
    options: BaseJobOptions = {},
  ): Promise<void> {
    // Split bulk email into individual jobs for better error handling and retry logic
    const jobs = campaignData.recipients.map((recipient, index) => {
      const jobData: EmailJobData = {
        to: recipient.email,
        subject: campaignData.subject,
        template: campaignData.template,
        data: {
          ...campaignData.globalData,
          ...recipient.customData,
          recipientName: recipient.name,
          campaignId: campaignData.id,
          campaignName: campaignData.name,
        },
      };

      return {
        name: JOB_TYPES.SEND_BULK_EMAIL,
        data: jobData,
        opts: {
          priority: JobPriority.LOW,
          delay: index * 100, // Stagger emails to avoid overwhelming SMTP
          ...options,
        },
      };
    });

    await this.emailQueue.addBulk(jobs);

    this.logger.log(`Queued ${jobs.length} bulk emails for campaign: ${campaignData.name}`);
  }

  /**
   * Schedule recurring email report
   */
  async scheduleRecurringReport(
    reportData: {
      type: string;
      recipients: string[];
      schedule: string; // cron pattern
      parameters?: Record<string, any>;
    },
    options: BaseJobOptions = {},
  ): Promise<void> {
    const jobData: EmailJobData = {
      to: reportData.recipients,
      subject: `Scheduled Report: ${reportData.type}`,
      template: 'scheduled-report',
      data: {
        reportType: reportData.type,
        parameters: reportData.parameters,
        generatedAt: new Date().toISOString(),
      },
    };

    await this.addEmailJob(
      `scheduled-report-${reportData.type}`,
      jobData,
      {
        priority: JobPriority.LOW,
        repeat: {
          pattern: reportData.schedule,
        },
        ...options,
      },
    );

    this.logger.log(`Scheduled recurring report: ${reportData.type} with pattern: ${reportData.schedule}`);
  }

  /**
   * Add email job to queue
   */
  private async addEmailJob(
    jobName: string,
    jobData: EmailJobData,
    options: BaseJobOptions = {},
  ): Promise<void> {
    try {
      await this.emailQueue.add(jobName, jobData, {
        priority: options.priority || JobPriority.NORMAL,
        delay: options.delay,
        repeat: options.repeat,
        attempts: options.attempts,
        backoff: options.backoff,
        removeOnComplete: options.removeOnComplete,
        removeOnFail: options.removeOnFail,
      });
    } catch (error) {
      this.logger.error(`Failed to add email job: ${jobName}`, error);
      throw error;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
  }> {
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      this.emailQueue.getWaiting(),
      this.emailQueue.getActive(),
      this.emailQueue.getCompleted(),
      this.emailQueue.getFailed(),
      this.emailQueue.getDelayed(),
    ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
    };
  }

  /**
   * Pause email queue
   */
  async pauseQueue(): Promise<void> {
    await this.emailQueue.pause();
    this.logger.log('Email queue paused');
  }

  /**
   * Resume email queue
   */
  async resumeQueue(): Promise<void> {
    await this.emailQueue.resume();
    this.logger.log('Email queue resumed');
  }

  /**
   * Clean completed jobs
   */
  async cleanCompletedJobs(olderThan: number = 24 * 60 * 60 * 1000): Promise<number> {
    const cleaned = await this.emailQueue.clean(olderThan, 100, 'completed');
    this.logger.log(`Cleaned ${cleaned.length} completed email jobs`);
    return cleaned.length;
  }

  /**
   * Clean failed jobs
   */
  async cleanFailedJobs(olderThan: number = 7 * 24 * 60 * 60 * 1000): Promise<number> {
    const cleaned = await this.emailQueue.clean(olderThan, 50, 'failed');
    this.logger.log(`Cleaned ${cleaned.length} failed email jobs`);
    return cleaned.length;
  }
}
