import { Injectable, Logger, Inject } from '@nestjs/common';
import { EventStore, DomainEvent } from '../../../domain/events/base/domain-event.base';
import { LeadAggregate } from '../../../domain/aggregates/lead.aggregate';
import { SnapshotService } from './snapshot.service';
import { ProjectionService } from './projection.service';

/**
 * Event Replay Service
 * Handles event replay for aggregate reconstruction and system recovery
 */
@Injectable()
export class EventReplayService {
  private readonly logger = new Logger(EventReplayService.name);

  constructor(
    @Inject('EVENT_STORE') private readonly eventStore: EventStore,
    private readonly snapshotService: SnapshotService,
    private readonly projectionService: ProjectionService,
  ) {}

  /**
   * Replay events for a specific aggregate
   */
  async replayAggregateEvents(
    aggregateId: string,
    aggregateType: string,
    fromVersion?: number,
    toVersion?: number
  ): Promise<ReplayResult> {
    this.logger.log(`Replaying events for aggregate: ${aggregateType}-${aggregateId}`);

    const result: ReplayResult = {
      aggregateId,
      aggregateType,
      startTime: new Date(),
      endTime: new Date(),
      eventsReplayed: 0,
      success: false,
      errors: [],
    };

    try {
      const streamName = `${aggregateType}-${aggregateId}`;
      
      // Get events from event store
      const allEvents = await this.eventStore.getEvents(streamName, fromVersion);
      const events = toVersion ? allEvents.filter(e => e.aggregateVersion <= toVersion) : allEvents;
      
      if (events.length === 0) {
        this.logger.warn(`No events found for aggregate: ${aggregateType}-${aggregateId}`);
        result.success = true;
        result.endTime = new Date();
        return result;
      }

      // Replay events based on aggregate type
      let aggregate: any;
      switch (aggregateType) {
        case 'Lead':
          aggregate = await this.replayLeadAggregate(aggregateId, events);
          break;
        default:
          throw new Error(`Unsupported aggregate type for replay: ${aggregateType}`);
      }

      result.eventsReplayed = events.length;
      result.success = true;
      result.aggregate = aggregate;
      result.endTime = new Date();

      this.logger.log(`Successfully replayed ${events.length} events for aggregate: ${aggregateType}-${aggregateId}`);
      return result;

    } catch (error) {
      result.errors.push(error.message);
      result.endTime = new Date();
      this.logger.error(`Failed to replay events for aggregate: ${aggregateType}-${aggregateId}`, error);
      throw error;
    }
  }

  /**
   * Replay events for Lead aggregate
   */
  private async replayLeadAggregate(aggregateId: string, events: DomainEvent[]): Promise<LeadAggregate> {
    // Try to load from snapshot first
    const snapshot = await this.snapshotService.getSnapshot(aggregateId, 'Lead');
    
    let aggregate: LeadAggregate;
    let eventsToReplay = events;

    if (snapshot) {
      // Create aggregate from snapshot
      aggregate = new LeadAggregate();
      // Would need to implement restoreFromSnapshot method
      this.logger.debug(`Loaded Lead aggregate from snapshot at version: ${snapshot.version}`);
      
      // Filter events to only those after snapshot
      eventsToReplay = events.filter(event => event.aggregateVersion > snapshot.version);
    } else {
      // Create new aggregate
      aggregate = new LeadAggregate();
    }

    // Apply events to reconstruct state
    if (eventsToReplay.length > 0) {
      aggregate.loadFromHistory(eventsToReplay);
      this.logger.debug(`Applied ${eventsToReplay.length} events to Lead aggregate: ${aggregateId}`);
    }

    return aggregate;
  }

  /**
   * Replay all events for projection rebuilding
   */
  async replayAllEvents(
    fromPosition?: number,
    toPosition?: number,
    batchSize: number = 100
  ): Promise<ReplayAllResult> {
    this.logger.log(`Starting replay of all events from position: ${fromPosition || 0}`);

    const result: ReplayAllResult = {
      startTime: new Date(),
      endTime: new Date(),
      totalEventsReplayed: 0,
      batchesProcessed: 0,
      success: false,
      errors: [],
    };

    try {
      let currentPosition = fromPosition || 0;
      let hasMoreEvents = true;

      while (hasMoreEvents) {
        // Get batch of events
        const allEvents = await this.eventStore.getAllEvents(currentPosition);
        const events = allEvents.slice(0, batchSize);
        
        if (events.length === 0) {
          hasMoreEvents = false;
          break;
        }

        // Filter events if toPosition is specified
        const eventsToProcess = toPosition 
          ? events.filter(event => event.metadata.position <= toPosition)
          : events;

        if (eventsToProcess.length === 0) {
          hasMoreEvents = false;
          break;
        }

        // Process batch
        await this.processBatch(eventsToProcess);

        result.totalEventsReplayed += eventsToProcess.length;
        result.batchesProcessed++;
        currentPosition += eventsToProcess.length;

        // Check if we've reached the end position
        if (toPosition && currentPosition >= toPosition) {
          hasMoreEvents = false;
        }

        this.logger.debug(`Processed batch ${result.batchesProcessed}: ${eventsToProcess.length} events`);
      }

      result.success = true;
      result.endTime = new Date();

      this.logger.log(`Successfully replayed ${result.totalEventsReplayed} events in ${result.batchesProcessed} batches`);
      return result;

    } catch (error) {
      result.errors.push(error.message);
      result.endTime = new Date();
      this.logger.error('Failed to replay all events', error);
      throw error;
    }
  }

  /**
   * Process a batch of events
   */
  private async processBatch(events: DomainEvent[]): Promise<void> {
    for (const event of events) {
      try {
        // Update projections
        await this.projectionService.updateProjection('lead-projection', event);
        
        // Could add other projection updates here
        
      } catch (error) {
        this.logger.error(`Failed to process event: ${event.eventId}`, error);
        // Continue processing other events
      }
    }
  }

  /**
   * Rebuild specific aggregate from events
   */
  async rebuildAggregate(aggregateId: string, aggregateType: string): Promise<any> {
    this.logger.log(`Rebuilding aggregate: ${aggregateType}-${aggregateId}`);

    try {
      const replayResult = await this.replayAggregateEvents(aggregateId, aggregateType);
      
      if (!replayResult.success) {
        throw new Error(`Failed to rebuild aggregate: ${replayResult.errors.join(', ')}`);
      }

      this.logger.log(`Successfully rebuilt aggregate: ${aggregateType}-${aggregateId}`);
      return replayResult.aggregate;

    } catch (error) {
      this.logger.error(`Failed to rebuild aggregate: ${aggregateType}-${aggregateId}`, error);
      throw error;
    }
  }

  /**
   * Validate event stream consistency
   */
  async validateEventStream(aggregateId: string, aggregateType: string): Promise<ValidationResult> {
    this.logger.log(`Validating event stream for aggregate: ${aggregateType}-${aggregateId}`);

    const result: ValidationResult = {
      aggregateId,
      aggregateType,
      isValid: true,
      issues: [],
      eventCount: 0,
      lastEventVersion: 0,
    };

    try {
      const streamName = `${aggregateType}-${aggregateId}`;
      const events = await this.eventStore.getEvents(streamName);

      result.eventCount = events.length;

      if (events.length === 0) {
        result.issues.push('No events found in stream');
        result.isValid = false;
        return result;
      }

      // Validate event sequence
      for (let i = 0; i < events.length; i++) {
        const event = events[i];
        const expectedVersion = i + 1;

        if (event.aggregateVersion !== expectedVersion) {
          result.issues.push(`Event ${i} has version ${event.aggregateVersion}, expected ${expectedVersion}`);
          result.isValid = false;
        }

        if (event.aggregateId !== aggregateId) {
          result.issues.push(`Event ${i} has wrong aggregate ID: ${event.aggregateId}`);
          result.isValid = false;
        }

        if (event.aggregateType !== aggregateType) {
          result.issues.push(`Event ${i} has wrong aggregate type: ${event.aggregateType}`);
          result.isValid = false;
        }
      }

      result.lastEventVersion = events[events.length - 1].aggregateVersion;

      if (result.isValid) {
        this.logger.log(`Event stream validation passed for: ${aggregateType}-${aggregateId}`);
      } else {
        this.logger.warn(`Event stream validation failed for: ${aggregateType}-${aggregateId} with ${result.issues.length} issues`);
      }

      return result;

    } catch (error) {
      result.isValid = false;
      result.issues.push(`Validation error: ${error.message}`);
      this.logger.error(`Failed to validate event stream for: ${aggregateType}-${aggregateId}`, error);
      return result;
    }
  }

  /**
   * Get replay statistics
   */
  async getReplayStats(): Promise<ReplayStats> {
    // This would be implemented to track replay statistics
    // For now, return mock data
    return {
      totalReplaysPerformed: 0,
      totalEventsReplayed: 0,
      averageReplayDuration: 0,
      lastReplayTime: null,
      successfulReplays: 0,
      failedReplays: 0,
    };
  }
}

/**
 * Replay Result Interface
 */
export interface ReplayResult {
  aggregateId: string;
  aggregateType: string;
  startTime: Date;
  endTime: Date;
  eventsReplayed: number;
  success: boolean;
  errors: string[];
  aggregate?: any;
}

/**
 * Replay All Result Interface
 */
export interface ReplayAllResult {
  startTime: Date;
  endTime: Date;
  totalEventsReplayed: number;
  batchesProcessed: number;
  success: boolean;
  errors: string[];
}

/**
 * Validation Result Interface
 */
export interface ValidationResult {
  aggregateId: string;
  aggregateType: string;
  isValid: boolean;
  issues: string[];
  eventCount: number;
  lastEventVersion: number;
}

/**
 * Replay Statistics Interface
 */
export interface ReplayStats {
  totalReplaysPerformed: number;
  totalEventsReplayed: number;
  averageReplayDuration: number;
  lastReplayTime: Date | null;
  successfulReplays: number;
  failedReplays: number;
}
