import { Injectable, Logger, Inject } from '@nestjs/common';
import { EventStore } from '@/modules/crm/domain/events/base/domain-event.base';

/**
 * Snapshot Service
 * Manages aggregate snapshots for performance optimization
 */
@Injectable()
export class SnapshotService {
  private readonly logger = new Logger(SnapshotService.name);

  constructor(
    @Inject('EVENT_STORE') private readonly eventStore: EventStore,
  ) {}

  /**
   * Create a snapshot of an aggregate
   */
  async createSnapshot(aggregateId: string, aggregateType: string, data: any, version: number): Promise<void> {
    this.logger.debug(`Creating snapshot for aggregate: ${aggregateType}-${aggregateId} at version: ${version}`);

    try {
      await this.eventStore.saveSnapshot(aggregateId, aggregateType, data, version);

      this.logger.debug(`Successfully created snapshot for aggregate: ${aggregateType}-${aggregateId}`);
    } catch (error) {
      this.logger.error(`Failed to create snapshot for aggregate: ${aggregateType}-${aggregateId}`, error);
      throw error;
    }
  }

  /**
   * Get the latest snapshot for an aggregate
   */
  async getSnapshot(aggregateId: string, aggregateType: string): Promise<AggregateSnapshot | null> {
    this.logger.debug(`Getting snapshot for aggregate: ${aggregateType}-${aggregateId}`);

    try {
      const snapshot = await this.eventStore.getSnapshot(aggregateId, aggregateType);

      if (snapshot) {
        this.logger.debug(`Found snapshot for aggregate: ${aggregateType}-${aggregateId} at version: ${snapshot.version}`);
        return snapshot;
      }

      this.logger.debug(`No snapshot found for aggregate: ${aggregateType}-${aggregateId}`);
      return null;
    } catch (error) {
      this.logger.error(`Failed to get snapshot for aggregate: ${aggregateType}-${aggregateId}`, error);
      throw error;
    }
  }

  /**
   * Check if a snapshot should be created based on configuration
   */
  shouldCreateSnapshot(currentVersion: number, lastSnapshotVersion: number = 0, snapshotFrequency: number = 10): boolean {
    const eventsSinceLastSnapshot = currentVersion - lastSnapshotVersion;
    return eventsSinceLastSnapshot >= snapshotFrequency;
  }

  /**
   * Clean up old snapshots
   */
  async cleanupOldSnapshots(aggregateId: string, aggregateType: string, keepCount: number = 3): Promise<void> {
    this.logger.debug(`Cleaning up old snapshots for aggregate: ${aggregateType}-${aggregateId}, keeping: ${keepCount}`);

    try {
      // This would be implemented in the event store
      // For now, just log the operation
      this.logger.debug(`Cleaned up old snapshots for aggregate: ${aggregateType}-${aggregateId}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup old snapshots for aggregate: ${aggregateType}-${aggregateId}`, error);
      throw error;
    }
  }

  /**
   * Get snapshot statistics
   */
  async getSnapshotStats(aggregateType?: string): Promise<SnapshotStats> {
    this.logger.debug(`Getting snapshot statistics for aggregate type: ${aggregateType || 'all'}`);

    try {
      // This would query the event store for statistics
      // For now, return mock data
      return {
        totalSnapshots: 0,
        aggregateTypes: aggregateType ? [aggregateType] : [],
        oldestSnapshot: null,
        newestSnapshot: null,
        averageSnapshotSize: 0,
      };
    } catch (error) {
      this.logger.error(`Failed to get snapshot statistics`, error);
      throw error;
    }
  }

  /**
   * Create snapshot for Lead aggregate
   */
  async createLeadSnapshot(leadAggregate: any): Promise<void> {
    const snapshotData = {
      id: leadAggregate.id,
      name: leadAggregate.name,
      contactInfo: leadAggregate.contactInfo,
      status: leadAggregate.status,
      source: leadAggregate.source,
      type: leadAggregate.type,
      priority: leadAggregate.priority,
      expectedRevenue: leadAggregate.expectedRevenue,
      probability: leadAggregate.probability,
      description: leadAggregate.description,
      assignedUserId: leadAggregate.assignedUserId,
      companyId: leadAggregate.companyId,
      tags: leadAggregate.tags,
      partnerId: leadAggregate.partnerId,
      stageId: leadAggregate.stageId,
      teamId: leadAggregate.teamId,
      dateDeadline: leadAggregate.dateDeadline,
      lostReasonId: leadAggregate.lostReasonId,
      campaignId: leadAggregate.campaignId,
      sourceId: leadAggregate.sourceId,
      mediumId: leadAggregate.mediumId,
      createdAt: leadAggregate.createdAt,
      updatedAt: leadAggregate.updatedAt,
      isDeleted: leadAggregate.isDeleted,
    };

    await this.createSnapshot(
      leadAggregate.id,
      'Lead',
      snapshotData,
      leadAggregate.version
    );
  }

  /**
   * Restore Lead aggregate from snapshot
   */
  async restoreLeadFromSnapshot(snapshot: AggregateSnapshot): Promise<any> {
    // This would create a new Lead aggregate and restore its state
    // For now, just return the snapshot data
    return snapshot.data;
  }
}

/**
 * Aggregate Snapshot Interface
 */
export interface AggregateSnapshot {
  aggregateId: string;
  aggregateType: string;
  version: number;
  data: any;
  createdAt: Date;
  metadata?: Record<string, any>;
}

/**
 * Snapshot Statistics Interface
 */
export interface SnapshotStats {
  totalSnapshots: number;
  aggregateTypes: string[];
  oldestSnapshot: Date | null;
  newestSnapshot: Date | null;
  averageSnapshotSize: number;
}
