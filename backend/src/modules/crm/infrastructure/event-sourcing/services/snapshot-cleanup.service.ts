import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { SnapshotService } from './snapshot.service';
import { SnapshotConfigService } from './snapshot-config.service';

/**
 * Snapshot Cleanup Service
 * Handles automatic cleanup of old snapshots based on retention policies
 */
@Injectable()
export class SnapshotCleanupService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(SnapshotCleanupService.name);
  private cleanupIntervals: Map<string, NodeJS.Timeout> = new Map();
  private isRunning = false;

  constructor(
    private readonly snapshotService: SnapshotService,
    private readonly snapshotConfigService: SnapshotConfigService,
  ) {}

  async onModuleInit() {
    this.logger.log('Initializing Snapshot Cleanup Service');
    await this.startCleanupSchedulers();
  }

  async onModuleDestroy() {
    this.logger.log('Destroying Snapshot Cleanup Service');
    await this.stopCleanupSchedulers();
  }

  /**
   * Start cleanup schedulers for all configured aggregate types
   */
  private async startCleanupSchedulers(): Promise<void> {
    const aggregateTypes = this.snapshotConfigService.getConfiguredAggregateTypes();
    
    for (const aggregateType of aggregateTypes) {
      if (this.snapshotConfigService.isAutoCleanupEnabled(aggregateType)) {
        await this.startCleanupScheduler(aggregateType);
      }
    }

    this.isRunning = true;
    this.logger.log(`Started cleanup schedulers for ${this.cleanupIntervals.size} aggregate types`);
  }

  /**
   * Stop all cleanup schedulers
   */
  private async stopCleanupSchedulers(): Promise<void> {
    for (const [aggregateType, interval] of this.cleanupIntervals) {
      clearInterval(interval);
      this.logger.debug(`Stopped cleanup scheduler for: ${aggregateType}`);
    }

    this.cleanupIntervals.clear();
    this.isRunning = false;
    this.logger.log('Stopped all cleanup schedulers');
  }

  /**
   * Start cleanup scheduler for specific aggregate type
   */
  private async startCleanupScheduler(aggregateType: string): Promise<void> {
    const cleanupInterval = this.snapshotConfigService.getCleanupInterval(aggregateType);
    
    const interval = setInterval(async () => {
      try {
        await this.cleanupSnapshots(aggregateType);
      } catch (error) {
        this.logger.error(`Failed to cleanup snapshots for ${aggregateType}`, error);
      }
    }, cleanupInterval);

    this.cleanupIntervals.set(aggregateType, interval);
    this.logger.debug(`Started cleanup scheduler for: ${aggregateType} (interval: ${cleanupInterval}ms)`);
  }

  /**
   * Manual cleanup trigger for all aggregate types
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async performDailyCleanup(): Promise<void> {
    if (!this.isRunning) {
      this.logger.debug('Cleanup service not running, skipping daily cleanup');
      return;
    }

    this.logger.log('Starting daily snapshot cleanup');
    
    try {
      const aggregateTypes = this.snapshotConfigService.getConfiguredAggregateTypes();
      const cleanupResults: CleanupResult[] = [];

      for (const aggregateType of aggregateTypes) {
        const result = await this.cleanupSnapshots(aggregateType);
        cleanupResults.push(result);
      }

      const totalCleaned = cleanupResults.reduce((sum, result) => sum + result.cleanedCount, 0);
      const totalErrors = cleanupResults.reduce((sum, result) => sum + result.errorCount, 0);

      this.logger.log(`Daily cleanup completed: ${totalCleaned} snapshots cleaned, ${totalErrors} errors`);
    } catch (error) {
      this.logger.error('Failed to perform daily cleanup', error);
    }
  }

  /**
   * Cleanup snapshots for specific aggregate type
   */
  async cleanupSnapshots(aggregateType: string): Promise<CleanupResult> {
    this.logger.debug(`Starting cleanup for aggregate type: ${aggregateType}`);

    const result: CleanupResult = {
      aggregateType,
      cleanedCount: 0,
      errorCount: 0,
      startTime: new Date(),
      endTime: new Date(),
      errors: [],
    };

    try {
      // This would be implemented to query and cleanup old snapshots
      // For now, just log the operation
      this.logger.debug(`Cleanup completed for aggregate type: ${aggregateType}`);
      
      result.endTime = new Date();
      return result;
    } catch (error) {
      result.errorCount++;
      result.errors.push(error.message);
      result.endTime = new Date();
      
      this.logger.error(`Failed to cleanup snapshots for ${aggregateType}`, error);
      return result;
    }
  }

  /**
   * Cleanup snapshots for specific aggregate
   */
  async cleanupAggregateSnapshots(aggregateId: string, aggregateType: string): Promise<CleanupResult> {
    this.logger.debug(`Cleaning up snapshots for aggregate: ${aggregateType}-${aggregateId}`);

    const result: CleanupResult = {
      aggregateType,
      aggregateId,
      cleanedCount: 0,
      errorCount: 0,
      startTime: new Date(),
      endTime: new Date(),
      errors: [],
    };

    try {
      // Use the snapshot service to cleanup old snapshots
      await this.snapshotService.cleanupOldSnapshots(aggregateId, aggregateType);
      
      result.cleanedCount = 1; // Simplified for now
      result.endTime = new Date();
      
      this.logger.debug(`Cleaned up snapshots for aggregate: ${aggregateType}-${aggregateId}`);
      return result;
    } catch (error) {
      result.errorCount++;
      result.errors.push(error.message);
      result.endTime = new Date();
      
      this.logger.error(`Failed to cleanup snapshots for aggregate: ${aggregateType}-${aggregateId}`, error);
      return result;
    }
  }

  /**
   * Get cleanup statistics
   */
  async getCleanupStats(): Promise<CleanupStats> {
    const aggregateTypes = this.snapshotConfigService.getConfiguredAggregateTypes();
    const stats: CleanupStats = {
      isRunning: this.isRunning,
      activeSchedulers: this.cleanupIntervals.size,
      configuredAggregateTypes: aggregateTypes.length,
      lastCleanupTime: new Date(), // Would be tracked in real implementation
      totalCleanupsPerformed: 0, // Would be tracked in real implementation
      totalSnapshotsCleaned: 0, // Would be tracked in real implementation
      averageCleanupDuration: 0, // Would be calculated in real implementation
    };

    return stats;
  }

  /**
   * Force cleanup for specific aggregate type
   */
  async forceCleanup(aggregateType: string): Promise<CleanupResult> {
    this.logger.log(`Force cleanup requested for aggregate type: ${aggregateType}`);
    return await this.cleanupSnapshots(aggregateType);
  }

  /**
   * Restart cleanup scheduler for specific aggregate type
   */
  async restartScheduler(aggregateType: string): Promise<void> {
    // Stop existing scheduler if running
    const existingInterval = this.cleanupIntervals.get(aggregateType);
    if (existingInterval) {
      clearInterval(existingInterval);
      this.cleanupIntervals.delete(aggregateType);
    }

    // Start new scheduler if auto cleanup is enabled
    if (this.snapshotConfigService.isAutoCleanupEnabled(aggregateType)) {
      await this.startCleanupScheduler(aggregateType);
      this.logger.log(`Restarted cleanup scheduler for: ${aggregateType}`);
    } else {
      this.logger.log(`Auto cleanup disabled for: ${aggregateType}, scheduler not started`);
    }
  }

  /**
   * Update cleanup configuration
   */
  async updateCleanupConfig(aggregateType: string, enabled: boolean): Promise<void> {
    if (enabled) {
      await this.startCleanupScheduler(aggregateType);
    } else {
      const existingInterval = this.cleanupIntervals.get(aggregateType);
      if (existingInterval) {
        clearInterval(existingInterval);
        this.cleanupIntervals.delete(aggregateType);
      }
    }

    this.logger.log(`Updated cleanup config for ${aggregateType}: enabled=${enabled}`);
  }
}

/**
 * Cleanup Result Interface
 */
export interface CleanupResult {
  aggregateType: string;
  aggregateId?: string;
  cleanedCount: number;
  errorCount: number;
  startTime: Date;
  endTime: Date;
  errors: string[];
}

/**
 * Cleanup Statistics Interface
 */
export interface CleanupStats {
  isRunning: boolean;
  activeSchedulers: number;
  configuredAggregateTypes: number;
  lastCleanupTime: Date;
  totalCleanupsPerformed: number;
  totalSnapshotsCleaned: number;
  averageCleanupDuration: number;
}
