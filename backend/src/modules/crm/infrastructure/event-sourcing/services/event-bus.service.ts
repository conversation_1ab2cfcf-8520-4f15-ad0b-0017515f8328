import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { DomainEvent, EventBus, EventHandler } from '@/modules/crm/domain/events/base/domain-event.base';
import { EventPublisherService } from './event-publisher.service';

/**
 * Event Bus Service
 * Provides in-memory event bus with external publishing capabilities
 */
@Injectable()
export class EventBusService implements EventBus {
  private readonly logger = new Logger(EventBusService.name);
  private readonly handlers = new Map<string, Set<EventHandler>>();

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly eventPublisher: EventPublisherService,
  ) {}

  /**
   * Publish a single event
   */
  async publish(event: DomainEvent): Promise<void> {
    this.logger.debug(`Publishing event: ${event.eventType} (${event.eventId})`);

    try {
      // Emit to local handlers first
      await this.emitToLocalHandlers(event);

      // Publish externally if required
      if (event.shouldPublishExternally()) {
        await this.eventPublisher.publishExternal(event);
      }

      this.logger.debug(`Successfully published event: ${event.eventType} (${event.eventId})`);
    } catch (error) {
      this.logger.error(`Failed to publish event: ${event.eventType} (${event.eventId})`, error);
      throw error;
    }
  }

  /**
   * Publish multiple events in batch
   */
  async publishBatch(events: DomainEvent[]): Promise<void> {
    this.logger.debug(`Publishing batch of ${events.length} events`);

    try {
      // Process events in parallel for better performance
      await Promise.all(events.map(event => this.publish(event)));

      this.logger.debug(`Successfully published batch of ${events.length} events`);
    } catch (error) {
      this.logger.error(`Failed to publish event batch`, error);
      throw error;
    }
  }

  /**
   * Subscribe to events
   */
  subscribe<T extends DomainEvent>(eventType: string, handler: EventHandler<T>): void {
    this.logger.debug(`Subscribing handler to event type: ${eventType}`);

    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, new Set());
    }

    this.handlers.get(eventType)!.add(handler);

    // Also subscribe to EventEmitter2 for NestJS integration
    this.eventEmitter.on(eventType, async (event: T) => {
      try {
        if (handler.canHandle(event)) {
          await handler.handle(event);
        }
      } catch (error) {
        this.logger.error(`Handler failed for event: ${eventType}`, error);
        // Don't throw - we want other handlers to continue
      }
    });

    this.logger.debug(`Handler subscribed to event type: ${eventType}`);
  }

  /**
   * Unsubscribe from events
   */
  unsubscribe(eventType: string, handler: EventHandler): void {
    this.logger.debug(`Unsubscribing handler from event type: ${eventType}`);

    const handlers = this.handlers.get(eventType);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.handlers.delete(eventType);
      }
    }

    // Note: EventEmitter2 doesn't provide easy way to remove specific handler
    // In production, you might want to use a more sophisticated approach

    this.logger.debug(`Handler unsubscribed from event type: ${eventType}`);
  }

  /**
   * Emit event to local handlers
   */
  private async emitToLocalHandlers(event: DomainEvent): Promise<void> {
    // Emit via EventEmitter2 for NestJS integration
    this.eventEmitter.emit(event.eventType, event);

    // Also emit to directly registered handlers
    const handlers = this.handlers.get(event.eventType);
    if (handlers && handlers.size > 0) {
      const handlerPromises = Array.from(handlers)
        .filter(handler => handler.canHandle(event))
        .map(async handler => {
          try {
            await handler.handle(event);
          } catch (error) {
            this.logger.error(`Handler failed for event: ${event.eventType}`, error);
            // Don't throw - we want other handlers to continue
          }
        });

      await Promise.allSettled(handlerPromises);
    }
  }

  /**
   * Get event statistics
   */
  getEventStats(): {
    totalHandlers: number;
    handlersByEventType: Record<string, number>;
  } {
    const handlersByEventType: Record<string, number> = {};
    let totalHandlers = 0;

    for (const [eventType, handlers] of this.handlers.entries()) {
      handlersByEventType[eventType] = handlers.size;
      totalHandlers += handlers.size;
    }

    return {
      totalHandlers,
      handlersByEventType,
    };
  }

  /**
   * Clear all handlers (useful for testing)
   */
  clearAllHandlers(): void {
    this.logger.warn('Clearing all event handlers');
    this.handlers.clear();
    this.eventEmitter.removeAllListeners();
  }

  /**
   * Get registered event types
   */
  getRegisteredEventTypes(): string[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * Check if event type has handlers
   */
  hasHandlers(eventType: string): boolean {
    const handlers = this.handlers.get(eventType);
    return handlers ? handlers.size > 0 : false;
  }

  /**
   * Publish event with retry logic
   */
  async publishWithRetry(
    event: DomainEvent,
    maxRetries: number = 3,
    retryDelay: number = 1000,
  ): Promise<void> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.publish(event);
        return; // Success
      } catch (error) {
        lastError = error as Error;
        this.logger.warn(
          `Failed to publish event (attempt ${attempt}/${maxRetries}): ${event.eventType}`,
          error,
        );

        if (attempt < maxRetries) {
          await this.delay(retryDelay * attempt); // Exponential backoff
        }
      }
    }

    // All retries failed
    this.logger.error(
      `Failed to publish event after ${maxRetries} attempts: ${event.eventType}`,
      lastError,
    );
    throw lastError;
  }

  /**
   * Publish events with ordering guarantee
   */
  async publishOrdered(events: DomainEvent[]): Promise<void> {
    this.logger.debug(`Publishing ${events.length} events in order`);

    for (const event of events) {
      await this.publish(event);
    }

    this.logger.debug(`Successfully published ${events.length} events in order`);
  }

  /**
   * Utility method for delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
