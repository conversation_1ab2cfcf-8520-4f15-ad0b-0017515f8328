import { Injectable, Logger, Inject } from '@nestjs/common';
import { EventStore, DomainEvent } from '../../../domain/events/base/domain-event.base';

/**
 * Event Migration Service
 * Handles event schema migrations and transformations
 */
@Injectable()
export class EventMigrationService {
  private readonly logger = new Logger(EventMigrationService.name);
  private readonly migrations: Map<string, EventMigration> = new Map();

  constructor(
    @Inject('EVENT_STORE') private readonly eventStore: EventStore,
  ) {
    this.registerMigrations();
  }

  /**
   * Register event migrations
   */
  private registerMigrations(): void {
    // Example migration: LeadCreated v1 to v2
    this.registerMigration({
      eventType: 'LeadCreated',
      fromVersion: 1,
      toVersion: 2,
      description: 'Add contactInfo structure to LeadCreated event',
      migrate: (event: any) => {
        // Transform old event structure to new structure
        if (event.payload.email || event.payload.phone) {
          event.payload.contactInfo = {
            email: event.payload.email,
            phone: event.payload.phone,
            company: event.payload.company,
            website: event.payload.website,
          };
          
          // Remove old fields
          delete event.payload.email;
          delete event.payload.phone;
          delete event.payload.company;
          delete event.payload.website;
        }
        
        event.eventVersion = 2;
        return event;
      },
    });

    // Example migration: LeadStatusChanged v1 to v2
    this.registerMigration({
      eventType: 'LeadStatusChanged',
      fromVersion: 1,
      toVersion: 2,
      description: 'Add reason field to LeadStatusChanged event',
      migrate: (event: any) => {
        if (!event.payload.reason) {
          event.payload.reason = 'System update';
        }
        
        event.eventVersion = 2;
        return event;
      },
    });

    this.logger.log(`Registered ${this.migrations.size} event migrations`);
  }

  /**
   * Register a migration
   */
  registerMigration(migration: EventMigration): void {
    const key = `${migration.eventType}_${migration.fromVersion}_${migration.toVersion}`;
    this.migrations.set(key, migration);
    
    this.logger.debug(`Registered migration: ${migration.eventType} v${migration.fromVersion} -> v${migration.toVersion}`);
  }

  /**
   * Migrate events for a specific aggregate
   */
  async migrateAggregateEvents(
    aggregateId: string,
    aggregateType: string,
    targetVersion?: number
  ): Promise<MigrationResult> {
    this.logger.log(`Migrating events for aggregate: ${aggregateType}-${aggregateId}`);

    const result: MigrationResult = {
      aggregateId,
      aggregateType,
      startTime: new Date(),
      endTime: new Date(),
      eventsMigrated: 0,
      migrationsApplied: 0,
      success: false,
      errors: [],
    };

    try {
      const streamName = `${aggregateType}-${aggregateId}`;
      const events = await this.eventStore.getEvents(streamName);

      if (events.length === 0) {
        this.logger.warn(`No events found for aggregate: ${aggregateType}-${aggregateId}`);
        result.success = true;
        result.endTime = new Date();
        return result;
      }

      const migratedEvents: DomainEvent[] = [];
      let migrationsApplied = 0;

      for (const event of events) {
        const migratedEvent = await this.migrateEvent(event, targetVersion);
        migratedEvents.push(migratedEvent);
        
        if (migratedEvent !== event) {
          migrationsApplied++;
        }
      }

      // If any events were migrated, update the event store
      if (migrationsApplied > 0) {
        await this.updateEventStream(streamName, migratedEvents);
      }

      result.eventsMigrated = events.length;
      result.migrationsApplied = migrationsApplied;
      result.success = true;
      result.endTime = new Date();

      this.logger.log(`Successfully migrated ${migrationsApplied} events for aggregate: ${aggregateType}-${aggregateId}`);
      return result;

    } catch (error) {
      result.errors.push(error.message);
      result.endTime = new Date();
      this.logger.error(`Failed to migrate events for aggregate: ${aggregateType}-${aggregateId}`, error);
      throw error;
    }
  }

  /**
   * Migrate a single event
   */
  async migrateEvent(event: DomainEvent, targetVersion?: number): Promise<DomainEvent> {
    let currentEvent: any = { ...event };
    const currentVersion = (currentEvent as any).eventVersion || 1;
    const target = targetVersion || this.getLatestVersion(event.eventType);

    if (currentVersion >= target) {
      return event; // No migration needed
    }

    // Apply migrations sequentially
    for (let version = currentVersion; version < target; version++) {
      const migrationKey = `${event.eventType}_${version}_${version + 1}`;
      const migration = this.migrations.get(migrationKey);

      if (migration) {
        this.logger.debug(`Applying migration: ${migration.description}`);
        currentEvent = migration.migrate(currentEvent);
      }
    }

    // Return the original event if no migrations were applied, otherwise return the migrated event
    return currentEvent.eventVersion ? currentEvent : event;
  }

  /**
   * Migrate all events in the system
   */
  async migrateAllEvents(
    targetVersion?: number,
    batchSize: number = 100
  ): Promise<MigrationAllResult> {
    this.logger.log('Starting migration of all events in the system');

    const result: MigrationAllResult = {
      startTime: new Date(),
      endTime: new Date(),
      totalEventsMigrated: 0,
      totalMigrationsApplied: 0,
      batchesProcessed: 0,
      success: false,
      errors: [],
    };

    try {
      let currentPosition = 0;
      let hasMoreEvents = true;

      while (hasMoreEvents) {
        // Get batch of events
        const allEvents = await this.eventStore.getAllEvents(currentPosition);
        const events = allEvents.slice(0, batchSize);
        
        if (events.length === 0) {
          hasMoreEvents = false;
          break;
        }

        // Migrate batch
        const batchResult = await this.migrateBatch(events, targetVersion);
        
        result.totalEventsMigrated += batchResult.eventsMigrated;
        result.totalMigrationsApplied += batchResult.migrationsApplied;
        result.batchesProcessed++;
        currentPosition += events.length;

        this.logger.debug(`Processed batch ${result.batchesProcessed}: ${batchResult.migrationsApplied} migrations applied`);
      }

      result.success = true;
      result.endTime = new Date();

      this.logger.log(`Successfully migrated ${result.totalMigrationsApplied} events in ${result.batchesProcessed} batches`);
      return result;

    } catch (error) {
      result.errors.push(error.message);
      result.endTime = new Date();
      this.logger.error('Failed to migrate all events', error);
      throw error;
    }
  }

  /**
   * Migrate a batch of events
   */
  private async migrateBatch(events: DomainEvent[], targetVersion?: number): Promise<BatchMigrationResult> {
    const result: BatchMigrationResult = {
      eventsMigrated: 0,
      migrationsApplied: 0,
    };

    const eventsByStream = new Map<string, DomainEvent[]>();

    // Group events by stream
    for (const event of events) {
      const streamName = `${event.aggregateType}-${event.aggregateId}`;
      if (!eventsByStream.has(streamName)) {
        eventsByStream.set(streamName, []);
      }
      eventsByStream.get(streamName)!.push(event);
    }

    // Migrate each stream
    for (const [streamName, streamEvents] of eventsByStream) {
      const migratedEvents: DomainEvent[] = [];
      let streamMigrationsApplied = 0;

      for (const event of streamEvents) {
        const migratedEvent = await this.migrateEvent(event, targetVersion);
        migratedEvents.push(migratedEvent);
        
        if (migratedEvent !== event) {
          streamMigrationsApplied++;
        }
      }

      // Update stream if any events were migrated
      if (streamMigrationsApplied > 0) {
        await this.updateEventStream(streamName, migratedEvents);
      }

      result.eventsMigrated += streamEvents.length;
      result.migrationsApplied += streamMigrationsApplied;
    }

    return result;
  }

  /**
   * Update event stream with migrated events
   */
  private async updateEventStream(streamName: string, events: DomainEvent[]): Promise<void> {
    // This would update the event store with migrated events
    // For now, just log the operation
    this.logger.debug(`Would update stream ${streamName} with ${events.length} migrated events`);
  }

  /**
   * Get latest version for event type
   */
  private getLatestVersion(eventType: string): number {
    let latestVersion = 1;
    
    for (const [key, migration] of this.migrations) {
      if (key.startsWith(eventType)) {
        latestVersion = Math.max(latestVersion, migration.toVersion);
      }
    }
    
    return latestVersion;
  }

  /**
   * Get migration plan for event type
   */
  getMigrationPlan(eventType: string, fromVersion: number, toVersion?: number): MigrationPlan {
    const target = toVersion || this.getLatestVersion(eventType);
    const steps: MigrationStep[] = [];

    for (let version = fromVersion; version < target; version++) {
      const migrationKey = `${eventType}_${version}_${version + 1}`;
      const migration = this.migrations.get(migrationKey);

      if (migration) {
        steps.push({
          fromVersion: version,
          toVersion: version + 1,
          description: migration.description,
          required: true,
        });
      } else {
        steps.push({
          fromVersion: version,
          toVersion: version + 1,
          description: `No migration available for ${eventType} v${version} -> v${version + 1}`,
          required: false,
        });
      }
    }

    return {
      eventType,
      fromVersion,
      toVersion: target,
      steps,
      totalSteps: steps.length,
      requiredSteps: steps.filter(s => s.required).length,
    };
  }

  /**
   * Validate migration compatibility
   */
  validateMigration(eventType: string, fromVersion: number, toVersion: number): ValidationResult {
    const plan = this.getMigrationPlan(eventType, fromVersion, toVersion);
    const missingMigrations = plan.steps.filter(s => !s.required);

    return {
      isValid: missingMigrations.length === 0,
      issues: missingMigrations.map(s => s.description),
      plan,
    };
  }
}

/**
 * Event Migration Interface
 */
export interface EventMigration {
  eventType: string;
  fromVersion: number;
  toVersion: number;
  description: string;
  migrate: (event: any) => any;
}

/**
 * Migration Result Interface
 */
export interface MigrationResult {
  aggregateId: string;
  aggregateType: string;
  startTime: Date;
  endTime: Date;
  eventsMigrated: number;
  migrationsApplied: number;
  success: boolean;
  errors: string[];
}

/**
 * Migration All Result Interface
 */
export interface MigrationAllResult {
  startTime: Date;
  endTime: Date;
  totalEventsMigrated: number;
  totalMigrationsApplied: number;
  batchesProcessed: number;
  success: boolean;
  errors: string[];
}

/**
 * Batch Migration Result Interface
 */
interface BatchMigrationResult {
  eventsMigrated: number;
  migrationsApplied: number;
}

/**
 * Migration Plan Interface
 */
export interface MigrationPlan {
  eventType: string;
  fromVersion: number;
  toVersion: number;
  steps: MigrationStep[];
  totalSteps: number;
  requiredSteps: number;
}

/**
 * Migration Step Interface
 */
export interface MigrationStep {
  fromVersion: number;
  toVersion: number;
  description: string;
  required: boolean;
}

/**
 * Migration Validation Result Interface
 */
interface ValidationResult {
  isValid: boolean;
  issues: string[];
  plan: MigrationPlan;
}
