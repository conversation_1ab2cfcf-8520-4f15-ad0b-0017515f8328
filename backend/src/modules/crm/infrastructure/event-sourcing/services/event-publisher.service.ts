import { Injectable, Logger } from '@nestjs/common';
import { DomainEvent } from '../../../domain/events/base/domain-event.base';

@Injectable()
export class EventPublisherService {
  private readonly logger = new Logger(EventPublisherService.name);

  async publishExternal(event: DomainEvent): Promise<void> {
    this.logger.log(`EventPublisherService - publishExternal - Placeholder for event: ${event.eventType}`);
  }
}
