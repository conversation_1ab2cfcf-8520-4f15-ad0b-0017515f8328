import { Injectable, Logger, Inject } from '@nestjs/common';
import { DomainEvent, EventStore } from '@/modules/crm/domain/events/base/domain-event.base';

/**
 * Event Publisher Service
 * Handles external event publishing and event store persistence
 */
@Injectable()
export class EventPublisherService {
  private readonly logger = new Logger(EventPublisherService.name);

  constructor(
    @Inject('EVENT_STORE') private readonly eventStore: EventStore,
  ) {}

  /**
   * Publish event externally (to message brokers, webhooks, etc.)
   */
  async publishExternal(event: DomainEvent): Promise<void> {
    this.logger.debug(`Publishing external event: ${event.eventType} (${event.eventId})`);

    try {
      // For now, we'll just log the event
      // In a real implementation, this would publish to:
      // - Message brokers (RabbitMQ, Apache Kafka, etc.)
      // - Webhooks
      // - External APIs
      // - Push notifications

      this.logger.log(`External event published: ${event.eventType}`, {
        eventId: event.eventId,
        aggregateId: event.aggregateId,
        aggregateType: event.aggregateType,
        eventType: event.eventType,
        correlationId: event.correlationId,
        metadata: event.metadata,
      });

      // Example: Publish to message broker
      // await this.messageBroker.publish(event.getRoutingKey(), event.toJSON());

      // Example: Send webhook
      // if (event.metadata.webhookUrl) {
      //   await this.webhookService.send(event.metadata.webhookUrl, event.toJSON());
      // }

    } catch (error) {
      this.logger.error(`Failed to publish external event: ${event.eventType} (${event.eventId})`, error);
      throw error;
    }
  }

  /**
   * Persist event to event store
   */
  async persistEvent(event: DomainEvent): Promise<void> {
    this.logger.debug(`Persisting event to store: ${event.eventType} (${event.eventId})`);

    try {
      const streamName = event.getStreamName();
      await this.eventStore.append(streamName, [event]);

      this.logger.debug(`Event persisted to store: ${event.eventType} (${event.eventId})`);
    } catch (error) {
      this.logger.error(`Failed to persist event: ${event.eventType} (${event.eventId})`, error);
      throw error;
    }
  }

  /**
   * Persist multiple events to event store
   */
  async persistEvents(events: DomainEvent[]): Promise<void> {
    if (events.length === 0) return;

    this.logger.debug(`Persisting ${events.length} events to store`);

    try {
      // Group events by stream name
      const eventsByStream = new Map<string, DomainEvent[]>();

      for (const event of events) {
        const streamName = event.getStreamName();
        if (!eventsByStream.has(streamName)) {
          eventsByStream.set(streamName, []);
        }
        eventsByStream.get(streamName)!.push(event);
      }

      // Persist events for each stream
      const persistPromises = Array.from(eventsByStream.entries()).map(
        ([streamName, streamEvents]) => this.eventStore.append(streamName, streamEvents)
      );

      await Promise.all(persistPromises);

      this.logger.debug(`Successfully persisted ${events.length} events to store`);
    } catch (error) {
      this.logger.error(`Failed to persist events to store`, error);
      throw error;
    }
  }
}
