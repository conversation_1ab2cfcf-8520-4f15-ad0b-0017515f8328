import { Injectable, Logger, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DomainEvent, EventStore } from '@/modules/crm/domain/events/base/domain-event.base';

/**
 * Projection Service
 * Manages read model projections built from domain events
 */
@Injectable()
export class ProjectionService {
  private readonly logger = new Logger(ProjectionService.name);
  private readonly projectionHandlers = new Map<string, ProjectionHandler>();

  constructor(
    @Inject('EVENT_STORE') private readonly eventStore: EventStore,
    @InjectModel('EventProjection') private readonly projectionModel: Model<any>,
  ) {}

  /**
   * Register a projection handler for specific event types
   */
  registerProjectionHandler(eventTypes: string[], handler: ProjectionHandler): void {
    for (const eventType of eventTypes) {
      this.projectionHandlers.set(eventType, handler);
      this.logger.debug(`Registered projection handler for event type: ${eventType}`);
    }
  }

  /**
   * Update projection based on an event
   */
  async updateProjection(projectionName: string, event: DomainEvent): Promise<void> {
    this.logger.debug(`Updating projection: ${projectionName} for event: ${event.eventType}`);

    try {
      const handler = this.projectionHandlers.get(event.eventType);
      if (!handler) {
        this.logger.debug(`No projection handler found for event type: ${event.eventType}`);
        return;
      }

      // Apply the event to the projection
      await handler.handle(event, projectionName);

      // Update projection metadata
      await this.updateProjectionMetadata(projectionName, event);

      this.logger.debug(`Successfully updated projection: ${projectionName}`);
    } catch (error) {
      this.logger.error(`Failed to update projection: ${projectionName}`, error);
      throw error;
    }
  }

  /**
   * Rebuild projection from all events
   */
  async rebuildProjection(projectionName: string, fromPosition?: number): Promise<void> {
    this.logger.log(`Rebuilding projection: ${projectionName} from position: ${fromPosition || 0}`);

    try {
      // Clear existing projection data
      await this.clearProjection(projectionName);

      // Get all events from the event store
      const events = await this.eventStore.getAllEvents(fromPosition);

      // Process events in order
      for (const event of events) {
        await this.updateProjection(projectionName, event);
      }

      this.logger.log(`Successfully rebuilt projection: ${projectionName} with ${events.length} events`);
    } catch (error) {
      this.logger.error(`Failed to rebuild projection: ${projectionName}`, error);
      throw error;
    }
  }

  /**
   * Get projection status
   */
  async getProjectionStatus(projectionName: string): Promise<ProjectionStatus> {
    try {
      const projection = await this.projectionModel.findOne({ name: projectionName }).exec();

      if (!projection) {
        return {
          name: projectionName,
          lastProcessedPosition: 0,
          lastProcessedAt: null,
          status: 'not_started',
          eventCount: 0,
        };
      }

      return {
        name: projection.name,
        lastProcessedPosition: projection.lastProcessedPosition,
        lastProcessedAt: projection.lastProcessedAt,
        status: projection.status,
        eventCount: projection.eventCount,
      };
    } catch (error) {
      this.logger.error(`Failed to get projection status: ${projectionName}`, error);
      throw error;
    }
  }

  /**
   * Clear projection data
   */
  private async clearProjection(projectionName: string): Promise<void> {
    try {
      // This would clear the actual projection data
      // Implementation depends on where projections are stored
      // For now, just reset the metadata
      await this.projectionModel.deleteOne({ name: projectionName }).exec();

      this.logger.debug(`Cleared projection: ${projectionName}`);
    } catch (error) {
      this.logger.error(`Failed to clear projection: ${projectionName}`, error);
      throw error;
    }
  }

  /**
   * Update projection metadata
   */
  private async updateProjectionMetadata(projectionName: string, event: DomainEvent): Promise<void> {
    try {
      await this.projectionModel.findOneAndUpdate(
        { name: projectionName },
        {
          $set: {
            lastProcessedPosition: event.metadata.position || 0,
            lastProcessedAt: new Date(),
            status: 'running',
          },
          $inc: {
            eventCount: 1,
          },
        },
        { upsert: true }
      ).exec();
    } catch (error) {
      this.logger.error(`Failed to update projection metadata: ${projectionName}`, error);
      throw error;
    }
  }
}

/**
 * Projection Handler Interface
 */
export interface ProjectionHandler {
  handle(event: DomainEvent, projectionName: string): Promise<void>;
}

/**
 * Projection Status Interface
 */
export interface ProjectionStatus {
  name: string;
  lastProcessedPosition: number;
  lastProcessedAt: Date | null;
  status: 'not_started' | 'running' | 'stopped' | 'error';
  eventCount: number;
}
