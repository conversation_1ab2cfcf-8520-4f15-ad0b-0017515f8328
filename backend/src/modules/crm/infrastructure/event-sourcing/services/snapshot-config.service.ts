import { Injectable, Logger } from '@nestjs/common';

/**
 * Snapshot Configuration Service
 * Manages snapshot policies and configurations for different aggregate types
 */
@Injectable()
export class SnapshotConfigService {
  private readonly logger = new Logger(SnapshotConfigService.name);

  // Default snapshot configurations
  private readonly defaultConfigs: Map<string, SnapshotConfig> = new Map([
    ['Lead', {
      aggregateType: 'Lead',
      frequency: 10, // Take snapshot every 10 events
      retentionCount: 5, // Keep 5 snapshots
      retentionDays: 30, // Keep snapshots for 30 days
      compressionEnabled: true,
      autoCleanup: true,
      cleanupInterval: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
    }],
    ['Opportunity', {
      aggregateType: 'Opportunity',
      frequency: 15, // Take snapshot every 15 events
      retentionCount: 3, // Keep 3 snapshots
      retentionDays: 60, // Keep snapshots for 60 days
      compressionEnabled: true,
      autoCleanup: true,
      cleanupInterval: 24 * 60 * 60 * 1000,
    }],
    ['Contact', {
      aggregateType: 'Contact',
      frequency: 20, // Take snapshot every 20 events
      retentionCount: 3, // Keep 3 snapshots
      retentionDays: 90, // Keep snapshots for 90 days
      compressionEnabled: false,
      autoCleanup: true,
      cleanupInterval: 7 * 24 * 60 * 60 * 1000, // 7 days
    }],
  ]);

  /**
   * Get snapshot configuration for aggregate type
   */
  getConfig(aggregateType: string): SnapshotConfig {
    const config = this.defaultConfigs.get(aggregateType);
    
    if (!config) {
      this.logger.warn(`No snapshot config found for aggregate type: ${aggregateType}, using default`);
      return this.getDefaultConfig(aggregateType);
    }

    return config;
  }

  /**
   * Set snapshot configuration for aggregate type
   */
  setConfig(aggregateType: string, config: Partial<SnapshotConfig>): void {
    const existingConfig = this.getConfig(aggregateType);
    const newConfig = { ...existingConfig, ...config };
    
    this.defaultConfigs.set(aggregateType, newConfig);
    this.logger.log(`Updated snapshot config for aggregate type: ${aggregateType}`);
  }

  /**
   * Check if snapshot should be created
   */
  shouldCreateSnapshot(
    aggregateType: string,
    currentVersion: number,
    lastSnapshotVersion: number = 0
  ): boolean {
    const config = this.getConfig(aggregateType);
    const eventsSinceLastSnapshot = currentVersion - lastSnapshotVersion;
    
    return eventsSinceLastSnapshot >= config.frequency;
  }

  /**
   * Check if snapshot should be cleaned up
   */
  shouldCleanupSnapshot(
    aggregateType: string,
    snapshotCreatedAt: Date,
    snapshotCount: number
  ): boolean {
    const config = this.getConfig(aggregateType);
    const now = new Date();
    const daysSinceCreation = (now.getTime() - snapshotCreatedAt.getTime()) / (1000 * 60 * 60 * 24);
    
    // Cleanup if exceeded retention days or retention count
    return daysSinceCreation > config.retentionDays || snapshotCount > config.retentionCount;
  }

  /**
   * Get cleanup interval for aggregate type
   */
  getCleanupInterval(aggregateType: string): number {
    const config = this.getConfig(aggregateType);
    return config.cleanupInterval;
  }

  /**
   * Check if auto cleanup is enabled
   */
  isAutoCleanupEnabled(aggregateType: string): boolean {
    const config = this.getConfig(aggregateType);
    return config.autoCleanup;
  }

  /**
   * Check if compression is enabled
   */
  isCompressionEnabled(aggregateType: string): boolean {
    const config = this.getConfig(aggregateType);
    return config.compressionEnabled;
  }

  /**
   * Get all configured aggregate types
   */
  getConfiguredAggregateTypes(): string[] {
    return Array.from(this.defaultConfigs.keys());
  }

  /**
   * Get snapshot statistics configuration
   */
  getStatsConfig(): SnapshotStatsConfig {
    return {
      enableMetrics: true,
      metricsRetentionDays: 30,
      enablePerformanceTracking: true,
      enableSizeTracking: true,
      enableFrequencyTracking: true,
    };
  }

  /**
   * Validate snapshot configuration
   */
  validateConfig(config: Partial<SnapshotConfig>): boolean {
    if (config.frequency !== undefined && config.frequency <= 0) {
      throw new Error('Snapshot frequency must be greater than 0');
    }

    if (config.retentionCount !== undefined && config.retentionCount <= 0) {
      throw new Error('Snapshot retention count must be greater than 0');
    }

    if (config.retentionDays !== undefined && config.retentionDays <= 0) {
      throw new Error('Snapshot retention days must be greater than 0');
    }

    if (config.cleanupInterval !== undefined && config.cleanupInterval <= 0) {
      throw new Error('Snapshot cleanup interval must be greater than 0');
    }

    return true;
  }

  /**
   * Get default configuration for unknown aggregate types
   */
  private getDefaultConfig(aggregateType: string): SnapshotConfig {
    return {
      aggregateType,
      frequency: 10,
      retentionCount: 3,
      retentionDays: 30,
      compressionEnabled: true,
      autoCleanup: true,
      cleanupInterval: 24 * 60 * 60 * 1000,
    };
  }

  /**
   * Get performance recommendations
   */
  getPerformanceRecommendations(aggregateType: string, metrics: SnapshotMetrics): SnapshotRecommendations {
    const config = this.getConfig(aggregateType);
    const recommendations: string[] = [];

    // Check frequency recommendations
    if (metrics.averageEventsPerSnapshot > config.frequency * 2) {
      recommendations.push(`Consider reducing snapshot frequency from ${config.frequency} to ${Math.floor(config.frequency * 0.7)} events`);
    } else if (metrics.averageEventsPerSnapshot < config.frequency * 0.5) {
      recommendations.push(`Consider increasing snapshot frequency from ${config.frequency} to ${Math.floor(config.frequency * 1.3)} events`);
    }

    // Check size recommendations
    if (metrics.averageSnapshotSize > 1024 * 1024 && !config.compressionEnabled) { // 1MB
      recommendations.push('Consider enabling compression for large snapshots');
    }

    // Check retention recommendations
    if (metrics.oldestSnapshotAge > config.retentionDays * 1.5) {
      recommendations.push('Consider reducing retention days or enabling auto cleanup');
    }

    return {
      aggregateType,
      recommendations,
      currentConfig: config,
      metrics,
      priority: this.calculateRecommendationPriority(recommendations),
    };
  }

  /**
   * Calculate recommendation priority
   */
  private calculateRecommendationPriority(recommendations: string[]): 'low' | 'medium' | 'high' {
    if (recommendations.length === 0) return 'low';
    if (recommendations.length <= 2) return 'medium';
    return 'high';
  }
}

/**
 * Snapshot Configuration Interface
 */
export interface SnapshotConfig {
  aggregateType: string;
  frequency: number; // Events between snapshots
  retentionCount: number; // Number of snapshots to keep
  retentionDays: number; // Days to keep snapshots
  compressionEnabled: boolean;
  autoCleanup: boolean;
  cleanupInterval: number; // Milliseconds between cleanup runs
}

/**
 * Snapshot Statistics Configuration
 */
export interface SnapshotStatsConfig {
  enableMetrics: boolean;
  metricsRetentionDays: number;
  enablePerformanceTracking: boolean;
  enableSizeTracking: boolean;
  enableFrequencyTracking: boolean;
}

/**
 * Snapshot Metrics Interface
 */
export interface SnapshotMetrics {
  totalSnapshots: number;
  averageSnapshotSize: number;
  averageEventsPerSnapshot: number;
  oldestSnapshotAge: number; // Days
  newestSnapshotAge: number; // Days
  compressionRatio?: number;
}

/**
 * Snapshot Recommendations Interface
 */
export interface SnapshotRecommendations {
  aggregateType: string;
  recommendations: string[];
  currentConfig: SnapshotConfig;
  metrics: SnapshotMetrics;
  priority: 'low' | 'medium' | 'high';
}
