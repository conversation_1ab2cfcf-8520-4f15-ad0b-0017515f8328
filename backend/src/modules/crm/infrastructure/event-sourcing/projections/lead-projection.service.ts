import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { LeadProjection, LeadProjectionDocument } from '../../event-store/schemas/lead-projection.schema';
import { LeadProjectionHandler } from './lead-projection.handler';
import { ProjectionService } from '../services/projection.service';

/**
 * Lead Projection Service
 * Provides high-level operations for Lead projections
 */
@Injectable()
export class LeadProjectionService {
  private readonly logger = new Logger(LeadProjectionService.name);
  private readonly PROJECTION_NAME = 'lead-projection';

  constructor(
    @InjectModel(LeadProjection.name) private readonly leadProjectionModel: Model<LeadProjectionDocument>,
    private readonly projectionService: ProjectionService,
    private readonly leadProjectionHandler: LeadProjectionHandler,
  ) {
    this.registerProjectionHandler();
  }

  /**
   * Register the lead projection handler
   */
  private registerProjectionHandler(): void {
    const leadEventTypes = [
      'LeadCreated',
      'LeadStatusChanged',
      'LeadAssigned',
      'LeadUpdated',
      'LeadDeleted',
      'LeadScoreChanged',
      'LeadActivityCreated',
    ];

    this.projectionService.registerProjectionHandler(leadEventTypes, this.leadProjectionHandler);
    this.logger.log(`Registered Lead projection handler for ${leadEventTypes.length} event types`);
  }

  /**
   * Find lead projection by ID
   */
  async findById(leadId: string): Promise<LeadProjectionDocument | null> {
    try {
      return await this.leadProjectionModel.findOne({ leadId, isDeleted: false }).exec();
    } catch (error) {
      this.logger.error(`Failed to find lead projection: ${leadId}`, error);
      throw error;
    }
  }

  /**
   * Find lead projections by criteria
   */
  async findByCriteria(criteria: LeadProjectionCriteria): Promise<LeadProjectionResult> {
    try {
      const filter: any = { isDeleted: false };

      // Apply filters
      if (criteria.status) filter.status = criteria.status;
      if (criteria.assignedUserId) filter.assignedUserId = criteria.assignedUserId;
      if (criteria.teamId) filter.teamId = criteria.teamId;
      if (criteria.source) filter.source = criteria.source;
      if (criteria.type) filter.type = criteria.type;
      if (criteria.priority) filter.priority = criteria.priority;
      if (criteria.tags && criteria.tags.length > 0) {
        filter.tags = { $in: criteria.tags };
      }

      // Date filters
      if (criteria.createdAfter || criteria.createdBefore) {
        filter.createdAt = {};
        if (criteria.createdAfter) filter.createdAt.$gte = criteria.createdAfter;
        if (criteria.createdBefore) filter.createdAt.$lte = criteria.createdBefore;
      }

      // Build query
      const query = this.leadProjectionModel.find(filter);

      // Apply sorting
      if (criteria.sortBy) {
        const sortDirection = criteria.sortDirection === 'desc' ? -1 : 1;
        query.sort({ [criteria.sortBy]: sortDirection });
      } else {
        query.sort({ updatedAt: -1 }); // Default sort by updated date
      }

      // Apply pagination
      const limit = criteria.limit || 50;
      const offset = criteria.offset || 0;
      
      query.skip(offset).limit(limit);

      // Execute query
      const [leads, total] = await Promise.all([
        query.exec(),
        this.leadProjectionModel.countDocuments(filter).exec(),
      ]);

      return {
        leads,
        total,
        hasMore: offset + leads.length < total,
        offset,
        limit,
      };
    } catch (error) {
      this.logger.error(`Failed to find lead projections by criteria`, error);
      throw error;
    }
  }

  /**
   * Search lead projections
   */
  async search(searchTerm: string, criteria?: LeadProjectionCriteria): Promise<LeadProjectionResult> {
    try {
      const filter: any = { 
        isDeleted: false,
        $or: [
          { name: { $regex: searchTerm, $options: 'i' } },
          { 'contactInfo.email': { $regex: searchTerm, $options: 'i' } },
          { 'contactInfo.company': { $regex: searchTerm, $options: 'i' } },
          { 'contactInfo.phone': { $regex: searchTerm, $options: 'i' } },
          { description: { $regex: searchTerm, $options: 'i' } },
        ],
      };

      // Apply additional criteria if provided
      if (criteria) {
        if (criteria.status) filter.status = criteria.status;
        if (criteria.assignedUserId) filter.assignedUserId = criteria.assignedUserId;
        if (criteria.teamId) filter.teamId = criteria.teamId;
        if (criteria.source) filter.source = criteria.source;
        if (criteria.type) filter.type = criteria.type;
        if (criteria.priority) filter.priority = criteria.priority;
      }

      // Build query
      const query = this.leadProjectionModel.find(filter);

      // Apply sorting (relevance-based for search)
      query.sort({ updatedAt: -1 });

      // Apply pagination
      const limit = criteria?.limit || 50;
      const offset = criteria?.offset || 0;
      
      query.skip(offset).limit(limit);

      // Execute query
      const [leads, total] = await Promise.all([
        query.exec(),
        this.leadProjectionModel.countDocuments(filter).exec(),
      ]);

      return {
        leads,
        total,
        hasMore: offset + leads.length < total,
        offset,
        limit,
      };
    } catch (error) {
      this.logger.error(`Failed to search lead projections`, error);
      throw error;
    }
  }

  /**
   * Get lead projection statistics
   */
  async getStatistics(criteria?: Partial<LeadProjectionCriteria>): Promise<LeadProjectionStats> {
    try {
      const filter: any = { isDeleted: false };

      // Apply filters
      if (criteria?.assignedUserId) filter.assignedUserId = criteria.assignedUserId;
      if (criteria?.teamId) filter.teamId = criteria.teamId;
      if (criteria?.createdAfter || criteria?.createdBefore) {
        filter.createdAt = {};
        if (criteria.createdAfter) filter.createdAt.$gte = criteria.createdAfter;
        if (criteria.createdBefore) filter.createdAt.$lte = criteria.createdBefore;
      }

      const [
        totalLeads,
        statusStats,
        typeStats,
        priorityStats,
        sourceStats,
      ] = await Promise.all([
        this.leadProjectionModel.countDocuments(filter).exec(),
        this.leadProjectionModel.aggregate([
          { $match: filter },
          { $group: { _id: '$status', count: { $sum: 1 } } },
        ]).exec(),
        this.leadProjectionModel.aggregate([
          { $match: filter },
          { $group: { _id: '$type', count: { $sum: 1 } } },
        ]).exec(),
        this.leadProjectionModel.aggregate([
          { $match: filter },
          { $group: { _id: '$priority', count: { $sum: 1 } } },
        ]).exec(),
        this.leadProjectionModel.aggregate([
          { $match: filter },
          { $group: { _id: '$source', count: { $sum: 1 } } },
        ]).exec(),
      ]);

      return {
        totalLeads,
        byStatus: this.formatAggregationResult(statusStats),
        byType: this.formatAggregationResult(typeStats),
        byPriority: this.formatAggregationResult(priorityStats),
        bySource: this.formatAggregationResult(sourceStats),
      };
    } catch (error) {
      this.logger.error(`Failed to get lead projection statistics`, error);
      throw error;
    }
  }

  /**
   * Rebuild projection from events
   */
  async rebuildProjection(): Promise<void> {
    this.logger.log(`Starting rebuild of ${this.PROJECTION_NAME}`);
    
    try {
      await this.projectionService.rebuildProjection(this.PROJECTION_NAME);
      this.logger.log(`Successfully rebuilt ${this.PROJECTION_NAME}`);
    } catch (error) {
      this.logger.error(`Failed to rebuild ${this.PROJECTION_NAME}`, error);
      throw error;
    }
  }

  /**
   * Format aggregation result
   */
  private formatAggregationResult(result: any[]): Record<string, number> {
    return result.reduce((acc, item) => {
      acc[item._id] = item.count;
      return acc;
    }, {});
  }
}

/**
 * Lead Projection Search Criteria
 */
export interface LeadProjectionCriteria {
  status?: string;
  assignedUserId?: number;
  teamId?: number;
  source?: string;
  type?: string;
  priority?: string;
  tags?: string[];
  createdAfter?: Date;
  createdBefore?: Date;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

/**
 * Lead Projection Result
 */
export interface LeadProjectionResult {
  leads: LeadProjectionDocument[];
  total: number;
  hasMore: boolean;
  offset: number;
  limit: number;
}

/**
 * Lead Projection Statistics
 */
export interface LeadProjectionStats {
  totalLeads: number;
  byStatus: Record<string, number>;
  byType: Record<string, number>;
  byPriority: Record<string, number>;
  bySource: Record<string, number>;
}
