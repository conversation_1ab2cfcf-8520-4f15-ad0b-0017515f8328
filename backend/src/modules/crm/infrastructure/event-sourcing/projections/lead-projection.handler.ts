import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { EventsHandler, IEventHandler } from '@nestjs/cqrs';
import { DomainEvent } from '@/modules/crm/domain/events/base/domain-event.base';
import { LeadProjection, LeadProjectionDocument } from '@/modules/crm/infrastructure/event-store/schemas/lead-projection.schema';
import { ProjectionHandler } from '../services/projection.service';

// Import Lead Events
import {
  LeadCreatedEvent,
  LeadStatusChangedEvent,
  LeadAssignedEvent,
  LeadUpdatedEvent,
  LeadDeletedEvent,
  LeadScoreChangedEvent,
  LeadActivityCreatedEvent
} from '@/modules/crm/domain/events/lead-events';

/**
 * Lead Projection Handler
 * Handles domain events to build and maintain Lead read models
 */
@Injectable()
export class LeadProjectionHandler implements ProjectionHandler {
  private readonly logger = new Logger(LeadProjectionHandler.name);

  constructor(
    @InjectModel(LeadProjection.name, 'zenoo-main') private readonly leadProjectionModel: Model<LeadProjectionDocument>,
  ) {}

  /**
   * Handle domain events to update projections
   */
  async handle(event: DomainEvent, projectionName: string): Promise<void> {
    this.logger.debug(`Handling event: ${event.eventType} for projection: ${projectionName}`);

    try {
      switch (event.eventType) {
        case 'LeadCreated':
          await this.handleLeadCreated(event as LeadCreatedEvent);
          break;
        case 'LeadStatusChanged':
          await this.handleLeadStatusChanged(event as LeadStatusChangedEvent);
          break;
        case 'LeadAssigned':
          await this.handleLeadAssigned(event as LeadAssignedEvent);
          break;
        case 'LeadUpdated':
          await this.handleLeadUpdated(event as LeadUpdatedEvent);
          break;
        case 'LeadDeleted':
          await this.handleLeadDeleted(event as LeadDeletedEvent);
          break;
        case 'LeadScoreChanged':
          await this.handleLeadScoreChanged(event as LeadScoreChangedEvent);
          break;
        case 'LeadActivityCreated':
          await this.handleLeadActivityCreated(event as LeadActivityCreatedEvent);
          break;
        default:
          this.logger.debug(`No handler for event type: ${event.eventType}`);
      }

      this.logger.debug(`Successfully handled event: ${event.eventType} for projection: ${projectionName}`);
    } catch (error) {
      this.logger.error(`Failed to handle event: ${event.eventType} for projection: ${projectionName}`, error);
      throw error;
    }
  }

  /**
   * Handle LeadCreated event
   */
  private async handleLeadCreated(event: LeadCreatedEvent): Promise<void> {
    const leadProjection = new this.leadProjectionModel({
      leadId: event.aggregateId,
      name: event.lead.name,
      contactInfo: {
        email: event.lead.contactInfo?.email,
        phone: event.lead.contactInfo?.phone,
        company: event.lead.contactInfo?.company,
        website: event.lead.contactInfo?.website,
        address: event.lead.contactInfo?.address,
        city: event.lead.contactInfo?.city,
        country: event.lead.contactInfo?.country,
      },
      status: event.lead.status?.value || 'new',
      source: event.source,
      type: event.lead.type?.value || 'lead',
      priority: event.lead.priority?.value || 'medium',
      expectedRevenue: event.lead.expectedRevenue,
      probability: event.lead.probability,
      description: event.lead.description,
      assignedUserId: event.lead.assignedUserId,
      companyId: event.lead.companyId,
      tags: event.lead.tags || [],
      partnerId: event.lead.partnerId,
      stageId: event.lead.stageId,
      teamId: event.lead.teamId,
      dateDeadline: event.lead.dateDeadline,
      lostReasonId: event.lead.lostReasonId,
      campaignId: event.lead.campaignId,
      sourceId: event.lead.sourceId,
      mediumId: event.lead.mediumId,
      createdAt: event.occurredAt,
      updatedAt: event.occurredAt,
      version: event.aggregateVersion,
      isDeleted: false,
      lastEventId: event.eventId,
      lastEventType: event.eventType,
      lastEventTimestamp: event.occurredAt,
      eventCount: 1,
    });

    await leadProjection.save();
    this.logger.debug(`Created lead projection: ${event.aggregateId}`);
  }

  /**
   * Handle LeadStatusChanged event
   */
  private async handleLeadStatusChanged(event: LeadStatusChangedEvent): Promise<void> {
    await this.leadProjectionModel.findOneAndUpdate(
      { leadId: event.aggregateId },
      {
        $set: {
          status: event.newStatus.value,
          updatedAt: event.occurredAt,
          version: event.aggregateVersion,
          lastEventId: event.eventId,
          lastEventType: event.eventType,
          lastEventTimestamp: event.occurredAt,
        },
        $inc: {
          eventCount: 1,
        },
      },
      { new: true }
    );

    this.logger.debug(`Updated lead status projection: ${event.aggregateId} to ${event.newStatus.value}`);
  }

  /**
   * Handle LeadAssigned event
   */
  private async handleLeadAssigned(event: LeadAssignedEvent): Promise<void> {
    const updateData: any = {
      updatedAt: event.occurredAt,
      version: event.aggregateVersion,
      lastEventId: event.eventId,
      lastEventType: event.eventType,
      lastEventTimestamp: event.occurredAt,
    };

    if (event.assignedUserId !== undefined) {
      updateData.assignedUserId = event.assignedUserId;
    }

    if (event.assignedTeamId !== undefined) {
      updateData.teamId = event.assignedTeamId;
    }

    await this.leadProjectionModel.findOneAndUpdate(
      { leadId: event.aggregateId },
      {
        $set: updateData,
        $inc: {
          eventCount: 1,
        },
      },
      { new: true }
    );

    this.logger.debug(`Updated lead assignment projection: ${event.aggregateId}`);
  }

  /**
   * Handle LeadUpdated event
   */
  private async handleLeadUpdated(event: LeadUpdatedEvent): Promise<void> {
    const updateData: any = {
      updatedAt: event.occurredAt,
      version: event.aggregateVersion,
      lastEventId: event.eventId,
      lastEventType: event.eventType,
      lastEventTimestamp: event.occurredAt,
    };

    // Apply updates from event
    Object.keys(event.updates).forEach(key => {
      if (event.updates[key] !== undefined) {
        updateData[key] = event.updates[key];
      }
    });

    await this.leadProjectionModel.findOneAndUpdate(
      { leadId: event.aggregateId },
      {
        $set: updateData,
        $inc: {
          eventCount: 1,
        },
      },
      { new: true }
    );

    this.logger.debug(`Updated lead projection: ${event.aggregateId}`);
  }

  /**
   * Handle LeadDeleted event
   */
  private async handleLeadDeleted(event: LeadDeletedEvent): Promise<void> {
    await this.leadProjectionModel.findOneAndUpdate(
      { leadId: event.aggregateId },
      {
        $set: {
          isDeleted: true,
          updatedAt: event.occurredAt,
          version: event.aggregateVersion,
          lastEventId: event.eventId,
          lastEventType: event.eventType,
          lastEventTimestamp: event.occurredAt,
        },
        $inc: {
          eventCount: 1,
        },
      },
      { new: true }
    );

    this.logger.debug(`Marked lead projection as deleted: ${event.aggregateId}`);
  }

  /**
   * Handle LeadScoreChanged event
   */
  private async handleLeadScoreChanged(event: LeadScoreChangedEvent): Promise<void> {
    // For now, just update the event metadata
    // In a real implementation, you might store score in the projection
    await this.leadProjectionModel.findOneAndUpdate(
      { leadId: event.aggregateId },
      {
        $set: {
          updatedAt: event.occurredAt,
          lastEventId: event.eventId,
          lastEventType: event.eventType,
          lastEventTimestamp: event.occurredAt,
        },
        $inc: {
          eventCount: 1,
        },
      },
      { new: true }
    );

    this.logger.debug(`Updated lead score projection: ${event.aggregateId}`);
  }

  /**
   * Handle LeadActivityCreated event
   */
  private async handleLeadActivityCreated(event: LeadActivityCreatedEvent): Promise<void> {
    // For now, just update the event metadata
    // In a real implementation, you might store activity count or last activity date
    await this.leadProjectionModel.findOneAndUpdate(
      { leadId: event.aggregateId },
      {
        $set: {
          updatedAt: event.occurredAt,
          lastEventId: event.eventId,
          lastEventType: event.eventType,
          lastEventTimestamp: event.occurredAt,
        },
        $inc: {
          eventCount: 1,
        },
      },
      { new: true }
    );

    this.logger.debug(`Updated lead activity projection: ${event.aggregateId}`);
  }
}
