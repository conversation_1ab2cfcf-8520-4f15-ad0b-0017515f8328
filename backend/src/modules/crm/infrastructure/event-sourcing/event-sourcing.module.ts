import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';

// Event Store
import { MongoDbEventStore } from '@/modules/crm/infrastructure/event-store/mongodb-event-store';
import { EventStore } from '@/modules/crm/domain/events/base/domain-event.base';

// Schemas
import { EventStream, EventStreamSchema } from '@/modules/crm/infrastructure/event-store/schemas/event-stream.schema';
import { EventSnapshot, EventSnapshotSchema } from '@/modules/crm/infrastructure/event-store/schemas/event-stream.schema';
import { EventProjection, EventProjectionSchema } from '@/modules/crm/infrastructure/event-store/schemas/event-stream.schema';
import { EventSubscription, EventSubscriptionSchema } from '@/modules/crm/infrastructure/event-store/schemas/event-stream.schema';
import { EventSagaState, EventSagaStateSchema } from '@/modules/crm/infrastructure/event-store/schemas/event-stream.schema';
import { LeadProjection, LeadProjectionSchema } from '@/modules/crm/infrastructure/event-store/schemas/lead-projection.schema';

// Event Bus
import { EventBusService } from './services/event-bus.service';
import { EventPublisherService } from './services/event-publisher.service';
import { EventSubscriptionService } from './services/event-subscription.service';

// Projection and Snapshot Services
import { ProjectionService } from './services/projection.service';
import { SnapshotService } from './services/snapshot.service';
import { SnapshotConfigService } from './services/snapshot-config.service';
import { SnapshotCleanupService } from './services/snapshot-cleanup.service';

// Saga Services
import { SagaService } from './services/saga.service';

// Event Replay and Recovery Services
import { EventReplayService } from './services/event-replay.service';
import { EventMigrationService } from './services/event-migration.service';
import { EventRecoveryService } from './services/event-recovery.service';

// Aggregate Repositories
import { LeadAggregateRepository } from '@/modules/crm/infrastructure/repositories/lead-aggregate.repository';
import { ILeadAggregateRepository } from '@/modules/crm/domain/repositories/lead-aggregate.repository';

// Projection Services
import { LeadProjectionHandler } from './projections/lead-projection.handler';
import { LeadProjectionService } from './projections/lead-projection.service';

/**
 * Event Sourcing Module
 * Provides comprehensive event sourcing infrastructure
 */
@Module({
  imports: [
    ConfigModule,
    ScheduleModule.forRoot(), // For snapshot cleanup cron jobs

    // Register MongoDB schemas for event sourcing using named connection
    MongooseModule.forFeature([
      { name: EventStream.name, schema: EventStreamSchema },
      { name: EventSnapshot.name, schema: EventSnapshotSchema },
      { name: EventProjection.name, schema: EventProjectionSchema },
      { name: EventSubscription.name, schema: EventSubscriptionSchema },
      { name: EventSagaState.name, schema: EventSagaStateSchema },
      { name: LeadProjection.name, schema: LeadProjectionSchema },
    ], 'zenoo-main'),
  ],
  providers: [
    // Event Store
    {
      provide: 'EVENT_STORE',
      useClass: MongoDbEventStore,
    },
    MongoDbEventStore,

    // Event Bus and Publishing
    EventBusService,
    EventPublisherService,
    EventSubscriptionService,

    // Projection and Snapshot Services
    ProjectionService,
    SnapshotService,
    SnapshotConfigService,
    SnapshotCleanupService,

    // Saga Service
    SagaService,

    // Event Replay and Recovery Services
    EventReplayService,
    EventMigrationService,
    EventRecoveryService,

    // Aggregate Repositories
    {
      provide: 'LEAD_AGGREGATE_REPOSITORY',
      useClass: LeadAggregateRepository,
    },

    // Projection Services
    LeadProjectionHandler,
    LeadProjectionService,
  ],
  exports: [
    // Core interfaces
    'EVENT_STORE',
    
    // Services
    EventBusService,
    EventPublisherService,
    EventSubscriptionService,
    ProjectionService,
    SnapshotService,
    SnapshotConfigService,
    SnapshotCleanupService,
    SagaService,

    // Event Replay and Recovery Services
    EventReplayService,
    EventMigrationService,
    EventRecoveryService,

    // Aggregate Repositories
    'LEAD_AGGREGATE_REPOSITORY',

    // Projection Services
    LeadProjectionService,
    
    // Implementation
    MongoDbEventStore,
  ],
})
export class EventSourcingModule {}

/**
 * Event Sourcing Configuration
 */
export interface EventSourcingConfig {
  // Event Store Configuration
  eventStore: {
    snapshotFrequency: number; // Take snapshot every N events
    maxEventsPerStream: number; // Maximum events before requiring snapshot
    cleanupOlderThan: number; // Days to keep events
  };

  // Projection Configuration
  projections: {
    batchSize: number; // Number of events to process in batch
    maxRetries: number; // Maximum retries for failed projections
    retryDelay: number; // Delay between retries in ms
  };

  // Saga Configuration
  sagas: {
    timeoutDuration: number; // Default saga timeout in ms
    maxRetries: number; // Maximum retries for failed saga steps
    compensationTimeout: number; // Timeout for compensation actions
  };

  // Publishing Configuration
  publishing: {
    batchSize: number; // Number of events to publish in batch
    retryAttempts: number; // Number of retry attempts
    retryDelay: number; // Delay between retries
  };
}

/**
 * Default Event Sourcing Configuration
 */
export const DEFAULT_EVENT_SOURCING_CONFIG: EventSourcingConfig = {
  eventStore: {
    snapshotFrequency: 100,
    maxEventsPerStream: 1000,
    cleanupOlderThan: 365,
  },
  projections: {
    batchSize: 50,
    maxRetries: 3,
    retryDelay: 1000,
  },
  sagas: {
    timeoutDuration: 300000, // 5 minutes
    maxRetries: 3,
    compensationTimeout: 60000, // 1 minute
  },
  publishing: {
    batchSize: 25,
    retryAttempts: 3,
    retryDelay: 2000,
  },
};

/**
 * Event Sourcing Decorators
 */

/**
 * Marks a class as an aggregate root
 */
export function AggregateRoot(aggregateType: string) {
  return function (constructor: any) {
    Reflect.defineMetadata('aggregate:type', aggregateType, constructor);
    Reflect.defineMetadata('aggregate:root', true, constructor);
  };
}

/**
 * Marks a method as an event handler within an aggregate
 */
export function ApplyEvent(eventType: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const existingEvents = Reflect.getMetadata('aggregate:events', target.constructor) || [];
    existingEvents.push({ eventType, handler: propertyKey });
    Reflect.defineMetadata('aggregate:events', existingEvents, target.constructor);
  };
}

/**
 * Marks a class as a projection
 */
export function Projection(projectionName: string, eventTypes: string[]) {
  return function (constructor: any) {
    Reflect.defineMetadata('projection:name', projectionName, constructor);
    Reflect.defineMetadata('projection:events', eventTypes, constructor);
  };
}

/**
 * Marks a method as a projection event handler
 */
export function ProjectionHandler(eventType: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const existingHandlers = Reflect.getMetadata('projection:handlers', target.constructor) || [];
    existingHandlers.push({ eventType, handler: propertyKey });
    Reflect.defineMetadata('projection:handlers', existingHandlers, target.constructor);
  };
}

/**
 * Marks a class as a saga
 */
export function Saga(sagaType: string) {
  return function (constructor: any) {
    Reflect.defineMetadata('saga:type', sagaType, constructor);
  };
}

/**
 * Marks a method as a saga step
 */
export function SagaStep(stepName: string, eventTypes: string[]) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const existingSteps = Reflect.getMetadata('saga:steps', target.constructor) || [];
    existingSteps.push({ stepName, eventTypes, handler: propertyKey });
    Reflect.defineMetadata('saga:steps', existingSteps, target.constructor);
  };
}
