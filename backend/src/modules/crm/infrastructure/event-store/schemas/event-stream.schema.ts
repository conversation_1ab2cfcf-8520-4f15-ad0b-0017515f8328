import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * Event Stream Schema for MongoDB
 * Stores domain events for event sourcing
 */
@Schema({
  collection: 'event_streams',
  timestamps: false, // We manage timestamps manually
  versionKey: false,
})
export class EventStream extends Document {
  @Prop({ required: true, index: true })
  streamName: string;

  @Prop({ required: true, unique: true })
  eventId: string;

  @Prop({ required: true, index: true })
  eventType: string;

  @Prop({ required: true, index: true })
  aggregateId: string;

  @Prop({ required: true, index: true })
  aggregateType: string;

  @Prop({ required: true })
  aggregateVersion: number;

  @Prop({ required: true, index: true })
  occurredAt: Date;

  @Prop({ required: false, index: true })
  causationId?: string;

  @Prop({ required: false, index: true })
  correlationId?: string;

  @Prop({ required: false, index: true })
  userId?: string;

  @Prop({ required: false, index: true })
  tenantId?: string;

  @Prop({ type: Object, required: true })
  metadata: Record<string, any>;

  @Prop({ type: Object, required: true })
  payload: Record<string, any>;

  @Prop({ required: true, unique: true })
  position: number;
}

export const EventStreamSchema = SchemaFactory.createForClass(EventStream);

// Compound indexes for efficient querying
EventStreamSchema.index({ streamName: 1, aggregateVersion: 1 }, { unique: true });
EventStreamSchema.index({ aggregateType: 1, aggregateId: 1, aggregateVersion: 1 });
EventStreamSchema.index({ eventType: 1, occurredAt: -1 });
EventStreamSchema.index({ correlationId: 1, occurredAt: 1 });
EventStreamSchema.index({ tenantId: 1, occurredAt: -1 });
EventStreamSchema.index({ position: 1 }, { unique: true });

// TTL index for automatic cleanup (optional)
// EventStreamSchema.index({ occurredAt: 1 }, { expireAfterSeconds: 31536000 }); // 1 year

/**
 * Event Snapshot Schema for MongoDB
 * Stores aggregate snapshots for performance optimization
 */
@Schema({
  collection: 'event_snapshots',
  timestamps: false,
  versionKey: false,
})
export class EventSnapshot extends Document {
  @Prop({ required: true, index: true })
  aggregateId: string;

  @Prop({ required: true, index: true })
  aggregateType: string;

  @Prop({ required: true })
  version: number;

  @Prop({ type: Object, required: true })
  data: Record<string, any>;

  @Prop({ required: true, index: true })
  createdAt: Date;

  @Prop({ required: false })
  metadata?: Record<string, any>;
}

export const EventSnapshotSchema = SchemaFactory.createForClass(EventSnapshot);

// Compound indexes
EventSnapshotSchema.index({ aggregateType: 1, aggregateId: 1, version: -1 }, { unique: true });
EventSnapshotSchema.index({ createdAt: -1 });

// TTL index for automatic cleanup of old snapshots
EventSnapshotSchema.index({ createdAt: 1 }, { expireAfterSeconds: 7776000 }); // 90 days

/**
 * Event Projection Schema for MongoDB
 * Stores read model projections built from events
 */
@Schema({
  collection: 'event_projections',
  timestamps: true,
  versionKey: false,
})
export class EventProjection extends Document {
  @Prop({ required: true, index: true })
  projectionName: string;

  @Prop({ required: true, index: true })
  aggregateId: string;

  @Prop({ required: true, index: true })
  aggregateType: string;

  @Prop({ required: true })
  lastProcessedVersion: number;

  @Prop({ required: true })
  lastProcessedPosition: number;

  @Prop({ type: Object, required: true })
  data: Record<string, any>;

  @Prop({ required: true, index: true })
  updatedAt: Date;

  @Prop({ required: false, index: true })
  tenantId?: string;
}

export const EventProjectionSchema = SchemaFactory.createForClass(EventProjection);

// Compound indexes
EventProjectionSchema.index({ 
  projectionName: 1, 
  aggregateType: 1, 
  aggregateId: 1 
}, { unique: true });
EventProjectionSchema.index({ projectionName: 1, tenantId: 1, updatedAt: -1 });
EventProjectionSchema.index({ lastProcessedPosition: 1 });

/**
 * Event Subscription Schema for MongoDB
 * Tracks event subscriptions and their processing state
 */
@Schema({
  collection: 'event_subscriptions',
  timestamps: true,
  versionKey: false,
})
export class EventSubscription extends Document {
  @Prop({ required: true, unique: true })
  subscriptionName: string;

  @Prop({ required: true })
  eventTypes: string[];

  @Prop({ required: true })
  lastProcessedPosition: number;

  @Prop({ required: true, default: true })
  isActive: boolean;

  @Prop({ required: false })
  filterCriteria?: Record<string, any>;

  @Prop({ required: false })
  processingErrors?: Array<{
    position: number;
    eventId: string;
    error: string;
    occurredAt: Date;
    retryCount: number;
  }>;

  @Prop({ required: true, index: true })
  lastProcessedAt: Date;

  @Prop({ required: false })
  metadata?: Record<string, any>;
}

export const EventSubscriptionSchema = SchemaFactory.createForClass(EventSubscription);

// Indexes
EventSubscriptionSchema.index({ subscriptionName: 1 }, { unique: true });
EventSubscriptionSchema.index({ isActive: 1, lastProcessedPosition: 1 });
EventSubscriptionSchema.index({ lastProcessedAt: -1 });

/**
 * Event Saga State Schema for MongoDB
 * Stores saga state for long-running business processes
 */
@Schema({
  collection: 'event_saga_states',
  timestamps: true,
  versionKey: false,
})
export class EventSagaState extends Document {
  @Prop({ required: true, unique: true })
  sagaId: string;

  @Prop({ required: true, index: true })
  sagaType: string;

  @Prop({ required: true })
  currentStep: string;

  @Prop({ type: Object, required: true })
  data: Record<string, any>;

  @Prop({ required: true, index: true })
  status: 'active' | 'completed' | 'failed' | 'compensating';

  @Prop({ required: true, index: true })
  startedAt: Date;

  @Prop({ required: false })
  completedAt?: Date;

  @Prop({ required: false })
  failedAt?: Date;

  @Prop({ required: false })
  error?: string;

  @Prop({ required: false })
  compensationData?: Record<string, any>;

  @Prop({ required: false, index: true })
  correlationId?: string;

  @Prop({ required: false, index: true })
  tenantId?: string;

  @Prop({ required: false })
  metadata?: Record<string, any>;
}

export const EventSagaStateSchema = SchemaFactory.createForClass(EventSagaState);

// Indexes
EventSagaStateSchema.index({ sagaType: 1, status: 1, startedAt: -1 });
EventSagaStateSchema.index({ correlationId: 1 });
EventSagaStateSchema.index({ tenantId: 1, status: 1 });
EventSagaStateSchema.index({ status: 1, startedAt: 1 });

// TTL index for completed/failed sagas
EventSagaStateSchema.index({ 
  completedAt: 1 
}, { 
  expireAfterSeconds: 2592000, // 30 days
  partialFilterExpression: { status: { $in: ['completed', 'failed'] } }
});
