import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * Lead Projection Document
 * Read model for Lead aggregate built from events
 */
@Schema({
  collection: 'lead_projections',
  timestamps: true,
  versionKey: false,
})
export class LeadProjection extends Document {
  @Prop({ required: true, unique: true })
  leadId: string;

  @Prop({ required: true })
  name: string;

  @Prop({ type: Object, required: true })
  contactInfo: {
    email?: string;
    phone?: string;
    company?: string;
    website?: string;
    address?: string;
    city?: string;
    country?: string;
  };

  @Prop({ required: true })
  status: string;

  @Prop({ required: true })
  source: string;

  @Prop({ required: true })
  type: string;

  @Prop({ required: true })
  priority: string;

  @Prop()
  expectedRevenue?: number;

  @Prop()
  probability?: number;

  @Prop()
  description?: string;

  @Prop()
  assignedUserId?: number;

  @Prop()
  companyId?: number;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop()
  partnerId?: number;

  @Prop()
  stageId?: number;

  @Prop()
  teamId?: number;

  @Prop()
  dateDeadline?: Date;

  @Prop()
  lostReasonId?: number;

  @Prop()
  campaignId?: number;

  @Prop()
  sourceId?: number;

  @Prop()
  mediumId?: number;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  updatedAt: Date;

  @Prop({ required: true, default: 1 })
  version: number;

  @Prop({ required: true, default: false })
  isDeleted: boolean;

  // Projection metadata
  @Prop({ required: true })
  lastEventId: string;

  @Prop({ required: true })
  lastEventType: string;

  @Prop({ required: true })
  lastEventTimestamp: Date;

  @Prop({ required: true, default: 0 })
  eventCount: number;
}

export const LeadProjectionSchema = SchemaFactory.createForClass(LeadProjection);

// Create indexes for better query performance
LeadProjectionSchema.index({ leadId: 1 });
LeadProjectionSchema.index({ status: 1 });
LeadProjectionSchema.index({ assignedUserId: 1 });
LeadProjectionSchema.index({ teamId: 1 });
LeadProjectionSchema.index({ source: 1 });
LeadProjectionSchema.index({ type: 1 });
LeadProjectionSchema.index({ priority: 1 });
LeadProjectionSchema.index({ isDeleted: 1 });
LeadProjectionSchema.index({ createdAt: 1 });
LeadProjectionSchema.index({ updatedAt: 1 });
LeadProjectionSchema.index({ lastEventTimestamp: 1 });

// Compound indexes for common queries
LeadProjectionSchema.index({ status: 1, assignedUserId: 1 });
LeadProjectionSchema.index({ teamId: 1, status: 1 });
LeadProjectionSchema.index({ isDeleted: 1, status: 1 });
LeadProjectionSchema.index({ assignedUserId: 1, isDeleted: 1 });

export type LeadProjectionDocument = LeadProjection & Document;
