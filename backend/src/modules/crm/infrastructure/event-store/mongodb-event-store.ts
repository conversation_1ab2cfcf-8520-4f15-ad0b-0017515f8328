import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DomainEvent, EventStore, SerializedDomainEvent } from '../../domain/events/base/domain-event.base';

/**
 * MongoDB Event Store Implementation
 * Provides event sourcing capabilities with MongoDB as the storage backend
 */
@Injectable()
export class MongoDbEventStore implements EventStore {
  private readonly logger = new Logger(MongoDbEventStore.name);

  constructor(
    @InjectModel('EventStream') private readonly eventStreamModel: Model<EventStreamDocument>,
    @InjectModel('EventSnapshot') private readonly snapshotModel: Model<SnapshotDocument>,
  ) {}

  /**
   * Append events to a stream
   */
  async append(streamName: string, events: DomainEvent[], expectedVersion?: number): Promise<void> {
    this.logger.debug(`Appending ${events.length} events to stream: ${streamName}`);

    const session = await this.eventStreamModel.db.startSession();
    
    try {
      await session.withTransaction(async () => {
        // Check expected version if provided
        if (expectedVersion !== undefined) {
          const currentVersion = await this.getCurrentVersion(streamName, session);
          if (currentVersion !== expectedVersion) {
            throw new Error(`Concurrency conflict: expected version ${expectedVersion}, but current version is ${currentVersion}`);
          }
        }

        // Prepare event documents
        const eventDocuments = events.map((event, index) => ({
          streamName,
          eventId: event.eventId,
          eventType: event.eventType,
          aggregateId: event.aggregateId,
          aggregateType: event.aggregateType,
          aggregateVersion: event.aggregateVersion + index,
          occurredAt: event.occurredAt,
          causationId: event.causationId,
          correlationId: event.correlationId,
          userId: event.userId,
          tenantId: event.tenantId,
          metadata: event.metadata,
          payload: event.getPayload(),
          position: await this.getNextGlobalPosition(),
        }));

        // Insert events
        await this.eventStreamModel.insertMany(eventDocuments, { session });

        this.logger.debug(`Successfully appended ${events.length} events to stream: ${streamName}`);
      });
    } catch (error) {
      this.logger.error(`Failed to append events to stream: ${streamName}`, error);
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Get events from a stream
   */
  async getEvents(streamName: string, fromVersion?: number): Promise<DomainEvent[]> {
    this.logger.debug(`Getting events from stream: ${streamName}, fromVersion: ${fromVersion}`);

    const query: any = { streamName };
    if (fromVersion !== undefined) {
      query.aggregateVersion = { $gte: fromVersion };
    }

    const eventDocuments = await this.eventStreamModel
      .find(query)
      .sort({ aggregateVersion: 1 })
      .exec();

    const events = eventDocuments.map(doc => this.deserializeEvent(doc));
    
    this.logger.debug(`Retrieved ${events.length} events from stream: ${streamName}`);
    return events;
  }

  /**
   * Get all events from a global position
   */
  async getAllEvents(fromPosition?: number): Promise<DomainEvent[]> {
    this.logger.debug(`Getting all events from position: ${fromPosition}`);

    const query: any = {};
    if (fromPosition !== undefined) {
      query.position = { $gte: fromPosition };
    }

    const eventDocuments = await this.eventStreamModel
      .find(query)
      .sort({ position: 1 })
      .exec();

    const events = eventDocuments.map(doc => this.deserializeEvent(doc));
    
    this.logger.debug(`Retrieved ${events.length} events from position: ${fromPosition}`);
    return events;
  }

  /**
   * Get snapshot for an aggregate
   */
  async getSnapshot(aggregateId: string, aggregateType: string): Promise<any> {
    this.logger.debug(`Getting snapshot for aggregate: ${aggregateType}-${aggregateId}`);

    const snapshot = await this.snapshotModel
      .findOne({ aggregateId, aggregateType })
      .sort({ version: -1 })
      .exec();

    if (snapshot) {
      this.logger.debug(`Found snapshot for aggregate: ${aggregateType}-${aggregateId} at version: ${snapshot.version}`);
      return {
        data: snapshot.data,
        version: snapshot.version,
        createdAt: snapshot.createdAt,
      };
    }

    this.logger.debug(`No snapshot found for aggregate: ${aggregateType}-${aggregateId}`);
    return null;
  }

  /**
   * Save snapshot for an aggregate
   */
  async saveSnapshot(aggregateId: string, aggregateType: string, snapshot: any, version: number): Promise<void> {
    this.logger.debug(`Saving snapshot for aggregate: ${aggregateType}-${aggregateId} at version: ${version}`);

    await this.snapshotModel.create({
      aggregateId,
      aggregateType,
      version,
      data: snapshot,
      createdAt: new Date(),
    });

    this.logger.debug(`Successfully saved snapshot for aggregate: ${aggregateType}-${aggregateId}`);
  }

  /**
   * Get current version of a stream
   */
  private async getCurrentVersion(streamName: string, session?: any): Promise<number> {
    const lastEvent = await this.eventStreamModel
      .findOne({ streamName })
      .sort({ aggregateVersion: -1 })
      .session(session)
      .exec();

    return lastEvent ? lastEvent.aggregateVersion : 0;
  }

  /**
   * Get next global position
   */
  private async getNextGlobalPosition(): Promise<number> {
    const lastEvent = await this.eventStreamModel
      .findOne({})
      .sort({ position: -1 })
      .exec();

    return lastEvent ? lastEvent.position + 1 : 1;
  }

  /**
   * Deserialize event from document
   */
  private deserializeEvent(doc: EventStreamDocument): DomainEvent {
    // This is a simplified deserialization
    // In a real implementation, you would have a registry of event types
    // and their corresponding classes for proper deserialization
    
    const serializedEvent: SerializedDomainEvent = {
      eventId: doc.eventId,
      eventType: doc.eventType,
      aggregateId: doc.aggregateId,
      aggregateType: doc.aggregateType,
      aggregateVersion: doc.aggregateVersion,
      occurredAt: doc.occurredAt.toISOString(),
      causationId: doc.causationId,
      correlationId: doc.correlationId,
      userId: doc.userId,
      tenantId: doc.tenantId,
      metadata: doc.metadata,
      payload: doc.payload,
    };

    // For now, return a generic domain event
    // In practice, you'd use an event registry to create the correct event type
    return new GenericDomainEvent(serializedEvent);
  }

  /**
   * Clean up old events (for maintenance)
   */
  async cleanupOldEvents(beforeDate: Date, keepSnapshots: boolean = true): Promise<number> {
    this.logger.log(`Cleaning up events before: ${beforeDate.toISOString()}`);

    const result = await this.eventStreamModel.deleteMany({
      occurredAt: { $lt: beforeDate },
    });

    if (!keepSnapshots) {
      await this.snapshotModel.deleteMany({
        createdAt: { $lt: beforeDate },
      });
    }

    this.logger.log(`Cleaned up ${result.deletedCount} events`);
    return result.deletedCount;
  }

  /**
   * Get stream statistics
   */
  async getStreamStats(streamName: string): Promise<StreamStats> {
    const stats = await this.eventStreamModel.aggregate([
      { $match: { streamName } },
      {
        $group: {
          _id: null,
          eventCount: { $sum: 1 },
          firstEvent: { $min: '$occurredAt' },
          lastEvent: { $max: '$occurredAt' },
          currentVersion: { $max: '$aggregateVersion' },
        },
      },
    ]);

    if (stats.length === 0) {
      return {
        streamName,
        eventCount: 0,
        currentVersion: 0,
        firstEvent: null,
        lastEvent: null,
      };
    }

    return {
      streamName,
      eventCount: stats[0].eventCount,
      currentVersion: stats[0].currentVersion,
      firstEvent: stats[0].firstEvent,
      lastEvent: stats[0].lastEvent,
    };
  }
}

/**
 * Generic domain event for deserialization
 */
class GenericDomainEvent extends DomainEvent {
  constructor(private readonly serializedData: SerializedDomainEvent) {
    super(
      serializedData.aggregateId,
      serializedData.aggregateType,
      serializedData.aggregateVersion,
      serializedData.eventType,
      serializedData.metadata,
      serializedData.causationId,
      serializedData.correlationId,
      serializedData.userId,
      serializedData.tenantId,
    );
    
    // Override properties from serialized data
    (this as any).eventId = serializedData.eventId;
    (this as any).occurredAt = new Date(serializedData.occurredAt);
  }

  getPayload(): Record<string, any> {
    return this.serializedData.payload;
  }
}

/**
 * Event stream document interface
 */
interface EventStreamDocument {
  streamName: string;
  eventId: string;
  eventType: string;
  aggregateId: string;
  aggregateType: string;
  aggregateVersion: number;
  occurredAt: Date;
  causationId?: string;
  correlationId?: string;
  userId?: string;
  tenantId?: string;
  metadata: any;
  payload: any;
  position: number;
}

/**
 * Snapshot document interface
 */
interface SnapshotDocument {
  aggregateId: string;
  aggregateType: string;
  version: number;
  data: any;
  createdAt: Date;
}

/**
 * Stream statistics interface
 */
interface StreamStats {
  streamName: string;
  eventCount: number;
  currentVersion: number;
  firstEvent: Date | null;
  lastEvent: Date | null;
}
