import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * Lead MongoDB Schema
 * Defines the structure for storing leads in MongoDB
 */
@Schema({ timestamps: true })
export class LeadDocument extends Document {
  @Prop({ required: true })
  name: string;

  @Prop()
  description?: string;

  @Prop({
    type: {
      email: String,
      phone: String,
      mobile: String,
      website: String,
      company: String,
      jobTitle: String,
      address: String,
      city: String,
      state: String,
      country: String,
      postalCode: String,
      socialMedia: {
        type: Map,
        of: String,
      },
    },
  })
  contactInfo?: {
    email?: string;
    phone?: string;
    mobile?: string;
    website?: string;
    company?: string;
    jobTitle?: string;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
    socialMedia?: Map<string, string>;
  };

  @Prop({
    type: {
      value: { type: String, enum: ['new', 'qualified', 'proposition', 'won', 'lost', 'cancelled'] },
    },
    default: { value: 'new' },
  })
  status: {
    value: string;
  };

  @Prop({
    type: {
      value: { type: String, enum: ['low', 'medium', 'high'] },
    },
    default: { value: 'medium' },
  })
  priority: {
    value: string;
  };

  @Prop({
    type: {
      value: { type: String, enum: ['lead', 'opportunity'] },
    },
    default: { value: 'lead' },
  })
  type: {
    value: string;
  };

  @Prop()
  source?: string;

  @Prop({ type: Number, min: 0 })
  expectedRevenue?: number;

  @Prop({ type: Number, min: 0, max: 100 })
  probability?: number;

  @Prop({ type: Number })
  assignedUserId?: number;

  @Prop({ type: Number })
  teamId?: number;

  @Prop({ type: Number })
  stageId?: number;

  @Prop({ type: Date })
  dateDeadline?: Date;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const LeadSchema = SchemaFactory.createForClass(LeadDocument);

// Add indexes for better query performance
LeadSchema.index({ assignedUserId: 1 });
LeadSchema.index({ teamId: 1 });
LeadSchema.index({ 'status.value': 1 });
LeadSchema.index({ 'priority.value': 1 });
LeadSchema.index({ source: 1 });
LeadSchema.index({ createdAt: -1 });
LeadSchema.index({ dateDeadline: 1 });
LeadSchema.index({ tags: 1 });

// Text index for search functionality
LeadSchema.index({
  name: 'text',
  'contactInfo.email': 'text',
  'contactInfo.company': 'text',
  'contactInfo.phone': 'text',
});

// Compound indexes for common queries
LeadSchema.index({ teamId: 1, 'status.value': 1 });
LeadSchema.index({ assignedUserId: 1, 'status.value': 1 });
LeadSchema.index({ 'priority.value': 1, createdAt: -1 });

export { LeadDocument as Lead };
