import { Injectable } from '@nestjs/common';
import { IStageRepository, Stage, CreateStageData, UpdateStageData } from '@/modules/crm/domain/repositories/stage.repository';

/**
 * MongoDB Stage Repository Implementation
 * Simple implementation for stage repository
 */
@Injectable()
export class MongoStageRepository implements IStageRepository {
  // In-memory storage for now (would be replaced with actual MongoDB implementation)
  private stages: Stage[] = [
    {
      id: 1,
      name: 'New',
      description: 'New leads',
      pipelineId: 1,
      order: 1,
      type: 'lead',
      isActive: true,
      expectedDuration: 3,
      requirements: [],
      color: '#6B7280',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 2,
      name: 'Qualified',
      description: 'Qualified leads',
      pipelineId: 1,
      order: 2,
      type: 'lead',
      isActive: true,
      expectedDuration: 7,
      requirements: ['Contact information verified'],
      color: '#3B82F6',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 3,
      name: 'Proposal',
      description: 'Proposal sent',
      pipelineId: 1,
      order: 3,
      type: 'opportunity',
      isActive: true,
      expectedDuration: 14,
      requirements: ['Proposal prepared', 'Budget confirmed'],
      color: '#F59E0B',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 4,
      name: 'Won',
      description: 'Deal won',
      pipelineId: 1,
      order: 4,
      type: 'opportunity',
      isActive: true,
      expectedDuration: 0,
      requirements: ['Contract signed'],
      color: '#10B981',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  async findById(id: number): Promise<Stage | null> {
    return this.stages.find(stage => stage.id === id) || null;
  }

  async findAll(): Promise<Stage[]> {
    return [...this.stages];
  }

  async findByPipelineId(pipelineId: number): Promise<Stage[]> {
    return this.stages.filter(stage => stage.pipelineId === pipelineId);
  }

  async create(stageData: CreateStageData): Promise<Stage> {
    const newStage: Stage = {
      id: Math.max(...this.stages.map(s => s.id)) + 1,
      ...stageData,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    this.stages.push(newStage);
    return newStage;
  }

  async update(id: number, stageData: UpdateStageData): Promise<Stage> {
    const index = this.stages.findIndex(stage => stage.id === id);
    if (index === -1) {
      throw new Error(`Stage with ID ${id} not found`);
    }

    const updatedStage = {
      ...this.stages[index],
      ...stageData,
      updatedAt: new Date(),
    };

    this.stages[index] = updatedStage;
    return updatedStage;
  }

  async delete(id: number): Promise<boolean> {
    const index = this.stages.findIndex(stage => stage.id === id);
    if (index === -1) {
      return false;
    }

    this.stages.splice(index, 1);
    return true;
  }

  async findByType(type: string): Promise<Stage[]> {
    return this.stages.filter(stage => stage.type === type);
  }

  async reorderStages(stageOrders: Array<{ id: number; order: number }>): Promise<void> {
    stageOrders.forEach(({ id, order }) => {
      const stage = this.stages.find(s => s.id === id);
      if (stage) {
        stage.order = order;
        stage.updatedAt = new Date();
      }
    });

    // Sort stages by order
    this.stages.sort((a, b) => a.order - b.order);
  }
}
