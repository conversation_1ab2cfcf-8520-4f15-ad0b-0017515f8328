import { Injectable } from '@nestjs/common';
import { IStageRepository, CreateStageData, UpdateStageData } from '@/modules/crm/domain/repositories/stage.repository';
import { Stage } from '@/modules/crm/domain/entities/stage.entity';

/**
 * MongoDB Stage Repository Implementation
 * Simple implementation for stage repository
 */
@Injectable()
export class MongoStageRepository implements IStageRepository {
  // In-memory storage for now (would be replaced with actual MongoDB implementation)
  private stages: Stage[] = [
    new Stage(
      1,
      'New',
      1, // sequence
      false, // isWonStage
      false, // isLostStage
      undefined, // teamId
      'New leads requirements', // requirements
      false, // fold
      40, // probabilityMin
      60, // probabilityMax
      false, // onChange
      new Date(), // createdAt
      new Date(), // updatedAt
    ),
    new Stage(
      2,
      'Qualified',
      2, // sequence
      false, // isWonStage
      false, // isLostStage
      undefined, // teamId
      'Contact information verified', // requirements
      false, // fold
      50, // probabilityMin
      70, // probabilityMax
      false, // onChange
      new Date(), // createdAt
      new Date(), // updatedAt
    ),
    new Stage(
      3,
      'Proposal',
      3, // sequence
      false, // isWonStage
      false, // isLostStage
      undefined, // teamId
      'Proposal prepared, Budget confirmed', // requirements
      false, // fold
      70, // probabilityMin
      90, // probabilityMax
      false, // onChange
      new Date(), // createdAt
      new Date(), // updatedAt
    ),
    new Stage(
      4,
      'Won',
      4, // sequence
      true, // isWonStage
      false, // isLostStage
      undefined, // teamId
      'Contract signed', // requirements
      false, // fold
      100, // probabilityMin
      100, // probabilityMax
      false, // onChange
      new Date(), // createdAt
      new Date(), // updatedAt
    ),
  ];

  async findById(id: number): Promise<Stage | null> {
    return this.stages.find(stage => stage.id === id) || null;
  }

  async findAll(): Promise<Stage[]> {
    return [...this.stages];
  }

  async findByPipelineId(pipelineId: number): Promise<Stage[]> {
    // TODO: Stage entity doesn't have pipelineId property
    // For now, return all stages
    return this.stages;
  }

  async create(stageData: CreateStageData): Promise<Stage> {
    const newStage = new Stage(
      Math.max(...this.stages.map(s => s.id)) + 1,
      stageData.name,
      stageData.sequence || this.stages.length + 1,
      false, // isWonStage
      false, // isLostStage
      stageData.teamId,
      stageData.requirements,
      stageData.foldInKanban || false,
      stageData.probability || 50, // probabilityMin
      stageData.probability || 50, // probabilityMax
      false, // onChange
      new Date(), // createdAt
      new Date(), // updatedAt
    );

    this.stages.push(newStage);
    return newStage;
  }

  async update(id: number, stageData: UpdateStageData): Promise<Stage> {
    const index = this.stages.findIndex(stage => stage.id === id);
    if (index === -1) {
      throw new Error(`Stage with ID ${id} not found`);
    }

    const existingStage = this.stages[index];
    const updatedStage = new Stage(
      existingStage.id,
      stageData.name || existingStage.name,
      stageData.sequence !== undefined ? stageData.sequence : existingStage.sequence,
      existingStage.isWonStage,
      existingStage.isLostStage,
      stageData.teamId !== undefined ? stageData.teamId : existingStage.teamId,
      stageData.requirements !== undefined ? stageData.requirements : existingStage.requirements,
      stageData.foldInKanban !== undefined ? stageData.foldInKanban : existingStage.fold,
      stageData.probability !== undefined ? stageData.probability : existingStage.probabilityMin,
      stageData.probability !== undefined ? stageData.probability : existingStage.probabilityMax,
      existingStage.onChange,
      existingStage.createdAt,
      new Date(), // updatedAt
    );

    this.stages[index] = updatedStage;
    return updatedStage;
  }

  async delete(id: number): Promise<boolean> {
    const index = this.stages.findIndex(stage => stage.id === id);
    if (index === -1) {
      return false;
    }

    this.stages.splice(index, 1);
    return true;
  }



  async reorderStages(stageSequences: Array<{ id: number; sequence: number }>): Promise<boolean> {
    try {
      stageSequences.forEach(({ id, sequence }) => {
        const stageIndex = this.stages.findIndex(s => s.id === id);
        if (stageIndex !== -1) {
          const existingStage = this.stages[stageIndex];
          const updatedStage = new Stage(
            existingStage.id,
            existingStage.name,
            sequence,
            existingStage.isWonStage,
            existingStage.isLostStage,
            existingStage.teamId,
            existingStage.requirements,
            existingStage.fold,
            existingStage.probabilityMin,
            existingStage.probabilityMax,
            existingStage.onChange,
            existingStage.createdAt,
            new Date(), // updatedAt
          );
          this.stages[stageIndex] = updatedStage;
        }
      });

      // Sort stages by sequence
      this.stages.sort((a, b) => a.sequence - b.sequence);
      return true;
    } catch (error) {
      return false;
    }
  }

  // Missing methods - placeholder implementations
  async findByPipeline(pipelineId: number): Promise<Stage[]> {
    // TODO: Stage entity doesn't have pipelineId property
    // For now, return all stages
    return this.stages;
  }

  async findByTeam(teamId?: number): Promise<Stage[]> {
    if (!teamId) return this.stages;
    return this.stages.filter(stage => (stage as any).teamId === teamId);
  }

  async findActive(teamId?: number): Promise<Stage[]> {
    let filtered = this.stages.filter(stage => stage.isActive());
    if (teamId) {
      filtered = filtered.filter(stage => stage.teamId === teamId);
    }
    return filtered;
  }

  async findByType(type: string, teamId?: number): Promise<Stage[]> {
    // TODO: Stage entity doesn't have type property
    // For now, return all stages filtered by teamId if provided
    let filtered = this.stages;
    if (teamId) {
      filtered = filtered.filter(stage => stage.teamId === teamId);
    }
    return filtered;
  }

  async getStageStatistics(stageId: number): Promise<any> {
    // TODO: Implement statistics calculation
    return {
      totalLeads: 0,
      averageTimeInStage: 0,
      conversionRate: 0
    };
  }

  async getStagePerformance(stageId: number, dateRange?: { start: Date; end: Date }): Promise<any> {
    // TODO: Implement performance metrics
    return {
      leadsEntered: 0,
      leadsExited: 0,
      averageTimeInStage: 0,
      conversionRate: 0
    };
  }

  async bulkUpdateSequence(updates: Array<{ id: number; sequence: number }>): Promise<boolean> {
    return this.reorderStages(updates);
  }

  async getNextStage(currentStageId: number): Promise<Stage | null> {
    const currentStage = this.stages.find(s => s.id === currentStageId);
    if (!currentStage) return null;

    const nextStage = this.stages.find(s =>
      s.sequence === currentStage.sequence + 1
    );
    return nextStage || null;
  }

  async getPreviousStage(currentStageId: number): Promise<Stage | null> {
    const currentStage = this.stages.find(s => s.id === currentStageId);
    if (!currentStage) return null;

    const prevStage = this.stages.find(s =>
      s.sequence === currentStage.sequence - 1
    );
    return prevStage || null;
  }

  async validateStageTransition(fromStageId: number, toStageId: number): Promise<boolean> {
    // TODO: Implement stage transition validation logic
    return true;
  }

  async applyTemplate(templateName: string, teamId?: number): Promise<Stage[]> {
    // TODO: Implement template application
    return [];
  }
}
