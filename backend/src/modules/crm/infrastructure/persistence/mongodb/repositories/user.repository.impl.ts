import { Injectable } from '@nestjs/common';
import { IUserRepository, CreateUserData, UpdateUserData, UserFilters, UserWorkloadStats } from '@/modules/crm/domain/repositories/user.repository';

// Simple User type for MongoDB implementation
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  teamId?: number;
  seniority?: string;
  territories?: string[];
  skills?: string[];
  isActive: boolean;
  isAvailable: boolean;
  maxLeads?: number;
  timezone?: string;
  workingHours?: any;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * MongoDB User Repository Implementation
 * Simple implementation for user repository
 */
@Injectable()
export class MongoUserRepository implements IUserRepository {
  // In-memory storage for now (would be replaced with actual MongoDB implementation)
  private users: User[] = [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'sales_rep',
      teamId: 1,
      seniority: 'senior',
      territories: ['North America'],
      skills: ['B2B Sales', 'Enterprise'],
      isActive: true,
      isAvailable: true,
      maxLeads: 20,
      timezone: 'America/New_York',
      workingHours: {
        monday: { start: '09:00', end: '17:00' },
        tuesday: { start: '09:00', end: '17:00' },
        wednesday: { start: '09:00', end: '17:00' },
        thursday: { start: '09:00', end: '17:00' },
        friday: { start: '09:00', end: '17:00' },
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      role: 'sales_rep',
      teamId: 1,
      seniority: 'junior',
      territories: ['Europe'],
      skills: ['B2B Sales', 'SMB'],
      isActive: true,
      isAvailable: true,
      maxLeads: 15,
      timezone: 'Europe/London',
      workingHours: {
        monday: { start: '08:00', end: '16:00' },
        tuesday: { start: '08:00', end: '16:00' },
        wednesday: { start: '08:00', end: '16:00' },
        thursday: { start: '08:00', end: '16:00' },
        friday: { start: '08:00', end: '16:00' },
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '3',
      name: 'Mike Johnson',
      email: '<EMAIL>',
      role: 'sales_manager',
      teamId: 1,
      seniority: 'senior',
      territories: ['North America', 'Europe'],
      skills: ['Team Management', 'Enterprise Sales'],
      isActive: true,
      isAvailable: true,
      maxLeads: 10,
      timezone: 'America/New_York',
      workingHours: {
        monday: { start: '08:00', end: '18:00' },
        tuesday: { start: '08:00', end: '18:00' },
        wednesday: { start: '08:00', end: '18:00' },
        thursday: { start: '08:00', end: '18:00' },
        friday: { start: '08:00', end: '18:00' },
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  async findById(id: string): Promise<User | null> {
    return this.users.find(user => user.id === id) || null;
  }

  async findByTeamId(teamId: number, filters?: UserFilters): Promise<User[]> {
    let users = this.users.filter(user => user.teamId === teamId);
    
    if (filters) {
      users = this.applyFilters(users, filters);
    }
    
    return users;
  }

  async findAll(filters?: UserFilters): Promise<User[]> {
    let users = [...this.users];
    
    if (filters) {
      users = this.applyFilters(users, filters);
    }
    
    return users;
  }

  async create(userData: CreateUserData): Promise<User> {
    const newUser: User = {
      id: (Math.max(...this.users.map(u => parseInt(u.id))) + 1).toString(),
      ...userData,
      isActive: true,
      isAvailable: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    this.users.push(newUser);
    return newUser;
  }

  async update(id: string, userData: UpdateUserData): Promise<User> {
    const index = this.users.findIndex(user => user.id === id);
    if (index === -1) {
      throw new Error(`User with ID ${id} not found`);
    }

    const updatedUser = {
      ...this.users[index],
      ...userData,
      updatedAt: new Date(),
    };

    this.users[index] = updatedUser;
    return updatedUser;
  }

  async delete(id: string): Promise<boolean> {
    const index = this.users.findIndex(user => user.id === id);
    if (index === -1) {
      return false;
    }

    this.users.splice(index, 1);
    return true;
  }

  async findByRole(role: string): Promise<User[]> {
    return this.users.filter(user => user.role === role);
  }

  async findAvailableUsers(teamId?: number): Promise<User[]> {
    let users = this.users.filter(user => user.isActive && user.isAvailable);
    
    if (teamId) {
      users = users.filter(user => user.teamId === teamId);
    }
    
    return users;
  }

  async updateAvailability(id: string, isAvailable: boolean): Promise<void> {
    const user = this.users.find(u => u.id === id);
    if (!user) {
      throw new Error(`User with ID ${id} not found`);
    }

    user.isAvailable = isAvailable;
    user.updatedAt = new Date();
  }

  async getUserWorkloadStats(id: string): Promise<UserWorkloadStats> {
    // Mock workload stats - in real implementation, this would query lead data
    return {
      totalLeads: 15,
      activeLeads: 12,
      convertedLeads: 3,
      totalValue: 150000,
      conversionRate: 20,
      averageLeadAge: 14,
      lastActivity: new Date(),
    };
  }

  async findByTerritory(territory: string): Promise<User[]> {
    return this.users.filter(user => 
      user.territories && user.territories.includes(territory)
    );
  }

  private applyFilters(users: User[], filters: UserFilters): User[] {
    let filteredUsers = users;

    if (filters.isActive !== undefined) {
      filteredUsers = filteredUsers.filter(user => user.isActive === filters.isActive);
    }

    if (filters.isAvailable !== undefined) {
      filteredUsers = filteredUsers.filter(user => user.isAvailable === filters.isAvailable);
    }

    if (filters.role) {
      filteredUsers = filteredUsers.filter(user => user.role === filters.role);
    }

    if (filters.seniority) {
      filteredUsers = filteredUsers.filter(user => user.seniority === filters.seniority);
    }

    if (filters.territory) {
      filteredUsers = filteredUsers.filter(user => 
        user.territories && user.territories.includes(filters.territory!)
      );
    }

    if (filters.skill) {
      filteredUsers = filteredUsers.filter(user => 
        user.skills && user.skills.includes(filters.skill!)
      );
    }

    return filteredUsers;
  }
}
