import { Injectable } from '@nestjs/common';
import { ITeamRepository, Team, CreateTeamData, UpdateTeamData } from '@/modules/crm/domain/repositories/team.repository';

/**
 * MongoDB Team Repository Implementation
 * Simple implementation for team repository
 */
@Injectable()
export class MongoTeamRepository implements ITeamRepository {
  // In-memory storage for now (would be replaced with actual MongoDB implementation)
  private teams: Team[] = [
    {
      id: 1,
      name: 'Sales Team',
      description: 'Main sales team',
      managerId: 1,
      memberIds: [1, 2, 3],
      isActive: true,
      territories: ['North America', 'Europe'],
      targets: {
        monthly: 100000,
        quarterly: 300000,
        annual: 1200000,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 2,
      name: 'Enterprise Team',
      description: 'Enterprise sales team',
      managerId: 2,
      memberIds: [4, 5],
      isActive: true,
      territories: ['Global'],
      targets: {
        monthly: 200000,
        quarterly: 600000,
        annual: 2400000,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  async findById(id: number): Promise<Team | null> {
    return this.teams.find(team => team.id === id) || null;
  }

  async findAll(): Promise<Team[]> {
    return [...this.teams];
  }

  async findByManagerId(managerId: number): Promise<Team[]> {
    return this.teams.filter(team => team.managerId === managerId);
  }

  async findByMemberId(memberId: number): Promise<Team[]> {
    return this.teams.filter(team => team.memberIds.includes(memberId));
  }

  async create(teamData: CreateTeamData): Promise<Team> {
    const newTeam: Team = {
      id: Math.max(...this.teams.map(t => t.id)) + 1,
      ...teamData,
      memberIds: teamData.memberIds || [],
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    this.teams.push(newTeam);
    return newTeam;
  }

  async update(id: number, teamData: UpdateTeamData): Promise<Team> {
    const index = this.teams.findIndex(team => team.id === id);
    if (index === -1) {
      throw new Error(`Team with ID ${id} not found`);
    }

    const updatedTeam = {
      ...this.teams[index],
      ...teamData,
      updatedAt: new Date(),
    };

    this.teams[index] = updatedTeam;
    return updatedTeam;
  }

  async delete(id: number): Promise<boolean> {
    const index = this.teams.findIndex(team => team.id === id);
    if (index === -1) {
      return false;
    }

    this.teams.splice(index, 1);
    return true;
  }

  async addMember(teamId: number, userId: number): Promise<boolean> {
    const team = this.teams.find(t => t.id === teamId);
    if (!team) {
      throw new Error(`Team with ID ${teamId} not found`);
    }

    if (!team.memberIds.includes(userId)) {
      team.memberIds.push(userId);
      (team as any).updatedAt = new Date();
      return true;
    }
    return false;
  }

  async removeMember(teamId: number, userId: number): Promise<boolean> {
    const team = this.teams.find(t => t.id === teamId);
    if (!team) {
      throw new Error(`Team with ID ${teamId} not found`);
    }

    const index = team.memberIds.indexOf(userId);
    if (index > -1) {
      team.memberIds.splice(index, 1);
      (team as any).updatedAt = new Date();
      return true;
    }
    return false;
  }

  async getTeamMembers(teamId: number): Promise<number[]> {
    const team = this.teams.find(t => t.id === teamId);
    return team ? [...team.memberIds] : [];
  }

  async updateTargets(teamId: number, targets: any): Promise<void> {
    const team = this.teams.find(t => t.id === teamId);
    if (!team) {
      throw new Error(`Team with ID ${teamId} not found`);
    }

    team.targets = { ...team.targets, ...targets };
    team.updatedAt = new Date();
  }

  async findByTerritory(territory: string): Promise<Team[]> {
    return this.teams.filter(team => 
      team.territories && team.territories.includes(territory)
    );
  }
}
