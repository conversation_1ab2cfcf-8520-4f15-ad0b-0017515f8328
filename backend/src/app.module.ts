import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { AppController } from './app.controller';
import { AppService } from './app.service';

// Import shared module instead of individual modules
import { SharedModule } from './shared/shared.module';
import { CommonModule } from './common/common.module';

// Import domain modules
// import { CrmModule } from './modules/crm/crm.module';
// import { SalesModule } from './modules/sales/sales.module';
// import { InventoryModule } from './modules/inventory/inventory.module';

// Global infrastructure
import { OdooModule } from './infrastructure/odoo.module';
import { GlobalExceptionFilter } from './infrastructure/filters/global-exception.filter';
import { ResponseInterceptor } from './common/interceptors/response.interceptor';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    SharedModule,      // Shared kernel (includes auth, database, core Odoo services)
    CommonModule,      // Cross-cutting concerns
    CrmModule,         // Domain modules
    // SalesModule,
    // InventoryModule,
    OdooModule,        // Global Odoo module (keeps existing controllers)
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
  ],
})
export class AppModule {}
