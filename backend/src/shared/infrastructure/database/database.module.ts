import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UserOdooMapping, UserOdooMappingSchema } from './schemas/user-odoo-mapping.schema';

console.log('🚀 [DatabaseModule] Loading DatabaseModule...');

@Module({
  imports: [
    // MongoDB connection with configuration - force connection name to prevent conflicts
    MongooseModule.forRootAsync({
      connectionName: 'zenoo-main', // Explicit connection name to prevent conflicts
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const uri = configService.get<string>('MONGODB_URI') ||
                   'mongodb://localhost:27018/universal-odoo-adapter?replicaSet=rs0';

        console.log('🔗 [SharedDatabaseModule] MONGODB_URI from env:', configService.get<string>('MONGODB_URI'));
        console.log('🔗 [SharedDatabaseModule] Final URI:', uri);
        console.log('🔗 [SharedDatabaseModule] NODE_ENV:', process.env.NODE_ENV);
        console.log('🔗 [SharedDatabaseModule] All MONGO env vars:', Object.keys(process.env).filter(k => k.includes('MONGO')).map(k => `${k}=${process.env[k]}`));

        return {
          uri,
          // Connection pool settings
          maxPoolSize: 10,
          serverSelectionTimeoutMS: 5000,
          socketTimeoutMS: 45000,
          // Retry settings
          retryWrites: true,
          retryReads: true,
          // Compression
          compressors: ['zlib'],
          // Monitoring
          monitorCommands: process.env.NODE_ENV === 'development',
        };
      },
      inject: [ConfigService],
    }),

    // Register schemas for the named connection
    MongooseModule.forFeature([
      { name: UserOdooMapping.name, schema: UserOdooMappingSchema },
    ], 'zenoo-main'),
  ],
  exports: [
    MongooseModule,
  ],
})
export class DatabaseModule {}
