import {
  Injectable,
  NestInterceptor,
  Exec<PERSON><PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Reflector } from '@nestjs/core';
import { CacheService } from '../cache.service';
import { CacheKeyService } from '../cache-key.service';

// Decorators for cache configuration
export const CACHE_KEY_METADATA = 'cache_key';
export const CACHE_TTL_METADATA = 'cache_ttl';
export const CACHE_ENABLED_METADATA = 'cache_enabled';

/**
 * Cache Key Decorator
 */
export const CacheKey = (key: string) => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    if (descriptor) {
      Reflect.defineMetadata(CACHE_KEY_METADATA, key, descriptor.value);
    } else {
      Reflect.defineMetadata(CACHE_KEY_METADATA, key, target);
    }
  };
};

/**
 * <PERSON>ache TTL Decorator
 */
export const CacheTTL = (ttl: number) => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    if (descriptor) {
      Reflect.defineMetadata(CACHE_TTL_METADATA, ttl, descriptor.value);
    } else {
      Reflect.defineMetadata(CACHE_TTL_METADATA, ttl, target);
    }
  };
};

/**
 * Cache Enabled Decorator
 */
export const CacheEnabled = (enabled: boolean = true) => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    if (descriptor) {
      Reflect.defineMetadata(CACHE_ENABLED_METADATA, enabled, descriptor.value);
    } else {
      Reflect.defineMetadata(CACHE_ENABLED_METADATA, enabled, target);
    }
  };
};

/**
 * Query Cache Interceptor
 * Automatically caches query results based on method parameters
 */
@Injectable()
export class QueryCacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(QueryCacheInterceptor.name);

  constructor(
    private readonly cacheService: CacheService,
    private readonly cacheKeyService: CacheKeyService,
    private readonly reflector: Reflector,
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const handler = context.getHandler();
    const controller = context.getClass();

    // Check if caching is enabled for this handler
    const cacheEnabled = this.reflector.getAllAndOverride<boolean>(
      CACHE_ENABLED_METADATA,
      [handler, controller],
    );

    if (cacheEnabled === false) {
      return next.handle();
    }

    // Get cache configuration
    const cacheKey = this.reflector.getAllAndOverride<string>(
      CACHE_KEY_METADATA,
      [handler, controller],
    );

    const cacheTTL = this.reflector.getAllAndOverride<number>(
      CACHE_TTL_METADATA,
      [handler, controller],
    );

    // Generate cache key
    const key = await this.generateCacheKey(context, cacheKey);
    
    if (!key) {
      return next.handle();
    }

    try {
      // Try to get from cache
      const cachedResult = await this.cacheService.get(key);
      
      if (cachedResult !== null) {
        this.logger.debug(`Cache HIT for key: ${key}`);
        return of(cachedResult);
      }

      this.logger.debug(`Cache MISS for key: ${key}`);

      // Execute handler and cache result
      return next.handle().pipe(
        tap(async (result) => {
          if (result !== undefined && result !== null) {
            await this.cacheService.set(key, result, cacheTTL);
            this.logger.debug(`Cached result for key: ${key}`);
          }
        }),
      );

    } catch (error) {
      this.logger.error(`Cache error for key ${key}:`, error);
      return next.handle();
    }
  }

  /**
   * Generate cache key based on context and configuration
   */
  private async generateCacheKey(
    context: ExecutionContext,
    customKey?: string,
  ): Promise<string | null> {
    try {
      const request = context.switchToHttp().getRequest();
      const handler = context.getHandler();
      const controller = context.getClass();

      if (customKey) {
        return customKey;
      }

      // Generate key based on controller, handler, and parameters
      const controllerName = controller.name;
      const handlerName = handler.name;
      
      // Extract parameters from request
      const params = {
        ...request.params,
        ...request.query,
        ...(request.body || {}),
      };

      // Remove sensitive data from cache key
      const sanitizedParams = this.sanitizeParams(params);

      return this.cacheKeyService.generateQueryKey(
        `${controllerName}.${handlerName}`,
        sanitizedParams,
      );

    } catch (error) {
      this.logger.error('Failed to generate cache key:', error);
      return null;
    }
  }

  /**
   * Remove sensitive data from parameters
   */
  private sanitizeParams(params: any): Record<string, any> {
    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'authorization'];
    const sanitized = { ...params };

    for (const key of sensitiveKeys) {
      if (key in sanitized) {
        delete sanitized[key];
      }
    }

    return sanitized;
  }
}

/**
 * Entity Cache Interceptor
 * Specialized for entity-based caching
 */
@Injectable()
export class EntityCacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(EntityCacheInterceptor.name);

  constructor(
    private readonly cacheService: CacheService,
    private readonly cacheKeyService: CacheKeyService,
    private readonly reflector: Reflector,
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const handler = context.getHandler();
    const request = context.switchToHttp().getRequest();

    // Check if this is an entity operation
    const entityType = this.reflector.get<string>('entity_type', handler);
    if (!entityType) {
      return next.handle();
    }

    const entityId = request.params.id;
    if (!entityId) {
      return next.handle();
    }

    const method = request.method.toLowerCase();

    try {
      if (method === 'get') {
        // Try to get from cache for GET requests
        const cachedEntity = await this.cacheService.cacheEntity(
          entityType,
          entityId,
          async () => {
            const result = await next.handle().toPromise();
            return result;
          },
        );

        return of(cachedEntity);
      } else if (['post', 'put', 'patch', 'delete'].includes(method)) {
        // Invalidate cache for modifying operations
        await this.cacheService.invalidateEntity(entityType, entityId);
        return next.handle();
      }

      return next.handle();

    } catch (error) {
      this.logger.error(`Entity cache error for ${entityType}:${entityId}:`, error);
      return next.handle();
    }
  }
}

/**
 * List Cache Interceptor
 * Specialized for list/collection caching
 */
@Injectable()
export class ListCacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ListCacheInterceptor.name);

  constructor(
    private readonly cacheService: CacheService,
    private readonly cacheKeyService: CacheKeyService,
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const handler = context.getHandler();
    const controller = context.getClass();

    // Only cache GET requests for lists
    if (request.method.toLowerCase() !== 'get') {
      return next.handle();
    }

    try {
      const operation = `${controller.name}.${handler.name}`;
      const params = {
        ...request.query,
        ...request.params,
      };

      const cachedResult = await this.cacheService.cacheList(
        operation,
        params,
        async () => {
          const result = await next.handle().toPromise();
          return result;
        },
      );

      return of(cachedResult);

    } catch (error) {
      this.logger.error('List cache error:', error);
      return next.handle();
    }
  }
}
