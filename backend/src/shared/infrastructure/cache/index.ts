// Cache Module
export { CacheModule } from './cache.module';

// Cache Services
export { CacheService } from './cache.service';
export { CacheKeyService } from './cache-key.service';

// Cache Interceptors
export {
  QueryCacheInterceptor,
  EntityCacheInterceptor,
  ListCacheInterceptor,
  CacheKey,
  CacheTTL,
  CacheEnabled,
  CACHE_KEY_METADATA,
  CACHE_TTL_METADATA,
  CACHE_ENABLED_METADATA,
} from './interceptors/query-cache.interceptor';

// Cache Types
export interface CacheConfig {
  ttl?: number;
  max?: number;
  stores?: any[];
}

export interface CacheStats {
  status: string;
  timestamp: Date;
  hits?: number;
  misses?: number;
  keys?: number;
  memory?: number;
}

export interface CacheKeyMetadata {
  app: string;
  version: string;
  type: string;
  operation?: string;
  hash?: string;
}

// Cache Constants
export const CACHE_CONSTANTS = {
  DEFAULT_TTL: 300000, // 5 minutes
  ANALYTICS_TTL: 600000, // 10 minutes
  ENTITY_TTL: 180000, // 3 minutes
  LIST_TTL: 120000, // 2 minutes
  SEARCH_TTL: 60000, // 1 minute
  SESSION_TTL: 1800000, // 30 minutes
  TEMP_TTL: 30000, // 30 seconds
} as const;

// Cache Strategies
export enum CacheStrategy {
  CACHE_ASIDE = 'cache_aside',
  WRITE_THROUGH = 'write_through',
  WRITE_BEHIND = 'write_behind',
  REFRESH_AHEAD = 'refresh_ahead',
}

// Cache Invalidation Patterns
export enum InvalidationPattern {
  TTL_BASED = 'ttl_based',
  EVENT_BASED = 'event_based',
  MANUAL = 'manual',
  PATTERN_BASED = 'pattern_based',
}

// Cache Levels
export enum CacheLevel {
  L1_MEMORY = 'l1_memory',
  L2_REDIS = 'l2_redis',
  L3_DATABASE = 'l3_database',
}

// Cache Decorators for easy use
export const CacheQuery = (ttl?: number) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const { CacheEnabled, CacheTTL } = require('./interceptors/query-cache.interceptor');
    CacheEnabled(true)(target, propertyKey, descriptor);
    if (ttl) {
      CacheTTL(ttl)(target, propertyKey, descriptor);
    }
  };
};

export const CacheEntity = (entityType: string, ttl?: number) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const { CacheEnabled, CacheTTL } = require('./interceptors/query-cache.interceptor');
    Reflect.defineMetadata('entity_type', entityType, descriptor.value);
    CacheEnabled(true)(target, propertyKey, descriptor);
    if (ttl) {
      CacheTTL(ttl)(target, propertyKey, descriptor);
    }
  };
};

export const CacheList = (ttl?: number) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const { CacheEnabled, CacheTTL } = require('./interceptors/query-cache.interceptor');
    CacheEnabled(true)(target, propertyKey, descriptor);
    if (ttl) {
      CacheTTL(ttl)(target, propertyKey, descriptor);
    }
  };
};

export const CacheAnalytics = (ttl: number = CACHE_CONSTANTS.ANALYTICS_TTL) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const { CacheEnabled, CacheTTL } = require('./interceptors/query-cache.interceptor');
    CacheEnabled(true)(target, propertyKey, descriptor);
    CacheTTL(ttl)(target, propertyKey, descriptor);
  };
};

export const NoCache = () => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const { CacheEnabled } = require('./interceptors/query-cache.interceptor');
    CacheEnabled(false)(target, propertyKey, descriptor);
  };
};
