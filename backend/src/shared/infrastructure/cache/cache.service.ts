import { Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { CacheKeyService } from './cache-key.service';

/**
 * Enhanced Cache Service
 * Provides high-level caching operations with key management and TTL strategies
 */
@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);

  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly cacheKeyService: CacheKeyService,
  ) {}

  /**
   * Get value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.cacheManager.get<T>(key);
      if (value !== undefined && value !== null) {
        this.logger.debug(`Cache HIT for key: ${key}`);
        return value;
      }
      this.logger.debug(`Cache MISS for key: ${key}`);
      return null;
    } catch (error) {
      this.logger.error(`Cache GET error for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Set value in cache with TTL
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      await this.cacheManager.set(key, value, ttl);
      this.logger.debug(`Cache SET for key: ${key}, TTL: ${ttl || 'default'}`);
    } catch (error) {
      this.logger.error(`Cache SET error for key ${key}:`, error);
    }
  }

  /**
   * Delete value from cache
   */
  async del(key: string): Promise<void> {
    try {
      await this.cacheManager.del(key);
      this.logger.debug(`Cache DEL for key: ${key}`);
    } catch (error) {
      this.logger.error(`Cache DEL error for key ${key}:`, error);
    }
  }

  /**
   * Clear all cache
   */
  async clear(): Promise<void> {
    try {
      await this.cacheManager.clear();
      this.logger.debug('Cache CLEAR all');
    } catch (error) {
      this.logger.error('Cache CLEAR error:', error);
    }
  }

  /**
   * Get or set pattern - if not in cache, execute function and cache result
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl?: number,
  ): Promise<T> {
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const value = await factory();
    await this.set(key, value, ttl);
    return value;
  }

  /**
   * Cache query results with automatic key generation
   */
  async cacheQuery<T>(
    operation: string,
    params: Record<string, any>,
    factory: () => Promise<T>,
    ttl?: number,
  ): Promise<T> {
    const key = this.cacheKeyService.generateQueryKey(operation, params);
    return this.getOrSet(key, factory, ttl);
  }

  /**
   * Cache entity by ID
   */
  async cacheEntity<T>(
    entityType: string,
    id: number | string,
    factory: () => Promise<T>,
    ttl?: number,
  ): Promise<T> {
    const key = this.cacheKeyService.generateEntityKey(entityType, id);
    return this.getOrSet(key, factory, ttl);
  }

  /**
   * Invalidate entity cache
   */
  async invalidateEntity(entityType: string, id: number | string): Promise<void> {
    const key = this.cacheKeyService.generateEntityKey(entityType, id);
    await this.del(key);
  }

  /**
   * Invalidate query cache by pattern
   */
  async invalidateQueryPattern(operation: string, pattern?: Record<string, any>): Promise<void> {
    // For simple implementation, we'll clear specific keys
    // In production, you might want to use Redis SCAN for pattern matching
    const key = this.cacheKeyService.generateQueryKey(operation, pattern || {});
    await this.del(key);
  }

  /**
   * Cache list results with pagination
   */
  async cacheList<T>(
    operation: string,
    params: Record<string, any>,
    factory: () => Promise<{ items: T[]; total: number }>,
    ttl?: number,
  ): Promise<{ items: T[]; total: number }> {
    const key = this.cacheKeyService.generateListKey(operation, params);
    return this.getOrSet(key, factory, ttl);
  }

  /**
   * Cache analytics/aggregation results
   */
  async cacheAnalytics<T>(
    operation: string,
    params: Record<string, any>,
    factory: () => Promise<T>,
    ttl: number = 600000, // 10 minutes default for analytics
  ): Promise<T> {
    const key = this.cacheKeyService.generateAnalyticsKey(operation, params);
    return this.getOrSet(key, factory, ttl);
  }

  /**
   * Warm up cache with data
   */
  async warmUp<T>(key: string, factory: () => Promise<T>, ttl?: number): Promise<void> {
    try {
      const value = await factory();
      await this.set(key, value, ttl);
      this.logger.log(`Cache warmed up for key: ${key}`);
    } catch (error) {
      this.logger.error(`Cache warm up failed for key ${key}:`, error);
    }
  }

  /**
   * Get cache statistics (if supported by the cache store)
   */
  async getStats(): Promise<any> {
    try {
      // This would depend on the cache store implementation
      // For now, return basic info
      return {
        status: 'active',
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('Failed to get cache stats:', error);
      return { status: 'error', error: error.message };
    }
  }

  /**
   * Batch get multiple keys
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    try {
      const promises = keys.map(key => this.get<T>(key));
      return await Promise.all(promises);
    } catch (error) {
      this.logger.error('Cache MGET error:', error);
      return keys.map(() => null);
    }
  }

  /**
   * Batch set multiple key-value pairs
   */
  async mset<T>(items: Array<{ key: string; value: T; ttl?: number }>): Promise<void> {
    try {
      const promises = items.map(item => this.set(item.key, item.value, item.ttl));
      await Promise.all(promises);
      this.logger.debug(`Cache MSET for ${items.length} items`);
    } catch (error) {
      this.logger.error('Cache MSET error:', error);
    }
  }

  /**
   * Check if key exists in cache
   */
  async exists(key: string): Promise<boolean> {
    try {
      const value = await this.cacheManager.get(key);
      return value !== undefined && value !== null;
    } catch (error) {
      this.logger.error(`Cache EXISTS error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Set with expiration time (Unix timestamp)
   */
  async setWithExpiry<T>(key: string, value: T, expiryTime: Date): Promise<void> {
    const now = new Date();
    const ttl = Math.max(0, expiryTime.getTime() - now.getTime());
    await this.set(key, value, ttl);
  }
}
