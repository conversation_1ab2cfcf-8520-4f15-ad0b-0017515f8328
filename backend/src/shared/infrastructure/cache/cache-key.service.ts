import { Injectable } from '@nestjs/common';
import { createHash } from 'crypto';

/**
 * Cache Key Service
 * Generates consistent and optimized cache keys for different operations
 */
@Injectable()
export class CacheKeyService {
  private readonly APP_PREFIX = 'zenoo';
  private readonly VERSION = 'v1';

  /**
   * Generate entity cache key
   */
  generateEntityKey(entityType: string, id: number | string): string {
    return `${this.APP_PREFIX}:${this.VERSION}:entity:${entityType}:${id}`;
  }

  /**
   * Generate query cache key
   */
  generateQueryKey(operation: string, params: Record<string, any>): string {
    const paramsHash = this.hashParams(params);
    return `${this.APP_PREFIX}:${this.VERSION}:query:${operation}:${paramsHash}`;
  }

  /**
   * Generate list cache key with pagination
   */
  generateListKey(operation: string, params: Record<string, any>): string {
    const paramsHash = this.hashParams(params);
    return `${this.APP_PREFIX}:${this.VERSION}:list:${operation}:${paramsHash}`;
  }

  /**
   * Generate analytics cache key
   */
  generateAnalyticsKey(operation: string, params: Record<string, any>): string {
    const paramsHash = this.hashParams(params);
    return `${this.APP_PREFIX}:${this.VERSION}:analytics:${operation}:${paramsHash}`;
  }

  /**
   * Generate user-specific cache key
   */
  generateUserKey(userId: number | string, operation: string, params?: Record<string, any>): string {
    const paramsHash = params ? this.hashParams(params) : '';
    const suffix = paramsHash ? `:${paramsHash}` : '';
    return `${this.APP_PREFIX}:${this.VERSION}:user:${userId}:${operation}${suffix}`;
  }

  /**
   * Generate team-specific cache key
   */
  generateTeamKey(teamId: number | string, operation: string, params?: Record<string, any>): string {
    const paramsHash = params ? this.hashParams(params) : '';
    const suffix = paramsHash ? `:${paramsHash}` : '';
    return `${this.APP_PREFIX}:${this.VERSION}:team:${teamId}:${operation}${suffix}`;
  }

  /**
   * Generate company-specific cache key
   */
  generateCompanyKey(companyId: number | string, operation: string, params?: Record<string, any>): string {
    const paramsHash = params ? this.hashParams(params) : '';
    const suffix = paramsHash ? `:${paramsHash}` : '';
    return `${this.APP_PREFIX}:${this.VERSION}:company:${companyId}:${operation}${suffix}`;
  }

  /**
   * Generate session cache key
   */
  generateSessionKey(sessionId: string, operation: string): string {
    return `${this.APP_PREFIX}:${this.VERSION}:session:${sessionId}:${operation}`;
  }

  /**
   * Generate temporary cache key with timestamp
   */
  generateTempKey(operation: string, identifier: string): string {
    const timestamp = Date.now();
    return `${this.APP_PREFIX}:${this.VERSION}:temp:${operation}:${identifier}:${timestamp}`;
  }

  /**
   * Generate search cache key
   */
  generateSearchKey(searchTerm: string, filters?: Record<string, any>): string {
    const searchHash = this.hashString(searchTerm);
    const filtersHash = filters ? this.hashParams(filters) : '';
    const suffix = filtersHash ? `:${filtersHash}` : '';
    return `${this.APP_PREFIX}:${this.VERSION}:search:${searchHash}${suffix}`;
  }

  /**
   * Generate aggregation cache key
   */
  generateAggregationKey(aggregationType: string, params: Record<string, any>): string {
    const paramsHash = this.hashParams(params);
    return `${this.APP_PREFIX}:${this.VERSION}:agg:${aggregationType}:${paramsHash}`;
  }

  /**
   * Generate report cache key
   */
  generateReportKey(reportType: string, params: Record<string, any>): string {
    const paramsHash = this.hashParams(params);
    return `${this.APP_PREFIX}:${this.VERSION}:report:${reportType}:${paramsHash}`;
  }

  /**
   * Generate export cache key
   */
  generateExportKey(exportType: string, params: Record<string, any>): string {
    const paramsHash = this.hashParams(params);
    return `${this.APP_PREFIX}:${this.VERSION}:export:${exportType}:${paramsHash}`;
  }

  /**
   * Generate pattern for cache invalidation
   */
  generatePattern(prefix: string, pattern: string = '*'): string {
    return `${this.APP_PREFIX}:${this.VERSION}:${prefix}:${pattern}`;
  }

  /**
   * Extract entity type and ID from entity key
   */
  parseEntityKey(key: string): { entityType: string; id: string } | null {
    const parts = key.split(':');
    if (parts.length >= 5 && parts[2] === 'entity') {
      return {
        entityType: parts[3],
        id: parts[4],
      };
    }
    return null;
  }

  /**
   * Check if key matches pattern
   */
  matchesPattern(key: string, pattern: string): boolean {
    // Simple pattern matching - in production you might want more sophisticated matching
    const regexPattern = pattern.replace(/\*/g, '.*');
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(key);
  }

  /**
   * Generate time-based cache key (for time-sensitive data)
   */
  generateTimeBasedKey(operation: string, params: Record<string, any>, timeWindow: number = 300000): string {
    // Round timestamp to time window (default 5 minutes)
    const roundedTime = Math.floor(Date.now() / timeWindow) * timeWindow;
    const paramsHash = this.hashParams({ ...params, _time: roundedTime });
    return `${this.APP_PREFIX}:${this.VERSION}:time:${operation}:${paramsHash}`;
  }

  /**
   * Generate cache key for API responses
   */
  generateApiResponseKey(method: string, path: string, params?: Record<string, any>): string {
    const paramsHash = params ? this.hashParams(params) : '';
    const pathHash = this.hashString(path);
    const suffix = paramsHash ? `:${paramsHash}` : '';
    return `${this.APP_PREFIX}:${this.VERSION}:api:${method}:${pathHash}${suffix}`;
  }

  /**
   * Hash parameters to create consistent key suffix
   */
  private hashParams(params: Record<string, any>): string {
    // Sort keys to ensure consistent hashing
    const sortedKeys = Object.keys(params).sort();
    const sortedParams = sortedKeys.reduce((acc, key) => {
      acc[key] = params[key];
      return acc;
    }, {} as Record<string, any>);

    const paramString = JSON.stringify(sortedParams);
    return this.hashString(paramString);
  }

  /**
   * Hash string using SHA-256
   */
  private hashString(input: string): string {
    return createHash('sha256').update(input).digest('hex').substring(0, 16);
  }

  /**
   * Validate cache key format
   */
  isValidKey(key: string): boolean {
    // Check if key follows our naming convention
    const parts = key.split(':');
    return parts.length >= 3 && parts[0] === this.APP_PREFIX && parts[1] === this.VERSION;
  }

  /**
   * Get key metadata
   */
  getKeyMetadata(key: string): {
    app: string;
    version: string;
    type: string;
    operation?: string;
    hash?: string;
  } | null {
    const parts = key.split(':');
    if (parts.length < 4) return null;

    return {
      app: parts[0],
      version: parts[1],
      type: parts[2],
      operation: parts[3],
      hash: parts[4],
    };
  }

  /**
   * Generate cache key with custom TTL suffix
   */
  generateKeyWithTTL(baseKey: string, ttlSeconds: number): string {
    return `${baseKey}:ttl:${ttlSeconds}`;
  }

  /**
   * Generate hierarchical cache key
   */
  generateHierarchicalKey(hierarchy: string[]): string {
    const path = hierarchy.join(':');
    return `${this.APP_PREFIX}:${this.VERSION}:${path}`;
  }
}
