import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CacheModule as NestCacheModule } from '@nestjs/cache-manager';
import { createKeyv } from '@keyv/redis';
import { Keyv } from 'keyv';
import { CacheableMemory } from 'cacheable';

import { CacheService } from './cache.service';
import { CacheKeyService } from './cache-key.service';

/**
 * Global Cache Module
 * Provides caching infrastructure with Redis and in-memory fallback
 */
@Global()
@Module({
  imports: [
    ConfigModule,
    NestCacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const redisUrl = configService.get<string>('REDIS_URL') || 'redis://localhost:6379';
        const isProduction = configService.get<string>('NODE_ENV') === 'production';
        
        // Create stores array with fallback strategy
        const stores: any[] = [];

        // In-memory cache as primary for development, fallback for production
        stores.push(
          new Keyv({
            store: new CacheableMemory({
              ttl: 60000, // 1 minute default TTL
              lruSize: 5000, // Max 5000 items in memory
            }),
          })
        );

        // Redis cache for production or when Redis is available
        if (isProduction || redisUrl !== 'redis://localhost:6379') {
          try {
            stores.push(createKeyv(redisUrl));
          } catch (error) {
            console.warn('Redis cache not available, using in-memory only:', error.message);
          }
        }
        
        return {
          stores,
          ttl: configService.get<number>('CACHE_TTL') || 300000, // 5 minutes default
          max: configService.get<number>('CACHE_MAX_ITEMS') || 10000,
        };
      },
      inject: [ConfigService],
    }),
  ],
  providers: [
    CacheService,
    CacheKeyService,
  ],
  exports: [
    NestCacheModule,
    CacheService,
    CacheKeyService,
  ],
})
export class CacheModule {}
