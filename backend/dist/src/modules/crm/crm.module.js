"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrmModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const common_module_1 = require("../../common/common.module");
const cqrs_module_1 = require("./application/cqrs/cqrs.module");
const lead_repository_impl_1 = require("./infrastructure/persistence/mongodb/repositories/lead.repository.impl");
const stage_repository_impl_1 = require("./infrastructure/persistence/mongodb/repositories/stage.repository.impl");
const team_repository_impl_1 = require("./infrastructure/persistence/mongodb/repositories/team.repository.impl");
const user_repository_impl_1 = require("./infrastructure/persistence/mongodb/repositories/user.repository.impl");
const injection_tokens_1 = require("./domain/repositories/injection-tokens");
const lead_schema_1 = require("./infrastructure/persistence/mongodb/schemas/lead.schema");
const leads_cqrs_controller_1 = require("./presentation/controllers/leads-cqrs.controller");
const UseCases = [];
const Queries = [];
const Controllers = [
    leads_cqrs_controller_1.LeadsCqrsController,
];
let CrmModule = class CrmModule {
};
exports.CrmModule = CrmModule;
exports.CrmModule = CrmModule = __decorate([
    (0, common_1.Module)({
        imports: [
            common_module_1.CommonModule,
            cqrs_module_1.CrmCqrsModule,
            mongoose_1.MongooseModule.forFeature([
                { name: 'Lead', schema: lead_schema_1.LeadSchema },
            ]),
        ],
        providers: [
            {
                provide: injection_tokens_1.LEAD_REPOSITORY_TOKEN,
                useClass: lead_repository_impl_1.MongoLeadRepository,
            },
            {
                provide: injection_tokens_1.STAGE_REPOSITORY_TOKEN,
                useClass: stage_repository_impl_1.MongoStageRepository,
            },
            {
                provide: injection_tokens_1.TEAM_REPOSITORY_TOKEN,
                useClass: team_repository_impl_1.MongoTeamRepository,
            },
            {
                provide: injection_tokens_1.USER_REPOSITORY_TOKEN,
                useClass: user_repository_impl_1.MongoUserRepository,
            },
            lead_repository_impl_1.MongoLeadRepository,
            stage_repository_impl_1.MongoStageRepository,
            team_repository_impl_1.MongoTeamRepository,
            user_repository_impl_1.MongoUserRepository,
            ...UseCases,
            ...Queries,
        ],
        controllers: Controllers,
        exports: [
            cqrs_module_1.CrmCqrsModule,
            ...UseCases,
            ...Queries,
        ],
    })
], CrmModule);
//# sourceMappingURL=crm.module.js.map