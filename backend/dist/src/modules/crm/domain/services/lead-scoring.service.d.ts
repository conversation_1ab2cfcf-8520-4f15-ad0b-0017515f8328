import { Lead } from '@/modules/crm/domain/entities/lead.entity';
import { LeadScore } from '@/modules/crm/domain/value-objects/lead-score.vo';
export declare class LeadScoringService {
    private readonly logger;
    private readonly scoringWeights;
    private readonly industryMultipliers;
    private readonly sourceQualityScores;
    calculateScore(lead: Lead, historicalData?: any): Promise<LeadScore>;
    private calculateCompletenessScore;
    private calculateRevenuePotentialScore;
    private calculateEngagementScore;
    private calculateCompanyFitScore;
    private calculateBehavioralScore;
    private calculateSourceQualityScore;
    private applyMLPredictions;
    private getIndustryMultiplier;
    private extractIndustryFromCompany;
    private getSourceEngagementBonus;
    private calculateGeographicFit;
    private getScoreGrade;
    private getRecommendedActions;
    private calculateConfidence;
    private calculateFactorVariance;
}
