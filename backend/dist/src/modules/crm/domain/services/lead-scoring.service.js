"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var LeadScoringService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadScoringService = void 0;
const common_1 = require("@nestjs/common");
const lead_score_vo_1 = require("../value-objects/lead-score.vo");
const scoring_factor_vo_1 = require("../value-objects/scoring-factor.vo");
let LeadScoringService = LeadScoringService_1 = class LeadScoringService {
    logger = new common_1.Logger(LeadScoringService_1.name);
    scoringWeights = {
        contactCompleteness: 0.20,
        revenuePotential: 0.25,
        engagementLevel: 0.20,
        companyFit: 0.15,
        behavioralSignals: 0.10,
        sourceQuality: 0.10,
    };
    industryMultipliers = new Map([
        ['technology', 1.2],
        ['finance', 1.15],
        ['healthcare', 1.1],
        ['manufacturing', 1.0],
        ['retail', 0.95],
        ['education', 0.9],
        ['government', 0.85],
    ]);
    sourceQualityScores = new Map([
        ['referral', 100],
        ['partner', 90],
        ['direct', 85],
        ['organic_search', 80],
        ['paid_search', 75],
        ['social_media', 70],
        ['email_campaign', 65],
        ['webinar', 85],
        ['trade_show', 80],
        ['cold_outreach', 50],
        ['unknown', 30],
    ]);
    async calculateScore(lead, historicalData) {
        this.logger.debug(`Calculating score for lead: ${lead.id}`);
        const factors = [];
        let totalScore = 0;
        try {
            const completenessScore = this.calculateCompletenessScore(lead);
            factors.push(new scoring_factor_vo_1.ScoringFactor('contact_completeness', completenessScore, this.scoringWeights.contactCompleteness));
            totalScore += completenessScore * this.scoringWeights.contactCompleteness;
            const revenueScore = this.calculateRevenuePotentialScore(lead);
            factors.push(new scoring_factor_vo_1.ScoringFactor('revenue_potential', revenueScore, this.scoringWeights.revenuePotential));
            totalScore += revenueScore * this.scoringWeights.revenuePotential;
            const engagementScore = await this.calculateEngagementScore(lead);
            factors.push(new scoring_factor_vo_1.ScoringFactor('engagement_level', engagementScore, this.scoringWeights.engagementLevel));
            totalScore += engagementScore * this.scoringWeights.engagementLevel;
            const companyFitScore = this.calculateCompanyFitScore(lead);
            factors.push(new scoring_factor_vo_1.ScoringFactor('company_fit', companyFitScore, this.scoringWeights.companyFit));
            totalScore += companyFitScore * this.scoringWeights.companyFit;
            const behavioralScore = await this.calculateBehavioralScore(lead);
            factors.push(new scoring_factor_vo_1.ScoringFactor('behavioral_signals', behavioralScore, this.scoringWeights.behavioralSignals));
            totalScore += behavioralScore * this.scoringWeights.behavioralSignals;
            const sourceScore = this.calculateSourceQualityScore(lead);
            factors.push(new scoring_factor_vo_1.ScoringFactor('source_quality', sourceScore, this.scoringWeights.sourceQuality));
            totalScore += sourceScore * this.scoringWeights.sourceQuality;
            const industryMultiplier = this.getIndustryMultiplier(lead);
            const adjustedScore = Math.min(100, totalScore * industryMultiplier);
            const mlScore = await this.applyMLPredictions(lead, totalScore, historicalData);
            const finalScore = Math.round(Math.max(0, Math.min(100, mlScore || adjustedScore)));
            const leadScore = new lead_score_vo_1.LeadScore(finalScore, factors, this.getScoreGrade(finalScore), this.getRecommendedActions(finalScore, factors), new Date(), {
                industryMultiplier,
                mlAdjustment: mlScore ? mlScore - adjustedScore : 0,
                confidence: this.calculateConfidence(factors),
            });
            this.logger.debug(`Calculated score for lead ${lead.id}: ${finalScore}`);
            return leadScore;
        }
        catch (error) {
            this.logger.error(`Failed to calculate score for lead ${lead.id}`, error);
            return new lead_score_vo_1.LeadScore(50, [new scoring_factor_vo_1.ScoringFactor('error', 50, 1.0)], 'C', ['Review lead data quality'], new Date(), { error: error.message });
        }
    }
    calculateCompletenessScore(lead) {
        let score = 0;
        const maxScore = 100;
        if (lead.name)
            score += 15;
        if (lead.contactInfo?.email)
            score += 20;
        if (lead.contactInfo?.phone)
            score += 15;
        if (lead.contactInfo?.company)
            score += 10;
        if (lead.contactInfo?.website)
            score += 8;
        if (lead.contactInfo?.address)
            score += 5;
        if (lead.contactInfo?.city)
            score += 3;
        if (lead.contactInfo?.country)
            score += 4;
        if (lead.description)
            score += 5;
        if (lead.tags && lead.tags.length > 0)
            score += 5;
        if (lead.campaignId)
            score += 3;
        if (lead.sourceId)
            score += 2;
        if (lead.mediumId)
            score += 2;
        if (lead.expectedRevenue)
            score += 3;
        return Math.min(maxScore, score);
    }
    calculateRevenuePotentialScore(lead) {
        if (!lead.expectedRevenue) {
            return 30;
        }
        if (lead.expectedRevenue >= 100000)
            return 100;
        if (lead.expectedRevenue >= 50000)
            return 85;
        if (lead.expectedRevenue >= 25000)
            return 70;
        if (lead.expectedRevenue >= 10000)
            return 55;
        if (lead.expectedRevenue >= 5000)
            return 40;
        return 25;
    }
    async calculateEngagementScore(lead) {
        let score = 0;
        const daysSinceCreated = Math.floor((Date.now() - lead.createdAt.getTime()) / (1000 * 60 * 60 * 24));
        if (daysSinceCreated <= 1)
            score += 30;
        else if (daysSinceCreated <= 7)
            score += 20;
        else if (daysSinceCreated <= 30)
            score += 10;
        if (lead.priority) {
            switch (lead.priority.value) {
                case 'high':
                    score += 25;
                    break;
                case 'medium':
                    score += 15;
                    break;
                case 'low':
                    score += 5;
                    break;
            }
        }
        const sourceEngagementBonus = this.getSourceEngagementBonus(lead.source);
        score += sourceEngagementBonus;
        if (lead.dateDeadline)
            score += 15;
        return Math.min(100, score);
    }
    calculateCompanyFitScore(lead) {
        let score = 50;
        if (lead.contactInfo?.company) {
            score += 20;
            const industry = this.extractIndustryFromCompany(lead.contactInfo.company);
            const industryMultiplier = this.industryMultipliers.get(industry) || 1.0;
            score = score * industryMultiplier;
        }
        if (lead.contactInfo?.country) {
            const geoScore = this.calculateGeographicFit(lead.contactInfo.country);
            score += geoScore;
        }
        if (lead.contactInfo?.website) {
            score += 15;
        }
        return Math.min(100, Math.max(0, score));
    }
    async calculateBehavioralScore(lead) {
        let score = 0;
        if (lead.type) {
            switch (lead.type.value) {
                case 'opportunity':
                    score += 40;
                    break;
                case 'lead':
                    score += 20;
                    break;
                default:
                    score += 10;
                    break;
            }
        }
        if (lead.probability) {
            score += Math.min(30, lead.probability * 0.3);
        }
        if (lead.assignedUserId)
            score += 15;
        if (lead.teamId)
            score += 10;
        if (lead.tags && lead.tags.length > 0) {
            score += Math.min(15, lead.tags.length * 3);
        }
        return Math.min(100, score);
    }
    calculateSourceQualityScore(lead) {
        const sourceScore = this.sourceQualityScores.get(lead.source?.toLowerCase()) || 50;
        return sourceScore;
    }
    async applyMLPredictions(lead, baseScore, historicalData) {
        if (!historicalData) {
            return null;
        }
        try {
            return null;
        }
        catch (error) {
            this.logger.warn('ML prediction failed, falling back to rule-based scoring', error);
            return null;
        }
    }
    getIndustryMultiplier(lead) {
        const industry = this.extractIndustryFromCompany(lead.contactInfo?.company);
        return this.industryMultipliers.get(industry) || 1.0;
    }
    extractIndustryFromCompany(company) {
        if (!company)
            return 'unknown';
        const companyLower = company.toLowerCase();
        if (companyLower.includes('tech') || companyLower.includes('software') || companyLower.includes('digital')) {
            return 'technology';
        }
        if (companyLower.includes('bank') || companyLower.includes('finance') || companyLower.includes('investment')) {
            return 'finance';
        }
        if (companyLower.includes('health') || companyLower.includes('medical') || companyLower.includes('pharma')) {
            return 'healthcare';
        }
        if (companyLower.includes('manufacturing') || companyLower.includes('factory')) {
            return 'manufacturing';
        }
        if (companyLower.includes('retail') || companyLower.includes('store')) {
            return 'retail';
        }
        if (companyLower.includes('school') || companyLower.includes('university') || companyLower.includes('education')) {
            return 'education';
        }
        if (companyLower.includes('government') || companyLower.includes('public')) {
            return 'government';
        }
        return 'unknown';
    }
    getSourceEngagementBonus(source) {
        if (!source)
            return 0;
        const engagementBonuses = new Map([
            ['referral', 25],
            ['webinar', 20],
            ['trade_show', 18],
            ['direct', 15],
            ['organic_search', 12],
            ['paid_search', 10],
            ['social_media', 8],
            ['email_campaign', 6],
            ['cold_outreach', 3],
        ]);
        return engagementBonuses.get(source.toLowerCase()) || 5;
    }
    calculateGeographicFit(country) {
        const preferredCountries = ['US', 'CA', 'UK', 'DE', 'FR', 'AU'];
        return preferredCountries.includes(country.toUpperCase()) ? 10 : 5;
    }
    getScoreGrade(score) {
        if (score >= 90)
            return 'A+';
        if (score >= 80)
            return 'A';
        if (score >= 70)
            return 'B';
        if (score >= 60)
            return 'C';
        if (score >= 50)
            return 'D';
        return 'F';
    }
    getRecommendedActions(score, factors) {
        const actions = [];
        if (score >= 80) {
            actions.push('Immediate follow-up required');
            actions.push('Assign to senior sales rep');
            actions.push('Schedule demo/meeting within 24 hours');
        }
        else if (score >= 60) {
            actions.push('Follow up within 2-3 days');
            actions.push('Send relevant case studies');
            actions.push('Add to nurturing campaign');
        }
        else if (score >= 40) {
            actions.push('Add to long-term nurturing');
            actions.push('Gather more information');
            actions.push('Monitor engagement');
        }
        else {
            actions.push('Low priority follow-up');
            actions.push('Verify contact information');
            actions.push('Consider lead qualification');
        }
        factors.forEach(factor => {
            if (factor.score < 50) {
                switch (factor.name) {
                    case 'contact_completeness':
                        actions.push('Complete missing contact information');
                        break;
                    case 'engagement_level':
                        actions.push('Increase engagement through targeted content');
                        break;
                    case 'company_fit':
                        actions.push('Research company and industry fit');
                        break;
                }
            }
        });
        return [...new Set(actions)];
    }
    calculateConfidence(factors) {
        const completenessScore = factors.find(f => f.name === 'contact_completeness')?.score || 0;
        const factorVariance = this.calculateFactorVariance(factors);
        const confidence = (completenessScore * 0.6) + ((100 - factorVariance) * 0.4);
        return Math.round(Math.max(0, Math.min(100, confidence)));
    }
    calculateFactorVariance(factors) {
        const scores = factors.map(f => f.score);
        const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
        return Math.sqrt(variance);
    }
};
exports.LeadScoringService = LeadScoringService;
exports.LeadScoringService = LeadScoringService = LeadScoringService_1 = __decorate([
    (0, common_1.Injectable)()
], LeadScoringService);
//# sourceMappingURL=lead-scoring.service.js.map