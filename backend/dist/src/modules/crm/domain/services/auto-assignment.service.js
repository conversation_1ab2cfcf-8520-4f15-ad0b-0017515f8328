"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AutoAssignmentService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoAssignmentService = void 0;
const common_1 = require("@nestjs/common");
const assignment_rule_vo_1 = require("../value-objects/assignment-rule.vo");
const assignment_result_vo_1 = require("../value-objects/assignment-result.vo");
const workload_balance_vo_1 = require("../value-objects/workload-balance.vo");
const injection_tokens_1 = require("../repositories/injection-tokens");
let AutoAssignmentService = AutoAssignmentService_1 = class AutoAssignmentService {
    leadRepository;
    teamRepository;
    userRepository;
    logger = new common_1.Logger(AutoAssignmentService_1.name);
    assignmentRules = new Map();
    workloadCache = new Map();
    constructor(leadRepository, teamRepository, userRepository) {
        this.leadRepository = leadRepository;
        this.teamRepository = teamRepository;
        this.userRepository = userRepository;
        this.initializeDefaultRules();
    }
    async autoAssignLead(lead, teamId) {
        this.logger.debug(`Auto-assigning lead: ${lead.id} to team: ${teamId}`);
        try {
            const rules = await this.getApplicableRules(lead, teamId);
            const availableUsers = await this.getAvailableUsers(teamId);
            if (availableUsers.length === 0) {
                return new assignment_result_vo_1.AssignmentResult(null, null, false, 'No available users found', [], new Date());
            }
            const scoredUsers = await this.scoreUsers(lead, availableUsers, rules);
            const balancedUsers = await this.applyWorkloadBalancing(scoredUsers, teamId);
            const selectedUser = this.selectBestUser(balancedUsers);
            if (!selectedUser) {
                return new assignment_result_vo_1.AssignmentResult(null, null, false, 'No suitable user found after applying rules', [], new Date());
            }
            await this.updateWorkloadTracking(selectedUser.userId, lead);
            this.logger.debug(`Lead ${lead.id} auto-assigned to user: ${selectedUser.userId}`);
            return new assignment_result_vo_1.AssignmentResult(selectedUser.userId, teamId, true, 'Successfully auto-assigned', this.generateAssignmentReasons(selectedUser, rules), new Date(), {
                score: selectedUser.score,
                workloadBefore: selectedUser.workloadBefore,
                workloadAfter: selectedUser.workloadAfter,
                rulesApplied: rules.map(r => r.name),
            });
        }
        catch (error) {
            this.logger.error(`Failed to auto-assign lead: ${lead.id}`, error);
            return new assignment_result_vo_1.AssignmentResult(null, null, false, `Assignment failed: ${error.message}`, [], new Date());
        }
    }
    async bulkAutoAssignLeads(leads, teamId) {
        this.logger.debug(`Bulk auto-assigning ${leads.length} leads to team: ${teamId}`);
        const results = [];
        const batchSize = 10;
        for (let i = 0; i < leads.length; i += batchSize) {
            const batch = leads.slice(i, i + batchSize);
            const batchPromises = batch.map(lead => this.autoAssignLead(lead, teamId));
            const batchResults = await Promise.allSettled(batchPromises);
            batchResults.forEach((result, index) => {
                if (result.status === 'fulfilled') {
                    results.push(result.value);
                }
                else {
                    this.logger.error(`Bulk assignment failed for lead ${batch[index].id}`, result.reason);
                    results.push(new assignment_result_vo_1.AssignmentResult(null, null, false, `Bulk assignment failed: ${result.reason}`, [], new Date()));
                }
            });
            if (i + batchSize < leads.length) {
                await this.delay(100);
            }
        }
        this.logger.debug(`Bulk auto-assignment completed: ${results.filter(r => r.success).length}/${leads.length} successful`);
        return results;
    }
    async rebalanceWorkload(teamId) {
        this.logger.debug(`Rebalancing workload for team: ${teamId}`);
        try {
            const workloadDistribution = await this.getWorkloadDistribution(teamId);
            const imbalancedLeads = await this.identifyImbalancedLeads(workloadDistribution);
            if (imbalancedLeads.length === 0) {
                return {
                    success: true,
                    message: 'Workload is already balanced',
                    reassignments: [],
                    workloadBefore: workloadDistribution,
                    workloadAfter: workloadDistribution,
                };
            }
            const reassignments = [];
            for (const lead of imbalancedLeads) {
                const assignmentResult = await this.autoAssignLead(lead, teamId);
                if (assignmentResult.success && assignmentResult.assignedUserId !== lead.assignedUserId) {
                    reassignments.push({
                        leadId: lead.id,
                        fromUserId: lead.assignedUserId,
                        toUserId: assignmentResult.assignedUserId,
                        reason: 'Workload rebalancing',
                    });
                }
            }
            const newWorkloadDistribution = await this.getWorkloadDistribution(teamId);
            this.logger.debug(`Workload rebalancing completed: ${reassignments.length} reassignments`);
            return {
                success: true,
                message: `Successfully rebalanced workload with ${reassignments.length} reassignments`,
                reassignments,
                workloadBefore: workloadDistribution,
                workloadAfter: newWorkloadDistribution,
            };
        }
        catch (error) {
            this.logger.error(`Failed to rebalance workload for team: ${teamId}`, error);
            return {
                success: false,
                message: `Rebalancing failed: ${error.message}`,
                reassignments: [],
                workloadBefore: {},
                workloadAfter: {},
            };
        }
    }
    addAssignmentRule(teamId, rule) {
        if (!this.assignmentRules.has(teamId)) {
            this.assignmentRules.set(teamId, []);
        }
        const rules = this.assignmentRules.get(teamId);
        rules.push(rule);
        rules.sort((a, b) => b.priority - a.priority);
        this.logger.debug(`Added assignment rule: ${rule.name} for team: ${teamId}`);
    }
    removeAssignmentRule(teamId, ruleId) {
        const rules = this.assignmentRules.get(teamId);
        if (!rules)
            return false;
        const index = rules.findIndex(rule => rule.id === ruleId);
        if (index === -1)
            return false;
        rules.splice(index, 1);
        this.logger.debug(`Removed assignment rule: ${ruleId} from team: ${teamId}`);
        return true;
    }
    async getAssignmentStats(teamId, dateFrom, dateTo) {
        return {
            totalAssignments: 0,
            autoAssignments: 0,
            manualAssignments: 0,
            autoAssignmentRate: 0,
            averageAssignmentTime: 0,
            workloadBalance: 0,
            userStats: {},
        };
    }
    async getApplicableRules(lead, teamId) {
        const teamKey = teamId?.toString() || 'default';
        const allRules = this.assignmentRules.get(teamKey) || [];
        return allRules.filter(rule => rule.appliesTo(lead));
    }
    async getAvailableUsers(teamId) {
        if (teamId) {
            return this.userRepository.findByTeamId(teamId, { isActive: true, isAvailable: true });
        }
        else {
            return this.userRepository.findAll({ isActive: true, isAvailable: true });
        }
    }
    async scoreUsers(lead, users, rules) {
        const scoredUsers = [];
        for (const user of users) {
            let score = 50;
            const appliedRules = [];
            for (const rule of rules) {
                if (rule.appliesTo(lead) && rule.appliesToUser(user)) {
                    score += rule.getScoreAdjustment(lead, user);
                    appliedRules.push(rule.name);
                }
            }
            const workload = await this.getUserWorkload(user.id);
            scoredUsers.push({
                userId: user.id,
                user,
                score: Math.max(0, Math.min(100, score)),
                workloadBefore: workload,
                workloadAfter: workload,
                appliedRules,
            });
        }
        return scoredUsers.sort((a, b) => b.score - a.score);
    }
    async applyWorkloadBalancing(scoredUsers, teamId) {
        const teamWorkload = await this.getTeamWorkloadStats(teamId);
        return scoredUsers.map(user => {
            const workloadRatio = user.workloadBefore.totalLeads / (teamWorkload.averageLeads || 1);
            let workloadAdjustment = 0;
            if (workloadRatio > 1.5) {
                workloadAdjustment = -20;
            }
            else if (workloadRatio > 1.2) {
                workloadAdjustment = -10;
            }
            else if (workloadRatio < 0.8) {
                workloadAdjustment = 10;
            }
            return {
                ...user,
                score: Math.max(0, Math.min(100, user.score + workloadAdjustment)),
                workloadAfter: {
                    ...user.workloadBefore,
                    totalLeads: user.workloadBefore.totalLeads + 1,
                },
            };
        }).sort((a, b) => b.score - a.score);
    }
    selectBestUser(scoredUsers) {
        if (scoredUsers.length === 0)
            return null;
        const acceptableUsers = scoredUsers.filter(user => user.score >= 30);
        if (acceptableUsers.length === 0)
            return null;
        return acceptableUsers[0];
    }
    async updateWorkloadTracking(userId, lead) {
        const currentWorkload = await this.getUserWorkload(userId);
        const updatedWorkload = new workload_balance_vo_1.WorkloadBalance(userId, currentWorkload.totalLeads + 1, currentWorkload.activeLeads + 1, currentWorkload.totalValue + (lead.expectedRevenue || 0), currentWorkload.averageLeadAge, new Date());
        this.workloadCache.set(userId, updatedWorkload);
    }
    generateAssignmentReasons(user, rules) {
        const reasons = [];
        reasons.push(`User scored ${user.score}/100 points`);
        if (user.appliedRules.length > 0) {
            reasons.push(`Applied rules: ${user.appliedRules.join(', ')}`);
        }
        if (user.workloadBefore.totalLeads < 10) {
            reasons.push('User has low workload');
        }
        return reasons;
    }
    async getUserWorkload(userId) {
        let workload = this.workloadCache.get(userId);
        if (!workload) {
            const userLeads = await this.leadRepository.findByAssignedUserId(userId);
            const totalLeads = userLeads.length;
            const activeLeads = userLeads.filter(lead => !lead.isConverted() && !lead.isLost()).length;
            const totalValue = userLeads.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
            const averageAge = this.calculateAverageLeadAge(userLeads);
            workload = new workload_balance_vo_1.WorkloadBalance(userId, totalLeads, activeLeads, totalValue, averageAge, new Date());
            this.workloadCache.set(userId, workload);
        }
        return workload;
    }
    async getWorkloadDistribution(teamId) {
        const teamUsers = await this.userRepository.findByTeamId(teamId);
        const distribution = {};
        for (const user of teamUsers) {
            distribution[user.id] = await this.getUserWorkload(user.id);
        }
        return distribution;
    }
    async identifyImbalancedLeads(workloadDistribution) {
        const workloads = Object.values(workloadDistribution);
        const averageWorkload = workloads.reduce((sum, w) => sum + w.totalLeads, 0) / workloads.length;
        const overloadedUsers = Object.entries(workloadDistribution)
            .filter(([_, workload]) => workload.totalLeads > averageWorkload * 1.5)
            .map(([userId, _]) => userId);
        const imbalancedLeads = [];
        for (const userId of overloadedUsers) {
            const userLeads = await this.leadRepository.findByAssignedUserId(userId);
            const leadsToReassign = userLeads
                .filter(lead => !lead.isConverted() && !lead.isLost())
                .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
                .slice(0, Math.floor((userLeads.length - averageWorkload) / 2));
            imbalancedLeads.push(...leadsToReassign);
        }
        return imbalancedLeads;
    }
    async getTeamWorkloadStats(teamId) {
        return {
            averageLeads: 10,
            totalLeads: 100,
            activeUsers: 10,
        };
    }
    calculateAverageLeadAge(leads) {
        if (leads.length === 0)
            return 0;
        const totalAge = leads.reduce((sum, lead) => {
            const ageInDays = Math.floor((Date.now() - lead.createdAt.getTime()) / (1000 * 60 * 60 * 24));
            return sum + ageInDays;
        }, 0);
        return totalAge / leads.length;
    }
    initializeDefaultRules() {
        const defaultRules = [
            new assignment_rule_vo_1.AssignmentRule('high-value-leads', 'High Value Leads', 'Assign high-value leads to senior reps', (lead) => (lead.expectedRevenue || 0) > 50000, (user) => user.seniority === 'senior', 20, 1, true),
            new assignment_rule_vo_1.AssignmentRule('geographic-routing', 'Geographic Routing', 'Assign leads based on geographic location', (lead) => !!lead.contactInfo?.country, (user) => !!user.territories, 15, 2, true),
        ];
        defaultRules.forEach(rule => this.addAssignmentRule('default', rule));
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
};
exports.AutoAssignmentService = AutoAssignmentService;
exports.AutoAssignmentService = AutoAssignmentService = AutoAssignmentService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(injection_tokens_1.LEAD_REPOSITORY_TOKEN)),
    __param(1, (0, common_1.Inject)(injection_tokens_1.TEAM_REPOSITORY_TOKEN)),
    __param(2, (0, common_1.Inject)(injection_tokens_1.USER_REPOSITORY_TOKEN)),
    __metadata("design:paramtypes", [Object, Object, Object])
], AutoAssignmentService);
//# sourceMappingURL=auto-assignment.service.js.map