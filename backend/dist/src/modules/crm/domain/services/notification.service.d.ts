import { NotificationTemplate } from '@/modules/crm/domain/value-objects/notification-template.vo';
import { NotificationPreference } from '@/modules/crm/domain/value-objects/notification-preference.vo';
import { NotificationDelivery } from '@/modules/crm/domain/value-objects/notification-delivery.vo';
import { NotificationChannel } from '@/modules/crm/domain/enums/notification-channel.enum';
import { NotificationPriority } from '@/modules/crm/domain/enums/notification-priority.enum';
export declare class NotificationService {
    private readonly logger;
    private readonly templates;
    private readonly userPreferences;
    constructor();
    sendNotification(request: NotificationRequest): Promise<NotificationDelivery[]>;
    sendBulkNotifications(requests: NotificationRequest[]): Promise<NotificationDelivery[]>;
    sendLeadAssignmentNotification(data: {
        leadId: number;
        leadName: string;
        assigneeId: string;
        assigneeName: string;
        assigneeEmail: string;
        assignerName: string;
        priority: string;
        dueDate?: Date;
    }): Promise<NotificationDelivery[]>;
    sendOpportunityUpdateNotification(data: {
        opportunityId: number;
        opportunityName: string;
        recipientId: string;
        recipientName: string;
        recipientEmail: string;
        updateType: 'stage_change' | 'value_change' | 'won' | 'lost';
        newStage?: string;
        newValue?: number;
        probability?: number;
    }): Promise<NotificationDelivery[]>;
    sendPipelineAlertNotification(data: {
        alertType: 'bottleneck' | 'forecast_risk' | 'target_miss' | 'conversion_drop';
        severity: 'low' | 'medium' | 'high';
        message: string;
        details: Record<string, any>;
        recipients: Array<{
            userId: string;
            email: string;
            name: string;
            role: string;
        }>;
    }): Promise<NotificationDelivery[]>;
    sendScheduledReportNotification(data: {
        reportType: string;
        reportName: string;
        recipients: Array<{
            userId: string;
            email: string;
            name: string;
        }>;
        reportUrl: string;
        generatedAt: Date;
        summary: Record<string, any>;
    }): Promise<NotificationDelivery[]>;
    registerTemplate(template: NotificationTemplate): void;
    updateUserPreferences(userId: string, preferences: NotificationPreference[]): Promise<void>;
    getUserPreferences(userId: string): Promise<NotificationPreference[]>;
    getDeliveryStatus(deliveryId: string): Promise<NotificationDelivery | null>;
    getNotificationStats(filters: {
        userId?: string;
        dateFrom?: Date;
        dateTo?: Date;
        type?: string;
        channel?: NotificationChannel;
    }): Promise<NotificationStats>;
    private getTemplate;
    private getEnabledChannels;
    private sendToChannel;
    private sendEmail;
    private sendSMS;
    private sendPushNotification;
    private sendInAppNotification;
    private sendSlackNotification;
    private generateDeliveryId;
    private createDefaultPreferences;
    private initializeDefaultTemplates;
    private delay;
}
export interface NotificationRequest {
    type: string;
    recipients: NotificationRecipient[];
    data: Record<string, any>;
    priority: NotificationPriority;
    scheduledAt?: Date;
}
export interface NotificationRecipient {
    userId: string;
    email?: string;
    phone?: string;
    name?: string;
    slackUserId?: string;
    pushTokens?: string[];
}
export interface NotificationStats {
    totalSent: number;
    totalDelivered: number;
    totalFailed: number;
    deliveryRate: number;
    channelBreakdown: Record<string, number>;
    typeBreakdown: Record<string, number>;
}
