"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var NotificationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationService = void 0;
const common_1 = require("@nestjs/common");
const notification_template_vo_1 = require("../value-objects/notification-template.vo");
const notification_preference_vo_1 = require("../value-objects/notification-preference.vo");
const notification_delivery_vo_1 = require("../value-objects/notification-delivery.vo");
const notification_channel_enum_1 = require("../enums/notification-channel.enum");
const notification_priority_enum_1 = require("../enums/notification-priority.enum");
let NotificationService = NotificationService_1 = class NotificationService {
    logger = new common_1.Logger(NotificationService_1.name);
    templates = new Map();
    userPreferences = new Map();
    constructor() {
        this.initializeDefaultTemplates();
    }
    async sendNotification(request) {
        this.logger.debug(`Sending notification: ${request.type} to ${request.recipients.length} recipients`);
        try {
            const template = this.getTemplate(request.type);
            const deliveries = [];
            for (const recipient of request.recipients) {
                const userPreferences = await this.getUserPreferences(recipient.userId);
                const enabledChannels = this.getEnabledChannels(userPreferences, request.type, request.priority);
                for (const channel of enabledChannels) {
                    const delivery = await this.sendToChannel(channel, recipient, template, request.data, request.priority);
                    deliveries.push(delivery);
                }
            }
            this.logger.debug(`Notification sent successfully: ${deliveries.length} deliveries`);
            return deliveries;
        }
        catch (error) {
            this.logger.error(`Failed to send notification: ${request.type}`, error);
            throw error;
        }
    }
    async sendBulkNotifications(requests) {
        this.logger.debug(`Sending bulk notifications: ${requests.length} requests`);
        const allDeliveries = [];
        const batchSize = 50;
        for (let i = 0; i < requests.length; i += batchSize) {
            const batch = requests.slice(i, i + batchSize);
            const batchPromises = batch.map(request => this.sendNotification(request));
            const batchResults = await Promise.allSettled(batchPromises);
            batchResults.forEach((result, index) => {
                if (result.status === 'fulfilled') {
                    allDeliveries.push(...result.value);
                }
                else {
                    this.logger.error(`Bulk notification failed for request ${i + index}`, result.reason);
                }
            });
            if (i + batchSize < requests.length) {
                await this.delay(100);
            }
        }
        this.logger.debug(`Bulk notifications completed: ${allDeliveries.length} total deliveries`);
        return allDeliveries;
    }
    async sendLeadAssignmentNotification(data) {
        return this.sendNotification({
            type: 'lead_assigned',
            recipients: [{
                    userId: data.assigneeId,
                    email: data.assigneeEmail,
                    name: data.assigneeName,
                }],
            data: {
                leadId: data.leadId,
                leadName: data.leadName,
                assignerName: data.assignerName,
                priority: data.priority,
                dueDate: data.dueDate?.toISOString(),
                dashboardUrl: `${process.env.FRONTEND_URL}/leads/${data.leadId}`,
            },
            priority: data.priority === 'high' ? notification_priority_enum_1.NotificationPriority.HIGH : notification_priority_enum_1.NotificationPriority.NORMAL,
        });
    }
    async sendOpportunityUpdateNotification(data) {
        const priority = data.updateType === 'won' || data.updateType === 'lost'
            ? notification_priority_enum_1.NotificationPriority.CRITICAL
            : notification_priority_enum_1.NotificationPriority.NORMAL;
        return this.sendNotification({
            type: 'opportunity_updated',
            recipients: [{
                    userId: data.recipientId,
                    email: data.recipientEmail,
                    name: data.recipientName,
                }],
            data: {
                opportunityId: data.opportunityId,
                opportunityName: data.opportunityName,
                updateType: data.updateType,
                newStage: data.newStage,
                newValue: data.newValue,
                probability: data.probability,
                dashboardUrl: `${process.env.FRONTEND_URL}/opportunities/${data.opportunityId}`,
            },
            priority,
        });
    }
    async sendPipelineAlertNotification(data) {
        const priority = data.severity === 'high'
            ? notification_priority_enum_1.NotificationPriority.CRITICAL
            : data.severity === 'medium'
                ? notification_priority_enum_1.NotificationPriority.HIGH
                : notification_priority_enum_1.NotificationPriority.NORMAL;
        return this.sendNotification({
            type: 'pipeline_alert',
            recipients: data.recipients,
            data: {
                alertType: data.alertType,
                severity: data.severity,
                message: data.message,
                details: data.details,
                dashboardUrl: `${process.env.FRONTEND_URL}/analytics/pipeline`,
            },
            priority,
        });
    }
    async sendScheduledReportNotification(data) {
        return this.sendNotification({
            type: 'scheduled_report',
            recipients: data.recipients,
            data: {
                reportType: data.reportType,
                reportName: data.reportName,
                reportUrl: data.reportUrl,
                generatedAt: data.generatedAt.toISOString(),
                summary: data.summary,
            },
            priority: notification_priority_enum_1.NotificationPriority.LOW,
        });
    }
    registerTemplate(template) {
        this.templates.set(template.type, template);
        this.logger.debug(`Registered notification template: ${template.type}`);
    }
    async updateUserPreferences(userId, preferences) {
        this.userPreferences.set(userId, preferences);
        this.logger.debug(`Updated notification preferences for user: ${userId}`);
    }
    async getUserPreferences(userId) {
        let preferences = this.userPreferences.get(userId);
        if (!preferences) {
            preferences = this.createDefaultPreferences();
            this.userPreferences.set(userId, preferences);
        }
        return preferences;
    }
    async getDeliveryStatus(deliveryId) {
        return null;
    }
    async getNotificationStats(filters) {
        return {
            totalSent: 0,
            totalDelivered: 0,
            totalFailed: 0,
            deliveryRate: 0,
            channelBreakdown: {},
            typeBreakdown: {},
        };
    }
    getTemplate(type) {
        const template = this.templates.get(type);
        if (!template) {
            throw new Error(`Notification template not found: ${type}`);
        }
        return template;
    }
    getEnabledChannels(preferences, notificationType, priority) {
        const enabledChannels = [];
        preferences.forEach(pref => {
            if (pref.notificationType === notificationType || pref.notificationType === 'all') {
                if (pref.enabled) {
                    enabledChannels.push(...pref.channels);
                }
            }
        });
        if (priority === notification_priority_enum_1.NotificationPriority.CRITICAL) {
            if (!enabledChannels.includes(notification_channel_enum_1.NotificationChannel.EMAIL)) {
                const emailDisabled = preferences.some(pref => pref.notificationType === notificationType &&
                    !pref.channels.includes(notification_channel_enum_1.NotificationChannel.EMAIL));
                if (!emailDisabled) {
                    enabledChannels.push(notification_channel_enum_1.NotificationChannel.EMAIL);
                }
            }
        }
        return [...new Set(enabledChannels)];
    }
    async sendToChannel(channel, recipient, template, data, priority) {
        const deliveryId = this.generateDeliveryId();
        try {
            const content = template.render(data);
            let success = false;
            let error;
            switch (channel) {
                case notification_channel_enum_1.NotificationChannel.EMAIL:
                    success = await this.sendEmail(recipient, content, priority);
                    break;
                case notification_channel_enum_1.NotificationChannel.SMS:
                    success = await this.sendSMS(recipient, content, priority);
                    break;
                case notification_channel_enum_1.NotificationChannel.PUSH:
                    success = await this.sendPushNotification(recipient, content, priority);
                    break;
                case notification_channel_enum_1.NotificationChannel.IN_APP:
                    success = await this.sendInAppNotification(recipient, content, priority);
                    break;
                case notification_channel_enum_1.NotificationChannel.SLACK:
                    success = await this.sendSlackNotification(recipient, content, priority);
                    break;
                default:
                    throw new Error(`Unsupported notification channel: ${channel}`);
            }
            return new notification_delivery_vo_1.NotificationDelivery(deliveryId, recipient.userId, template.type, channel, success ? 'delivered' : 'failed', new Date(), success ? undefined : error, {
                priority,
                retryCount: 0,
            });
        }
        catch (err) {
            this.logger.error(`Failed to send notification via ${channel}`, err);
            return new notification_delivery_vo_1.NotificationDelivery(deliveryId, recipient.userId, template.type, channel, 'failed', new Date(), err instanceof Error ? err.message : 'Unknown error', {
                priority,
                retryCount: 0,
            });
        }
    }
    async sendEmail(recipient, content, priority) {
        this.logger.debug(`Sending email to: ${recipient.email}`);
        return true;
    }
    async sendSMS(recipient, content, priority) {
        this.logger.debug(`Sending SMS to: ${recipient.phone}`);
        return true;
    }
    async sendPushNotification(recipient, content, priority) {
        this.logger.debug(`Sending push notification to: ${recipient.userId}`);
        return true;
    }
    async sendInAppNotification(recipient, content, priority) {
        this.logger.debug(`Sending in-app notification to: ${recipient.userId}`);
        return true;
    }
    async sendSlackNotification(recipient, content, priority) {
        this.logger.debug(`Sending Slack notification to: ${recipient.slackUserId}`);
        return true;
    }
    generateDeliveryId() {
        return `delivery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    createDefaultPreferences() {
        return [
            new notification_preference_vo_1.NotificationPreference('all', true, [notification_channel_enum_1.NotificationChannel.EMAIL, notification_channel_enum_1.NotificationChannel.IN_APP], { quietHours: { start: '22:00', end: '08:00' } }),
        ];
    }
    initializeDefaultTemplates() {
        this.registerTemplate(new notification_template_vo_1.NotificationTemplate('lead_assigned', 'Lead Assigned', 'You have been assigned a new lead: {{leadName}}', 'A new lead "{{leadName}}" has been assigned to you by {{assignerName}}. Priority: {{priority}}. View details: {{dashboardUrl}}', {
            email: {
                subject: 'New Lead Assignment: {{leadName}}',
                template: 'lead-assignment-email',
            },
            sms: {
                template: 'New lead assigned: {{leadName}}. Priority: {{priority}}. Check your dashboard.',
            },
            push: {
                title: 'New Lead Assignment',
                body: '{{leadName}} assigned by {{assignerName}}',
            },
        }));
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
};
exports.NotificationService = NotificationService;
exports.NotificationService = NotificationService = NotificationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], NotificationService);
//# sourceMappingURL=notification.service.js.map