{"version": 3, "file": "notification-preference.vo.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/value-objects/notification-preference.vo.ts"], "names": [], "mappings": ";;;AAAA,kFAA2F;AAM3F,MAAa,sBAAsB;IAEf;IACA;IACA;IACA;IAJlB,YACkB,gBAAwB,EACxB,OAAgB,EAChB,QAA+B,EAC/B,WAAiC,EAAE;QAHnC,qBAAgB,GAAhB,gBAAgB,CAAQ;QACxB,YAAO,GAAP,OAAO,CAAS;QAChB,aAAQ,GAAR,QAAQ,CAAuB;QAC/B,aAAQ,GAAR,QAAQ,CAA2B;QAEnD,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxE,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAKD,gBAAgB,CAAC,OAA4B;QAC3C,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACzD,CAAC;IAKD,SAAS,CAAC,gBAAwB;QAChC,OAAO,IAAI,CAAC,gBAAgB,KAAK,KAAK,IAAI,IAAI,CAAC,gBAAgB,KAAK,gBAAgB,CAAC;IACvF,CAAC;IAKD,kBAAkB;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,SAAS,CAAC;IAChD,CAAC;IAKD,WAAW;QACT,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU;YAAE,OAAO,KAAK,CAAC;QAE5C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;QAEpH,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;QAGhD,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;YAChB,OAAO,WAAW,IAAI,KAAK,IAAI,WAAW,IAAI,GAAG,CAAC;QACpD,CAAC;QAED,OAAO,WAAW,IAAI,KAAK,IAAI,WAAW,IAAI,GAAG,CAAC;IACpD,CAAC;IAKD,YAAY;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,WAAW,CAAC;IAChD,CAAC;IAKD,aAAa;QACX,OAAO;YACL,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC7C,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;SAC/B,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,eAAe,CAAC,IAAS;QAC9B,OAAO,IAAI,sBAAsB,CAC/B,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,cAAc;QACnB,OAAO;YACL,IAAI,sBAAsB,CACxB,KAAK,EACL,IAAI,EACJ,CAAC,+CAAmB,CAAC,KAAK,EAAE,+CAAmB,CAAC,MAAM,CAAC,EACvD;gBACE,UAAU,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE;gBAC5C,SAAS,EAAE,WAAW;aACvB,CACF;YACD,IAAI,sBAAsB,CACxB,eAAe,EACf,IAAI,EACJ,CAAC,+CAAmB,CAAC,KAAK,EAAE,+CAAmB,CAAC,MAAM,EAAE,+CAAmB,CAAC,IAAI,CAAC,EACjF,EAAE,SAAS,EAAE,WAAW,EAAE,CAC3B;YACD,IAAI,sBAAsB,CACxB,qBAAqB,EACrB,IAAI,EACJ,CAAC,+CAAmB,CAAC,KAAK,EAAE,+CAAmB,CAAC,MAAM,CAAC,EACvD,EAAE,SAAS,EAAE,WAAW,EAAE,CAC3B;YACD,IAAI,sBAAsB,CACxB,gBAAgB,EAChB,IAAI,EACJ,CAAC,+CAAmB,CAAC,KAAK,EAAE,+CAAmB,CAAC,KAAK,CAAC,EACtD,EAAE,SAAS,EAAE,WAAW,EAAE,CAC3B;YACD,IAAI,sBAAsB,CACxB,kBAAkB,EAClB,IAAI,EACJ,CAAC,+CAAmB,CAAC,KAAK,CAAC,EAC3B,EAAE,SAAS,EAAE,SAAS,EAAE,CACzB;SACF,CAAC;IACJ,CAAC;CACF;AArID,wDAqIC"}