import { Lead } from '../entities/lead.entity';
export declare class AssignmentRule {
    readonly id: string;
    readonly name: string;
    readonly description: string;
    readonly leadCondition: (lead: Lead) => boolean;
    readonly userCondition: (user: any) => boolean;
    readonly scoreAdjustment: number;
    readonly priority: number;
    readonly isActive: boolean;
    constructor(id: string, name: string, description: string, leadCondition: (lead: Lead) => boolean, userCondition: (user: any) => boolean, scoreAdjustment: number, priority: number, isActive?: boolean);
    private validateRule;
    appliesTo(lead: Lead): boolean;
    appliesToUser(user: any): boolean;
    getScoreAdjustment(lead: Lead, user: any): number;
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): AssignmentRule;
}
