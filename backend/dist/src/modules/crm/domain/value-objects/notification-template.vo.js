"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationTemplate = void 0;
class NotificationTemplate {
    type;
    name;
    shortDescription;
    longDescription;
    channelTemplates;
    variables;
    metadata;
    constructor(type, name, shortDescription, longDescription, channelTemplates, variables = [], metadata = {}) {
        this.type = type;
        this.name = name;
        this.shortDescription = shortDescription;
        this.longDescription = longDescription;
        this.channelTemplates = channelTemplates;
        this.variables = variables;
        this.metadata = metadata;
        this.validateTemplate();
    }
    validateTemplate() {
        if (!this.type || this.type.trim().length === 0) {
            throw new Error('Notification template must have a type');
        }
        if (!this.name || this.name.trim().length === 0) {
            throw new Error('Notification template must have a name');
        }
        if (!this.channelTemplates || Object.keys(this.channelTemplates).length === 0) {
            throw new Error('Notification template must have at least one channel template');
        }
    }
    render(data) {
        const renderedChannels = {};
        Object.entries(this.channelTemplates).forEach(([channel, template]) => {
            try {
                renderedChannels[channel] = this.renderChannel(channel, template, data);
            }
            catch (error) {
                throw new Error(`Failed to render template for channel ${channel}: ${error.message}`);
            }
        });
        return {
            type: this.type,
            channels: renderedChannels,
            renderedAt: new Date(),
            data,
        };
    }
    renderForChannel(channel, data) {
        const template = this.channelTemplates[channel];
        if (!template) {
            throw new Error(`Template not found for channel: ${channel}`);
        }
        return this.renderChannel(channel, template, data);
    }
    getSupportedChannels() {
        return Object.keys(this.channelTemplates);
    }
    supportsChannel(channel) {
        return this.channelTemplates.hasOwnProperty(channel);
    }
    getVariables() {
        return this.variables;
    }
    getRequiredVariables() {
        return this.variables.filter(variable => variable.required);
    }
    validateData(data) {
        const errors = [];
        const warnings = [];
        this.getRequiredVariables().forEach(variable => {
            if (!(variable.name in data) || data[variable.name] === null || data[variable.name] === undefined) {
                errors.push(`Required variable '${variable.name}' is missing`);
            }
        });
        this.variables.forEach(variable => {
            if (variable.name in data) {
                const value = data[variable.name];
                if (!this.isValidType(value, variable.type)) {
                    errors.push(`Variable '${variable.name}' has invalid type. Expected: ${variable.type}`);
                }
            }
        });
        Object.keys(data).forEach(key => {
            if (!this.variables.some(variable => variable.name === key)) {
                warnings.push(`Unused variable '${key}' provided`);
            }
        });
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
        };
    }
    getPreview() {
        const sampleData = this.generateSampleData();
        return this.render(sampleData);
    }
    clone(modifications) {
        return new NotificationTemplate(modifications.type || this.type, modifications.name || this.name, modifications.shortDescription || this.shortDescription, modifications.longDescription || this.longDescription, modifications.channelTemplates || this.channelTemplates, modifications.variables || this.variables, { ...this.metadata, ...modifications.metadata });
    }
    toPlainObject() {
        return {
            type: this.type,
            name: this.name,
            shortDescription: this.shortDescription,
            longDescription: this.longDescription,
            channelTemplates: this.channelTemplates,
            variables: this.variables,
            metadata: this.metadata,
            supportedChannels: this.getSupportedChannels(),
            requiredVariables: this.getRequiredVariables().map(v => v.name),
        };
    }
    static fromPlainObject(data) {
        return new NotificationTemplate(data.type, data.name, data.shortDescription, data.longDescription, data.channelTemplates, data.variables, data.metadata);
    }
    renderChannel(channel, template, data) {
        const rendered = {};
        Object.entries(template).forEach(([key, value]) => {
            if (typeof value === 'string') {
                rendered[key] = this.interpolateString(value, data);
            }
            else {
                rendered[key] = value;
            }
        });
        return rendered;
    }
    interpolateString(template, data) {
        return template.replace(/\{\{(\w+)\}\}/g, (match, variable) => {
            const value = data[variable];
            return value !== undefined ? String(value) : match;
        });
    }
    isValidType(value, expectedType) {
        switch (expectedType) {
            case 'string': return typeof value === 'string';
            case 'number': return typeof value === 'number';
            case 'boolean': return typeof value === 'boolean';
            case 'date': return value instanceof Date || !isNaN(Date.parse(value));
            case 'array': return Array.isArray(value);
            case 'object': return typeof value === 'object' && value !== null;
            case 'any': return true;
            default: return true;
        }
    }
    generateSampleData() {
        const sampleData = {};
        this.variables.forEach(variable => {
            switch (variable.type) {
                case 'string':
                    sampleData[variable.name] = variable.defaultValue || `Sample ${variable.name}`;
                    break;
                case 'number':
                    sampleData[variable.name] = variable.defaultValue || 123;
                    break;
                case 'boolean':
                    sampleData[variable.name] = variable.defaultValue || true;
                    break;
                case 'date':
                    sampleData[variable.name] = variable.defaultValue || new Date().toISOString();
                    break;
                case 'array':
                    sampleData[variable.name] = variable.defaultValue || ['Sample', 'Array'];
                    break;
                case 'object':
                    sampleData[variable.name] = variable.defaultValue || { sample: 'object' };
                    break;
                default:
                    sampleData[variable.name] = variable.defaultValue || `Sample ${variable.name}`;
            }
        });
        return sampleData;
    }
}
exports.NotificationTemplate = NotificationTemplate;
//# sourceMappingURL=notification-template.vo.js.map