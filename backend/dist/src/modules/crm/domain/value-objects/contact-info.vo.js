"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactInfo = void 0;
class ContactInfo {
    email;
    phone;
    mobile;
    website;
    company;
    jobTitle;
    address;
    city;
    state;
    country;
    postalCode;
    socialMedia;
    constructor(email, phone, mobile, website, company, jobTitle, address, city, state, country, postalCode, socialMedia) {
        this.email = email;
        this.phone = phone;
        this.mobile = mobile;
        this.website = website;
        this.company = company;
        this.jobTitle = jobTitle;
        this.address = address;
        this.city = city;
        this.state = state;
        this.country = country;
        this.postalCode = postalCode;
        this.socialMedia = socialMedia;
        this.validateContactInfo();
    }
    validateContactInfo() {
        if (this.email && !this.isValidEmail(this.email)) {
            throw new Error('Invalid email format');
        }
        if (this.website && !this.isValidWebsite(this.website)) {
            throw new Error('Invalid website format');
        }
    }
    hasAnyInfo() {
        return !!(this.email ||
            this.phone ||
            this.mobile ||
            this.website ||
            this.company ||
            this.jobTitle ||
            this.address ||
            this.city ||
            this.state ||
            this.country ||
            this.postalCode ||
            this.socialMedia);
    }
    hasCompleteAddress() {
        return !!(this.address && this.city && this.country);
    }
    hasBusinessInfo() {
        return !!(this.company || this.jobTitle || this.website);
    }
    getCompletenessScore() {
        let score = 0;
        const fields = [
            this.email,
            this.phone,
            this.mobile,
            this.website,
            this.company,
            this.jobTitle,
            this.address,
            this.city,
            this.state,
            this.country,
            this.postalCode,
        ];
        const filledFields = fields.filter(field => field && field.trim().length > 0).length;
        score = (filledFields / fields.length) * 100;
        if (this.socialMedia && Object.keys(this.socialMedia).length > 0) {
            score += 5;
        }
        return Math.min(100, Math.round(score));
    }
    getPrimaryContactMethod() {
        if (this.email)
            return 'email';
        if (this.mobile)
            return 'mobile';
        if (this.phone)
            return 'phone';
        if (this.website)
            return 'website';
        return null;
    }
    getFormattedAddress() {
        const parts = [
            this.address,
            this.city,
            this.state,
            this.postalCode,
            this.country,
        ].filter(part => part && part.trim().length > 0);
        return parts.join(', ');
    }
    getDisplayName() {
        if (this.company) {
            return this.jobTitle ? `${this.company} (${this.jobTitle})` : this.company;
        }
        if (this.jobTitle) {
            return this.jobTitle;
        }
        return this.email || this.phone || this.mobile || 'Unknown Contact';
    }
    toPlainObject() {
        return {
            email: this.email,
            phone: this.phone,
            mobile: this.mobile,
            website: this.website,
            company: this.company,
            jobTitle: this.jobTitle,
            address: this.address,
            city: this.city,
            state: this.state,
            country: this.country,
            postalCode: this.postalCode,
            socialMedia: this.socialMedia,
            hasAnyInfo: this.hasAnyInfo(),
            hasCompleteAddress: this.hasCompleteAddress(),
            hasBusinessInfo: this.hasBusinessInfo(),
            completenessScore: this.getCompletenessScore(),
            primaryContactMethod: this.getPrimaryContactMethod(),
            formattedAddress: this.getFormattedAddress(),
            displayName: this.getDisplayName(),
        };
    }
    static fromPlainObject(data) {
        return new ContactInfo(data.email, data.phone, data.mobile, data.website, data.company, data.jobTitle, data.address, data.city, data.state, data.country, data.postalCode, data.socialMedia);
    }
    static createEmpty() {
        return new ContactInfo();
    }
    mergeWith(other) {
        return new ContactInfo(this.email || other.email, this.phone || other.phone, this.mobile || other.mobile, this.website || other.website, this.company || other.company, this.jobTitle || other.jobTitle, this.address || other.address, this.city || other.city, this.state || other.state, this.country || other.country, this.postalCode || other.postalCode, { ...other.socialMedia, ...this.socialMedia });
    }
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    isValidWebsite(website) {
        try {
            new URL(website.startsWith('http') ? website : `https://${website}`);
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.ContactInfo = ContactInfo;
//# sourceMappingURL=contact-info.vo.js.map