"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationDelivery = void 0;
class NotificationDelivery {
    id;
    userId;
    notificationType;
    channel;
    status;
    sentAt;
    error;
    metadata;
    constructor(id, userId, notificationType, channel, status, sentAt, error, metadata = {}) {
        this.id = id;
        this.userId = userId;
        this.notificationType = notificationType;
        this.channel = channel;
        this.status = status;
        this.sentAt = sentAt;
        this.error = error;
        this.metadata = metadata;
        this.validateDelivery();
    }
    validateDelivery() {
        if (!this.id || this.id.trim().length === 0) {
            throw new Error('Notification delivery must have an ID');
        }
        if (!this.userId || this.userId.trim().length === 0) {
            throw new Error('Notification delivery must have a user ID');
        }
        if (!this.notificationType || this.notificationType.trim().length === 0) {
            throw new Error('Notification delivery must have a type');
        }
    }
    isSuccessful() {
        return this.status === 'delivered';
    }
    isFailed() {
        return this.status === 'failed';
    }
    isPending() {
        return this.status === 'pending';
    }
    getDeliveryDuration() {
        const deliveredAt = this.metadata.deliveredAt;
        if (!deliveredAt)
            return 0;
        return new Date(deliveredAt).getTime() - this.sentAt.getTime();
    }
    getRetryCount() {
        return this.metadata.retryCount || 0;
    }
    getPriority() {
        return this.metadata.priority || 'normal';
    }
    canRetry() {
        return this.isFailed() && this.getRetryCount() < 3;
    }
    getSummary() {
        return {
            id: this.id,
            userId: this.userId,
            notificationType: this.notificationType,
            channel: this.channel,
            status: this.status,
            isSuccessful: this.isSuccessful(),
            sentAt: this.sentAt,
            deliveryDuration: this.getDeliveryDuration(),
            retryCount: this.getRetryCount(),
            priority: this.getPriority(),
            error: this.error,
        };
    }
    toPlainObject() {
        return {
            id: this.id,
            userId: this.userId,
            notificationType: this.notificationType,
            channel: this.channel,
            status: this.status,
            sentAt: this.sentAt.toISOString(),
            error: this.error,
            metadata: this.metadata,
            isSuccessful: this.isSuccessful(),
            deliveryDuration: this.getDeliveryDuration(),
            retryCount: this.getRetryCount(),
            priority: this.getPriority(),
            canRetry: this.canRetry(),
            summary: this.getSummary(),
        };
    }
    static fromPlainObject(data) {
        return new NotificationDelivery(data.id, data.userId, data.notificationType, data.channel, data.status, new Date(data.sentAt), data.error, data.metadata);
    }
    static success(id, userId, notificationType, channel, metadata = {}) {
        return new NotificationDelivery(id, userId, notificationType, channel, 'delivered', new Date(), undefined, {
            ...metadata,
            deliveredAt: new Date().toISOString(),
        });
    }
    static failure(id, userId, notificationType, channel, error, metadata = {}) {
        return new NotificationDelivery(id, userId, notificationType, channel, 'failed', new Date(), error, metadata);
    }
    static pending(id, userId, notificationType, channel, metadata = {}) {
        return new NotificationDelivery(id, userId, notificationType, channel, 'pending', new Date(), undefined, metadata);
    }
}
exports.NotificationDelivery = NotificationDelivery;
//# sourceMappingURL=notification-delivery.vo.js.map