"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScoringCondition = exports.ScoringRule = void 0;
class ScoringRule {
    id;
    name;
    description;
    factor;
    condition;
    scoreAdjustment;
    isActive;
    priority;
    metadata;
    constructor(id, name, description, factor, condition, scoreAdjustment, isActive = true, priority = 0, metadata = {}) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.factor = factor;
        this.condition = condition;
        this.scoreAdjustment = scoreAdjustment;
        this.isActive = isActive;
        this.priority = priority;
        this.metadata = metadata;
        this.validateRule();
    }
    validateRule() {
        if (!this.id || this.id.trim().length === 0) {
            throw new Error('Scoring rule must have an ID');
        }
        if (!this.name || this.name.trim().length === 0) {
            throw new Error('Scoring rule must have a name');
        }
        if (!this.factor || this.factor.trim().length === 0) {
            throw new Error('Scoring rule must specify a factor');
        }
        if (this.scoreAdjustment < -100 || this.scoreAdjustment > 100) {
            throw new Error('Score adjustment must be between -100 and 100');
        }
    }
    appliesTo(leadData) {
        if (!this.isActive)
            return false;
        return this.condition.evaluate(leadData);
    }
    apply(leadData) {
        if (!this.appliesTo(leadData))
            return 0;
        return this.scoreAdjustment;
    }
    getRuleType() {
        if (this.scoreAdjustment > 0)
            return 'bonus';
        if (this.scoreAdjustment < 0)
            return 'penalty';
        return 'neutral';
    }
    getImpactLevel() {
        const magnitude = Math.abs(this.scoreAdjustment);
        if (magnitude >= 20)
            return 'high';
        if (magnitude >= 10)
            return 'medium';
        return 'low';
    }
    toPlainObject() {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            factor: this.factor,
            condition: this.condition.toPlainObject(),
            scoreAdjustment: this.scoreAdjustment,
            isActive: this.isActive,
            priority: this.priority,
            ruleType: this.getRuleType(),
            impactLevel: this.getImpactLevel(),
            metadata: this.metadata,
        };
    }
    static fromPlainObject(data) {
        return new ScoringRule(data.id, data.name, data.description, data.factor, ScoringCondition.fromPlainObject(data.condition), data.scoreAdjustment, data.isActive, data.priority, data.metadata);
    }
    static createPredefinedRules() {
        return [
            new ScoringRule('high-revenue-bonus', 'High Revenue Bonus', 'Bonus for leads with high expected revenue', 'revenue_potential', new ScoringCondition('expectedRevenue', 'gte', 50000), 15, true, 1),
            new ScoringRule('referral-source-bonus', 'Referral Source Bonus', 'Bonus for leads from referral sources', 'source_quality', new ScoringCondition('source', 'equals', 'referral'), 10, true, 2),
            new ScoringRule('tech-industry-bonus', 'Technology Industry Bonus', 'Bonus for technology industry leads', 'company_fit', new ScoringCondition('industry', 'equals', 'technology'), 8, true, 3),
            new ScoringRule('missing-email-penalty', 'Missing Email Penalty', 'Penalty for leads without email', 'contact_completeness', new ScoringCondition('email', 'empty', null), -20, true, 4),
            new ScoringRule('recent-activity-bonus', 'Recent Activity Bonus', 'Bonus for recently created leads', 'engagement_level', new ScoringCondition('daysSinceCreated', 'lte', 1), 12, true, 5),
        ];
    }
}
exports.ScoringRule = ScoringRule;
class ScoringCondition {
    field;
    operator;
    value;
    logicalOperator;
    subConditions;
    constructor(field, operator, value, logicalOperator, subConditions) {
        this.field = field;
        this.operator = operator;
        this.value = value;
        this.logicalOperator = logicalOperator;
        this.subConditions = subConditions;
        this.validateCondition();
    }
    validateCondition() {
        if (!this.field || this.field.trim().length === 0) {
            throw new Error('Scoring condition must have a field');
        }
        if (!this.operator) {
            throw new Error('Scoring condition must have an operator');
        }
    }
    evaluate(leadData) {
        const fieldValue = this.getFieldValue(leadData, this.field);
        const result = this.evaluateOperator(fieldValue, this.operator, this.value);
        if (this.subConditions && this.subConditions.length > 0) {
            const subResults = this.subConditions.map(condition => condition.evaluate(leadData));
            if (this.logicalOperator === 'or') {
                return result || subResults.some(r => r);
            }
            else {
                return result && subResults.every(r => r);
            }
        }
        return result;
    }
    getFieldValue(data, field) {
        const parts = field.split('.');
        let value = data;
        for (const part of parts) {
            if (value && typeof value === 'object') {
                value = value[part];
            }
            else {
                return undefined;
            }
        }
        return value;
    }
    evaluateOperator(fieldValue, operator, compareValue) {
        switch (operator) {
            case 'equals':
                return fieldValue === compareValue;
            case 'not_equals':
                return fieldValue !== compareValue;
            case 'gt':
                return typeof fieldValue === 'number' && fieldValue > compareValue;
            case 'gte':
                return typeof fieldValue === 'number' && fieldValue >= compareValue;
            case 'lt':
                return typeof fieldValue === 'number' && fieldValue < compareValue;
            case 'lte':
                return typeof fieldValue === 'number' && fieldValue <= compareValue;
            case 'contains':
                return typeof fieldValue === 'string' && fieldValue.toLowerCase().includes(compareValue.toLowerCase());
            case 'not_contains':
                return typeof fieldValue === 'string' && !fieldValue.toLowerCase().includes(compareValue.toLowerCase());
            case 'starts_with':
                return typeof fieldValue === 'string' && fieldValue.toLowerCase().startsWith(compareValue.toLowerCase());
            case 'ends_with':
                return typeof fieldValue === 'string' && fieldValue.toLowerCase().endsWith(compareValue.toLowerCase());
            case 'empty':
                return !fieldValue || fieldValue === '' || (Array.isArray(fieldValue) && fieldValue.length === 0);
            case 'not_empty':
                return !!fieldValue && fieldValue !== '' && (!Array.isArray(fieldValue) || fieldValue.length > 0);
            case 'in':
                return Array.isArray(compareValue) && compareValue.includes(fieldValue);
            case 'not_in':
                return Array.isArray(compareValue) && !compareValue.includes(fieldValue);
            case 'regex':
                return typeof fieldValue === 'string' && new RegExp(compareValue).test(fieldValue);
            default:
                throw new Error(`Unsupported operator: ${operator}`);
        }
    }
    toPlainObject() {
        return {
            field: this.field,
            operator: this.operator,
            value: this.value,
            logicalOperator: this.logicalOperator,
            subConditions: this.subConditions?.map(c => c.toPlainObject()),
        };
    }
    static fromPlainObject(data) {
        return new ScoringCondition(data.field, data.operator, data.value, data.logicalOperator, data.subConditions?.map((c) => ScoringCondition.fromPlainObject(c)));
    }
}
exports.ScoringCondition = ScoringCondition;
//# sourceMappingURL=scoring-rule.vo.js.map