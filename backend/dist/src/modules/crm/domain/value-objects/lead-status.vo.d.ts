export declare class LeadStatus {
    readonly value: string;
    private static readonly VALID_STATUSES;
    private static readonly STATUS_HIERARCHY;
    constructor(value: string);
    private validateStatus;
    isProgression(previousStatus: LeadStatus): boolean;
    isRegression(previousStatus: LeadStatus): boolean;
    isActive(): boolean;
    isClosed(): boolean;
    isWon(): boolean;
    isLost(): boolean;
    isCancelled(): boolean;
    isQualified(): boolean;
    isConverted(): boolean;
    getDisplayName(): string;
    getColor(): string;
    getNextPossibleStatuses(): LeadStatus[];
    canTransitionTo(newStatus: LeadStatus): boolean;
    getPriority(): number;
    equals(other: LeadStatus): boolean;
    toString(): string;
    toPlainObject(): Record<string, any>;
    static fromString(value: string): LeadStatus;
    static fromPlainObject(data: any): LeadStatus;
    static getAllStatuses(): LeadStatus[];
    static new(): LeadStatus;
    static qualified(): LeadStatus;
    static proposition(): LeadStatus;
    static won(): LeadStatus;
    static lost(): LeadStatus;
    static cancelled(): LeadStatus;
}
