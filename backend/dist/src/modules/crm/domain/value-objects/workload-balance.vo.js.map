{"version": 3, "file": "workload-balance.vo.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/value-objects/workload-balance.vo.ts"], "names": [], "mappings": ";;;AAIA,MAAa,eAAe;IAER;IACA;IACA;IACA;IACA;IACA;IACA;IAPlB,YACkB,MAAc,EACd,UAAkB,EAClB,WAAmB,EACnB,UAAkB,EAClB,cAAsB,EACtB,WAAiB,EACjB,WAAgC,EAAE;QANlC,WAAM,GAAN,MAAM,CAAQ;QACd,eAAU,GAAV,UAAU,CAAQ;QAClB,gBAAW,GAAX,WAAW,CAAQ;QACnB,eAAU,GAAV,UAAU,CAAQ;QAClB,mBAAc,GAAd,cAAc,CAAQ;QACtB,gBAAW,GAAX,WAAW,CAAM;QACjB,aAAQ,GAAR,QAAQ,CAA0B;QAElD,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAKD,oBAAoB;QAElB,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QAGpD,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC;YAC7B,SAAS,IAAI,EAAE,CAAC;QAClB,CAAC;aAAM,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC;YACpC,SAAS,IAAI,CAAC,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;IAClC,CAAC;IAKD,gBAAgB;QACd,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE9C,IAAI,SAAS,IAAI,EAAE;YAAE,OAAO,YAAY,CAAC;QACzC,IAAI,SAAS,IAAI,EAAE;YAAE,OAAO,OAAO,CAAC;QACpC,IAAI,SAAS,IAAI,EAAE;YAAE,OAAO,UAAU,CAAC;QACvC,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,YAAY;QACV,OAAO,IAAI,CAAC,gBAAgB,EAAE,KAAK,YAAY,CAAC;IAClD,CAAC;IAKD,gBAAgB,CAAC,WAAmB,EAAE;QACpC,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;IAC7D,CAAC;IAKD,sBAAsB;QACpB,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC;IAKD,iBAAiB;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI,CAAC,CAAC;IAC3C,CAAC;IAKD,WAAW,CAAC,KAAsB;QAChC,OAAO;YACL,cAAc,EAAE,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU;YAClD,eAAe,EAAE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW;YACrD,cAAc,EAAE,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU;YAClD,aAAa,EAAE,IAAI,CAAC,oBAAoB,EAAE,GAAG,KAAK,CAAC,oBAAoB,EAAE;YACzE,cAAc,EAAE,IAAI,CAAC,oBAAoB,EAAE,GAAG,KAAK,CAAC,oBAAoB,EAAE;SAC3E,CAAC;IACJ,CAAC;IAKD,UAAU;QACR,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE;YAC9C,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YAClD,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACzC,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAC;IACJ,CAAC;IAKD,aAAa;QACX,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;YAC3C,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE;YAC9C,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YAClD,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACzC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;SAC3B,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,eAAe,CAAC,IAAS;QAC9B,OAAO,IAAI,eAAe,CACxB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,cAAc,EACnB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAC1B,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,WAAW,CAAC,MAAc;QAC/B,OAAO,IAAI,eAAe,CACxB,MAAM,EACN,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,IAAI,IAAI,EAAE,CACX,CAAC;IACJ,CAAC;CACF;AAvKD,0CAuKC"}