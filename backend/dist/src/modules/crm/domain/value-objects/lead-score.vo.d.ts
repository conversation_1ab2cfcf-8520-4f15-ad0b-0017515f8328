import { ScoringFactor } from './scoring-factor.vo';
export declare class LeadScore {
    readonly score: number;
    readonly factors: ScoringFactor[];
    readonly grade: string;
    readonly recommendedActions: string[];
    readonly calculatedAt: Date;
    readonly metadata: Record<string, any>;
    constructor(score: number, factors: ScoringFactor[], grade: string, recommendedActions: string[], calculatedAt: Date, metadata?: Record<string, any>);
    private validateScore;
    isHighPriority(): boolean;
    isMediumPriority(): boolean;
    isLowPriority(): boolean;
    getConfidence(): number;
    isReliable(): boolean;
    getPrimaryFactor(): ScoringFactor | null;
    getWeakFactors(threshold?: number): ScoringFactor[];
    getStrongFactors(threshold?: number): ScoringFactor[];
    getImprovementPotential(): number;
    getUrgencyLevel(): 'critical' | 'high' | 'medium' | 'low';
    getRecommendedFollowUpTime(): string;
    hasSignificantChange(previousScore?: LeadScore): boolean;
    getTrend(previousScore?: LeadScore): 'up' | 'down' | 'stable' | 'new';
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): LeadScore;
    static compare(a: LeadScore, b: LeadScore): number;
    static createDefault(): LeadScore;
    static isValid(data: any): boolean;
}
