export declare class WorkloadBalance {
    readonly userId: string;
    readonly totalLeads: number;
    readonly activeLeads: number;
    readonly totalValue: number;
    readonly averageLeadAge: number;
    readonly lastUpdated: Date;
    readonly metadata: Record<string, any>;
    constructor(userId: string, totalLeads: number, activeLeads: number, totalValue: number, averageLeadAge: number, lastUpdated: Date, metadata?: Record<string, any>);
    private validateWorkload;
    getWorkloadIntensity(): number;
    getWorkloadLevel(): 'light' | 'moderate' | 'heavy' | 'overloaded';
    isOverloaded(): boolean;
    canTakeMoreLeads(maxLeads?: number): boolean;
    getAverageValuePerLead(): number;
    getConversionRate(): number;
    compareWith(other: WorkloadBalance): WorkloadComparison;
    getSummary(): WorkloadSummary;
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): WorkloadBalance;
    static createEmpty(userId: string): WorkloadBalance;
}
export interface WorkloadComparison {
    totalLeadsDiff: number;
    activeLeadsDiff: number;
    totalValueDiff: number;
    intensityDiff: number;
    isMoreBalanced: boolean;
}
export interface WorkloadSummary {
    userId: string;
    totalLeads: number;
    activeLeads: number;
    totalValue: number;
    averageLeadAge: number;
    workloadLevel: 'light' | 'moderate' | 'heavy' | 'overloaded';
    workloadIntensity: number;
    averageValuePerLead: number;
    canTakeMoreLeads: boolean;
    lastUpdated: Date;
}
