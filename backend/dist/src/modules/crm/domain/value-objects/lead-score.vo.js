"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadScore = void 0;
const scoring_factor_vo_1 = require("./scoring-factor.vo");
class LeadScore {
    score;
    factors;
    grade;
    recommendedActions;
    calculatedAt;
    metadata;
    constructor(score, factors, grade, recommendedActions, calculatedAt, metadata = {}) {
        this.score = score;
        this.factors = factors;
        this.grade = grade;
        this.recommendedActions = recommendedActions;
        this.calculatedAt = calculatedAt;
        this.metadata = metadata;
        this.validateScore();
    }
    validateScore() {
        if (this.score < 0 || this.score > 100) {
            throw new Error('Lead score must be between 0 and 100');
        }
        if (!this.factors || this.factors.length === 0) {
            throw new Error('Lead score must have at least one scoring factor');
        }
        if (!this.grade) {
            throw new Error('Lead score must have a grade');
        }
    }
    isHighPriority() {
        return this.score >= 80;
    }
    isMediumPriority() {
        return this.score >= 60 && this.score < 80;
    }
    isLowPriority() {
        return this.score < 60;
    }
    getConfidence() {
        return this.metadata.confidence || 50;
    }
    isReliable() {
        return this.getConfidence() >= 70;
    }
    getPrimaryFactor() {
        if (this.factors.length === 0)
            return null;
        return this.factors.reduce((max, factor) => (factor.weightedScore > max.weightedScore) ? factor : max);
    }
    getWeakFactors(threshold = 50) {
        return this.factors.filter(factor => factor.score < threshold);
    }
    getStrongFactors(threshold = 70) {
        return this.factors.filter(factor => factor.score >= threshold);
    }
    getImprovementPotential() {
        const weakFactors = this.getWeakFactors();
        if (weakFactors.length === 0)
            return 0;
        let potentialIncrease = 0;
        weakFactors.forEach(factor => {
            const improvement = Math.max(0, 70 - factor.score);
            potentialIncrease += improvement * factor.weight;
        });
        return Math.min(100 - this.score, potentialIncrease);
    }
    getUrgencyLevel() {
        if (this.score >= 90)
            return 'critical';
        if (this.score >= 80)
            return 'high';
        if (this.score >= 60)
            return 'medium';
        return 'low';
    }
    getRecommendedFollowUpTime() {
        switch (this.getUrgencyLevel()) {
            case 'critical': return 'within 1 hour';
            case 'high': return 'within 24 hours';
            case 'medium': return 'within 3 days';
            case 'low': return 'within 1 week';
            default: return 'within 1 week';
        }
    }
    hasSignificantChange(previousScore) {
        if (!previousScore)
            return true;
        const scoreDifference = Math.abs(this.score - previousScore.score);
        return scoreDifference >= 10;
    }
    getTrend(previousScore) {
        if (!previousScore)
            return 'new';
        const difference = this.score - previousScore.score;
        if (difference > 5)
            return 'up';
        if (difference < -5)
            return 'down';
        return 'stable';
    }
    toPlainObject() {
        return {
            score: this.score,
            grade: this.grade,
            urgencyLevel: this.getUrgencyLevel(),
            confidence: this.getConfidence(),
            isHighPriority: this.isHighPriority(),
            recommendedActions: this.recommendedActions,
            recommendedFollowUpTime: this.getRecommendedFollowUpTime(),
            improvementPotential: this.getImprovementPotential(),
            factors: this.factors.map(factor => factor.toPlainObject()),
            calculatedAt: this.calculatedAt.toISOString(),
            metadata: this.metadata,
        };
    }
    static fromPlainObject(data) {
        return new LeadScore(data.score, data.factors.map((f) => scoring_factor_vo_1.ScoringFactor.fromPlainObject(f)), data.grade, data.recommendedActions, new Date(data.calculatedAt), data.metadata);
    }
    static compare(a, b) {
        if (a.score !== b.score) {
            return b.score - a.score;
        }
        const aConfidence = a.getConfidence();
        const bConfidence = b.getConfidence();
        if (aConfidence !== bConfidence) {
            return bConfidence - aConfidence;
        }
        return b.calculatedAt.getTime() - a.calculatedAt.getTime();
    }
    static createDefault() {
        return new LeadScore(50, [new scoring_factor_vo_1.ScoringFactor('default', 50, 1.0)], 'C', ['Review and complete lead information'], new Date(), { isDefault: true });
    }
    static isValid(data) {
        return (typeof data.score === 'number' &&
            data.score >= 0 &&
            data.score <= 100 &&
            Array.isArray(data.factors) &&
            data.factors.length > 0 &&
            typeof data.grade === 'string' &&
            Array.isArray(data.recommendedActions) &&
            data.calculatedAt instanceof Date);
    }
}
exports.LeadScore = LeadScore;
//# sourceMappingURL=lead-score.vo.js.map