{"version": 3, "file": "lead-score.vo.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/value-objects/lead-score.vo.ts"], "names": [], "mappings": ";;;AAAA,2DAAoD;AAMpD,MAAa,SAAS;IAEF;IACA;IACA;IACA;IACA;IACA;IANlB,YACkB,KAAa,EACb,OAAwB,EACxB,KAAa,EACb,kBAA4B,EAC5B,YAAkB,EAClB,WAAgC,EAAE;QALlC,UAAK,GAAL,KAAK,CAAQ;QACb,YAAO,GAAP,OAAO,CAAiB;QACxB,UAAK,GAAL,KAAK,CAAQ;QACb,uBAAkB,GAAlB,kBAAkB,CAAU;QAC5B,iBAAY,GAAZ,YAAY,CAAM;QAClB,aAAQ,GAAR,QAAQ,CAA0B;QAElD,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAEO,aAAa;QACnB,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAKD,cAAc;QACZ,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;IAC1B,CAAC;IAKD,gBAAgB;QACd,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IAC7C,CAAC;IAKD,aAAa;QACX,OAAO,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IACzB,CAAC;IAKD,aAAa;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAE,CAAC;IACxC,CAAC;IAKD,UAAU;QACR,OAAO,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;IACpC,CAAC;IAKD,gBAAgB;QACd,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAE3C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CACzC,CAAC,MAAM,CAAC,aAAa,GAAG,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAC1D,CAAC;IACJ,CAAC;IAKD,cAAc,CAAC,YAAoB,EAAE;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC;IACjE,CAAC;IAKD,gBAAgB,CAAC,YAAoB,EAAE;QACrC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,IAAI,SAAS,CAAC,CAAC;IAClE,CAAC;IAKD,uBAAuB;QACrB,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAGvC,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC3B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YACnD,iBAAiB,IAAI,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;IACvD,CAAC;IAKD,eAAe;QACb,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;YAAE,OAAO,UAAU,CAAC;QACxC,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QACpC,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;YAAE,OAAO,QAAQ,CAAC;QACtC,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,0BAA0B;QACxB,QAAQ,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;YAC/B,KAAK,UAAU,CAAC,CAAC,OAAO,eAAe,CAAC;YACxC,KAAK,MAAM,CAAC,CAAC,OAAO,iBAAiB,CAAC;YACtC,KAAK,QAAQ,CAAC,CAAC,OAAO,eAAe,CAAC;YACtC,KAAK,KAAK,CAAC,CAAC,OAAO,eAAe,CAAC;YACnC,OAAO,CAAC,CAAC,OAAO,eAAe,CAAC;QAClC,CAAC;IACH,CAAC;IAKD,oBAAoB,CAAC,aAAyB;QAC5C,IAAI,CAAC,aAAa;YAAE,OAAO,IAAI,CAAC;QAEhC,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;QACnE,OAAO,eAAe,IAAI,EAAE,CAAC;IAC/B,CAAC;IAKD,QAAQ,CAAC,aAAyB;QAChC,IAAI,CAAC,aAAa;YAAE,OAAO,KAAK,CAAC;QAEjC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QACpD,IAAI,UAAU,GAAG,CAAC;YAAE,OAAO,IAAI,CAAC;QAChC,IAAI,UAAU,GAAG,CAAC,CAAC;YAAE,OAAO,MAAM,CAAC;QACnC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKD,aAAa;QACX,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;YACpC,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;YAChC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YACrC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,uBAAuB,EAAE,IAAI,CAAC,0BAA0B,EAAE;YAC1D,oBAAoB,EAAE,IAAI,CAAC,uBAAuB,EAAE;YACpD,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YAC3D,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;YAC7C,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,eAAe,CAAC,IAAS;QAC9B,OAAO,IAAI,SAAS,CAClB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,iCAAa,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAC9D,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,kBAAkB,EACvB,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,EAC3B,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,OAAO,CAAC,CAAY,EAAE,CAAY;QAEvC,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;YACxB,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;QAC3B,CAAC;QAGD,MAAM,WAAW,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC;QACtC,MAAM,WAAW,GAAG,CAAC,CAAC,aAAa,EAAE,CAAC;QACtC,IAAI,WAAW,KAAK,WAAW,EAAE,CAAC;YAChC,OAAO,WAAW,GAAG,WAAW,CAAC;QACnC,CAAC;QAGD,OAAO,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;IAC7D,CAAC;IAKD,MAAM,CAAC,aAAa;QAClB,OAAO,IAAI,SAAS,CAClB,EAAE,EACF,CAAC,IAAI,iCAAa,CAAC,SAAS,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EACvC,GAAG,EACH,CAAC,sCAAsC,CAAC,EACxC,IAAI,IAAI,EAAE,EACV,EAAE,SAAS,EAAE,IAAI,EAAE,CACpB,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,OAAO,CAAC,IAAS;QACtB,OAAO,CACL,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ;YAC9B,IAAI,CAAC,KAAK,IAAI,CAAC;YACf,IAAI,CAAC,KAAK,IAAI,GAAG;YACjB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;YACvB,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ;YAC9B,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC;YACtC,IAAI,CAAC,YAAY,YAAY,IAAI,CAClC,CAAC;IACJ,CAAC;CACF;AAtOD,8BAsOC"}