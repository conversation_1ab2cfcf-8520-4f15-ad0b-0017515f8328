"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PipelineMetrics = void 0;
class PipelineMetrics {
    totalLeads;
    qualifiedLeads;
    convertedLeads;
    totalValue;
    weightedValue;
    conversionRate;
    qualificationRate;
    averageSalesCycle;
    pipelineVelocity;
    stageDistribution;
    sourcePerformance;
    teamPerformance;
    winLossAnalysis;
    calculatedAt;
    constructor(totalLeads, qualifiedLeads, convertedLeads, totalValue, weightedValue, conversionRate, qualificationRate, averageSalesCycle, pipelineVelocity, stageDistribution, sourcePerformance, teamPerformance, winLossAnalysis, calculatedAt) {
        this.totalLeads = totalLeads;
        this.qualifiedLeads = qualifiedLeads;
        this.convertedLeads = convertedLeads;
        this.totalValue = totalValue;
        this.weightedValue = weightedValue;
        this.conversionRate = conversionRate;
        this.qualificationRate = qualificationRate;
        this.averageSalesCycle = averageSalesCycle;
        this.pipelineVelocity = pipelineVelocity;
        this.stageDistribution = stageDistribution;
        this.sourcePerformance = sourcePerformance;
        this.teamPerformance = teamPerformance;
        this.winLossAnalysis = winLossAnalysis;
        this.calculatedAt = calculatedAt;
        this.validateMetrics();
    }
    validateMetrics() {
        if (this.totalLeads < 0) {
            throw new Error('Total leads cannot be negative');
        }
        if (this.conversionRate < 0 || this.conversionRate > 100) {
            throw new Error('Conversion rate must be between 0 and 100');
        }
        if (this.qualificationRate < 0 || this.qualificationRate > 100) {
            throw new Error('Qualification rate must be between 0 and 100');
        }
    }
    getEfficiencyScore() {
        let score = 0;
        score += Math.min(this.conversionRate * 2, 40);
        score += Math.min(this.qualificationRate * 0.3, 30);
        const cycleScore = Math.max(0, 20 - (this.averageSalesCycle / 10));
        score += cycleScore;
        const velocityScore = Math.min(this.pipelineVelocity / 1000, 10);
        score += velocityScore;
        return Math.round(Math.max(0, Math.min(100, score)));
    }
    getAverageDealSize() {
        return this.totalLeads > 0 ? this.totalValue / this.totalLeads : 0;
    }
    getWeightedAverageDealSize() {
        return this.totalLeads > 0 ? this.weightedValue / this.totalLeads : 0;
    }
    getPipelineCoverageRatio() {
        return this.weightedValue;
    }
    getTopPerformingSource() {
        if (this.sourcePerformance.length === 0)
            return null;
        return this.sourcePerformance.reduce((top, current) => current.conversionRate > top.conversionRate ? current : top);
    }
    getWorstPerformingSource() {
        if (this.sourcePerformance.length === 0)
            return null;
        return this.sourcePerformance.reduce((worst, current) => current.conversionRate < worst.conversionRate ? current : worst);
    }
    getMostPopulatedStage() {
        if (this.stageDistribution.length === 0)
            return null;
        return this.stageDistribution.reduce((max, current) => current.count > max.count ? current : max);
    }
    getPerformanceSummary() {
        return {
            efficiency: this.getEfficiencyScore(),
            averageDealSize: this.getAverageDealSize(),
            weightedAverageDealSize: this.getWeightedAverageDealSize(),
            topSource: this.getTopPerformingSource()?.source,
            worstSource: this.getWorstPerformingSource()?.source,
            mostPopulatedStage: this.getMostPopulatedStage()?.stageName,
            winRate: this.winLossAnalysis.winRate,
            lossRate: this.winLossAnalysis.lossRate,
        };
    }
    compareWith(previousMetrics) {
        return {
            totalLeadsChange: this.totalLeads - previousMetrics.totalLeads,
            conversionRateChange: this.conversionRate - previousMetrics.conversionRate,
            qualificationRateChange: this.qualificationRate - previousMetrics.qualificationRate,
            averageSalesCycleChange: this.averageSalesCycle - previousMetrics.averageSalesCycle,
            pipelineVelocityChange: this.pipelineVelocity - previousMetrics.pipelineVelocity,
            totalValueChange: this.totalValue - previousMetrics.totalValue,
            weightedValueChange: this.weightedValue - previousMetrics.weightedValue,
            efficiencyScoreChange: this.getEfficiencyScore() - previousMetrics.getEfficiencyScore(),
        };
    }
    toPlainObject() {
        return {
            totalLeads: this.totalLeads,
            qualifiedLeads: this.qualifiedLeads,
            convertedLeads: this.convertedLeads,
            totalValue: this.totalValue,
            weightedValue: this.weightedValue,
            conversionRate: this.conversionRate,
            qualificationRate: this.qualificationRate,
            averageSalesCycle: this.averageSalesCycle,
            pipelineVelocity: this.pipelineVelocity,
            stageDistribution: this.stageDistribution,
            sourcePerformance: this.sourcePerformance,
            teamPerformance: this.teamPerformance,
            winLossAnalysis: this.winLossAnalysis,
            efficiencyScore: this.getEfficiencyScore(),
            averageDealSize: this.getAverageDealSize(),
            weightedAverageDealSize: this.getWeightedAverageDealSize(),
            performanceSummary: this.getPerformanceSummary(),
            calculatedAt: this.calculatedAt.toISOString(),
        };
    }
    static fromPlainObject(data) {
        return new PipelineMetrics(data.totalLeads, data.qualifiedLeads, data.convertedLeads, data.totalValue, data.weightedValue, data.conversionRate, data.qualificationRate, data.averageSalesCycle, data.pipelineVelocity, data.stageDistribution, data.sourcePerformance, data.teamPerformance, data.winLossAnalysis, new Date(data.calculatedAt));
    }
    static createDefault() {
        return new PipelineMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0, [], [], {}, { winRate: 0, lossRate: 0, winReasons: [], lossReasons: [] }, new Date());
    }
}
exports.PipelineMetrics = PipelineMetrics;
//# sourceMappingURL=pipeline-metrics.vo.js.map