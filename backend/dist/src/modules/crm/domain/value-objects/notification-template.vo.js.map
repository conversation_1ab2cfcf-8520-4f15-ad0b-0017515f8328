{"version": 3, "file": "notification-template.vo.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/value-objects/notification-template.vo.ts"], "names": [], "mappings": ";;;AAMA,MAAa,oBAAoB;IAEb;IACA;IACA;IACA;IACA;IACA;IACA;IAPlB,YACkB,IAAY,EACZ,IAAY,EACZ,gBAAwB,EACxB,eAAuB,EACvB,gBAAiD,EACjD,YAAgC,EAAE,EAClC,WAAgC,EAAE;QANlC,SAAI,GAAJ,IAAI,CAAQ;QACZ,SAAI,GAAJ,IAAI,CAAQ;QACZ,qBAAgB,GAAhB,gBAAgB,CAAQ;QACxB,oBAAe,GAAf,eAAe,CAAQ;QACvB,qBAAgB,GAAhB,gBAAgB,CAAiC;QACjD,cAAS,GAAT,SAAS,CAAyB;QAClC,aAAQ,GAAR,QAAQ,CAA0B;QAElD,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,IAAyB;QAC9B,MAAM,gBAAgB,GAAwB,EAAE,CAAC;QAEjD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,EAAE;YACpE,IAAI,CAAC;gBACH,gBAAgB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC1E,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,yCAAyC,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACxF,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,gBAAgB;YAC1B,UAAU,EAAE,IAAI,IAAI,EAAE;YACtB,IAAI;SACL,CAAC;IACJ,CAAC;IAKD,gBAAgB,CAAC,OAAe,EAAE,IAAyB;QACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,mCAAmC,OAAO,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;IACrD,CAAC;IAKD,oBAAoB;QAClB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC5C,CAAC;IAKD,eAAe,CAAC,OAAe;QAC7B,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IACvD,CAAC;IAKD,YAAY;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAKD,oBAAoB;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC9D,CAAC;IAKD,YAAY,CAAC,IAAyB;QACpC,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAG9B,IAAI,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC7C,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE,CAAC;gBAClG,MAAM,CAAC,IAAI,CAAC,sBAAsB,QAAQ,CAAC,IAAI,cAAc,CAAC,CAAC;YACjE,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAChC,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;gBAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAClC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5C,MAAM,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,IAAI,iCAAiC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC1F,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC9B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBAC5D,QAAQ,CAAC,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC,CAAC;YACrD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAKD,UAAU;QACR,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC7C,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IACjC,CAAC;IAKD,KAAK,CAAC,aAQJ;QACA,OAAO,IAAI,oBAAoB,CAC7B,aAAa,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAC/B,aAAa,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAC/B,aAAa,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EACvD,aAAa,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,EACrD,aAAa,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EACvD,aAAa,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EACzC,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,aAAa,CAAC,QAAQ,EAAE,CAChD,CAAC;IACJ,CAAC;IAKD,aAAa;QACX,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE;YAC9C,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;SAChE,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,eAAe,CAAC,IAAS;QAC9B,OAAO,IAAI,oBAAoB,CAC7B,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAKO,aAAa,CAAC,OAAe,EAAE,QAAyB,EAAE,IAAyB;QACzF,MAAM,QAAQ,GAAQ,EAAE,CAAC;QAGzB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAChD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACtD,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACxB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,iBAAiB,CAAC,QAAgB,EAAE,IAAyB;QACnE,OAAO,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;YAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7B,OAAO,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,WAAW,CAAC,KAAU,EAAE,YAAoB;QAClD,QAAQ,YAAY,EAAE,CAAC;YACrB,KAAK,QAAQ,CAAC,CAAC,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC;YAChD,KAAK,QAAQ,CAAC,CAAC,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC;YAChD,KAAK,SAAS,CAAC,CAAC,OAAO,OAAO,KAAK,KAAK,SAAS,CAAC;YAClD,KAAK,MAAM,CAAC,CAAC,OAAO,KAAK,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YACvE,KAAK,OAAO,CAAC,CAAC,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC1C,KAAK,QAAQ,CAAC,CAAC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,CAAC;YAClE,KAAK,KAAK,CAAC,CAAC,OAAO,IAAI,CAAC;YACxB,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC;QACvB,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,MAAM,UAAU,GAAwB,EAAE,CAAC;QAE3C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAChC,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACtB,KAAK,QAAQ;oBACX,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,YAAY,IAAI,UAAU,QAAQ,CAAC,IAAI,EAAE,CAAC;oBAC/E,MAAM;gBACR,KAAK,QAAQ;oBACX,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,YAAY,IAAI,GAAG,CAAC;oBACzD,MAAM;gBACR,KAAK,SAAS;oBACZ,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,YAAY,IAAI,IAAI,CAAC;oBAC1D,MAAM;gBACR,KAAK,MAAM;oBACT,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;oBAC9E,MAAM;gBACR,KAAK,OAAO;oBACV,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,YAAY,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;oBACzE,MAAM;gBACR,KAAK,QAAQ;oBACX,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,YAAY,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;oBAC1E,MAAM;gBACR;oBACE,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,YAAY,IAAI,UAAU,QAAQ,CAAC,IAAI,EAAE,CAAC;YACnF,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACpB,CAAC;CACF;AAlQD,oDAkQC"}