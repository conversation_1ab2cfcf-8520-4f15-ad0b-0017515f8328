export declare class LeadPriority {
    readonly value: number;
    readonly label: string;
    readonly color: string;
    static readonly LOW: LeadPriority;
    static readonly MEDIUM: LeadPriority;
    static readonly HIGH: LeadPriority;
    static readonly VERY_HIGH: LeadPriority;
    private static readonly VALID_PRIORITIES;
    private static readonly PRIORITY_MAP;
    private constructor();
    static fromValue(value: number): LeadPriority;
    static fromLabel(label: string): LeadPriority;
    static getAllPriorities(): LeadPriority[];
    isHigherThan(other: LeadPriority): boolean;
    isLowerThan(other: LeadPriority): boolean;
    requiresImmediateAttention(): boolean;
    getScoreWeight(): number;
    getCssClass(): string;
    getIcon(): string;
    equals(other: LeadPriority): boolean;
    toPlainObject(): {
        value: number;
        label: string;
        color: string;
        cssClass: string;
        icon: string;
        requiresImmediateAttention: boolean;
    };
    toString(): string;
    toJSON(): number;
}
