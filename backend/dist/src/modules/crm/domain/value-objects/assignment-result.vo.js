"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssignmentResult = void 0;
class AssignmentResult {
    assignedUserId;
    teamId;
    success;
    message;
    reasons;
    assignedAt;
    metadata;
    constructor(assignedUserId, teamId, success, message, reasons, assignedAt, metadata = {}) {
        this.assignedUserId = assignedUserId;
        this.teamId = teamId;
        this.success = success;
        this.message = message;
        this.reasons = reasons;
        this.assignedAt = assignedAt;
        this.metadata = metadata;
        this.validateResult();
    }
    validateResult() {
        if (!this.message || this.message.trim().length === 0) {
            throw new Error('Assignment result must have a message');
        }
        if (!Array.isArray(this.reasons)) {
            throw new Error('Assignment result reasons must be an array');
        }
    }
    isSuccessful() {
        return this.success && this.assignedUserId !== null;
    }
    getConfidenceScore() {
        return this.metadata.score || 0;
    }
    getPrimaryReason() {
        return this.reasons.length > 0 ? this.reasons[0] : null;
    }
    toPlainObject() {
        return {
            assignedUserId: this.assignedUserId,
            teamId: this.teamId,
            success: this.success,
            message: this.message,
            reasons: this.reasons,
            assignedAt: this.assignedAt.toISOString(),
            metadata: this.metadata,
            isSuccessful: this.isSuccessful(),
            confidenceScore: this.getConfidenceScore(),
            primaryReason: this.getPrimaryReason(),
        };
    }
    static fromPlainObject(data) {
        return new AssignmentResult(data.assignedUserId, data.teamId, data.success, data.message, data.reasons, new Date(data.assignedAt), data.metadata);
    }
    static success(assignedUserId, teamId, reasons, metadata = {}) {
        return new AssignmentResult(assignedUserId, teamId, true, 'Successfully assigned', reasons, new Date(), metadata);
    }
    static failure(message, reasons = [], metadata = {}) {
        return new AssignmentResult(null, null, false, message, reasons, new Date(), metadata);
    }
}
exports.AssignmentResult = AssignmentResult;
//# sourceMappingURL=assignment-result.vo.js.map