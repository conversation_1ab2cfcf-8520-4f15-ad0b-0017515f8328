"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversionFunnel = void 0;
class ConversionFunnel {
    steps;
    overallConversionRate;
    bottlenecks;
    recommendations;
    analyzedAt;
    constructor(steps, overallConversionRate, bottlenecks, recommendations, analyzedAt) {
        this.steps = steps;
        this.overallConversionRate = overallConversionRate;
        this.bottlenecks = bottlenecks;
        this.recommendations = recommendations;
        this.analyzedAt = analyzedAt;
        this.validateFunnel();
    }
    validateFunnel() {
        if (!this.steps || this.steps.length === 0) {
            throw new Error('Conversion funnel must have at least one step');
        }
        if (this.overallConversionRate < 0 || this.overallConversionRate > 100) {
            throw new Error('Overall conversion rate must be between 0 and 100');
        }
    }
    getWorstPerformingStep() {
        if (this.steps.length === 0)
            return null;
        return this.steps.reduce((worst, current) => current.dropOffRate > worst.dropOffRate ? current : worst);
    }
    getBestPerformingStep() {
        if (this.steps.length === 0)
            return null;
        return this.steps.reduce((best, current) => current.conversionRate > best.conversionRate ? current : best);
    }
    getTotalLeadsAtTop() {
        return this.steps.length > 0 ? this.steps[0].leadsCount : 0;
    }
    getTotalConversions() {
        return this.steps.length > 0 ? this.steps[this.steps.length - 1].leadsCount : 0;
    }
    getTotalValueProgression() {
        return this.steps.map(step => step.value);
    }
    getAverageTimeInFunnel() {
        if (this.steps.length === 0)
            return 0;
        const totalTime = this.steps.reduce((sum, step) => sum + step.averageTimeInStage, 0);
        return totalTime;
    }
    getEfficiencyScore() {
        if (this.steps.length === 0)
            return 0;
        let score = this.overallConversionRate;
        const bottleneckPenalty = this.bottlenecks.length * 5;
        score -= bottleneckPenalty;
        const conversionRates = this.steps.map(step => step.conversionRate).filter(rate => rate > 0);
        if (conversionRates.length > 1) {
            const variance = this.calculateVariance(conversionRates);
            const consistencyBonus = Math.max(0, 10 - variance / 10);
            score += consistencyBonus;
        }
        return Math.round(Math.max(0, Math.min(100, score)));
    }
    getStageAnalysis() {
        return this.steps.map((step, index) => ({
            stageId: step.stageId,
            stageName: step.stageName,
            position: index + 1,
            leadsCount: step.leadsCount,
            value: step.value,
            conversionRate: step.conversionRate,
            dropOffRate: step.dropOffRate,
            averageTimeInStage: step.averageTimeInStage,
            performance: this.getStagePerformance(step),
            recommendations: this.getStageRecommendations(step),
        }));
    }
    getHealthIndicators() {
        const worstStep = this.getWorstPerformingStep();
        const bestStep = this.getBestPerformingStep();
        return {
            overallHealth: this.getEfficiencyScore() >= 70 ? 'good' : this.getEfficiencyScore() >= 50 ? 'fair' : 'poor',
            criticalBottlenecks: this.bottlenecks.filter(b => b.severity === 'high').length,
            worstStage: worstStep?.stageName,
            bestStage: bestStep?.stageName,
            averageDropOff: this.calculateAverageDropOff(),
            timeToConvert: this.getAverageTimeInFunnel(),
        };
    }
    getImprovementOpportunities() {
        const opportunities = [];
        this.steps.forEach(step => {
            if (step.dropOffRate > 50) {
                opportunities.push({
                    type: 'high_dropoff',
                    stage: step.stageName,
                    impact: 'high',
                    description: `${step.stageName} has ${step.dropOffRate.toFixed(1)}% drop-off rate`,
                    recommendation: `Optimize ${step.stageName} process to reduce drop-off`,
                    potentialImprovement: this.calculatePotentialImprovement(step),
                });
            }
        });
        this.steps.forEach(step => {
            if (step.averageTimeInStage > 14) {
                opportunities.push({
                    type: 'slow_stage',
                    stage: step.stageName,
                    impact: 'medium',
                    description: `${step.stageName} takes ${step.averageTimeInStage} days on average`,
                    recommendation: `Streamline ${step.stageName} to reduce time`,
                    potentialImprovement: 'Faster pipeline velocity',
                });
            }
        });
        return opportunities;
    }
    toPlainObject() {
        return {
            steps: this.steps,
            overallConversionRate: this.overallConversionRate,
            bottlenecks: this.bottlenecks,
            recommendations: this.recommendations,
            efficiencyScore: this.getEfficiencyScore(),
            healthIndicators: this.getHealthIndicators(),
            stageAnalysis: this.getStageAnalysis(),
            improvementOpportunities: this.getImprovementOpportunities(),
            totalLeadsAtTop: this.getTotalLeadsAtTop(),
            totalConversions: this.getTotalConversions(),
            averageTimeInFunnel: this.getAverageTimeInFunnel(),
            analyzedAt: this.analyzedAt.toISOString(),
        };
    }
    static fromPlainObject(data) {
        return new ConversionFunnel(data.steps, data.overallConversionRate, data.bottlenecks, data.recommendations, new Date(data.analyzedAt));
    }
    calculateVariance(values) {
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
        return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
    }
    getStagePerformance(step) {
        if (step.conversionRate >= 80)
            return 'excellent';
        if (step.conversionRate >= 60)
            return 'good';
        if (step.conversionRate >= 40)
            return 'fair';
        return 'poor';
    }
    getStageRecommendations(step) {
        const recommendations = [];
        if (step.dropOffRate > 50) {
            recommendations.push('High drop-off rate - review stage requirements');
        }
        if (step.averageTimeInStage > 14) {
            recommendations.push('Long stage duration - streamline process');
        }
        if (step.conversionRate < 40) {
            recommendations.push('Low conversion rate - improve qualification criteria');
        }
        return recommendations;
    }
    calculateAverageDropOff() {
        const dropOffs = this.steps.map(step => step.dropOffRate).filter(rate => rate > 0);
        return dropOffs.length > 0 ? dropOffs.reduce((sum, rate) => sum + rate, 0) / dropOffs.length : 0;
    }
    calculatePotentialImprovement(step) {
        const improvement = step.dropOffRate * 0.3;
        return `Potential ${improvement.toFixed(1)}% improvement in overall conversion`;
    }
}
exports.ConversionFunnel = ConversionFunnel;
//# sourceMappingURL=conversion-funnel.vo.js.map