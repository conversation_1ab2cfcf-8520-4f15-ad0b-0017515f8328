{"version": 3, "file": "notification-delivery.vo.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/value-objects/notification-delivery.vo.ts"], "names": [], "mappings": ";;;AAMA,MAAa,oBAAoB;IAEb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IARlB,YACkB,EAAU,EACV,MAAc,EACd,gBAAwB,EACxB,OAA4B,EAC5B,MAAsB,EACtB,MAAY,EACZ,KAAc,EACd,WAAgC,EAAE;QAPlC,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAAQ;QACd,qBAAgB,GAAhB,gBAAgB,CAAQ;QACxB,YAAO,GAAP,OAAO,CAAqB;QAC5B,WAAM,GAAN,MAAM,CAAgB;QACtB,WAAM,GAAN,MAAM,CAAM;QACZ,UAAK,GAAL,KAAK,CAAS;QACd,aAAQ,GAAR,QAAQ,CAA0B;QAElD,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAKD,YAAY;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC;IACrC,CAAC;IAKD,QAAQ;QACN,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;IAClC,CAAC;IAKD,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;IACnC,CAAC;IAKD,mBAAmB;QACjB,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC9C,IAAI,CAAC,WAAW;YAAE,OAAO,CAAC,CAAC;QAE3B,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;IACjE,CAAC;IAKD,aAAa;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,CAAC,CAAC;IACvC,CAAC;IAKD,WAAW;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC;IAC5C,CAAC;IAKD,QAAQ;QACN,OAAO,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;IACrD,CAAC;IAKD,UAAU;QACR,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;YACjC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;YAChC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC;IACJ,CAAC;IAKD,aAAa;QACX,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACjC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;YACjC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;YAChC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;SAC3B,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,eAAe,CAAC,IAAS;QAC9B,OAAO,IAAI,oBAAoB,CAC7B,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,MAAM,EACX,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EACrB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,OAAO,CACZ,EAAU,EACV,MAAc,EACd,gBAAwB,EACxB,OAA4B,EAC5B,WAAgC,EAAE;QAElC,OAAO,IAAI,oBAAoB,CAC7B,EAAE,EACF,MAAM,EACN,gBAAgB,EAChB,OAAO,EACP,WAAW,EACX,IAAI,IAAI,EAAE,EACV,SAAS,EACT;YACE,GAAG,QAAQ;YACX,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACtC,CACF,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,OAAO,CACZ,EAAU,EACV,MAAc,EACd,gBAAwB,EACxB,OAA4B,EAC5B,KAAa,EACb,WAAgC,EAAE;QAElC,OAAO,IAAI,oBAAoB,CAC7B,EAAE,EACF,MAAM,EACN,gBAAgB,EAChB,OAAO,EACP,QAAQ,EACR,IAAI,IAAI,EAAE,EACV,KAAK,EACL,QAAQ,CACT,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,OAAO,CACZ,EAAU,EACV,MAAc,EACd,gBAAwB,EACxB,OAA4B,EAC5B,WAAgC,EAAE;QAElC,OAAO,IAAI,oBAAoB,CAC7B,EAAE,EACF,MAAM,EACN,gBAAgB,EAChB,OAAO,EACP,SAAS,EACT,IAAI,IAAI,EAAE,EACV,SAAS,EACT,QAAQ,CACT,CAAC;IACJ,CAAC;CACF;AA9MD,oDA8MC"}