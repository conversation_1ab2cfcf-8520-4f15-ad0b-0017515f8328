export declare class RevenueForecast {
    readonly expectedRevenue: number;
    readonly probability: number;
    readonly currency: string;
    constructor(expectedRevenue: number, probability: number, currency?: string);
    get weightedRevenue(): number;
    get confidenceLevel(): 'low' | 'medium' | 'high' | 'very-high';
    get isRealistic(): boolean;
    get isOptimistic(): boolean;
    get isConservative(): boolean;
    get isHighValue(): boolean;
    get riskLevel(): 'low' | 'medium' | 'high';
    getRevenueRange(): {
        min: number;
        max: number;
        expected: number;
    };
    getForecastCategory(): 'commit' | 'best-case' | 'pipeline' | 'upside';
    getMonthlyRecurringRevenue(isRecurring?: boolean, months?: number): number;
    getConfidenceColor(): string;
    formatRevenue(locale?: string): string;
    formatWeightedRevenue(locale?: string): string;
    withProbability(newProbability: number): RevenueForecast;
    withExpectedRevenue(newRevenue: number): RevenueForecast;
    private validateExpectedRevenue;
    private validateProbability;
    private validateCurrency;
    equals(other: RevenueForecast): boolean;
    toPlainObject(): {
        expectedRevenue: number;
        probability: number;
        currency: string;
        weightedRevenue: number;
        confidenceLevel: "high" | "low" | "medium" | "very-high";
        isRealistic: boolean;
        isOptimistic: boolean;
        isConservative: boolean;
        isHighValue: boolean;
        riskLevel: "high" | "low" | "medium";
        forecastCategory: "commit" | "best-case" | "pipeline" | "upside";
        revenueRange: {
            min: number;
            max: number;
            expected: number;
        };
        confidenceColor: string;
        formattedRevenue: string;
        formattedWeightedRevenue: string;
    };
    toString(): string;
    toJSON(): {
        expectedRevenue: number;
        probability: number;
        currency: string;
        weightedRevenue: number;
    };
}
