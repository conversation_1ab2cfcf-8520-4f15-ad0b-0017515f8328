{"version": 3, "file": "revenue-forecast.vo.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/value-objects/revenue-forecast.vo.ts"], "names": [], "mappings": ";;;AAIA,MAAa,eAAe;IAER;IACA;IACA;IAHlB,YACkB,eAAuB,EACvB,WAAmB,EACnB,WAAmB,KAAK;QAFxB,oBAAe,GAAf,eAAe,CAAQ;QACvB,gBAAW,GAAX,WAAW,CAAQ;QACnB,aAAQ,GAAR,QAAQ,CAAgB;QAExC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAKD,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC;IACzD,CAAC;IAKD,IAAI,eAAe;QACjB,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE;YAAE,OAAO,WAAW,CAAC;QAC/C,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QAC1C,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE;YAAE,OAAO,QAAQ,CAAC;QAC5C,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,IAAI,GAAG,CAAC;IACzD,CAAC;IAKD,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;IAChC,CAAC;IAKD,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;IAChC,CAAC;IAKD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,eAAe,IAAI,KAAK,CAAC;IACvC,CAAC;IAKD,IAAI,SAAS;QACX,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE;YAAE,OAAO,KAAK,CAAC;QACzC,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE;YAAE,OAAO,QAAQ,CAAC;QAC5C,OAAO,MAAM,CAAC;IAChB,CAAC;IAKD,eAAe;QACb,MAAM,QAAQ,GAAG,GAAG,CAAC;QACrB,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;QAClD,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;QAElD,OAAO;YACL,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;YACrB,GAAG;YACH,QAAQ,EAAE,IAAI,CAAC,eAAe;SAC/B,CAAC;IACJ,CAAC;IAKD,mBAAmB;QACjB,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE;YAAE,OAAO,QAAQ,CAAC;QAC5C,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE;YAAE,OAAO,WAAW,CAAC;QAC/C,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE;YAAE,OAAO,UAAU,CAAC;QAC9C,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKD,0BAA0B,CAAC,cAAuB,KAAK,EAAE,SAAiB,EAAE;QAC1E,IAAI,CAAC,WAAW;YAAE,OAAO,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;IACvC,CAAC;IAKD,kBAAkB;QAChB,QAAQ,IAAI,CAAC,eAAe,EAAE,CAAC;YAC7B,KAAK,WAAW,CAAC,CAAC,OAAO,SAAS,CAAC;YACnC,KAAK,MAAM,CAAC,CAAC,OAAO,SAAS,CAAC;YAC9B,KAAK,QAAQ,CAAC,CAAC,OAAO,SAAS,CAAC;YAChC,KAAK,KAAK,CAAC,CAAC,OAAO,SAAS,CAAC;YAC7B,OAAO,CAAC,CAAC,OAAO,SAAS,CAAC;QAC5B,CAAC;IACH,CAAC;IAKD,aAAa,CAAC,SAAiB,OAAO;QACpC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YACnC,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,qBAAqB,EAAE,CAAC;YACxB,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAClC,CAAC;IAKD,qBAAqB,CAAC,SAAiB,OAAO;QAC5C,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YACnC,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,qBAAqB,EAAE,CAAC;YACxB,qBAAqB,EAAE,CAAC;SACzB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAClC,CAAC;IAKD,eAAe,CAAC,cAAsB;QACpC,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAClF,CAAC;IAKD,mBAAmB,CAAC,UAAkB;QACpC,OAAO,IAAI,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1E,CAAC;IAKO,uBAAuB;QAC7B,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAKO,mBAAmB;QACzB,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAKO,gBAAgB;QACtB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAsB;QAC3B,OAAO,CACL,IAAI,CAAC,eAAe,KAAK,KAAK,CAAC,eAAe;YAC9C,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC,WAAW;YACtC,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,CACjC,CAAC;IACJ,CAAC;IAKD,aAAa;QACX,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAErC,OAAO;YACL,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,YAAY,EAAE,KAAK;YACnB,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,gBAAgB,EAAE,IAAI,CAAC,aAAa,EAAE;YACtC,wBAAwB,EAAE,IAAI,CAAC,qBAAqB,EAAE;SACvD,CAAC;IACJ,CAAC;IAKD,QAAQ;QACN,OAAO,GAAG,IAAI,CAAC,aAAa,EAAE,KAAK,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,qBAAqB,EAAE,GAAG,CAAC;IAC5F,CAAC;IAKD,MAAM;QACJ,OAAO;YACL,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC;IACJ,CAAC;CACF;AA5OD,0CA4OC"}