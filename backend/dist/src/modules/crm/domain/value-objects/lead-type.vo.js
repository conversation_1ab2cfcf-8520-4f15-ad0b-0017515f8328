"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadType = void 0;
class LeadType {
    value;
    label;
    description;
    static VALID_TYPES = ['lead', 'opportunity'];
    static LEAD = new LeadType('lead', 'Lead', 'A potential customer that needs qualification');
    static OPPORTUNITY = new LeadType('opportunity', 'Opportunity', 'A qualified lead with sales potential');
    static TYPE_MAP = new Map([
        ['lead', LeadType.LEAD],
        ['opportunity', LeadType.OPPORTUNITY],
    ]);
    constructor(value, label, description) {
        this.value = value;
        this.label = label;
        this.description = description;
        if (!LeadType.VALID_TYPES.includes(value)) {
            throw new Error(`Invalid lead type: ${value}. Must be 'lead' or 'opportunity'.`);
        }
    }
    static fromValue(value) {
        const normalizedValue = value.toLowerCase().trim();
        const type = LeadType.TYPE_MAP.get(normalizedValue);
        if (!type) {
            throw new Error(`Invalid lead type: ${value}. Must be 'lead' or 'opportunity'.`);
        }
        return type;
    }
    static getAllTypes() {
        return [LeadType.LEAD, LeadType.OPPORTUNITY];
    }
    isLead() {
        return this.value === 'lead';
    }
    isOpportunity() {
        return this.value === 'opportunity';
    }
    canConvertToOpportunity() {
        return this.isLead();
    }
    requiresQualification() {
        return this.isLead();
    }
    canHaveRevenueForecast() {
        return this.isOpportunity();
    }
    canHaveProbability() {
        return this.isOpportunity();
    }
    canBeInPipeline() {
        return this.isOpportunity();
    }
    getDefaultProbability() {
        return this.isOpportunity() ? 10 : 0;
    }
    getColor() {
        switch (this.value) {
            case 'lead': return '#6c757d';
            case 'opportunity': return '#28a745';
            default: return '#6c757d';
        }
    }
    getCssClass() {
        return `type-${this.value}`;
    }
    getIcon() {
        switch (this.value) {
            case 'lead': return 'user-plus';
            case 'opportunity': return 'dollar-sign';
            default: return 'user';
        }
    }
    getBadgeVariant() {
        switch (this.value) {
            case 'lead': return 'secondary';
            case 'opportunity': return 'success';
            default: return 'secondary';
        }
    }
    equals(other) {
        return this.value === other.value;
    }
    toPlainObject() {
        return {
            value: this.value,
            label: this.label,
            description: this.description,
            color: this.getColor(),
            cssClass: this.getCssClass(),
            icon: this.getIcon(),
            badgeVariant: this.getBadgeVariant(),
            isLead: this.isLead(),
            isOpportunity: this.isOpportunity(),
            canConvertToOpportunity: this.canConvertToOpportunity(),
            requiresQualification: this.requiresQualification(),
            canHaveRevenueForecast: this.canHaveRevenueForecast(),
            canHaveProbability: this.canHaveProbability(),
            canBeInPipeline: this.canBeInPipeline(),
            defaultProbability: this.getDefaultProbability(),
        };
    }
    toString() {
        return this.label;
    }
    toJSON() {
        return this.value;
    }
}
exports.LeadType = LeadType;
//# sourceMappingURL=lead-type.vo.js.map