"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScoringFactor = void 0;
class ScoringFactor {
    name;
    score;
    weight;
    description;
    metadata;
    weightedScore;
    constructor(name, score, weight, description, metadata = {}) {
        this.name = name;
        this.score = score;
        this.weight = weight;
        this.description = description;
        this.metadata = metadata;
        this.validateFactor();
        this.weightedScore = this.score * this.weight;
    }
    validateFactor() {
        if (!this.name || this.name.trim().length === 0) {
            throw new Error('Scoring factor must have a name');
        }
        if (this.score < 0 || this.score > 100) {
            throw new Error('Scoring factor score must be between 0 and 100');
        }
        if (this.weight < 0 || this.weight > 1) {
            throw new Error('Scoring factor weight must be between 0 and 1');
        }
    }
    isStrength(threshold = 70) {
        return this.score >= threshold;
    }
    isWeakness(threshold = 50) {
        return this.score < threshold;
    }
    getImpactLevel() {
        if (this.weight >= 0.2)
            return 'high';
        if (this.weight >= 0.1)
            return 'medium';
        return 'low';
    }
    getPerformanceLevel() {
        if (this.score >= 80)
            return 'excellent';
        if (this.score >= 60)
            return 'good';
        if (this.score >= 40)
            return 'fair';
        return 'poor';
    }
    getContribution() {
        return this.weightedScore;
    }
    getImprovementPotential(targetScore = 80) {
        if (this.score >= targetScore)
            return 0;
        return (targetScore - this.score) * this.weight;
    }
    getDisplayName() {
        const nameMap = new Map([
            ['contact_completeness', 'Contact Completeness'],
            ['revenue_potential', 'Revenue Potential'],
            ['engagement_level', 'Engagement Level'],
            ['company_fit', 'Company Fit'],
            ['behavioral_signals', 'Behavioral Signals'],
            ['source_quality', 'Source Quality'],
            ['industry_fit', 'Industry Fit'],
            ['geographic_fit', 'Geographic Fit'],
            ['timing_signals', 'Timing Signals'],
            ['technology_fit', 'Technology Fit'],
        ]);
        return nameMap.get(this.name) || this.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
    getRecommendations() {
        const recommendations = [];
        if (this.isWeakness()) {
            switch (this.name) {
                case 'contact_completeness':
                    recommendations.push('Complete missing contact information');
                    recommendations.push('Verify email and phone number');
                    recommendations.push('Add company details and website');
                    break;
                case 'revenue_potential':
                    recommendations.push('Qualify budget and timeline');
                    recommendations.push('Understand decision-making process');
                    recommendations.push('Identify economic buyer');
                    break;
                case 'engagement_level':
                    recommendations.push('Send relevant content and resources');
                    recommendations.push('Schedule a discovery call');
                    recommendations.push('Invite to webinar or demo');
                    break;
                case 'company_fit':
                    recommendations.push('Research company and industry');
                    recommendations.push('Identify use cases and pain points');
                    recommendations.push('Find mutual connections');
                    break;
                case 'behavioral_signals':
                    recommendations.push('Monitor website activity');
                    recommendations.push('Track email engagement');
                    recommendations.push('Note content preferences');
                    break;
                case 'source_quality':
                    recommendations.push('Verify lead source accuracy');
                    recommendations.push('Improve lead capture forms');
                    recommendations.push('Focus on higher-quality channels');
                    break;
                default:
                    recommendations.push(`Improve ${this.getDisplayName().toLowerCase()}`);
                    break;
            }
        }
        return recommendations;
    }
    toPlainObject() {
        return {
            name: this.name,
            displayName: this.getDisplayName(),
            score: this.score,
            weight: this.weight,
            weightedScore: this.weightedScore,
            description: this.description,
            impactLevel: this.getImpactLevel(),
            performanceLevel: this.getPerformanceLevel(),
            isStrength: this.isStrength(),
            isWeakness: this.isWeakness(),
            recommendations: this.getRecommendations(),
            metadata: this.metadata,
        };
    }
    static fromPlainObject(data) {
        return new ScoringFactor(data.name, data.score, data.weight, data.description, data.metadata);
    }
    static compare(a, b) {
        return b.weightedScore - a.weightedScore;
    }
    static createDefaults() {
        return [
            new ScoringFactor('contact_completeness', 50, 0.20, 'Completeness of contact information'),
            new ScoringFactor('revenue_potential', 50, 0.25, 'Potential revenue value'),
            new ScoringFactor('engagement_level', 50, 0.20, 'Level of engagement with content'),
            new ScoringFactor('company_fit', 50, 0.15, 'Fit with ideal customer profile'),
            new ScoringFactor('behavioral_signals', 50, 0.10, 'Behavioral indicators of interest'),
            new ScoringFactor('source_quality', 50, 0.10, 'Quality of lead source'),
        ];
    }
    static isValid(data) {
        return (typeof data.name === 'string' &&
            data.name.trim().length > 0 &&
            typeof data.score === 'number' &&
            data.score >= 0 &&
            data.score <= 100 &&
            typeof data.weight === 'number' &&
            data.weight >= 0 &&
            data.weight <= 1);
    }
    static getTotalWeight(factors) {
        return factors.reduce((total, factor) => total + factor.weight, 0);
    }
    static normalizeWeights(factors) {
        const totalWeight = ScoringFactor.getTotalWeight(factors);
        if (totalWeight === 0) {
            throw new Error('Cannot normalize factors with zero total weight');
        }
        return factors.map(factor => new ScoringFactor(factor.name, factor.score, factor.weight / totalWeight, factor.description, factor.metadata));
    }
}
exports.ScoringFactor = ScoringFactor;
//# sourceMappingURL=scoring-factor.vo.js.map