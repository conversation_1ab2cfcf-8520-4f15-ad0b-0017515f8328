export declare class NotificationTemplate {
    readonly type: string;
    readonly name: string;
    readonly shortDescription: string;
    readonly longDescription: string;
    readonly channelTemplates: Record<string, ChannelTemplate>;
    readonly variables: TemplateVariable[];
    readonly metadata: Record<string, any>;
    constructor(type: string, name: string, shortDescription: string, longDescription: string, channelTemplates: Record<string, ChannelTemplate>, variables?: TemplateVariable[], metadata?: Record<string, any>);
    private validateTemplate;
    render(data: Record<string, any>): RenderedNotification;
    renderForChannel(channel: string, data: Record<string, any>): any;
    getSupportedChannels(): string[];
    supportsChannel(channel: string): boolean;
    getVariables(): TemplateVariable[];
    getRequiredVariables(): TemplateVariable[];
    validateData(data: Record<string, any>): ValidationResult;
    getPreview(): RenderedNotification;
    clone(modifications: Partial<{
        type: string;
        name: string;
        shortDescription: string;
        longDescription: string;
        channelTemplates: Record<string, ChannelTemplate>;
        variables: TemplateVariable[];
        metadata: Record<string, any>;
    }>): NotificationTemplate;
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): NotificationTemplate;
    private renderChannel;
    private interpolateString;
    private isValidType;
    private generateSampleData;
}
export interface ChannelTemplate {
    subject?: string;
    title?: string;
    body?: string;
    template?: string;
    html?: string;
    text?: string;
    [key: string]: any;
}
export interface TemplateVariable {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object' | 'any';
    description: string;
    required: boolean;
    defaultValue?: any;
    validation?: {
        pattern?: string;
        minLength?: number;
        maxLength?: number;
        min?: number;
        max?: number;
    };
}
export interface RenderedNotification {
    type: string;
    channels: Record<string, any>;
    renderedAt: Date;
    data: Record<string, any>;
}
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}
