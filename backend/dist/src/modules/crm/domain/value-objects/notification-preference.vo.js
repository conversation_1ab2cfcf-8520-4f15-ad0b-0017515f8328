"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationPreference = void 0;
const notification_channel_enum_1 = require("../enums/notification-channel.enum");
class NotificationPreference {
    notificationType;
    enabled;
    channels;
    settings;
    constructor(notificationType, enabled, channels, settings = {}) {
        this.notificationType = notificationType;
        this.enabled = enabled;
        this.channels = channels;
        this.settings = settings;
        this.validatePreference();
    }
    validatePreference() {
        if (!this.notificationType || this.notificationType.trim().length === 0) {
            throw new Error('Notification preference must have a type');
        }
        if (!Array.isArray(this.channels)) {
            throw new Error('Notification preference channels must be an array');
        }
    }
    isChannelEnabled(channel) {
        return this.enabled && this.channels.includes(channel);
    }
    appliesTo(notificationType) {
        return this.notificationType === 'all' || this.notificationType === notificationType;
    }
    respectsQuietHours() {
        return this.settings.quietHours !== undefined;
    }
    isQuietTime() {
        if (!this.settings.quietHours)
            return false;
        const now = new Date();
        const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
        const { start, end } = this.settings.quietHours;
        if (start > end) {
            return currentTime >= start || currentTime <= end;
        }
        return currentTime >= start && currentTime <= end;
    }
    getFrequency() {
        return this.settings.frequency || 'immediate';
    }
    toPlainObject() {
        return {
            notificationType: this.notificationType,
            enabled: this.enabled,
            channels: this.channels,
            settings: this.settings,
            respectsQuietHours: this.respectsQuietHours(),
            frequency: this.getFrequency(),
        };
    }
    static fromPlainObject(data) {
        return new NotificationPreference(data.notificationType, data.enabled, data.channels, data.settings);
    }
    static createDefaults() {
        return [
            new NotificationPreference('all', true, [notification_channel_enum_1.NotificationChannel.EMAIL, notification_channel_enum_1.NotificationChannel.IN_APP], {
                quietHours: { start: '22:00', end: '08:00' },
                frequency: 'immediate',
            }),
            new NotificationPreference('lead_assigned', true, [notification_channel_enum_1.NotificationChannel.EMAIL, notification_channel_enum_1.NotificationChannel.IN_APP, notification_channel_enum_1.NotificationChannel.PUSH], { frequency: 'immediate' }),
            new NotificationPreference('opportunity_updated', true, [notification_channel_enum_1.NotificationChannel.EMAIL, notification_channel_enum_1.NotificationChannel.IN_APP], { frequency: 'immediate' }),
            new NotificationPreference('pipeline_alert', true, [notification_channel_enum_1.NotificationChannel.EMAIL, notification_channel_enum_1.NotificationChannel.SLACK], { frequency: 'immediate' }),
            new NotificationPreference('scheduled_report', true, [notification_channel_enum_1.NotificationChannel.EMAIL], { frequency: 'batched' }),
        ];
    }
}
exports.NotificationPreference = NotificationPreference;
//# sourceMappingURL=notification-preference.vo.js.map