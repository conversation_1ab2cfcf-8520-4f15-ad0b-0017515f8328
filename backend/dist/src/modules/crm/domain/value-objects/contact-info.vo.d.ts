export declare class ContactInfo {
    readonly email?: string | undefined;
    readonly phone?: string | undefined;
    readonly mobile?: string | undefined;
    readonly website?: string | undefined;
    readonly company?: string | undefined;
    readonly jobTitle?: string | undefined;
    readonly address?: string | undefined;
    readonly city?: string | undefined;
    readonly state?: string | undefined;
    readonly country?: string | undefined;
    readonly postalCode?: string | undefined;
    readonly socialMedia?: SocialMediaInfo | undefined;
    constructor(email?: string | undefined, phone?: string | undefined, mobile?: string | undefined, website?: string | undefined, company?: string | undefined, jobTitle?: string | undefined, address?: string | undefined, city?: string | undefined, state?: string | undefined, country?: string | undefined, postalCode?: string | undefined, socialMedia?: SocialMediaInfo | undefined);
    private validateContactInfo;
    hasAnyInfo(): boolean;
    hasCompleteAddress(): boolean;
    hasBusinessInfo(): boolean;
    getCompletenessScore(): number;
    getPrimaryContactMethod(): 'email' | 'phone' | 'mobile' | 'website' | null;
    getFormattedAddress(): string;
    getDisplayName(): string;
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): ContactInfo;
    static createEmpty(): ContactInfo;
    mergeWith(other: ContactInfo): ContactInfo;
    private isValidEmail;
    private isValidWebsite;
}
export interface SocialMediaInfo {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
    instagram?: string;
    youtube?: string;
    github?: string;
    [platform: string]: string | undefined;
}
