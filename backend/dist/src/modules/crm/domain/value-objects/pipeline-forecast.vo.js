"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PipelineForecast = void 0;
class PipelineForecast {
    currentPipelineValue;
    weightedPipelineValue;
    forecasts;
    trendAnalysis;
    riskFactors;
    generatedAt;
    constructor(currentPipelineValue, weightedPipelineValue, forecasts, trendAnalysis, riskFactors, generatedAt) {
        this.currentPipelineValue = currentPipelineValue;
        this.weightedPipelineValue = weightedPipelineValue;
        this.forecasts = forecasts;
        this.trendAnalysis = trendAnalysis;
        this.riskFactors = riskFactors;
        this.generatedAt = generatedAt;
        this.validateForecast();
    }
    validateForecast() {
        if (this.currentPipelineValue < 0) {
            throw new Error('Current pipeline value cannot be negative');
        }
        if (this.weightedPipelineValue < 0) {
            throw new Error('Weighted pipeline value cannot be negative');
        }
        if (!this.forecasts || this.forecasts.length === 0) {
            throw new Error('Forecast must include at least one period');
        }
    }
    getForecastForPeriod(days) {
        return this.forecasts.find(f => f.period === `${days} days`) || null;
    }
    getNext30DayForecast() {
        return this.getForecastForPeriod(30);
    }
    getNextQuarterForecast() {
        return this.getForecastForPeriod(90);
    }
    getOverallConfidence() {
        if (this.forecasts.length === 0)
            return 0;
        const totalConfidence = this.forecasts.reduce((sum, f) => sum + f.confidence, 0);
        return Math.round(totalConfidence / this.forecasts.length);
    }
    getAccuracyGrade() {
        const confidence = this.getOverallConfidence();
        if (confidence >= 90)
            return 'A+';
        if (confidence >= 80)
            return 'A';
        if (confidence >= 70)
            return 'B';
        if (confidence >= 60)
            return 'C';
        if (confidence >= 50)
            return 'D';
        return 'F';
    }
    getGrowthProjection() {
        const next30 = this.getNext30DayForecast();
        const next90 = this.getNextQuarterForecast();
        if (!next30 || !next90) {
            return {
                monthlyGrowth: 0,
                quarterlyGrowth: 0,
                trend: 'stable',
            };
        }
        const monthlyGrowth = this.currentPipelineValue > 0
            ? ((next30.expectedValue - this.currentPipelineValue) / this.currentPipelineValue) * 100
            : 0;
        const quarterlyGrowth = this.currentPipelineValue > 0
            ? ((next90.expectedValue - this.currentPipelineValue) / this.currentPipelineValue) * 100
            : 0;
        let trend = 'stable';
        if (quarterlyGrowth > 5)
            trend = 'growing';
        else if (quarterlyGrowth < -5)
            trend = 'declining';
        return {
            monthlyGrowth,
            quarterlyGrowth,
            trend,
        };
    }
    getRiskAssessment() {
        const riskLevel = this.calculateRiskLevel();
        const confidence = this.getOverallConfidence();
        return {
            level: riskLevel,
            factors: this.riskFactors,
            confidence,
            mitigation: this.generateMitigationStrategies(),
        };
    }
    getScenarioAnalysis() {
        const scenarios = {};
        this.forecasts.forEach(forecast => {
            const period = forecast.period;
            scenarios[`${period}_optimistic`] = forecast.scenarios.optimistic;
            scenarios[`${period}_realistic`] = forecast.scenarios.realistic;
            scenarios[`${period}_pessimistic`] = forecast.scenarios.pessimistic;
        });
        return {
            scenarios,
            mostLikely: this.getMostLikelyScenario(),
            bestCase: this.getBestCaseScenario(),
            worstCase: this.getWorstCaseScenario(),
        };
    }
    getForecastSummary() {
        const next30 = this.getNext30DayForecast();
        const growth = this.getGrowthProjection();
        const risk = this.getRiskAssessment();
        return {
            currentValue: this.currentPipelineValue,
            weightedValue: this.weightedPipelineValue,
            next30DayValue: next30?.expectedValue || 0,
            next30DayClosures: next30?.expectedClosures || 0,
            confidence: this.getOverallConfidence(),
            accuracyGrade: this.getAccuracyGrade(),
            trend: growth.trend,
            monthlyGrowth: growth.monthlyGrowth,
            riskLevel: risk.level,
            riskFactorCount: this.riskFactors.length,
        };
    }
    isReliable() {
        return this.getOverallConfidence() >= 70 && this.riskFactors.length <= 3;
    }
    getForecastAlerts() {
        const alerts = [];
        if (this.getOverallConfidence() < 60) {
            alerts.push({
                type: 'low_confidence',
                severity: 'warning',
                message: 'Forecast confidence is below 60%',
                recommendation: 'Review pipeline data quality and update probabilities',
            });
        }
        if (this.riskFactors.length > 5) {
            alerts.push({
                type: 'high_risk',
                severity: 'error',
                message: `${this.riskFactors.length} risk factors identified`,
                recommendation: 'Address critical risk factors to improve forecast reliability',
            });
        }
        const growth = this.getGrowthProjection();
        if (growth.trend === 'declining' && growth.quarterlyGrowth < -10) {
            alerts.push({
                type: 'declining_trend',
                severity: 'warning',
                message: 'Pipeline showing declining trend',
                recommendation: 'Increase lead generation and improve conversion rates',
            });
        }
        return alerts;
    }
    toPlainObject() {
        return {
            currentPipelineValue: this.currentPipelineValue,
            weightedPipelineValue: this.weightedPipelineValue,
            forecasts: this.forecasts,
            trendAnalysis: this.trendAnalysis,
            riskFactors: this.riskFactors,
            overallConfidence: this.getOverallConfidence(),
            accuracyGrade: this.getAccuracyGrade(),
            growthProjection: this.getGrowthProjection(),
            riskAssessment: this.getRiskAssessment(),
            scenarioAnalysis: this.getScenarioAnalysis(),
            forecastSummary: this.getForecastSummary(),
            forecastAlerts: this.getForecastAlerts(),
            isReliable: this.isReliable(),
            generatedAt: this.generatedAt.toISOString(),
        };
    }
    static fromPlainObject(data) {
        return new PipelineForecast(data.currentPipelineValue, data.weightedPipelineValue, data.forecasts, data.trendAnalysis, data.riskFactors, new Date(data.generatedAt));
    }
    calculateRiskLevel() {
        const riskCount = this.riskFactors.length;
        const confidence = this.getOverallConfidence();
        if (riskCount > 5 || confidence < 50)
            return 'high';
        if (riskCount > 2 || confidence < 70)
            return 'medium';
        return 'low';
    }
    generateMitigationStrategies() {
        const strategies = [];
        if (this.riskFactors.includes('Market uncertainty')) {
            strategies.push('Diversify target markets and customer segments');
        }
        if (this.riskFactors.includes('Seasonal variations')) {
            strategies.push('Develop counter-seasonal products or services');
        }
        if (this.getOverallConfidence() < 70) {
            strategies.push('Improve data quality and update lead probabilities');
        }
        return strategies;
    }
    getMostLikelyScenario() {
        const next30 = this.getNext30DayForecast();
        return next30 ? `${next30.scenarios.realistic} in 30 days` : 'No forecast available';
    }
    getBestCaseScenario() {
        const next30 = this.getNext30DayForecast();
        return next30 ? `${next30.scenarios.optimistic} in 30 days` : 'No forecast available';
    }
    getWorstCaseScenario() {
        const next30 = this.getNext30DayForecast();
        return next30 ? `${next30.scenarios.pessimistic} in 30 days` : 'No forecast available';
    }
}
exports.PipelineForecast = PipelineForecast;
//# sourceMappingURL=pipeline-forecast.vo.js.map