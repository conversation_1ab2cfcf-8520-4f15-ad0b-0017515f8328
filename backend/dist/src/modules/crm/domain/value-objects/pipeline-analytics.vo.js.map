{"version": 3, "file": "pipeline-analytics.vo.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/value-objects/pipeline-analytics.vo.ts"], "names": [], "mappings": ";;;AAAA,+DAAwD;AACxD,iEAA0D;AAC1D,iEAA0D;AAC1D,qEAA8D;AAM9D,MAAa,iBAAiB;IAEV;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IARlB,YACkB,OAAwB,EACxB,gBAAkC,EAClC,QAA0B,EAC1B,WAA+B,EAC/B,MAAW,EACX,WAAgB,EAChB,WAAiB,EACjB,WAAgC,EAAE;QAPlC,YAAO,GAAP,OAAO,CAAiB;QACxB,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,aAAQ,GAAR,QAAQ,CAAkB;QAC1B,gBAAW,GAAX,WAAW,CAAoB;QAC/B,WAAM,GAAN,MAAM,CAAK;QACX,gBAAW,GAAX,WAAW,CAAK;QAChB,gBAAW,GAAX,WAAW,CAAM;QACjB,aAAQ,GAAR,QAAQ,CAA0B;QAElD,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAKD,cAAc;QACZ,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAClD,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChD,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACpD,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAGxD,MAAM,WAAW,GAAG,CAClB,YAAY,GAAG,GAAG;YAClB,WAAW,GAAG,IAAI;YAClB,aAAa,GAAG,IAAI;YACpB,eAAe,GAAG,GAAG,CACtB,CAAC;QAEF,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IAKD,cAAc;QACZ,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACpC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,IAAI,CAAC;QAC7B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,OAAO,GAAG,CAAC;IACb,CAAC;IAKD,cAAc;QACZ,MAAM,QAAQ,GAAa,EAAE,CAAC;QAG9B,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC;YACrC,QAAQ,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACzD,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC;YAC5C,QAAQ,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACrD,CAAC;QAGD,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,KAAK,EAAE,CAAC;YAC1C,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC5C,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,IAAI,EAAE,CAAC;YAChD,QAAQ,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACrD,CAAC;QAGD,MAAM,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC;QAC5F,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,QAAQ,CAAC,IAAI,CAAC,GAAG,mBAAmB,CAAC,MAAM,gCAAgC,CAAC,CAAC;QAC/E,CAAC;QAGD,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC;QACzD,IAAI,aAAa,KAAK,WAAW,EAAE,CAAC;YAClC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC1D,CAAC;aAAM,IAAI,aAAa,KAAK,WAAW,EAAE,CAAC;YACzC,QAAQ,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKD,qBAAqB;QACnB,MAAM,OAAO,GAAa,EAAE,CAAC;QAG7B,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;QAC5D,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACxD,CAAC;QAGD,MAAM,sBAAsB,GAAG,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC;QACvE,IAAI,sBAAsB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,YAAY,sBAAsB,CAAC,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC;QACxE,CAAC;QAGD,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,EAAE,EAAE,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAChD,CAAC;QAGD,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzC,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;IAC/B,CAAC;IAKD,wBAAwB;QACtB,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;YAC3C,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;YAC/C,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,UAAU,IAAI,CAAC;YAC7D,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM;YACpD,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,gBAAgB,EAAE,KAAK,IAAI,QAAQ;SACjE,CAAC;IACJ,CAAC;IAKD,OAAO,CAAC,cAAsB,EAAE;QAC9B,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAC9E,OAAO,QAAQ,IAAI,WAAW,CAAC;IACjC,CAAC;IAKD,mBAAmB;QACjB,OAAO;YACL,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;gBACnC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;gBAC3C,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;gBACtC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;aACnC;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC;gBACrE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,gBAAgB,EAAE,KAAK;aAC5C;YACD,MAAM,EAAE;gBACN,mBAAmB,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM;gBAC3F,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM;aAC9C;YACD,eAAe,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;SAC1D,CAAC;IACJ,CAAC;IAKD,aAAa;QACX,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;YACrC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE;YACvD,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;YACvC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;YAC7C,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YAChD,qBAAqB,EAAE,IAAI,CAAC,wBAAwB,EAAE;YACtD,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;YAC3C,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,eAAe,CAAC,IAAS;QAC9B,OAAO,IAAI,iBAAiB,CAC1B,qCAAe,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAC7C,uCAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,EACvD,uCAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAC/C,2CAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,EACpD,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,WAAW,EAChB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAC1B,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAKO,qBAAqB;QAC3B,IAAI,KAAK,GAAG,EAAE,CAAC;QAGf,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,EAAE;YAAE,KAAK,IAAI,EAAE,CAAC;aAC9C,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,EAAE;YAAE,KAAK,IAAI,EAAE,CAAC;aACnD,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,CAAC;YAAE,KAAK,IAAI,EAAE,CAAC;QAGtD,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,KAAK;YAAE,KAAK,IAAI,EAAE,CAAC;aACnD,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,IAAI;YAAE,KAAK,IAAI,CAAC,CAAC;aACtD,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,IAAI;YAAE,KAAK,IAAI,EAAE,CAAC;QAG3D,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,EAAE;YAAE,KAAK,IAAI,EAAE,CAAC;aACjD,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,EAAE;YAAE,KAAK,IAAI,CAAC,CAAC;aACrD,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,GAAG;YAAE,KAAK,IAAI,EAAE,CAAC;QAG3D,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,GAAG;YAAE,KAAK,IAAI,CAAC,CAAC;aAC1C,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;YAAE,KAAK,IAAI,EAAE,CAAC;QAEnD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAC3C,CAAC;IAEO,oBAAoB;QAC1B,IAAI,KAAK,GAAG,EAAE,CAAC;QAGf,IAAI,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,IAAI,EAAE;YAAE,KAAK,IAAI,EAAE,CAAC;aAC9D,IAAI,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,IAAI,EAAE;YAAE,KAAK,IAAI,EAAE,CAAC;aACnE,IAAI,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,GAAG,CAAC;YAAE,KAAK,IAAI,EAAE,CAAC;QAGtE,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;QACvE,KAAK,IAAI,iBAAiB,CAAC;QAE3B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAC3C,CAAC;IAEO,sBAAsB;QAC5B,IAAI,KAAK,GAAG,EAAE,CAAC;QAGf,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC;QACzH,IAAI,aAAa,IAAI,EAAE;YAAE,KAAK,IAAI,EAAE,CAAC;aAChC,IAAI,aAAa,IAAI,EAAE;YAAE,KAAK,IAAI,EAAE,CAAC;aACrC,IAAI,aAAa,GAAG,EAAE;YAAE,KAAK,IAAI,EAAE,CAAC;QAGzC,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;QACzD,KAAK,IAAI,WAAW,CAAC;QAErB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAC3C,CAAC;IAEO,wBAAwB;QAC9B,IAAI,KAAK,GAAG,GAAG,CAAC;QAGhB,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAChD,QAAQ,UAAU,CAAC,QAAQ,EAAE,CAAC;gBAC5B,KAAK,MAAM;oBAAE,KAAK,IAAI,EAAE,CAAC;oBAAC,MAAM;gBAChC,KAAK,QAAQ;oBAAE,KAAK,IAAI,EAAE,CAAC;oBAAC,MAAM;gBAClC,KAAK,KAAK;oBAAE,KAAK,IAAI,CAAC,CAAC;oBAAC,MAAM;YAChC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC5B,CAAC;CACF;AAtSD,8CAsSC"}