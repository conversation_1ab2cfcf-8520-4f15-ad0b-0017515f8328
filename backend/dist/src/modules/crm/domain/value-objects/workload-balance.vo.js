"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkloadBalance = void 0;
class WorkloadBalance {
    userId;
    totalLeads;
    activeLeads;
    totalValue;
    averageLeadAge;
    lastUpdated;
    metadata;
    constructor(userId, totalLeads, activeLeads, totalValue, averageLeadAge, lastUpdated, metadata = {}) {
        this.userId = userId;
        this.totalLeads = totalLeads;
        this.activeLeads = activeLeads;
        this.totalValue = totalValue;
        this.averageLeadAge = averageLeadAge;
        this.lastUpdated = lastUpdated;
        this.metadata = metadata;
        this.validateWorkload();
    }
    validateWorkload() {
        if (!this.userId || this.userId.trim().length === 0) {
            throw new Error('Workload balance must have a user ID');
        }
        if (this.totalLeads < 0) {
            throw new Error('Total leads cannot be negative');
        }
        if (this.activeLeads < 0) {
            throw new Error('Active leads cannot be negative');
        }
        if (this.activeLeads > this.totalLeads) {
            throw new Error('Active leads cannot exceed total leads');
        }
    }
    getWorkloadIntensity() {
        let intensity = Math.min(this.activeLeads * 5, 100);
        if (this.averageLeadAge > 30) {
            intensity += 10;
        }
        else if (this.averageLeadAge > 14) {
            intensity += 5;
        }
        return Math.min(100, intensity);
    }
    getWorkloadLevel() {
        const intensity = this.getWorkloadIntensity();
        if (intensity >= 80)
            return 'overloaded';
        if (intensity >= 60)
            return 'heavy';
        if (intensity >= 30)
            return 'moderate';
        return 'light';
    }
    isOverloaded() {
        return this.getWorkloadLevel() === 'overloaded';
    }
    canTakeMoreLeads(maxLeads = 20) {
        return this.activeLeads < maxLeads && !this.isOverloaded();
    }
    getAverageValuePerLead() {
        return this.totalLeads > 0 ? this.totalValue / this.totalLeads : 0;
    }
    getConversionRate() {
        return this.metadata.conversionRate || 0;
    }
    compareWith(other) {
        return {
            totalLeadsDiff: this.totalLeads - other.totalLeads,
            activeLeadsDiff: this.activeLeads - other.activeLeads,
            totalValueDiff: this.totalValue - other.totalValue,
            intensityDiff: this.getWorkloadIntensity() - other.getWorkloadIntensity(),
            isMoreBalanced: this.getWorkloadIntensity() < other.getWorkloadIntensity(),
        };
    }
    getSummary() {
        return {
            userId: this.userId,
            totalLeads: this.totalLeads,
            activeLeads: this.activeLeads,
            totalValue: this.totalValue,
            averageLeadAge: this.averageLeadAge,
            workloadLevel: this.getWorkloadLevel(),
            workloadIntensity: this.getWorkloadIntensity(),
            averageValuePerLead: this.getAverageValuePerLead(),
            canTakeMoreLeads: this.canTakeMoreLeads(),
            lastUpdated: this.lastUpdated,
        };
    }
    toPlainObject() {
        return {
            userId: this.userId,
            totalLeads: this.totalLeads,
            activeLeads: this.activeLeads,
            totalValue: this.totalValue,
            averageLeadAge: this.averageLeadAge,
            lastUpdated: this.lastUpdated.toISOString(),
            metadata: this.metadata,
            workloadIntensity: this.getWorkloadIntensity(),
            workloadLevel: this.getWorkloadLevel(),
            averageValuePerLead: this.getAverageValuePerLead(),
            canTakeMoreLeads: this.canTakeMoreLeads(),
            summary: this.getSummary(),
        };
    }
    static fromPlainObject(data) {
        return new WorkloadBalance(data.userId, data.totalLeads, data.activeLeads, data.totalValue, data.averageLeadAge, new Date(data.lastUpdated), data.metadata);
    }
    static createEmpty(userId) {
        return new WorkloadBalance(userId, 0, 0, 0, 0, new Date());
    }
}
exports.WorkloadBalance = WorkloadBalance;
//# sourceMappingURL=workload-balance.vo.js.map