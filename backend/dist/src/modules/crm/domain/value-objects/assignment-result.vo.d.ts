export declare class AssignmentResult {
    readonly assignedUserId: number | null;
    readonly teamId: number | null;
    readonly success: boolean;
    readonly message: string;
    readonly reasons: string[];
    readonly assignedAt: Date;
    readonly metadata: Record<string, any>;
    constructor(assignedUserId: number | null, teamId: number | null, success: boolean, message: string, reasons: string[], assignedAt: Date, metadata?: Record<string, any>);
    private validateResult;
    isSuccessful(): boolean;
    getConfidenceScore(): number;
    getPrimaryReason(): string | null;
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): AssignmentResult;
    static success(assignedUserId: number, teamId: number | null, reasons: string[], metadata?: Record<string, any>): AssignmentResult;
    static failure(message: string, reasons?: string[], metadata?: Record<string, any>): AssignmentResult;
}
