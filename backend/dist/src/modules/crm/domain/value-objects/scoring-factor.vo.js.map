{"version": 3, "file": "scoring-factor.vo.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/value-objects/scoring-factor.vo.ts"], "names": [], "mappings": ";;;AAIA,MAAa,aAAa;IAIN;IACA;IACA;IACA;IACA;IAPF,aAAa,CAAS;IAEtC,YACkB,IAAY,EACZ,KAAa,EACb,MAAc,EACd,WAAoB,EACpB,WAAgC,EAAE;QAJlC,SAAI,GAAJ,IAAI,CAAQ;QACZ,UAAK,GAAL,KAAK,CAAQ;QACb,WAAM,GAAN,MAAM,CAAQ;QACd,gBAAW,GAAX,WAAW,CAAS;QACpB,aAAQ,GAAR,QAAQ,CAA0B;QAElD,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;IAChD,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAKD,UAAU,CAAC,YAAoB,EAAE;QAC/B,OAAO,IAAI,CAAC,KAAK,IAAI,SAAS,CAAC;IACjC,CAAC;IAKD,UAAU,CAAC,YAAoB,EAAE;QAC/B,OAAO,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;IAChC,CAAC;IAKD,cAAc;QACZ,IAAI,IAAI,CAAC,MAAM,IAAI,GAAG;YAAE,OAAO,MAAM,CAAC;QACtC,IAAI,IAAI,CAAC,MAAM,IAAI,GAAG;YAAE,OAAO,QAAQ,CAAC;QACxC,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,mBAAmB;QACjB,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;YAAE,OAAO,WAAW,CAAC;QACzC,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QACpC,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QACpC,OAAO,MAAM,CAAC;IAChB,CAAC;IAKD,eAAe;QACb,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAKD,uBAAuB,CAAC,cAAsB,EAAE;QAC9C,IAAI,IAAI,CAAC,KAAK,IAAI,WAAW;YAAE,OAAO,CAAC,CAAC;QACxC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;IAClD,CAAC;IAKD,cAAc;QACZ,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC;YACtB,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;YAChD,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;YAC1C,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;YACxC,CAAC,aAAa,EAAE,aAAa,CAAC;YAC9B,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;YAC5C,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;YACpC,CAAC,cAAc,EAAE,cAAc,CAAC;YAChC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;YACpC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;YACpC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;SACrC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;IACvG,CAAC;IAKD,kBAAkB;QAChB,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACtB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,sBAAsB;oBACzB,eAAe,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;oBAC7D,eAAe,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;oBACtD,eAAe,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;oBACxD,MAAM;gBAER,KAAK,mBAAmB;oBACtB,eAAe,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;oBACpD,eAAe,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;oBAC3D,eAAe,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;oBAChD,MAAM;gBAER,KAAK,kBAAkB;oBACrB,eAAe,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;oBAC5D,eAAe,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;oBAClD,eAAe,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;oBAClD,MAAM;gBAER,KAAK,aAAa;oBAChB,eAAe,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;oBACtD,eAAe,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;oBAC3D,eAAe,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;oBAChD,MAAM;gBAER,KAAK,oBAAoB;oBACvB,eAAe,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;oBACjD,eAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;oBAC/C,eAAe,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;oBACjD,MAAM;gBAER,KAAK,gBAAgB;oBACnB,eAAe,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;oBACpD,eAAe,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;oBACnD,eAAe,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;oBACzD,MAAM;gBAER;oBACE,eAAe,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,cAAc,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;oBACvE,MAAM;YACV,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAKD,aAAa;QACX,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,eAAe,CAAC,IAAS;QAC9B,OAAO,IAAI,aAAa,CACtB,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,OAAO,CAAC,CAAgB,EAAE,CAAgB;QAC/C,OAAO,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,aAAa,CAAC;IAC3C,CAAC;IAKD,MAAM,CAAC,cAAc;QACnB,OAAO;YACL,IAAI,aAAa,CAAC,sBAAsB,EAAE,EAAE,EAAE,IAAI,EAAE,qCAAqC,CAAC;YAC1F,IAAI,aAAa,CAAC,mBAAmB,EAAE,EAAE,EAAE,IAAI,EAAE,yBAAyB,CAAC;YAC3E,IAAI,aAAa,CAAC,kBAAkB,EAAE,EAAE,EAAE,IAAI,EAAE,kCAAkC,CAAC;YACnF,IAAI,aAAa,CAAC,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,iCAAiC,CAAC;YAC7E,IAAI,aAAa,CAAC,oBAAoB,EAAE,EAAE,EAAE,IAAI,EAAE,mCAAmC,CAAC;YACtF,IAAI,aAAa,CAAC,gBAAgB,EAAE,EAAE,EAAE,IAAI,EAAE,wBAAwB,CAAC;SACxE,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,OAAO,CAAC,IAAS;QACtB,OAAO,CACL,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC;YAC3B,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ;YAC9B,IAAI,CAAC,KAAK,IAAI,CAAC;YACf,IAAI,CAAC,KAAK,IAAI,GAAG;YACjB,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ;YAC/B,IAAI,CAAC,MAAM,IAAI,CAAC;YAChB,IAAI,CAAC,MAAM,IAAI,CAAC,CACjB,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,cAAc,CAAC,OAAwB;QAC5C,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACrE,CAAC;IAKD,MAAM,CAAC,gBAAgB,CAAC,OAAwB;QAC9C,MAAM,WAAW,GAAG,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAE1D,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAC1B,IAAI,aAAa,CACf,MAAM,CAAC,IAAI,EACX,MAAM,CAAC,KAAK,EACZ,MAAM,CAAC,MAAM,GAAG,WAAW,EAC3B,MAAM,CAAC,WAAW,EAClB,MAAM,CAAC,QAAQ,CAChB,CACF,CAAC;IACJ,CAAC;CACF;AAtPD,sCAsPC"}