import { PipelineMetrics } from './pipeline-metrics.vo';
import { ConversionFunnel } from './conversion-funnel.vo';
import { PipelineForecast } from './pipeline-forecast.vo';
import { BottleneckAnalysis } from './bottleneck-analysis.vo';
export declare class PipelineAnalytics {
    readonly metrics: PipelineMetrics;
    readonly conversionFunnel: ConversionFunnel;
    readonly forecast: PipelineForecast;
    readonly bottlenecks: BottleneckAnalysis;
    readonly trends: any;
    readonly comparisons: any;
    readonly generatedAt: Date;
    readonly metadata: Record<string, any>;
    constructor(metrics: PipelineMetrics, conversionFunnel: ConversionFunnel, forecast: PipelineForecast, bottlenecks: BottleneckAnalysis, trends: any, comparisons: any, generatedAt: Date, metadata?: Record<string, any>);
    private validateAnalytics;
    getHealthScore(): number;
    getHealthGrade(): string;
    getKeyInsights(): string[];
    getRecommendedActions(): string[];
    getPerformanceIndicators(): Record<string, any>;
    isFresh(maxAgeHours?: number): boolean;
    getDashboardSummary(): Record<string, any>;
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): PipelineAnalytics;
    private calculateMetricsScore;
    private calculateFunnelScore;
    private calculateForecastScore;
    private calculateBottleneckScore;
}
