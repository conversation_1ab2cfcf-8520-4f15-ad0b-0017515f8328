import { NotificationChannel } from '@/modules/crm/domain/enums/notification-channel.enum';
export declare class NotificationDelivery {
    readonly id: string;
    readonly userId: string;
    readonly notificationType: string;
    readonly channel: NotificationChannel;
    readonly status: DeliveryStatus;
    readonly sentAt: Date;
    readonly error?: string | undefined;
    readonly metadata: Record<string, any>;
    constructor(id: string, userId: string, notificationType: string, channel: NotificationChannel, status: DeliveryStatus, sentAt: Date, error?: string | undefined, metadata?: Record<string, any>);
    private validateDelivery;
    isSuccessful(): boolean;
    isFailed(): boolean;
    isPending(): boolean;
    getDeliveryDuration(): number;
    getRetryCount(): number;
    getPriority(): string;
    canRetry(): boolean;
    getSummary(): DeliverySummary;
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): NotificationDelivery;
    static success(id: string, userId: string, notificationType: string, channel: NotificationChannel, metadata?: Record<string, any>): NotificationDelivery;
    static failure(id: string, userId: string, notificationType: string, channel: NotificationChannel, error: string, metadata?: Record<string, any>): NotificationDelivery;
    static pending(id: string, userId: string, notificationType: string, channel: NotificationChannel, metadata?: Record<string, any>): NotificationDelivery;
}
export type DeliveryStatus = 'pending' | 'delivered' | 'failed' | 'retrying';
export interface DeliverySummary {
    id: string;
    userId: string;
    notificationType: string;
    channel: NotificationChannel;
    status: DeliveryStatus;
    isSuccessful: boolean;
    sentAt: Date;
    deliveryDuration: number;
    retryCount: number;
    priority: string;
    error?: string;
}
