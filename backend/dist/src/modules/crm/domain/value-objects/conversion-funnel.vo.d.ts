export declare class ConversionFunnel {
    readonly steps: FunnelStep[];
    readonly overallConversionRate: number;
    readonly bottlenecks: FunnelBottleneck[];
    readonly recommendations: string[];
    readonly analyzedAt: Date;
    constructor(steps: FunnelStep[], overallConversionRate: number, bottlenecks: FunnelBottleneck[], recommendations: string[], analyzedAt: Date);
    private validateFunnel;
    getWorstPerformingStep(): FunnelStep | null;
    getBestPerformingStep(): FunnelStep | null;
    getTotalLeadsAtTop(): number;
    getTotalConversions(): number;
    getTotalValueProgression(): number[];
    getAverageTimeInFunnel(): number;
    getEfficiencyScore(): number;
    getStageAnalysis(): StageAnalysis[];
    getHealthIndicators(): FunnelHealthIndicators;
    getImprovementOpportunities(): ImprovementOpportunity[];
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): ConversionFunnel;
    private calculateVariance;
    private getStagePerformance;
    private getStageRecommendations;
    private calculateAverageDropOff;
    private calculatePotentialImprovement;
}
export interface FunnelStep {
    stageId: number;
    stageName: string;
    leadsCount: number;
    value: number;
    conversionRate: number;
    dropOffRate: number;
    averageTimeInStage: number;
}
export interface FunnelBottleneck {
    stageId: number;
    stageName: string;
    severity: 'low' | 'medium' | 'high';
    dropOffRate: number;
    impact: string;
    recommendation: string;
}
export interface StageAnalysis {
    stageId: number;
    stageName: string;
    position: number;
    leadsCount: number;
    value: number;
    conversionRate: number;
    dropOffRate: number;
    averageTimeInStage: number;
    performance: 'excellent' | 'good' | 'fair' | 'poor';
    recommendations: string[];
}
export interface FunnelHealthIndicators {
    overallHealth: 'good' | 'fair' | 'poor';
    criticalBottlenecks: number;
    worstStage?: string;
    bestStage?: string;
    averageDropOff: number;
    timeToConvert: number;
}
export interface ImprovementOpportunity {
    type: 'high_dropoff' | 'slow_stage' | 'low_conversion';
    stage: string;
    impact: 'high' | 'medium' | 'low';
    description: string;
    recommendation: string;
    potentialImprovement: string;
}
