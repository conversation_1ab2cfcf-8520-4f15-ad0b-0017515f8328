"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RevenueForecast = void 0;
class RevenueForecast {
    expectedRevenue;
    probability;
    currency;
    constructor(expectedRevenue, probability, currency = 'USD') {
        this.expectedRevenue = expectedRevenue;
        this.probability = probability;
        this.currency = currency;
        this.validateExpectedRevenue();
        this.validateProbability();
        this.validateCurrency();
    }
    get weightedRevenue() {
        return this.expectedRevenue * (this.probability / 100);
    }
    get confidenceLevel() {
        if (this.probability >= 80)
            return 'very-high';
        if (this.probability >= 60)
            return 'high';
        if (this.probability >= 30)
            return 'medium';
        return 'low';
    }
    get isRealistic() {
        return this.probability > 0 && this.probability <= 100;
    }
    get isOptimistic() {
        return this.probability >= 70;
    }
    get isConservative() {
        return this.probability <= 30;
    }
    get isHighValue() {
        return this.expectedRevenue >= 50000;
    }
    get riskLevel() {
        if (this.probability >= 70)
            return 'low';
        if (this.probability >= 40)
            return 'medium';
        return 'high';
    }
    getRevenueRange() {
        const variance = 0.2;
        const min = this.weightedRevenue * (1 - variance);
        const max = this.weightedRevenue * (1 + variance);
        return {
            min: Math.max(0, min),
            max,
            expected: this.weightedRevenue,
        };
    }
    getForecastCategory() {
        if (this.probability >= 90)
            return 'commit';
        if (this.probability >= 70)
            return 'best-case';
        if (this.probability >= 40)
            return 'pipeline';
        return 'upside';
    }
    getMonthlyRecurringRevenue(isRecurring = false, months = 12) {
        if (!isRecurring)
            return 0;
        return this.expectedRevenue / months;
    }
    getConfidenceColor() {
        switch (this.confidenceLevel) {
            case 'very-high': return '#28a745';
            case 'high': return '#20c997';
            case 'medium': return '#ffc107';
            case 'low': return '#dc3545';
            default: return '#6c757d';
        }
    }
    formatRevenue(locale = 'en-US') {
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: this.currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(this.expectedRevenue);
    }
    formatWeightedRevenue(locale = 'en-US') {
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: this.currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(this.weightedRevenue);
    }
    withProbability(newProbability) {
        return new RevenueForecast(this.expectedRevenue, newProbability, this.currency);
    }
    withExpectedRevenue(newRevenue) {
        return new RevenueForecast(newRevenue, this.probability, this.currency);
    }
    validateExpectedRevenue() {
        if (this.expectedRevenue < 0) {
            throw new Error('Expected revenue cannot be negative');
        }
        if (!Number.isFinite(this.expectedRevenue)) {
            throw new Error('Expected revenue must be a finite number');
        }
    }
    validateProbability() {
        if (this.probability < 0 || this.probability > 100) {
            throw new Error('Probability must be between 0 and 100');
        }
        if (!Number.isFinite(this.probability)) {
            throw new Error('Probability must be a finite number');
        }
    }
    validateCurrency() {
        if (!this.currency || this.currency.length !== 3) {
            throw new Error('Currency must be a valid 3-letter ISO code');
        }
    }
    equals(other) {
        return (this.expectedRevenue === other.expectedRevenue &&
            this.probability === other.probability &&
            this.currency === other.currency);
    }
    toPlainObject() {
        const range = this.getRevenueRange();
        return {
            expectedRevenue: this.expectedRevenue,
            probability: this.probability,
            currency: this.currency,
            weightedRevenue: this.weightedRevenue,
            confidenceLevel: this.confidenceLevel,
            isRealistic: this.isRealistic,
            isOptimistic: this.isOptimistic,
            isConservative: this.isConservative,
            isHighValue: this.isHighValue,
            riskLevel: this.riskLevel,
            forecastCategory: this.getForecastCategory(),
            revenueRange: range,
            confidenceColor: this.getConfidenceColor(),
            formattedRevenue: this.formatRevenue(),
            formattedWeightedRevenue: this.formatWeightedRevenue(),
        };
    }
    toString() {
        return `${this.formatRevenue()} (${this.probability}% = ${this.formatWeightedRevenue()})`;
    }
    toJSON() {
        return {
            expectedRevenue: this.expectedRevenue,
            probability: this.probability,
            currency: this.currency,
            weightedRevenue: this.weightedRevenue,
        };
    }
}
exports.RevenueForecast = RevenueForecast;
//# sourceMappingURL=revenue-forecast.vo.js.map