export declare class ScoringRule {
    readonly id: string;
    readonly name: string;
    readonly description: string;
    readonly factor: string;
    readonly condition: ScoringCondition;
    readonly scoreAdjustment: number;
    readonly isActive: boolean;
    readonly priority: number;
    readonly metadata: Record<string, any>;
    constructor(id: string, name: string, description: string, factor: string, condition: ScoringCondition, scoreAdjustment: number, isActive?: boolean, priority?: number, metadata?: Record<string, any>);
    private validateRule;
    appliesTo(leadData: Record<string, any>): boolean;
    apply(leadData: Record<string, any>): number;
    getRuleType(): 'bonus' | 'penalty' | 'neutral';
    getImpactLevel(): 'high' | 'medium' | 'low';
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): ScoringRule;
    static createPredefinedRules(): ScoringRule[];
}
export declare class ScoringCondition {
    readonly field: string;
    readonly operator: ConditionOperator;
    readonly value: any;
    readonly logicalOperator?: "and" | "or" | undefined;
    readonly subConditions?: ScoringCondition[] | undefined;
    constructor(field: string, operator: ConditionOperator, value: any, logicalOperator?: "and" | "or" | undefined, subConditions?: ScoringCondition[] | undefined);
    private validateCondition;
    evaluate(leadData: Record<string, any>): boolean;
    private getFieldValue;
    private evaluateOperator;
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): ScoringCondition;
}
export type ConditionOperator = 'equals' | 'not_equals' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'not_contains' | 'starts_with' | 'ends_with' | 'empty' | 'not_empty' | 'in' | 'not_in' | 'regex';
