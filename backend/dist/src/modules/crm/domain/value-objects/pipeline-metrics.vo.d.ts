export declare class PipelineMetrics {
    readonly totalLeads: number;
    readonly qualifiedLeads: number;
    readonly convertedLeads: number;
    readonly totalValue: number;
    readonly weightedValue: number;
    readonly conversionRate: number;
    readonly qualificationRate: number;
    readonly averageSalesCycle: number;
    readonly pipelineVelocity: number;
    readonly stageDistribution: StageDistribution[];
    readonly sourcePerformance: SourcePerformance[];
    readonly teamPerformance: TeamPerformance;
    readonly winLossAnalysis: WinLossAnalysis;
    readonly calculatedAt: Date;
    constructor(totalLeads: number, qualifiedLeads: number, convertedLeads: number, totalValue: number, weightedValue: number, conversionRate: number, qualificationRate: number, averageSalesCycle: number, pipelineVelocity: number, stageDistribution: StageDistribution[], sourcePerformance: SourcePerformance[], teamPerformance: TeamPerformance, winLossAnalysis: WinLossAnalysis, calculatedAt: Date);
    private validateMetrics;
    getEfficiencyScore(): number;
    getAverageDealSize(): number;
    getWeightedAverageDealSize(): number;
    getPipelineCoverageRatio(): number;
    getTopPerformingSource(): SourcePerformance | null;
    getWorstPerformingSource(): SourcePerformance | null;
    getMostPopulatedStage(): StageDistribution | null;
    getPerformanceSummary(): Record<string, any>;
    compareWith(previousMetrics: PipelineMetrics): MetricsComparison;
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): PipelineMetrics;
    static createDefault(): PipelineMetrics;
}
export interface StageDistribution {
    stageId: number;
    stageName: string;
    count: number;
    value: number;
    percentage?: number;
}
export interface SourcePerformance {
    source: string;
    count: number;
    conversionRate: number;
    averageValue: number;
    totalValue?: number;
}
export interface TeamPerformance {
    [teamId: string]: {
        teamName: string;
        leadCount: number;
        conversionRate: number;
        averageValue: number;
        totalValue: number;
    };
}
export interface WinLossAnalysis {
    winRate: number;
    lossRate: number;
    winReasons: Array<{
        reason: string;
        count: number;
        percentage?: number;
    }>;
    lossReasons: Array<{
        reason: string;
        count: number;
        percentage?: number;
    }>;
}
export interface MetricsComparison {
    totalLeadsChange: number;
    conversionRateChange: number;
    qualificationRateChange: number;
    averageSalesCycleChange: number;
    pipelineVelocityChange: number;
    totalValueChange: number;
    weightedValueChange: number;
    efficiencyScoreChange: number;
}
