export declare class ScoringFactor {
    readonly name: string;
    readonly score: number;
    readonly weight: number;
    readonly description?: string | undefined;
    readonly metadata: Record<string, any>;
    readonly weightedScore: number;
    constructor(name: string, score: number, weight: number, description?: string | undefined, metadata?: Record<string, any>);
    private validateFactor;
    isStrength(threshold?: number): boolean;
    isWeakness(threshold?: number): boolean;
    getImpactLevel(): 'high' | 'medium' | 'low';
    getPerformanceLevel(): 'excellent' | 'good' | 'fair' | 'poor';
    getContribution(): number;
    getImprovementPotential(targetScore?: number): number;
    getDisplayName(): string;
    getRecommendations(): string[];
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): ScoringFactor;
    static compare(a: ScoringFactor, b: ScoringFactor): number;
    static createDefaults(): ScoringFactor[];
    static isValid(data: any): boolean;
    static getTotalWeight(factors: ScoringFactor[]): number;
    static normalizeWeights(factors: ScoringFactor[]): ScoringFactor[];
}
