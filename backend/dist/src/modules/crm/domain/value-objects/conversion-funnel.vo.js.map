{"version": 3, "file": "conversion-funnel.vo.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/value-objects/conversion-funnel.vo.ts"], "names": [], "mappings": ";;;AAIA,MAAa,gBAAgB;IAET;IACA;IACA;IACA;IACA;IALlB,YACkB,KAAmB,EACnB,qBAA6B,EAC7B,WAA+B,EAC/B,eAAyB,EACzB,UAAgB;QAJhB,UAAK,GAAL,KAAK,CAAc;QACnB,0BAAqB,GAArB,qBAAqB,CAAQ;QAC7B,gBAAW,GAAX,WAAW,CAAoB;QAC/B,oBAAe,GAAf,eAAe,CAAU;QACzB,eAAU,GAAV,UAAU,CAAM;QAEhC,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,GAAG,CAAC,IAAI,IAAI,CAAC,qBAAqB,GAAG,GAAG,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAKD,sBAAsB;QACpB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAEzC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,CAC1C,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAC1D,CAAC;IACJ,CAAC;IAKD,qBAAqB;QACnB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAEzC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CACzC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAC9D,CAAC;IACJ,CAAC;IAKD,kBAAkB;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC;IAKD,mBAAmB;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAClF,CAAC;IAKD,wBAAwB;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IAKD,sBAAsB;QACpB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEtC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QACrF,OAAO,SAAS,CAAC;IACnB,CAAC;IAKD,kBAAkB;QAChB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAGtC,IAAI,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAGvC,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;QACtD,KAAK,IAAI,iBAAiB,CAAC;QAG3B,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QAC7F,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YACzD,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC;YACzD,KAAK,IAAI,gBAAgB,CAAC;QAC5B,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IAKD,gBAAgB;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YACtC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,KAAK,GAAG,CAAC;YACnB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YAC3C,eAAe,EAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;SACpD,CAAC,CAAC,CAAC;IACN,CAAC;IAKD,mBAAmB;QACjB,MAAM,SAAS,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE9C,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;YAC3G,mBAAmB,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM;YAC/E,UAAU,EAAE,SAAS,EAAE,SAAS;YAChC,SAAS,EAAE,QAAQ,EAAE,SAAS;YAC9B,cAAc,EAAE,IAAI,CAAC,uBAAuB,EAAE;YAC9C,aAAa,EAAE,IAAI,CAAC,sBAAsB,EAAE;SAC7C,CAAC;IACJ,CAAC;IAKD,2BAA2B;QACzB,MAAM,aAAa,GAA6B,EAAE,CAAC;QAGnD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACxB,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,EAAE,CAAC;gBAC1B,aAAa,CAAC,IAAI,CAAC;oBACjB,IAAI,EAAE,cAAc;oBACpB,KAAK,EAAE,IAAI,CAAC,SAAS;oBACrB,MAAM,EAAE,MAAM;oBACd,WAAW,EAAE,GAAG,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB;oBAClF,cAAc,EAAE,YAAY,IAAI,CAAC,SAAS,6BAA6B;oBACvE,oBAAoB,EAAE,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC;iBAC/D,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACxB,IAAI,IAAI,CAAC,kBAAkB,GAAG,EAAE,EAAE,CAAC;gBACjC,aAAa,CAAC,IAAI,CAAC;oBACjB,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,IAAI,CAAC,SAAS;oBACrB,MAAM,EAAE,QAAQ;oBAChB,WAAW,EAAE,GAAG,IAAI,CAAC,SAAS,UAAU,IAAI,CAAC,kBAAkB,kBAAkB;oBACjF,cAAc,EAAE,cAAc,IAAI,CAAC,SAAS,iBAAiB;oBAC7D,oBAAoB,EAAE,0BAA0B;iBACjD,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC;IACvB,CAAC;IAKD,aAAa;QACX,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;YACjD,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,wBAAwB,EAAE,IAAI,CAAC,2BAA2B,EAAE;YAC5D,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YAClD,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;SAC1C,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,eAAe,CAAC,IAAS;QAC9B,OAAO,IAAI,gBAAgB,CACzB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,qBAAqB,EAC1B,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,eAAe,EACpB,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAC1B,CAAC;IACJ,CAAC;IAKO,iBAAiB,CAAC,MAAgB;QACxC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QACvE,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAChE,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;IAC3E,CAAC;IAEO,mBAAmB,CAAC,IAAgB;QAC1C,IAAI,IAAI,CAAC,cAAc,IAAI,EAAE;YAAE,OAAO,WAAW,CAAC;QAClD,IAAI,IAAI,CAAC,cAAc,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QAC7C,IAAI,IAAI,CAAC,cAAc,IAAI,EAAE;YAAE,OAAO,MAAM,CAAC;QAC7C,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,uBAAuB,CAAC,IAAgB;QAC9C,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,EAAE,CAAC;YAC1B,eAAe,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,GAAG,EAAE,EAAE,CAAC;YACjC,eAAe,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC;YAC7B,eAAe,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAC/E,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,uBAAuB;QAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QACnF,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACnG,CAAC;IAEO,6BAA6B,CAAC,IAAgB;QACpD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;QAC3C,OAAO,aAAa,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,qCAAqC,CAAC;IAClF,CAAC;CACF;AAtPD,4CAsPC"}