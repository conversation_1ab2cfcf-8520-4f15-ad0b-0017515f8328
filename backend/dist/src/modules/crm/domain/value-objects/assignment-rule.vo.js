"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssignmentRule = void 0;
class AssignmentRule {
    id;
    name;
    description;
    leadCondition;
    userCondition;
    scoreAdjustment;
    priority;
    isActive;
    constructor(id, name, description, leadCondition, userCondition, scoreAdjustment, priority, isActive = true) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.leadCondition = leadCondition;
        this.userCondition = userCondition;
        this.scoreAdjustment = scoreAdjustment;
        this.priority = priority;
        this.isActive = isActive;
        this.validateRule();
    }
    validateRule() {
        if (!this.id || this.id.trim().length === 0) {
            throw new Error('Assignment rule must have an ID');
        }
        if (!this.name || this.name.trim().length === 0) {
            throw new Error('Assignment rule must have a name');
        }
        if (this.scoreAdjustment < -100 || this.scoreAdjustment > 100) {
            throw new Error('Score adjustment must be between -100 and 100');
        }
    }
    appliesTo(lead) {
        if (!this.isActive)
            return false;
        try {
            return this.leadCondition(lead);
        }
        catch (error) {
            return false;
        }
    }
    appliesToUser(user) {
        if (!this.isActive)
            return false;
        try {
            return this.userCondition(user);
        }
        catch (error) {
            return false;
        }
    }
    getScoreAdjustment(lead, user) {
        if (!this.appliesTo(lead) || !this.appliesToUser(user)) {
            return 0;
        }
        return this.scoreAdjustment;
    }
    toPlainObject() {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            scoreAdjustment: this.scoreAdjustment,
            priority: this.priority,
            isActive: this.isActive,
        };
    }
    static fromPlainObject(data) {
        return new AssignmentRule(data.id, data.name, data.description, () => true, () => true, data.scoreAdjustment, data.priority, data.isActive);
    }
}
exports.AssignmentRule = AssignmentRule;
//# sourceMappingURL=assignment-rule.vo.js.map