"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PipelineAnalytics = void 0;
const pipeline_metrics_vo_1 = require("./pipeline-metrics.vo");
const conversion_funnel_vo_1 = require("./conversion-funnel.vo");
const pipeline_forecast_vo_1 = require("./pipeline-forecast.vo");
const bottleneck_analysis_vo_1 = require("./bottleneck-analysis.vo");
class PipelineAnalytics {
    metrics;
    conversionFunnel;
    forecast;
    bottlenecks;
    trends;
    comparisons;
    generatedAt;
    metadata;
    constructor(metrics, conversionFunnel, forecast, bottlenecks, trends, comparisons, generatedAt, metadata = {}) {
        this.metrics = metrics;
        this.conversionFunnel = conversionFunnel;
        this.forecast = forecast;
        this.bottlenecks = bottlenecks;
        this.trends = trends;
        this.comparisons = comparisons;
        this.generatedAt = generatedAt;
        this.metadata = metadata;
        this.validateAnalytics();
    }
    validateAnalytics() {
        if (!this.metrics) {
            throw new Error('Pipeline analytics must include metrics');
        }
        if (!this.conversionFunnel) {
            throw new Error('Pipeline analytics must include conversion funnel');
        }
        if (!this.forecast) {
            throw new Error('Pipeline analytics must include forecast');
        }
        if (!this.bottlenecks) {
            throw new Error('Pipeline analytics must include bottleneck analysis');
        }
    }
    getHealthScore() {
        const metricsScore = this.calculateMetricsScore();
        const funnelScore = this.calculateFunnelScore();
        const forecastScore = this.calculateForecastScore();
        const bottleneckScore = this.calculateBottleneckScore();
        const healthScore = (metricsScore * 0.3 +
            funnelScore * 0.25 +
            forecastScore * 0.25 +
            bottleneckScore * 0.2);
        return Math.round(Math.max(0, Math.min(100, healthScore)));
    }
    getHealthGrade() {
        const score = this.getHealthScore();
        if (score >= 90)
            return 'A+';
        if (score >= 80)
            return 'A';
        if (score >= 70)
            return 'B';
        if (score >= 60)
            return 'C';
        if (score >= 50)
            return 'D';
        return 'F';
    }
    getKeyInsights() {
        const insights = [];
        if (this.metrics.conversionRate > 20) {
            insights.push('Excellent conversion rate performance');
        }
        else if (this.metrics.conversionRate < 10) {
            insights.push('Conversion rate needs improvement');
        }
        if (this.metrics.pipelineVelocity > 10000) {
            insights.push('Strong pipeline velocity');
        }
        else if (this.metrics.pipelineVelocity < 5000) {
            insights.push('Pipeline velocity is below target');
        }
        const criticalBottlenecks = this.bottlenecks.bottlenecks.filter(b => b.severity === 'high');
        if (criticalBottlenecks.length > 0) {
            insights.push(`${criticalBottlenecks.length} critical bottlenecks detected`);
        }
        const forecastTrend = this.forecast.trendAnalysis?.trend;
        if (forecastTrend === 'improving') {
            insights.push('Pipeline forecast shows positive trend');
        }
        else if (forecastTrend === 'declining') {
            insights.push('Pipeline forecast shows concerning decline');
        }
        return insights;
    }
    getRecommendedActions() {
        const actions = [];
        const funnelBottlenecks = this.conversionFunnel.bottlenecks;
        if (funnelBottlenecks.length > 0) {
            actions.push('Address conversion funnel bottlenecks');
        }
        const prioritizedBottlenecks = this.bottlenecks.prioritizedBottlenecks;
        if (prioritizedBottlenecks.length > 0) {
            actions.push(`Focus on ${prioritizedBottlenecks[0].type} bottleneck`);
        }
        if (this.metrics.conversionRate < 15) {
            actions.push('Improve lead qualification process');
        }
        if (this.metrics.averageSalesCycle > 60) {
            actions.push('Optimize sales cycle duration');
        }
        if (this.forecast.riskFactors.length > 0) {
            actions.push('Mitigate identified forecast risks');
        }
        return [...new Set(actions)];
    }
    getPerformanceIndicators() {
        return {
            healthScore: this.getHealthScore(),
            healthGrade: this.getHealthGrade(),
            conversionRate: this.metrics.conversionRate,
            pipelineVelocity: this.metrics.pipelineVelocity,
            forecastAccuracy: this.forecast.forecasts[0]?.confidence || 0,
            bottleneckCount: this.bottlenecks.bottlenecks.length,
            trendDirection: this.trends?.conversionTrends?.trend || 'stable',
        };
    }
    isFresh(maxAgeHours = 24) {
        const ageHours = (Date.now() - this.generatedAt.getTime()) / (1000 * 60 * 60);
        return ageHours <= maxAgeHours;
    }
    getDashboardSummary() {
        return {
            overview: {
                totalLeads: this.metrics.totalLeads,
                conversionRate: this.metrics.conversionRate,
                pipelineValue: this.metrics.totalValue,
                healthScore: this.getHealthScore(),
            },
            forecast: {
                next30Days: this.forecast.forecasts.find(f => f.period === '30 days'),
                trend: this.trends?.conversionTrends?.trend,
            },
            alerts: {
                criticalBottlenecks: this.bottlenecks.bottlenecks.filter(b => b.severity === 'high').length,
                riskFactors: this.forecast.riskFactors.length,
            },
            recommendations: this.getRecommendedActions().slice(0, 3),
        };
    }
    toPlainObject() {
        return {
            metrics: this.metrics.toPlainObject(),
            conversionFunnel: this.conversionFunnel.toPlainObject(),
            forecast: this.forecast.toPlainObject(),
            bottlenecks: this.bottlenecks.toPlainObject(),
            trends: this.trends,
            comparisons: this.comparisons,
            healthScore: this.getHealthScore(),
            healthGrade: this.getHealthGrade(),
            keyInsights: this.getKeyInsights(),
            recommendedActions: this.getRecommendedActions(),
            performanceIndicators: this.getPerformanceIndicators(),
            dashboardSummary: this.getDashboardSummary(),
            generatedAt: this.generatedAt.toISOString(),
            metadata: this.metadata,
        };
    }
    static fromPlainObject(data) {
        return new PipelineAnalytics(pipeline_metrics_vo_1.PipelineMetrics.fromPlainObject(data.metrics), conversion_funnel_vo_1.ConversionFunnel.fromPlainObject(data.conversionFunnel), pipeline_forecast_vo_1.PipelineForecast.fromPlainObject(data.forecast), bottleneck_analysis_vo_1.BottleneckAnalysis.fromPlainObject(data.bottlenecks), data.trends, data.comparisons, new Date(data.generatedAt), data.metadata);
    }
    calculateMetricsScore() {
        let score = 50;
        if (this.metrics.conversionRate >= 25)
            score += 20;
        else if (this.metrics.conversionRate >= 15)
            score += 10;
        else if (this.metrics.conversionRate < 5)
            score -= 20;
        if (this.metrics.pipelineVelocity >= 15000)
            score += 15;
        else if (this.metrics.pipelineVelocity >= 8000)
            score += 8;
        else if (this.metrics.pipelineVelocity < 3000)
            score -= 15;
        if (this.metrics.averageSalesCycle <= 30)
            score += 10;
        else if (this.metrics.averageSalesCycle <= 60)
            score += 5;
        else if (this.metrics.averageSalesCycle > 120)
            score -= 15;
        if (this.metrics.totalLeads >= 100)
            score += 5;
        else if (this.metrics.totalLeads < 20)
            score -= 10;
        return Math.max(0, Math.min(100, score));
    }
    calculateFunnelScore() {
        let score = 50;
        if (this.conversionFunnel.overallConversionRate >= 20)
            score += 25;
        else if (this.conversionFunnel.overallConversionRate >= 10)
            score += 10;
        else if (this.conversionFunnel.overallConversionRate < 5)
            score -= 25;
        const bottleneckPenalty = this.conversionFunnel.bottlenecks.length * 5;
        score -= bottleneckPenalty;
        return Math.max(0, Math.min(100, score));
    }
    calculateForecastScore() {
        let score = 50;
        const avgConfidence = this.forecast.forecasts.reduce((sum, f) => sum + f.confidence, 0) / this.forecast.forecasts.length;
        if (avgConfidence >= 80)
            score += 25;
        else if (avgConfidence >= 60)
            score += 10;
        else if (avgConfidence < 40)
            score -= 20;
        const riskPenalty = this.forecast.riskFactors.length * 5;
        score -= riskPenalty;
        return Math.max(0, Math.min(100, score));
    }
    calculateBottleneckScore() {
        let score = 100;
        this.bottlenecks.bottlenecks.forEach(bottleneck => {
            switch (bottleneck.severity) {
                case 'high':
                    score -= 20;
                    break;
                case 'medium':
                    score -= 10;
                    break;
                case 'low':
                    score -= 5;
                    break;
            }
        });
        return Math.max(0, score);
    }
}
exports.PipelineAnalytics = PipelineAnalytics;
//# sourceMappingURL=pipeline-analytics.vo.js.map