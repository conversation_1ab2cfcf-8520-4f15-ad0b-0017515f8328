"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadPriority = void 0;
class LeadPriority {
    value;
    label;
    color;
    static LOW = new LeadPriority(0, 'Low', '#6c757d');
    static MEDIUM = new LeadPriority(1, 'Medium', '#ffc107');
    static HIGH = new LeadPriority(2, 'High', '#fd7e14');
    static VERY_HIGH = new LeadPriority(3, 'Very High', '#dc3545');
    static VALID_PRIORITIES = [0, 1, 2, 3];
    static PRIORITY_MAP = new Map([
        [0, LeadPriority.LOW],
        [1, LeadPriority.MEDIUM],
        [2, LeadPriority.HIGH],
        [3, LeadPriority.VERY_HIGH],
    ]);
    constructor(value, label, color) {
        this.value = value;
        this.label = label;
        this.color = color;
        if (!LeadPriority.VALID_PRIORITIES.includes(value)) {
            throw new Error(`Invalid lead priority value: ${value}. Must be 0-3.`);
        }
    }
    static fromValue(value) {
        const priority = LeadPriority.PRIORITY_MAP.get(value);
        if (!priority) {
            throw new Error(`Invalid lead priority value: ${value}. Must be 0-3.`);
        }
        return priority;
    }
    static fromLabel(label) {
        const normalizedLabel = label.toLowerCase().trim();
        switch (normalizedLabel) {
            case 'low':
                return LeadPriority.LOW;
            case 'medium':
                return LeadPriority.MEDIUM;
            case 'high':
                return LeadPriority.HIGH;
            case 'very high':
            case 'very_high':
                return LeadPriority.VERY_HIGH;
            default:
                throw new Error(`Invalid lead priority label: ${label}`);
        }
    }
    static getAllPriorities() {
        return [
            LeadPriority.LOW,
            LeadPriority.MEDIUM,
            LeadPriority.HIGH,
            LeadPriority.VERY_HIGH,
        ];
    }
    isHigherThan(other) {
        return this.value > other.value;
    }
    isLowerThan(other) {
        return this.value < other.value;
    }
    requiresImmediateAttention() {
        return this.value >= 2;
    }
    getScoreWeight() {
        switch (this.value) {
            case 0: return 1;
            case 1: return 1.2;
            case 2: return 1.5;
            case 3: return 2;
            default: return 1;
        }
    }
    getCssClass() {
        switch (this.value) {
            case 0: return 'priority-low';
            case 1: return 'priority-medium';
            case 2: return 'priority-high';
            case 3: return 'priority-very-high';
            default: return 'priority-low';
        }
    }
    getIcon() {
        switch (this.value) {
            case 0: return 'arrow-down';
            case 1: return 'minus';
            case 2: return 'arrow-up';
            case 3: return 'exclamation-triangle';
            default: return 'minus';
        }
    }
    equals(other) {
        return this.value === other.value;
    }
    toPlainObject() {
        return {
            value: this.value,
            label: this.label,
            color: this.color,
            cssClass: this.getCssClass(),
            icon: this.getIcon(),
            requiresImmediateAttention: this.requiresImmediateAttention(),
        };
    }
    toString() {
        return this.label;
    }
    toJSON() {
        return this.value;
    }
}
exports.LeadPriority = LeadPriority;
//# sourceMappingURL=lead-priority.vo.js.map