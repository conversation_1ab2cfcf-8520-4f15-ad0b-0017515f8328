export declare class PipelineForecast {
    readonly currentPipelineValue: number;
    readonly weightedPipelineValue: number;
    readonly forecasts: ForecastPeriod[];
    readonly trendAnalysis: TrendAnalysis;
    readonly riskFactors: string[];
    readonly generatedAt: Date;
    constructor(currentPipelineValue: number, weightedPipelineValue: number, forecasts: ForecastPeriod[], trendAnalysis: TrendAnalysis, riskFactors: string[], generatedAt: Date);
    private validateForecast;
    getForecastForPeriod(days: number): ForecastPeriod | null;
    getNext30DayForecast(): ForecastPeriod | null;
    getNextQuarterForecast(): ForecastPeriod | null;
    getOverallConfidence(): number;
    getAccuracyGrade(): string;
    getGrowthProjection(): GrowthProjection;
    getRiskAssessment(): RiskAssessment;
    getScenarioAnalysis(): ScenarioAnalysis;
    getForecastSummary(): ForecastSummary;
    isReliable(): boolean;
    getForecastAlerts(): ForecastAlert[];
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): PipelineForecast;
    private calculateRiskLevel;
    private generateMitigationStrategies;
    private getMostLikelyScenario;
    private getBestCaseScenario;
    private getWorstCaseScenario;
}
export interface ForecastPeriod {
    period: string;
    periodEnd: Date;
    expectedClosures: number;
    expectedValue: number;
    confidence: number;
    scenarios: {
        optimistic: number;
        realistic: number;
        pessimistic: number;
    };
}
export interface TrendAnalysis {
    trend: 'improving' | 'stable' | 'declining';
    growth: number;
    seasonality?: {
        pattern: string;
        impact: number;
    };
}
export interface GrowthProjection {
    monthlyGrowth: number;
    quarterlyGrowth: number;
    trend: 'growing' | 'declining' | 'stable';
}
export interface RiskAssessment {
    level: 'low' | 'medium' | 'high';
    factors: string[];
    confidence: number;
    mitigation: string[];
}
export interface ScenarioAnalysis {
    scenarios: Record<string, number>;
    mostLikely: string;
    bestCase: string;
    worstCase: string;
}
export interface ForecastSummary {
    currentValue: number;
    weightedValue: number;
    next30DayValue: number;
    next30DayClosures: number;
    confidence: number;
    accuracyGrade: string;
    trend: 'growing' | 'declining' | 'stable';
    monthlyGrowth: number;
    riskLevel: 'low' | 'medium' | 'high';
    riskFactorCount: number;
}
export interface ForecastAlert {
    type: 'low_confidence' | 'high_risk' | 'declining_trend' | 'data_quality';
    severity: 'info' | 'warning' | 'error';
    message: string;
    recommendation: string;
}
