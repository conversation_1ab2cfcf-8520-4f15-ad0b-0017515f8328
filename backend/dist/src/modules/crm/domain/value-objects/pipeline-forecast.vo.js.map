{"version": 3, "file": "pipeline-forecast.vo.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/value-objects/pipeline-forecast.vo.ts"], "names": [], "mappings": ";;;AAIA,MAAa,gBAAgB;IAET;IACA;IACA;IACA;IACA;IACA;IANlB,YACkB,oBAA4B,EAC5B,qBAA6B,EAC7B,SAA2B,EAC3B,aAA4B,EAC5B,WAAqB,EACrB,WAAiB;QALjB,yBAAoB,GAApB,oBAAoB,CAAQ;QAC5B,0BAAqB,GAArB,qBAAqB,CAAQ;QAC7B,cAAS,GAAT,SAAS,CAAkB;QAC3B,kBAAa,GAAb,aAAa,CAAe;QAC5B,gBAAW,GAAX,WAAW,CAAU;QACrB,gBAAW,GAAX,WAAW,CAAM;QAEjC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,gBAAgB;QACtB,IAAI,IAAI,CAAC,oBAAoB,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,GAAG,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAKD,oBAAoB,CAAC,IAAY;QAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,GAAG,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC;IACvE,CAAC;IAKD,oBAAoB;QAClB,OAAO,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;IACvC,CAAC;IAKD,sBAAsB;QACpB,OAAO,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;IACvC,CAAC;IAKD,oBAAoB;QAClB,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE1C,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QACjF,OAAO,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC7D,CAAC;IAKD,gBAAgB;QACd,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC/C,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,IAAI,CAAC;QAClC,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QACjC,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QACjC,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QACjC,IAAI,UAAU,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QACjC,OAAO,GAAG,CAAC;IACb,CAAC;IAKD,mBAAmB;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE7C,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;YACvB,OAAO;gBACL,aAAa,EAAE,CAAC;gBAChB,eAAe,EAAE,CAAC;gBAClB,KAAK,EAAE,QAAQ;aAChB,CAAC;QACJ,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,GAAG,CAAC;YACjD,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,GAAG;YACxF,CAAC,CAAC,CAAC,CAAC;QAEN,MAAM,eAAe,GAAG,IAAI,CAAC,oBAAoB,GAAG,CAAC;YACnD,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,GAAG;YACxF,CAAC,CAAC,CAAC,CAAC;QAEN,IAAI,KAAK,GAAuC,QAAQ,CAAC;QACzD,IAAI,eAAe,GAAG,CAAC;YAAE,KAAK,GAAG,SAAS,CAAC;aACtC,IAAI,eAAe,GAAG,CAAC,CAAC;YAAE,KAAK,GAAG,WAAW,CAAC;QAEnD,OAAO;YACL,aAAa;YACb,eAAe;YACf,KAAK;SACN,CAAC;IACJ,CAAC;IAKD,iBAAiB;QACf,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE/C,OAAO;YACL,KAAK,EAAE,SAAS;YAChB,OAAO,EAAE,IAAI,CAAC,WAAW;YACzB,UAAU;YACV,UAAU,EAAE,IAAI,CAAC,4BAA4B,EAAE;SAChD,CAAC;IACJ,CAAC;IAKD,mBAAmB;QACjB,MAAM,SAAS,GAA2B,EAAE,CAAC;QAE7C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAChC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC/B,SAAS,CAAC,GAAG,MAAM,aAAa,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC;YAClE,SAAS,CAAC,GAAG,MAAM,YAAY,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC;YAChE,SAAS,CAAC,GAAG,MAAM,cAAc,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,SAAS;YACT,UAAU,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACxC,QAAQ,EAAE,IAAI,CAAC,mBAAmB,EAAE;YACpC,SAAS,EAAE,IAAI,CAAC,oBAAoB,EAAE;SACvC,CAAC;IACJ,CAAC;IAKD,kBAAkB;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEtC,OAAO;YACL,YAAY,EAAE,IAAI,CAAC,oBAAoB;YACvC,aAAa,EAAE,IAAI,CAAC,qBAAqB;YACzC,cAAc,EAAE,MAAM,EAAE,aAAa,IAAI,CAAC;YAC1C,iBAAiB,EAAE,MAAM,EAAE,gBAAgB,IAAI,CAAC;YAChD,UAAU,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACvC,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,SAAS,EAAE,IAAI,CAAC,KAAK;YACrB,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM;SACzC,CAAC;IACJ,CAAC;IAKD,UAAU;QACR,OAAO,IAAI,CAAC,oBAAoB,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,CAAC;IAC3E,CAAC;IAKD,iBAAiB;QACf,MAAM,MAAM,GAAoB,EAAE,CAAC;QAGnC,IAAI,IAAI,CAAC,oBAAoB,EAAE,GAAG,EAAE,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,gBAAgB;gBACtB,QAAQ,EAAE,SAAS;gBACnB,OAAO,EAAE,kCAAkC;gBAC3C,cAAc,EAAE,uDAAuD;aACxE,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE,OAAO;gBACjB,OAAO,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,0BAA0B;gBAC7D,cAAc,EAAE,+DAA+D;aAChF,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC1C,IAAI,MAAM,CAAC,KAAK,KAAK,WAAW,IAAI,MAAM,CAAC,eAAe,GAAG,CAAC,EAAE,EAAE,CAAC;YACjE,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,SAAS;gBACnB,OAAO,EAAE,kCAAkC;gBAC3C,cAAc,EAAE,uDAAuD;aACxE,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKD,aAAa;QACX,OAAO;YACL,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;YACjD,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE;YAC9C,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACxC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAC1C,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACxC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;SAC5C,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,eAAe,CAAC,IAAS;QAC9B,OAAO,IAAI,gBAAgB,CACzB,IAAI,CAAC,oBAAoB,EACzB,IAAI,CAAC,qBAAqB,EAC1B,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,WAAW,EAChB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAC3B,CAAC;IACJ,CAAC;IAKO,kBAAkB;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE/C,IAAI,SAAS,GAAG,CAAC,IAAI,UAAU,GAAG,EAAE;YAAE,OAAO,MAAM,CAAC;QACpD,IAAI,SAAS,GAAG,CAAC,IAAI,UAAU,GAAG,EAAE;YAAE,OAAO,QAAQ,CAAC;QACtD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,4BAA4B;QAClC,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACpD,UAAU,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,CAAC;YACrD,UAAU,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,GAAG,EAAE,EAAE,CAAC;YACrC,UAAU,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,qBAAqB;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC3C,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS,aAAa,CAAC,CAAC,CAAC,uBAAuB,CAAC;IACvF,CAAC;IAEO,mBAAmB;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC3C,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,aAAa,CAAC,CAAC,CAAC,uBAAuB,CAAC;IACxF,CAAC;IAEO,oBAAoB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC3C,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,aAAa,CAAC,CAAC,CAAC,uBAAuB,CAAC;IACzF,CAAC;CACF;AAjSD,4CAiSC"}