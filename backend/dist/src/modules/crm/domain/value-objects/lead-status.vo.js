"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadStatus = void 0;
class LeadStatus {
    value;
    static VALID_STATUSES = [
        'new',
        'qualified',
        'proposition',
        'won',
        'lost',
        'cancelled',
    ];
    static STATUS_HIERARCHY = {
        'new': 1,
        'qualified': 2,
        'proposition': 3,
        'won': 4,
        'lost': 0,
        'cancelled': 0,
    };
    constructor(value) {
        this.value = value;
        this.validateStatus();
    }
    validateStatus() {
        if (!LeadStatus.VALID_STATUSES.includes(this.value)) {
            throw new Error(`Invalid lead status: ${this.value}`);
        }
    }
    isProgression(previousStatus) {
        const currentLevel = LeadStatus.STATUS_HIERARCHY[this.value];
        const previousLevel = LeadStatus.STATUS_HIERARCHY[previousStatus.value];
        return currentLevel > previousLevel;
    }
    isRegression(previousStatus) {
        const currentLevel = LeadStatus.STATUS_HIERARCHY[this.value];
        const previousLevel = LeadStatus.STATUS_HIERARCHY[previousStatus.value];
        return currentLevel < previousLevel && currentLevel > 0 && previousLevel > 0;
    }
    isActive() {
        return !['won', 'lost', 'cancelled'].includes(this.value);
    }
    isClosed() {
        return ['won', 'lost'].includes(this.value);
    }
    isWon() {
        return this.value === 'won';
    }
    isLost() {
        return this.value === 'lost';
    }
    isCancelled() {
        return this.value === 'cancelled';
    }
    isQualified() {
        return ['qualified', 'proposition', 'won'].includes(this.value);
    }
    isConverted() {
        return this.value === 'won';
    }
    getDisplayName() {
        const displayNames = {
            'new': 'New',
            'qualified': 'Qualified',
            'proposition': 'Proposition',
            'won': 'Won',
            'lost': 'Lost',
            'cancelled': 'Cancelled',
        };
        return displayNames[this.value] || this.value;
    }
    getColor() {
        const colors = {
            'new': '#6B7280',
            'qualified': '#3B82F6',
            'proposition': '#F59E0B',
            'won': '#10B981',
            'lost': '#EF4444',
            'cancelled': '#6B7280',
        };
        return colors[this.value] || '#6B7280';
    }
    getNextPossibleStatuses() {
        const transitions = {
            'new': ['qualified', 'lost', 'cancelled'],
            'qualified': ['proposition', 'lost', 'cancelled'],
            'proposition': ['won', 'lost', 'cancelled'],
            'won': [],
            'lost': [],
            'cancelled': [],
        };
        return (transitions[this.value] || []).map(status => new LeadStatus(status));
    }
    canTransitionTo(newStatus) {
        const possibleStatuses = this.getNextPossibleStatuses();
        return possibleStatuses.some(status => status.value === newStatus.value);
    }
    getPriority() {
        return LeadStatus.STATUS_HIERARCHY[this.value] || 0;
    }
    equals(other) {
        return this.value === other.value;
    }
    toString() {
        return this.value;
    }
    toPlainObject() {
        return {
            value: this.value,
            displayName: this.getDisplayName(),
            color: this.getColor(),
            priority: this.getPriority(),
            isActive: this.isActive(),
            isClosed: this.isClosed(),
            isWon: this.isWon(),
            isLost: this.isLost(),
            isCancelled: this.isCancelled(),
            nextPossibleStatuses: this.getNextPossibleStatuses().map(s => s.value),
        };
    }
    static fromString(value) {
        return new LeadStatus(value);
    }
    static fromPlainObject(data) {
        return new LeadStatus(data.value);
    }
    static getAllStatuses() {
        return LeadStatus.VALID_STATUSES.map(status => new LeadStatus(status));
    }
    static new() {
        return new LeadStatus('new');
    }
    static qualified() {
        return new LeadStatus('qualified');
    }
    static proposition() {
        return new LeadStatus('proposition');
    }
    static won() {
        return new LeadStatus('won');
    }
    static lost() {
        return new LeadStatus('lost');
    }
    static cancelled() {
        return new LeadStatus('cancelled');
    }
}
exports.LeadStatus = LeadStatus;
//# sourceMappingURL=lead-status.vo.js.map