import { NotificationChannel } from '@/modules/crm/domain/enums/notification-channel.enum';
export declare class NotificationPreference {
    readonly notificationType: string;
    readonly enabled: boolean;
    readonly channels: NotificationChannel[];
    readonly settings: NotificationSettings;
    constructor(notificationType: string, enabled: boolean, channels: NotificationChannel[], settings?: NotificationSettings);
    private validatePreference;
    isChannelEnabled(channel: NotificationChannel): boolean;
    appliesTo(notificationType: string): boolean;
    respectsQuietHours(): boolean;
    isQuietTime(): boolean;
    getFrequency(): 'immediate' | 'batched' | 'daily' | 'weekly';
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): NotificationPreference;
    static createDefaults(): NotificationPreference[];
}
export interface NotificationSettings {
    quietHours?: {
        start: string;
        end: string;
    };
    frequency?: 'immediate' | 'batched' | 'daily' | 'weekly';
    maxPerDay?: number;
    timezone?: string;
    language?: string;
    customSettings?: Record<string, any>;
}
