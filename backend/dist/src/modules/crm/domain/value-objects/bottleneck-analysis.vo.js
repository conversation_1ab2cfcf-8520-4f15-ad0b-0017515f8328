"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BottleneckAnalysis = void 0;
class BottleneckAnalysis {
    bottlenecks;
    totalImpact;
    prioritizedBottlenecks;
    actionPlan;
    analyzedAt;
    constructor(bottlenecks, totalImpact, prioritizedBottlenecks, actionPlan, analyzedAt) {
        this.bottlenecks = bottlenecks;
        this.totalImpact = totalImpact;
        this.prioritizedBottlenecks = prioritizedBottlenecks;
        this.actionPlan = actionPlan;
        this.analyzedAt = analyzedAt;
        this.validateAnalysis();
    }
    validateAnalysis() {
        if (!Array.isArray(this.bottlenecks)) {
            throw new Error('Bottlenecks must be an array');
        }
        if (!this.totalImpact) {
            throw new Error('Total impact is required');
        }
    }
    getCriticalBottlenecks() {
        return this.bottlenecks.filter(bottleneck => bottleneck.severity === 'high');
    }
    getBottlenecksByType(type) {
        return this.bottlenecks.filter(bottleneck => bottleneck.type === type);
    }
    getSeverityScore() {
        if (this.bottlenecks.length === 0)
            return 0;
        const severityWeights = { low: 1, medium: 3, high: 5 };
        const totalSeverity = this.bottlenecks.reduce((sum, bottleneck) => {
            return sum + severityWeights[bottleneck.severity];
        }, 0);
        const maxPossibleSeverity = 20 * severityWeights.high;
        return Math.min(100, (totalSeverity / maxPossibleSeverity) * 100);
    }
    getHealthGrade() {
        const score = this.getSeverityScore();
        if (score <= 20)
            return 'A';
        if (score <= 40)
            return 'B';
        if (score <= 60)
            return 'C';
        if (score <= 80)
            return 'D';
        return 'F';
    }
    getMostImpactfulBottleneck() {
        if (this.bottlenecks.length === 0)
            return null;
        return this.bottlenecks.reduce((most, current) => current.affectedLeads > most.affectedLeads ? current : most);
    }
    getUrgentBottlenecks() {
        return this.bottlenecks.filter(bottleneck => bottleneck.severity === 'high' || bottleneck.affectedLeads > 50);
    }
    getEstimatedResolutionTime() {
        const urgentCount = this.getUrgentBottlenecks().length;
        const totalCount = this.bottlenecks.length;
        let weeks = 0;
        this.bottlenecks.forEach(bottleneck => {
            switch (bottleneck.severity) {
                case 'high':
                    weeks += 3;
                    break;
                case 'medium':
                    weeks += 2;
                    break;
                case 'low':
                    weeks += 1;
                    break;
            }
        });
        return {
            totalWeeks: Math.min(weeks, 52),
            urgentBottlenecks: urgentCount,
            parallelResolution: Math.ceil(weeks / 2),
            phases: this.generateResolutionPhases(),
        };
    }
    getBottleneckTrends() {
        return {
            newBottlenecks: this.bottlenecks.filter(b => b.isNew).length,
            resolvedBottlenecks: 0,
            worsening: this.bottlenecks.filter(b => b.trend === 'worsening').length,
            improving: this.bottlenecks.filter(b => b.trend === 'improving').length,
            overallTrend: this.calculateOverallTrend(),
        };
    }
    getResolutionROI() {
        const potentialRevenue = this.bottlenecks.reduce((sum, bottleneck) => {
            return sum + (bottleneck.potentialRevenue || 0);
        }, 0);
        const estimatedCost = this.bottlenecks.reduce((sum, bottleneck) => {
            return sum + (bottleneck.resolutionCost || 0);
        }, 0);
        const roi = estimatedCost > 0 ? ((potentialRevenue - estimatedCost) / estimatedCost) * 100 : 0;
        return {
            potentialRevenue,
            estimatedCost,
            roi,
            paybackPeriod: this.calculatePaybackPeriod(potentialRevenue, estimatedCost),
            priority: roi > 200 ? 'high' : roi > 100 ? 'medium' : 'low',
        };
    }
    getBottleneckSummary() {
        const critical = this.getCriticalBottlenecks();
        const urgent = this.getUrgentBottlenecks();
        const mostImpactful = this.getMostImpactfulBottleneck();
        return {
            totalBottlenecks: this.bottlenecks.length,
            criticalCount: critical.length,
            urgentCount: urgent.length,
            severityScore: this.getSeverityScore(),
            healthGrade: this.getHealthGrade(),
            mostImpactfulType: mostImpactful?.type,
            totalAffectedLeads: this.totalImpact.delayedDeals,
            estimatedRevenueLoss: this.totalImpact.lostRevenue,
            estimatedResolutionWeeks: this.getEstimatedResolutionTime().parallelResolution,
        };
    }
    toPlainObject() {
        return {
            bottlenecks: this.bottlenecks,
            totalImpact: this.totalImpact,
            prioritizedBottlenecks: this.prioritizedBottlenecks,
            actionPlan: this.actionPlan,
            severityScore: this.getSeverityScore(),
            healthGrade: this.getHealthGrade(),
            criticalBottlenecks: this.getCriticalBottlenecks(),
            urgentBottlenecks: this.getUrgentBottlenecks(),
            mostImpactfulBottleneck: this.getMostImpactfulBottleneck(),
            resolutionTimeEstimate: this.getEstimatedResolutionTime(),
            bottleneckTrends: this.getBottleneckTrends(),
            resolutionROI: this.getResolutionROI(),
            bottleneckSummary: this.getBottleneckSummary(),
            analyzedAt: this.analyzedAt.toISOString(),
        };
    }
    static fromPlainObject(data) {
        return new BottleneckAnalysis(data.bottlenecks, data.totalImpact, data.prioritizedBottlenecks, data.actionPlan, new Date(data.analyzedAt));
    }
    generateResolutionPhases() {
        const phases = [];
        const critical = this.getCriticalBottlenecks();
        if (critical.length > 0) {
            phases.push({
                phase: 1,
                name: 'Critical Issues',
                bottlenecks: critical.map(b => b.type),
                estimatedWeeks: critical.length * 2,
                priority: 'high',
            });
        }
        const highImpact = this.bottlenecks.filter(b => b.severity === 'medium' && b.affectedLeads > 20);
        if (highImpact.length > 0) {
            phases.push({
                phase: 2,
                name: 'High Impact Issues',
                bottlenecks: highImpact.map(b => b.type),
                estimatedWeeks: highImpact.length * 1.5,
                priority: 'medium',
            });
        }
        const remaining = this.bottlenecks.filter(b => b.severity === 'low' || (b.severity === 'medium' && b.affectedLeads <= 20));
        if (remaining.length > 0) {
            phases.push({
                phase: 3,
                name: 'Optimization',
                bottlenecks: remaining.map(b => b.type),
                estimatedWeeks: remaining.length,
                priority: 'low',
            });
        }
        return phases;
    }
    calculateOverallTrend() {
        const worsening = this.bottlenecks.filter(b => b.trend === 'worsening').length;
        const improving = this.bottlenecks.filter(b => b.trend === 'improving').length;
        if (worsening > improving)
            return 'worsening';
        if (improving > worsening)
            return 'improving';
        return 'stable';
    }
    calculatePaybackPeriod(revenue, cost) {
        if (cost === 0 || revenue === 0)
            return 0;
        const monthlyBenefit = revenue / 12;
        return cost / monthlyBenefit;
    }
}
exports.BottleneckAnalysis = BottleneckAnalysis;
//# sourceMappingURL=bottleneck-analysis.vo.js.map