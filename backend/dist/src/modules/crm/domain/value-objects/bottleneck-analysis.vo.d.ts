export declare class BottleneckAnalysis {
    readonly bottlenecks: Bottleneck[];
    readonly totalImpact: BottleneckImpact;
    readonly prioritizedBottlenecks: Bottleneck[];
    readonly actionPlan: ActionPlan[];
    readonly analyzedAt: Date;
    constructor(bottlenecks: Bottleneck[], totalImpact: BottleneckImpact, prioritizedBottlenecks: Bottleneck[], actionPlan: ActionPlan[], analyzedAt: Date);
    private validateAnalysis;
    getCriticalBottlenecks(): Bottleneck[];
    getBottlenecksByType(type: string): Bottleneck[];
    getSeverityScore(): number;
    getHealthGrade(): string;
    getMostImpactfulBottleneck(): Bottleneck | null;
    getUrgentBottlenecks(): Bottleneck[];
    getEstimatedResolutionTime(): ResolutionTimeEstimate;
    getBottleneckTrends(): BottleneckTrends;
    getResolutionROI(): ResolutionROI;
    getBottleneckSummary(): BottleneckSummary;
    toPlainObject(): Record<string, any>;
    static fromPlainObject(data: any): BottleneckAnalysis;
    private generateResolutionPhases;
    private calculateOverallTrend;
    private calculatePaybackPeriod;
}
export interface Bottleneck {
    type: string;
    stageId?: number;
    stageName?: string;
    severity: 'low' | 'medium' | 'high';
    affectedLeads: number;
    averageDelay?: number;
    recommendations: string[];
    potentialRevenue?: number;
    resolutionCost?: number;
    isNew?: boolean;
    trend?: 'improving' | 'stable' | 'worsening';
}
export interface BottleneckImpact {
    delayedDeals: number;
    lostRevenue: number;
    additionalMetrics?: Record<string, number>;
}
export interface ActionPlan {
    bottleneck: string;
    action: string;
    priority: 'low' | 'medium' | 'high';
    timeline: string;
    owner?: string;
    resources?: string[];
}
export interface ResolutionTimeEstimate {
    totalWeeks: number;
    urgentBottlenecks: number;
    parallelResolution: number;
    phases: ResolutionPhase[];
}
export interface ResolutionPhase {
    phase: number;
    name: string;
    bottlenecks: string[];
    estimatedWeeks: number;
    priority: 'low' | 'medium' | 'high';
}
export interface BottleneckTrends {
    newBottlenecks: number;
    resolvedBottlenecks: number;
    worsening: number;
    improving: number;
    overallTrend: 'improving' | 'stable' | 'worsening';
}
export interface ResolutionROI {
    potentialRevenue: number;
    estimatedCost: number;
    roi: number;
    paybackPeriod: number;
    priority: 'low' | 'medium' | 'high';
}
export interface BottleneckSummary {
    totalBottlenecks: number;
    criticalCount: number;
    urgentCount: number;
    severityScore: number;
    healthGrade: string;
    mostImpactfulType?: string;
    totalAffectedLeads: number;
    estimatedRevenueLoss: number;
    estimatedResolutionWeeks: number;
}
