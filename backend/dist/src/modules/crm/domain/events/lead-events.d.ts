import { DomainEvent } from './base/domain-event.base';
import { Lead } from '../entities/lead.entity';
import { LeadStatus } from '../value-objects/lead-status.vo';
import { LeadPriority } from '../value-objects/lead-priority.vo';
export declare class LeadCreatedEvent extends DomainEvent {
    readonly lead: Lead;
    readonly source: string;
    readonly campaign?: string | undefined;
    constructor(lead: Lead, source: string, campaign?: string | undefined, causationId?: string, correlationId?: string, userId?: string, tenantId?: string);
    getPayload(): Record<string, any>;
}
export declare class LeadStatusChangedEvent extends DomainEvent {
    readonly leadId: number;
    readonly previousStatus: LeadStatus;
    readonly newStatus: LeadStatus;
    readonly reason?: string | undefined;
    readonly changedBy?: string | undefined;
    constructor(leadId: number, previousStatus: LeadStatus, newStatus: LeadStatus, reason?: string | undefined, changedBy?: string | undefined, causationId?: string, correlationId?: string, userId?: string, tenantId?: string);
    getPayload(): Record<string, any>;
}
export declare class LeadAssignedEvent extends DomainEvent {
    readonly leadId: number;
    readonly assignedUserId?: number | undefined;
    readonly assignedTeamId?: number | undefined;
    readonly previousAssignedUserId?: number | undefined;
    readonly previousAssignedTeamId?: number | undefined;
    readonly assignmentReason?: string | undefined;
    readonly autoAssigned: boolean;
    constructor(leadId: number, assignedUserId?: number | undefined, assignedTeamId?: number | undefined, previousAssignedUserId?: number | undefined, previousAssignedTeamId?: number | undefined, assignmentReason?: string | undefined, autoAssigned?: boolean, causationId?: string, correlationId?: string, userId?: string, tenantId?: string);
    getPayload(): Record<string, any>;
}
export declare class LeadPriorityChangedEvent extends DomainEvent {
    readonly leadId: number;
    readonly previousPriority: LeadPriority;
    readonly newPriority: LeadPriority;
    readonly reason?: string | undefined;
    constructor(leadId: number, previousPriority: LeadPriority, newPriority: LeadPriority, reason?: string | undefined, causationId?: string, correlationId?: string, userId?: string, tenantId?: string);
    getPayload(): Record<string, any>;
}
export declare class LeadConvertedToOpportunityEvent extends DomainEvent {
    readonly leadId: number;
    readonly opportunityId: number;
    readonly conversionData: {
        expectedRevenue: number;
        probability: number;
        partnerId?: number;
        stageId?: number;
        dateDeadline?: Date;
        description?: string;
    };
    readonly convertedBy: string;
    readonly conversionReason?: string | undefined;
    constructor(leadId: number, opportunityId: number, conversionData: {
        expectedRevenue: number;
        probability: number;
        partnerId?: number;
        stageId?: number;
        dateDeadline?: Date;
        description?: string;
    }, convertedBy: string, conversionReason?: string | undefined, causationId?: string, correlationId?: string, userId?: string, tenantId?: string);
    getPayload(): Record<string, any>;
}
export declare class LeadScoreChangedEvent extends DomainEvent {
    readonly leadId: number;
    readonly previousScore: number;
    readonly newScore: number;
    readonly scoringFactors: Record<string, number>;
    readonly scoringReason: string;
    constructor(leadId: number, previousScore: number, newScore: number, scoringFactors: Record<string, number>, scoringReason: string, causationId?: string, correlationId?: string, userId?: string, tenantId?: string);
    getPayload(): Record<string, any>;
}
export declare class LeadActivityCreatedEvent extends DomainEvent {
    readonly leadId: number;
    readonly activityId: number;
    readonly activityType: string;
    readonly activityData: Record<string, any>;
    readonly createdBy: string;
    constructor(leadId: number, activityId: number, activityType: string, activityData: Record<string, any>, createdBy: string, causationId?: string, correlationId?: string, userId?: string, tenantId?: string);
    getPayload(): Record<string, any>;
}
