export declare abstract class DomainEvent {
    readonly eventId: string;
    readonly eventType: string;
    readonly aggregateId: string;
    readonly aggregateType: string;
    readonly aggregateVersion: number;
    readonly occurredAt: Date;
    readonly causationId?: string;
    readonly correlationId?: string;
    readonly userId?: string;
    readonly tenantId?: string;
    readonly metadata: EventMetadata;
    constructor(aggregateId: string, aggregateType: string, aggregateVersion: number, eventType: string, metadata?: Partial<EventMetadata>, causationId?: string, correlationId?: string, userId?: string, tenantId?: string);
    abstract getPayload(): Record<string, any>;
    toJSON(): SerializedDomainEvent;
    static fromJSON(data: SerializedDomainEvent): DomainEvent;
    isRelatedTo(other: DomainEvent): boolean;
    getStreamName(): string;
    getRoutingKey(): string;
    shouldPublishExternally(): boolean;
    getPriority(): EventPriority;
    getRetryConfig(): RetryConfig;
}
export interface EventMetadata {
    source?: string;
    version?: string;
    environment?: string;
    publishExternal?: boolean;
    priority?: EventPriority;
    retryConfig?: RetryConfig;
    tags?: string[];
    businessContext?: Record<string, any>;
    technicalContext?: Record<string, any>;
    [key: string]: any;
}
export interface SerializedDomainEvent {
    eventId: string;
    eventType: string;
    aggregateId: string;
    aggregateType: string;
    aggregateVersion: number;
    occurredAt: string;
    causationId?: string;
    correlationId?: string;
    userId?: string;
    tenantId?: string;
    metadata: EventMetadata;
    payload: Record<string, any>;
}
export declare enum EventPriority {
    LOW = 1,
    NORMAL = 2,
    HIGH = 3,
    CRITICAL = 4
}
export interface RetryConfig {
    maxRetries: number;
    backoffStrategy: 'linear' | 'exponential' | 'fixed';
    initialDelay: number;
    maxDelay?: number;
    jitter?: boolean;
}
export interface EventStore {
    append(streamName: string, events: DomainEvent[], expectedVersion?: number): Promise<void>;
    getEvents(streamName: string, fromVersion?: number): Promise<DomainEvent[]>;
    getAllEvents(fromPosition?: number): Promise<DomainEvent[]>;
    getSnapshot(aggregateId: string, aggregateType: string): Promise<any>;
    saveSnapshot(aggregateId: string, aggregateType: string, snapshot: any, version: number): Promise<void>;
}
export interface EventPublisher {
    publish(event: DomainEvent): Promise<void>;
    publishBatch(events: DomainEvent[]): Promise<void>;
}
export interface EventHandler<T extends DomainEvent = DomainEvent> {
    handle(event: T): Promise<void>;
    canHandle(event: DomainEvent): boolean;
    getHandledEventTypes(): string[];
}
export interface EventBus {
    publish(event: DomainEvent): Promise<void>;
    publishBatch(events: DomainEvent[]): Promise<void>;
    subscribe<T extends DomainEvent>(eventType: string, handler: EventHandler<T>): void;
    unsubscribe(eventType: string, handler: EventHandler): void;
}
export interface AggregateRoot {
    id: string;
    version: number;
    getUncommittedEvents(): DomainEvent[];
    markEventsAsCommitted(): void;
    loadFromHistory(events: DomainEvent[]): void;
}
