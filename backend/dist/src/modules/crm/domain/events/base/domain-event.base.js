"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventPriority = exports.DomainEvent = void 0;
const uuid_1 = require("uuid");
class DomainEvent {
    eventId;
    eventType;
    aggregateId;
    aggregateType;
    aggregateVersion;
    occurredAt;
    causationId;
    correlationId;
    userId;
    tenantId;
    metadata;
    constructor(aggregateId, aggregateType, aggregateVersion, eventType, metadata = {}, causationId, correlationId, userId, tenantId) {
        this.eventId = (0, uuid_1.v4)();
        this.eventType = eventType;
        this.aggregateId = aggregateId;
        this.aggregateType = aggregateType;
        this.aggregateVersion = aggregateVersion;
        this.occurredAt = new Date();
        this.causationId = causationId;
        this.correlationId = correlationId || (0, uuid_1.v4)();
        this.userId = userId;
        this.tenantId = tenantId;
        this.metadata = {
            source: 'crm-service',
            version: '1.0.0',
            environment: process.env.NODE_ENV || 'development',
            ...metadata,
        };
    }
    toJSON() {
        return {
            eventId: this.eventId,
            eventType: this.eventType,
            aggregateId: this.aggregateId,
            aggregateType: this.aggregateType,
            aggregateVersion: this.aggregateVersion,
            occurredAt: this.occurredAt.toISOString(),
            causationId: this.causationId,
            correlationId: this.correlationId,
            userId: this.userId,
            tenantId: this.tenantId,
            metadata: this.metadata,
            payload: this.getPayload(),
        };
    }
    static fromJSON(data) {
        throw new Error('fromJSON must be implemented by concrete event classes');
    }
    isRelatedTo(other) {
        return this.correlationId === other.correlationId ||
            this.causationId === other.eventId ||
            other.causationId === this.eventId;
    }
    getStreamName() {
        return `${this.aggregateType}-${this.aggregateId}`;
    }
    getRoutingKey() {
        return `${this.aggregateType}.${this.eventType}`;
    }
    shouldPublishExternally() {
        return this.metadata.publishExternal !== false;
    }
    getPriority() {
        return this.metadata.priority || EventPriority.NORMAL;
    }
    getRetryConfig() {
        return this.metadata.retryConfig || {
            maxRetries: 3,
            backoffStrategy: 'exponential',
            initialDelay: 1000,
        };
    }
}
exports.DomainEvent = DomainEvent;
var EventPriority;
(function (EventPriority) {
    EventPriority[EventPriority["LOW"] = 1] = "LOW";
    EventPriority[EventPriority["NORMAL"] = 2] = "NORMAL";
    EventPriority[EventPriority["HIGH"] = 3] = "HIGH";
    EventPriority[EventPriority["CRITICAL"] = 4] = "CRITICAL";
})(EventPriority || (exports.EventPriority = EventPriority = {}));
//# sourceMappingURL=domain-event.base.js.map