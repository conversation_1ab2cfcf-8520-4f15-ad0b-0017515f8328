"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadActivityCreatedEvent = exports.LeadScoreChangedEvent = exports.LeadConvertedToOpportunityEvent = exports.LeadPriorityChangedEvent = exports.LeadAssignedEvent = exports.LeadStatusChangedEvent = exports.LeadCreatedEvent = void 0;
const domain_event_base_1 = require("./base/domain-event.base");
class LeadCreatedEvent extends domain_event_base_1.DomainEvent {
    lead;
    source;
    campaign;
    constructor(lead, source, campaign, causationId, correlationId, userId, tenantId) {
        super(lead.id.toString(), 'Lead', 1, 'LeadCreated', {
            priority: domain_event_base_1.EventPriority.HIGH,
            publishExternal: true,
            tags: ['lead', 'creation', 'crm'],
            businessContext: {
                leadSource: source,
                campaign,
                leadType: lead.type?.value,
                leadPriority: lead.priority?.value,
                hasAssignment: !!lead.assignedUserId,
                hasTeam: !!lead.teamId,
                expectedRevenue: lead.expectedRevenue,
            },
            technicalContext: {
                createdVia: 'api',
                validationPassed: true,
                autoAssigned: !lead.assignedUserId,
            },
        }, causationId, correlationId, userId, tenantId);
        this.lead = lead;
        this.source = source;
        this.campaign = campaign;
    }
    getPayload() {
        return {
            leadId: this.lead.id,
            leadData: {
                name: this.lead.name,
                email: this.lead.contactInfo?.email,
                phone: this.lead.contactInfo?.phone,
                company: this.lead.contactInfo?.company,
                source: this.source,
                type: this.lead.type?.value,
                priority: this.lead.priority?.value,
                status: this.lead.status?.value,
                expectedRevenue: this.lead.expectedRevenue,
                probability: this.lead.probability,
                assignedUserId: this.lead.assignedUserId,
                teamId: this.lead.teamId,
                tags: this.lead.tags,
                dateDeadline: this.lead.dateDeadline?.toISOString(),
            },
            campaign: this.campaign,
            source: this.source,
        };
    }
}
exports.LeadCreatedEvent = LeadCreatedEvent;
class LeadStatusChangedEvent extends domain_event_base_1.DomainEvent {
    leadId;
    previousStatus;
    newStatus;
    reason;
    changedBy;
    constructor(leadId, previousStatus, newStatus, reason, changedBy, causationId, correlationId, userId, tenantId) {
        super(leadId.toString(), 'Lead', 1, 'LeadStatusChanged', {
            priority: domain_event_base_1.EventPriority.NORMAL,
            publishExternal: true,
            tags: ['lead', 'status-change', 'workflow'],
            businessContext: {
                previousStatus: previousStatus.value,
                newStatus: newStatus.value,
                reason,
                changedBy,
                isProgression: newStatus.isProgression(previousStatus),
                isRegression: newStatus.isRegression(previousStatus),
            },
        }, causationId, correlationId, userId, tenantId);
        this.leadId = leadId;
        this.previousStatus = previousStatus;
        this.newStatus = newStatus;
        this.reason = reason;
        this.changedBy = changedBy;
    }
    getPayload() {
        return {
            leadId: this.leadId,
            previousStatus: this.previousStatus.value,
            newStatus: this.newStatus.value,
            reason: this.reason,
            changedBy: this.changedBy,
            statusTransition: {
                from: this.previousStatus.value,
                to: this.newStatus.value,
                isProgression: this.newStatus.isProgression(this.previousStatus),
                isRegression: this.newStatus.isRegression(this.previousStatus),
            },
        };
    }
}
exports.LeadStatusChangedEvent = LeadStatusChangedEvent;
class LeadAssignedEvent extends domain_event_base_1.DomainEvent {
    leadId;
    assignedUserId;
    assignedTeamId;
    previousAssignedUserId;
    previousAssignedTeamId;
    assignmentReason;
    autoAssigned;
    constructor(leadId, assignedUserId, assignedTeamId, previousAssignedUserId, previousAssignedTeamId, assignmentReason, autoAssigned = false, causationId, correlationId, userId, tenantId) {
        super(leadId.toString(), 'Lead', 1, 'LeadAssigned', {
            priority: domain_event_base_1.EventPriority.HIGH,
            publishExternal: true,
            tags: ['lead', 'assignment', 'workflow'],
            businessContext: {
                assignedUserId,
                assignedTeamId,
                previousAssignedUserId,
                previousAssignedTeamId,
                assignmentReason,
                autoAssigned,
                isReassignment: !!(previousAssignedUserId || previousAssignedTeamId),
            },
        }, causationId, correlationId, userId, tenantId);
        this.leadId = leadId;
        this.assignedUserId = assignedUserId;
        this.assignedTeamId = assignedTeamId;
        this.previousAssignedUserId = previousAssignedUserId;
        this.previousAssignedTeamId = previousAssignedTeamId;
        this.assignmentReason = assignmentReason;
        this.autoAssigned = autoAssigned;
    }
    getPayload() {
        return {
            leadId: this.leadId,
            assignment: {
                userId: this.assignedUserId,
                teamId: this.assignedTeamId,
                reason: this.assignmentReason,
                autoAssigned: this.autoAssigned,
            },
            previousAssignment: {
                userId: this.previousAssignedUserId,
                teamId: this.previousAssignedTeamId,
            },
        };
    }
}
exports.LeadAssignedEvent = LeadAssignedEvent;
class LeadPriorityChangedEvent extends domain_event_base_1.DomainEvent {
    leadId;
    previousPriority;
    newPriority;
    reason;
    constructor(leadId, previousPriority, newPriority, reason, causationId, correlationId, userId, tenantId) {
        super(leadId.toString(), 'Lead', 1, 'LeadPriorityChanged', {
            priority: domain_event_base_1.EventPriority.NORMAL,
            publishExternal: true,
            tags: ['lead', 'priority-change'],
            businessContext: {
                previousPriority: previousPriority.value,
                newPriority: newPriority.value,
                reason,
                isEscalation: newPriority.value > previousPriority.value,
                isDeescalation: newPriority.value < previousPriority.value,
            },
        }, causationId, correlationId, userId, tenantId);
        this.leadId = leadId;
        this.previousPriority = previousPriority;
        this.newPriority = newPriority;
        this.reason = reason;
    }
    getPayload() {
        return {
            leadId: this.leadId,
            previousPriority: this.previousPriority.value,
            newPriority: this.newPriority.value,
            reason: this.reason,
            priorityChange: {
                from: this.previousPriority.value,
                to: this.newPriority.value,
                isEscalation: this.newPriority.value > this.previousPriority.value,
                isDeescalation: this.newPriority.value < this.previousPriority.value,
            },
        };
    }
}
exports.LeadPriorityChangedEvent = LeadPriorityChangedEvent;
class LeadConvertedToOpportunityEvent extends domain_event_base_1.DomainEvent {
    leadId;
    opportunityId;
    conversionData;
    convertedBy;
    conversionReason;
    constructor(leadId, opportunityId, conversionData, convertedBy, conversionReason, causationId, correlationId, userId, tenantId) {
        super(leadId.toString(), 'Lead', 1, 'LeadConvertedToOpportunity', {
            priority: domain_event_base_1.EventPriority.HIGH,
            publishExternal: true,
            tags: ['lead', 'opportunity', 'conversion', 'sales'],
            businessContext: {
                opportunityId,
                expectedRevenue: conversionData.expectedRevenue,
                probability: conversionData.probability,
                convertedBy,
                conversionReason,
                hasPartner: !!conversionData.partnerId,
                hasDeadline: !!conversionData.dateDeadline,
            },
        }, causationId, correlationId, userId, tenantId);
        this.leadId = leadId;
        this.opportunityId = opportunityId;
        this.conversionData = conversionData;
        this.convertedBy = convertedBy;
        this.conversionReason = conversionReason;
    }
    getPayload() {
        return {
            leadId: this.leadId,
            opportunityId: this.opportunityId,
            conversionData: {
                ...this.conversionData,
                dateDeadline: this.conversionData.dateDeadline?.toISOString(),
            },
            convertedBy: this.convertedBy,
            conversionReason: this.conversionReason,
        };
    }
}
exports.LeadConvertedToOpportunityEvent = LeadConvertedToOpportunityEvent;
class LeadScoreChangedEvent extends domain_event_base_1.DomainEvent {
    leadId;
    previousScore;
    newScore;
    scoringFactors;
    scoringReason;
    constructor(leadId, previousScore, newScore, scoringFactors, scoringReason, causationId, correlationId, userId, tenantId) {
        super(leadId.toString(), 'Lead', 1, 'LeadScoreChanged', {
            priority: domain_event_base_1.EventPriority.NORMAL,
            publishExternal: false,
            tags: ['lead', 'scoring', 'analytics'],
            businessContext: {
                previousScore,
                newScore,
                scoringFactors,
                scoringReason,
                scoreIncrease: newScore > previousScore,
                scoreDecrease: newScore < previousScore,
                scoreDelta: newScore - previousScore,
            },
        }, causationId, correlationId, userId, tenantId);
        this.leadId = leadId;
        this.previousScore = previousScore;
        this.newScore = newScore;
        this.scoringFactors = scoringFactors;
        this.scoringReason = scoringReason;
    }
    getPayload() {
        return {
            leadId: this.leadId,
            previousScore: this.previousScore,
            newScore: this.newScore,
            scoringFactors: this.scoringFactors,
            scoringReason: this.scoringReason,
            scoreChange: {
                delta: this.newScore - this.previousScore,
                percentage: this.previousScore > 0 ?
                    ((this.newScore - this.previousScore) / this.previousScore) * 100 : 0,
            },
        };
    }
}
exports.LeadScoreChangedEvent = LeadScoreChangedEvent;
class LeadActivityCreatedEvent extends domain_event_base_1.DomainEvent {
    leadId;
    activityId;
    activityType;
    activityData;
    createdBy;
    constructor(leadId, activityId, activityType, activityData, createdBy, causationId, correlationId, userId, tenantId) {
        super(leadId.toString(), 'Lead', 1, 'LeadActivityCreated', {
            priority: domain_event_base_1.EventPriority.LOW,
            publishExternal: false,
            tags: ['lead', 'activity', 'engagement'],
            businessContext: {
                activityType,
                createdBy,
                hasFollowUp: !!activityData.followUpDate,
            },
        }, causationId, correlationId, userId, tenantId);
        this.leadId = leadId;
        this.activityId = activityId;
        this.activityType = activityType;
        this.activityData = activityData;
        this.createdBy = createdBy;
    }
    getPayload() {
        return {
            leadId: this.leadId,
            activityId: this.activityId,
            activityType: this.activityType,
            activityData: this.activityData,
            createdBy: this.createdBy,
        };
    }
}
exports.LeadActivityCreatedEvent = LeadActivityCreatedEvent;
//# sourceMappingURL=lead-events.js.map