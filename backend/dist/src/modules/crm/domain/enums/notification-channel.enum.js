"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RECOMMENDED_CHANNELS = exports.CHANNEL_CAPABILITIES = exports.NotificationChannel = void 0;
exports.getChannelCapabilities = getChannelCapabilities;
exports.supportsRichContent = supportsRichContent;
exports.supportsAttachments = supportsAttachments;
exports.getMaxContentLength = getMaxContentLength;
exports.getRecommendedChannels = getRecommendedChannels;
var NotificationChannel;
(function (NotificationChannel) {
    NotificationChannel["EMAIL"] = "email";
    NotificationChannel["SMS"] = "sms";
    NotificationChannel["PUSH"] = "push";
    NotificationChannel["IN_APP"] = "in_app";
    NotificationChannel["SLACK"] = "slack";
    NotificationChannel["WEBHOOK"] = "webhook";
    NotificationChannel["TEAMS"] = "teams";
    NotificationChannel["DISCORD"] = "discord";
})(NotificationChannel || (exports.NotificationChannel = NotificationChannel = {}));
exports.CHANNEL_CAPABILITIES = {
    [NotificationChannel.EMAIL]: {
        supportsRichContent: true,
        supportsAttachments: true,
        supportsScheduling: true,
        maxContentLength: 100000,
        deliverySpeed: 'medium',
        reliability: 'high',
        cost: 'low',
    },
    [NotificationChannel.SMS]: {
        supportsRichContent: false,
        supportsAttachments: false,
        supportsScheduling: true,
        maxContentLength: 160,
        deliverySpeed: 'fast',
        reliability: 'high',
        cost: 'medium',
    },
    [NotificationChannel.PUSH]: {
        supportsRichContent: true,
        supportsAttachments: false,
        supportsScheduling: true,
        maxContentLength: 4000,
        deliverySpeed: 'fast',
        reliability: 'medium',
        cost: 'low',
    },
    [NotificationChannel.IN_APP]: {
        supportsRichContent: true,
        supportsAttachments: true,
        supportsScheduling: false,
        maxContentLength: 10000,
        deliverySpeed: 'instant',
        reliability: 'high',
        cost: 'free',
    },
    [NotificationChannel.SLACK]: {
        supportsRichContent: true,
        supportsAttachments: true,
        supportsScheduling: false,
        maxContentLength: 40000,
        deliverySpeed: 'fast',
        reliability: 'high',
        cost: 'free',
    },
    [NotificationChannel.WEBHOOK]: {
        supportsRichContent: true,
        supportsAttachments: true,
        supportsScheduling: true,
        maxContentLength: 1000000,
        deliverySpeed: 'fast',
        reliability: 'medium',
        cost: 'free',
    },
    [NotificationChannel.TEAMS]: {
        supportsRichContent: true,
        supportsAttachments: true,
        supportsScheduling: false,
        maxContentLength: 28000,
        deliverySpeed: 'fast',
        reliability: 'high',
        cost: 'free',
    },
    [NotificationChannel.DISCORD]: {
        supportsRichContent: true,
        supportsAttachments: true,
        supportsScheduling: false,
        maxContentLength: 2000,
        deliverySpeed: 'fast',
        reliability: 'high',
        cost: 'free',
    },
};
function getChannelCapabilities(channel) {
    return exports.CHANNEL_CAPABILITIES[channel];
}
function supportsRichContent(channel) {
    return exports.CHANNEL_CAPABILITIES[channel].supportsRichContent;
}
function supportsAttachments(channel) {
    return exports.CHANNEL_CAPABILITIES[channel].supportsAttachments;
}
function getMaxContentLength(channel) {
    return exports.CHANNEL_CAPABILITIES[channel].maxContentLength;
}
exports.RECOMMENDED_CHANNELS = {
    urgent: [NotificationChannel.SMS, NotificationChannel.PUSH, NotificationChannel.EMAIL],
    important: [NotificationChannel.EMAIL, NotificationChannel.IN_APP, NotificationChannel.PUSH],
    informational: [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
    marketing: [NotificationChannel.EMAIL, NotificationChannel.PUSH],
    system: [NotificationChannel.IN_APP, NotificationChannel.EMAIL],
    team_collaboration: [NotificationChannel.SLACK, NotificationChannel.TEAMS, NotificationChannel.DISCORD],
    external_integration: [NotificationChannel.WEBHOOK],
};
function getRecommendedChannels(type) {
    return exports.RECOMMENDED_CHANNELS[type] || [NotificationChannel.EMAIL, NotificationChannel.IN_APP];
}
//# sourceMappingURL=notification-channel.enum.js.map