{"version": 3, "file": "notification-priority.enum.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/enums/notification-priority.enum.ts"], "names": [], "mappings": ";;;AAiFA,gEAEC;AAKD,0CAEC;AAKD,4CAEC;AAKD,4CAEC;AAKD,wCASC;AAKD,gDAEC;AAKD,wCAEC;AAKD,8EAEC;AAKD,8CAEC;AAKD,wCAEC;AAKD,gDAEC;AAKD,sCAWC;AAKD,4CAkBC;AAnMD,IAAY,oBAMX;AAND,WAAY,oBAAoB;IAC9B,6DAAO,CAAA;IACP,mEAAU,CAAA;IACV,+DAAQ,CAAA;IACR,uEAAY,CAAA;IACZ,mEAAU,CAAA;AACZ,CAAC,EANW,oBAAoB,oCAApB,oBAAoB,QAM/B;AAKY,QAAA,wBAAwB,GAAG;IACtC,CAAC,oBAAoB,CAAC,GAAG,CAAC,EAAE;QAC1B,IAAI,EAAE,KAAK;QACX,WAAW,EAAE,wCAAwC;QACrD,aAAa,EAAE,GAAG;QAClB,aAAa,EAAE,CAAC;QAChB,UAAU,EAAE,IAAI;QAChB,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE,IAAI;QACrB,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;QAC7B,KAAK,EAAE,SAAS;QAChB,IAAI,EAAE,MAAM;KACb;IACD,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE;QAC7B,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,iCAAiC;QAC9C,aAAa,EAAE,EAAE;QACjB,aAAa,EAAE,CAAC;QAChB,UAAU,EAAE,IAAI;QAChB,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE,IAAI;QACrB,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;QACrC,KAAK,EAAE,SAAS;QAChB,IAAI,EAAE,cAAc;KACrB;IACD,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;QAC3B,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,6CAA6C;QAC1D,aAAa,EAAE,EAAE;QACjB,aAAa,EAAE,CAAC;QAChB,UAAU,EAAE,GAAG;QACf,iBAAiB,EAAE,KAAK;QACxB,eAAe,EAAE,KAAK;QACtB,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC;QAC5C,KAAK,EAAE,SAAS;QAChB,IAAI,EAAE,SAAS;KAChB;IACD,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE;QAC/B,IAAI,EAAE,UAAU;QAChB,WAAW,EAAE,sDAAsD;QACnE,aAAa,EAAE,CAAC;QAChB,aAAa,EAAE,CAAC;QAChB,UAAU,EAAE,GAAG;QACf,iBAAiB,EAAE,KAAK;QACxB,eAAe,EAAE,KAAK;QACtB,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC;QACrD,KAAK,EAAE,SAAS;QAChB,IAAI,EAAE,OAAO;KACd;IACD,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE;QAC7B,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,oDAAoD;QACjE,aAAa,EAAE,CAAC;QAChB,aAAa,EAAE,EAAE;QACjB,UAAU,EAAE,EAAE;QACd,iBAAiB,EAAE,KAAK;QACxB,eAAe,EAAE,KAAK;QACtB,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC;QAChE,KAAK,EAAE,SAAS;QAChB,IAAI,EAAE,WAAW;KAClB;CACO,CAAC;AAKX,SAAgB,0BAA0B,CAAC,QAA8B;IACvE,OAAO,gCAAwB,CAAC,QAAQ,CAAC,CAAC;AAC5C,CAAC;AAKD,SAAgB,eAAe,CAAC,QAA8B;IAC5D,OAAO,gCAAwB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;AACjD,CAAC;AAKD,SAAgB,gBAAgB,CAAC,QAA8B;IAC7D,OAAO,gCAAwB,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;AAClD,CAAC;AAKD,SAAgB,gBAAgB,CAAC,QAA8B;IAC7D,OAAO,gCAAwB,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC;AAC1D,CAAC;AAKD,SAAgB,cAAc,CAAC,QAA8B;IAI3D,MAAM,eAAe,GAAG,gCAAwB,CAAC,QAAQ,CAAC,CAAC;IAC3D,OAAO;QACL,QAAQ,EAAE,eAAe,CAAC,aAAa;QACvC,KAAK,EAAE,eAAe,CAAC,UAAU;KAClC,CAAC;AACJ,CAAC;AAKD,SAAgB,kBAAkB,CAAC,QAA8B;IAC/D,OAAO,gCAAwB,CAAC,QAAQ,CAAC,CAAC,iBAAiB,CAAC;AAC9D,CAAC;AAKD,SAAgB,cAAc,CAAC,QAA8B;IAC3D,OAAO,gCAAwB,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC;AAC5D,CAAC;AAKD,SAAgB,iCAAiC,CAAC,QAA8B;IAC9E,OAAO,gCAAwB,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC;AACrD,CAAC;AAKD,SAAgB,iBAAiB,CAAC,CAAuB,EAAE,CAAuB;IAChF,OAAO,CAAC,GAAG,CAAC,CAAC;AACf,CAAC;AAKD,SAAgB,cAAc,CAAC,QAA8B;IAC3D,OAAO,QAAQ,IAAI,oBAAoB,CAAC,IAAI,CAAC;AAC/C,CAAC;AAKD,SAAgB,kBAAkB,CAAC,QAA8B;IAC/D,OAAO,QAAQ,IAAI,oBAAoB,CAAC,QAAQ,CAAC;AACnD,CAAC;AAKD,SAAgB,aAAa,CAAC,KAAa;IACzC,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;IAEvC,QAAQ,UAAU,EAAE,CAAC;QACnB,KAAK,KAAK,CAAC,CAAC,OAAO,oBAAoB,CAAC,GAAG,CAAC;QAC5C,KAAK,QAAQ,CAAC,CAAC,OAAO,oBAAoB,CAAC,MAAM,CAAC;QAClD,KAAK,MAAM,CAAC,CAAC,OAAO,oBAAoB,CAAC,IAAI,CAAC;QAC9C,KAAK,UAAU,CAAC,CAAC,OAAO,oBAAoB,CAAC,QAAQ,CAAC;QACtD,KAAK,QAAQ,CAAC,CAAC,OAAO,oBAAoB,CAAC,MAAM,CAAC;QAClD,OAAO,CAAC,CAAC,OAAO,oBAAoB,CAAC,MAAM,CAAC;IAC9C,CAAC;AACH,CAAC;AAKD,SAAgB,gBAAgB;IAM9B,OAAO,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC;SACvC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC;SAC1C,GAAG,CAAC,QAAQ,CAAC,EAAE;QACd,MAAM,eAAe,GAAG,gCAAwB,CAAC,QAAgC,CAAC,CAAC;QACnF,OAAO;YACL,KAAK,EAAE,QAAgC;YACvC,IAAI,EAAE,eAAe,CAAC,IAAI;YAC1B,WAAW,EAAE,eAAe,CAAC,WAAW;YACxC,KAAK,EAAE,eAAe,CAAC,KAAK;SAC7B,CAAC;IACJ,CAAC,CAAC;SACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;AACvC,CAAC"}