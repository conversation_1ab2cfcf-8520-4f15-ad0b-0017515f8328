export declare enum NotificationChannel {
    EMAIL = "email",
    SMS = "sms",
    PUSH = "push",
    IN_APP = "in_app",
    SLACK = "slack",
    WEBHOOK = "webhook",
    TEAMS = "teams",
    DISCORD = "discord"
}
export declare const CHANNEL_CAPABILITIES: {
    readonly email: {
        readonly supportsRichContent: true;
        readonly supportsAttachments: true;
        readonly supportsScheduling: true;
        readonly maxContentLength: 100000;
        readonly deliverySpeed: "medium";
        readonly reliability: "high";
        readonly cost: "low";
    };
    readonly sms: {
        readonly supportsRichContent: false;
        readonly supportsAttachments: false;
        readonly supportsScheduling: true;
        readonly maxContentLength: 160;
        readonly deliverySpeed: "fast";
        readonly reliability: "high";
        readonly cost: "medium";
    };
    readonly push: {
        readonly supportsRichContent: true;
        readonly supportsAttachments: false;
        readonly supportsScheduling: true;
        readonly maxContentLength: 4000;
        readonly deliverySpeed: "fast";
        readonly reliability: "medium";
        readonly cost: "low";
    };
    readonly in_app: {
        readonly supportsRichContent: true;
        readonly supportsAttachments: true;
        readonly supportsScheduling: false;
        readonly maxContentLength: 10000;
        readonly deliverySpeed: "instant";
        readonly reliability: "high";
        readonly cost: "free";
    };
    readonly slack: {
        readonly supportsRichContent: true;
        readonly supportsAttachments: true;
        readonly supportsScheduling: false;
        readonly maxContentLength: 40000;
        readonly deliverySpeed: "fast";
        readonly reliability: "high";
        readonly cost: "free";
    };
    readonly webhook: {
        readonly supportsRichContent: true;
        readonly supportsAttachments: true;
        readonly supportsScheduling: true;
        readonly maxContentLength: 1000000;
        readonly deliverySpeed: "fast";
        readonly reliability: "medium";
        readonly cost: "free";
    };
    readonly teams: {
        readonly supportsRichContent: true;
        readonly supportsAttachments: true;
        readonly supportsScheduling: false;
        readonly maxContentLength: 28000;
        readonly deliverySpeed: "fast";
        readonly reliability: "high";
        readonly cost: "free";
    };
    readonly discord: {
        readonly supportsRichContent: true;
        readonly supportsAttachments: true;
        readonly supportsScheduling: false;
        readonly maxContentLength: 2000;
        readonly deliverySpeed: "fast";
        readonly reliability: "high";
        readonly cost: "free";
    };
};
export declare function getChannelCapabilities(channel: NotificationChannel): {
    readonly supportsRichContent: true;
    readonly supportsAttachments: true;
    readonly supportsScheduling: true;
    readonly maxContentLength: 100000;
    readonly deliverySpeed: "medium";
    readonly reliability: "high";
    readonly cost: "low";
} | {
    readonly supportsRichContent: false;
    readonly supportsAttachments: false;
    readonly supportsScheduling: true;
    readonly maxContentLength: 160;
    readonly deliverySpeed: "fast";
    readonly reliability: "high";
    readonly cost: "medium";
} | {
    readonly supportsRichContent: true;
    readonly supportsAttachments: false;
    readonly supportsScheduling: true;
    readonly maxContentLength: 4000;
    readonly deliverySpeed: "fast";
    readonly reliability: "medium";
    readonly cost: "low";
} | {
    readonly supportsRichContent: true;
    readonly supportsAttachments: true;
    readonly supportsScheduling: false;
    readonly maxContentLength: 10000;
    readonly deliverySpeed: "instant";
    readonly reliability: "high";
    readonly cost: "free";
} | {
    readonly supportsRichContent: true;
    readonly supportsAttachments: true;
    readonly supportsScheduling: false;
    readonly maxContentLength: 40000;
    readonly deliverySpeed: "fast";
    readonly reliability: "high";
    readonly cost: "free";
} | {
    readonly supportsRichContent: true;
    readonly supportsAttachments: true;
    readonly supportsScheduling: true;
    readonly maxContentLength: 1000000;
    readonly deliverySpeed: "fast";
    readonly reliability: "medium";
    readonly cost: "free";
} | {
    readonly supportsRichContent: true;
    readonly supportsAttachments: true;
    readonly supportsScheduling: false;
    readonly maxContentLength: 28000;
    readonly deliverySpeed: "fast";
    readonly reliability: "high";
    readonly cost: "free";
} | {
    readonly supportsRichContent: true;
    readonly supportsAttachments: true;
    readonly supportsScheduling: false;
    readonly maxContentLength: 2000;
    readonly deliverySpeed: "fast";
    readonly reliability: "high";
    readonly cost: "free";
};
export declare function supportsRichContent(channel: NotificationChannel): boolean;
export declare function supportsAttachments(channel: NotificationChannel): boolean;
export declare function getMaxContentLength(channel: NotificationChannel): number;
export declare const RECOMMENDED_CHANNELS: {
    readonly urgent: readonly [NotificationChannel.SMS, NotificationChannel.PUSH, NotificationChannel.EMAIL];
    readonly important: readonly [NotificationChannel.EMAIL, NotificationChannel.IN_APP, NotificationChannel.PUSH];
    readonly informational: readonly [NotificationChannel.IN_APP, NotificationChannel.EMAIL];
    readonly marketing: readonly [NotificationChannel.EMAIL, NotificationChannel.PUSH];
    readonly system: readonly [NotificationChannel.IN_APP, NotificationChannel.EMAIL];
    readonly team_collaboration: readonly [NotificationChannel.SLACK, NotificationChannel.TEAMS, NotificationChannel.DISCORD];
    readonly external_integration: readonly [NotificationChannel.WEBHOOK];
};
export declare function getRecommendedChannels(type: keyof typeof RECOMMENDED_CHANNELS): NotificationChannel[];
