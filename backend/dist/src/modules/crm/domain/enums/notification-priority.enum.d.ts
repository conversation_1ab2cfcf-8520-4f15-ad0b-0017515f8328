export declare enum NotificationPriority {
    LOW = 1,
    NORMAL = 2,
    HIGH = 3,
    CRITICAL = 4,
    URGENT = 5
}
export declare const PRIORITY_CHARACTERISTICS: {
    readonly 1: {
        readonly name: "Low";
        readonly description: "Non-urgent informational notifications";
        readonly deliveryDelay: 300;
        readonly retryAttempts: 1;
        readonly retryDelay: 3600;
        readonly respectQuietHours: true;
        readonly batchingAllowed: true;
        readonly channels: readonly ["email", "in_app"];
        readonly color: "#6B7280";
        readonly icon: "info";
    };
    readonly 2: {
        readonly name: "Normal";
        readonly description: "Standard business notifications";
        readonly deliveryDelay: 60;
        readonly retryAttempts: 2;
        readonly retryDelay: 1800;
        readonly respectQuietHours: true;
        readonly batchingAllowed: true;
        readonly channels: readonly ["email", "in_app", "push"];
        readonly color: "#3B82F6";
        readonly icon: "notification";
    };
    readonly 3: {
        readonly name: "High";
        readonly description: "Important notifications requiring attention";
        readonly deliveryDelay: 10;
        readonly retryAttempts: 3;
        readonly retryDelay: 600;
        readonly respectQuietHours: false;
        readonly batchingAllowed: false;
        readonly channels: readonly ["email", "in_app", "push", "sms"];
        readonly color: "#F59E0B";
        readonly icon: "warning";
    };
    readonly 4: {
        readonly name: "Critical";
        readonly description: "Critical notifications requiring immediate attention";
        readonly deliveryDelay: 0;
        readonly retryAttempts: 5;
        readonly retryDelay: 300;
        readonly respectQuietHours: false;
        readonly batchingAllowed: false;
        readonly channels: readonly ["email", "in_app", "push", "sms", "slack"];
        readonly color: "#EF4444";
        readonly icon: "alert";
    };
    readonly 5: {
        readonly name: "Urgent";
        readonly description: "Emergency notifications requiring immediate action";
        readonly deliveryDelay: 0;
        readonly retryAttempts: 10;
        readonly retryDelay: 60;
        readonly respectQuietHours: false;
        readonly batchingAllowed: false;
        readonly channels: readonly ["email", "in_app", "push", "sms", "slack", "webhook"];
        readonly color: "#DC2626";
        readonly icon: "emergency";
    };
};
export declare function getPriorityCharacteristics(priority: NotificationPriority): {
    readonly name: "Low";
    readonly description: "Non-urgent informational notifications";
    readonly deliveryDelay: 300;
    readonly retryAttempts: 1;
    readonly retryDelay: 3600;
    readonly respectQuietHours: true;
    readonly batchingAllowed: true;
    readonly channels: readonly ["email", "in_app"];
    readonly color: "#6B7280";
    readonly icon: "info";
} | {
    readonly name: "Normal";
    readonly description: "Standard business notifications";
    readonly deliveryDelay: 60;
    readonly retryAttempts: 2;
    readonly retryDelay: 1800;
    readonly respectQuietHours: true;
    readonly batchingAllowed: true;
    readonly channels: readonly ["email", "in_app", "push"];
    readonly color: "#3B82F6";
    readonly icon: "notification";
} | {
    readonly name: "High";
    readonly description: "Important notifications requiring attention";
    readonly deliveryDelay: 10;
    readonly retryAttempts: 3;
    readonly retryDelay: 600;
    readonly respectQuietHours: false;
    readonly batchingAllowed: false;
    readonly channels: readonly ["email", "in_app", "push", "sms"];
    readonly color: "#F59E0B";
    readonly icon: "warning";
} | {
    readonly name: "Critical";
    readonly description: "Critical notifications requiring immediate attention";
    readonly deliveryDelay: 0;
    readonly retryAttempts: 5;
    readonly retryDelay: 300;
    readonly respectQuietHours: false;
    readonly batchingAllowed: false;
    readonly channels: readonly ["email", "in_app", "push", "sms", "slack"];
    readonly color: "#EF4444";
    readonly icon: "alert";
} | {
    readonly name: "Urgent";
    readonly description: "Emergency notifications requiring immediate action";
    readonly deliveryDelay: 0;
    readonly retryAttempts: 10;
    readonly retryDelay: 60;
    readonly respectQuietHours: false;
    readonly batchingAllowed: false;
    readonly channels: readonly ["email", "in_app", "push", "sms", "slack", "webhook"];
    readonly color: "#DC2626";
    readonly icon: "emergency";
};
export declare function getPriorityName(priority: NotificationPriority): string;
export declare function getPriorityColor(priority: NotificationPriority): string;
export declare function getDeliveryDelay(priority: NotificationPriority): number;
export declare function getRetryConfig(priority: NotificationPriority): {
    attempts: number;
    delay: number;
};
export declare function respectsQuietHours(priority: NotificationPriority): boolean;
export declare function allowsBatching(priority: NotificationPriority): boolean;
export declare function getRecommendedChannelsForPriority(priority: NotificationPriority): string[];
export declare function comparePriorities(a: NotificationPriority, b: NotificationPriority): number;
export declare function isHighPriority(priority: NotificationPriority): boolean;
export declare function isCriticalPriority(priority: NotificationPriority): boolean;
export declare function parsePriority(value: string): NotificationPriority;
export declare function getAllPriorities(): Array<{
    value: NotificationPriority;
    name: string;
    description: string;
    color: string;
}>;
