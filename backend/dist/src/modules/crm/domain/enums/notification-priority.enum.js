"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PRIORITY_CHARACTERISTICS = exports.NotificationPriority = void 0;
exports.getPriorityCharacteristics = getPriorityCharacteristics;
exports.getPriorityName = getPriorityName;
exports.getPriorityColor = getPriorityColor;
exports.getDeliveryDelay = getDeliveryDelay;
exports.getRetryConfig = getRetryConfig;
exports.respectsQuietHours = respectsQuietHours;
exports.allowsBatching = allowsBatching;
exports.getRecommendedChannelsForPriority = getRecommendedChannelsForPriority;
exports.comparePriorities = comparePriorities;
exports.isHighPriority = isHighPriority;
exports.isCriticalPriority = isCriticalPriority;
exports.parsePriority = parsePriority;
exports.getAllPriorities = getAllPriorities;
var NotificationPriority;
(function (NotificationPriority) {
    NotificationPriority[NotificationPriority["LOW"] = 1] = "LOW";
    NotificationPriority[NotificationPriority["NORMAL"] = 2] = "NORMAL";
    NotificationPriority[NotificationPriority["HIGH"] = 3] = "HIGH";
    NotificationPriority[NotificationPriority["CRITICAL"] = 4] = "CRITICAL";
    NotificationPriority[NotificationPriority["URGENT"] = 5] = "URGENT";
})(NotificationPriority || (exports.NotificationPriority = NotificationPriority = {}));
exports.PRIORITY_CHARACTERISTICS = {
    [NotificationPriority.LOW]: {
        name: 'Low',
        description: 'Non-urgent informational notifications',
        deliveryDelay: 300,
        retryAttempts: 1,
        retryDelay: 3600,
        respectQuietHours: true,
        batchingAllowed: true,
        channels: ['email', 'in_app'],
        color: '#6B7280',
        icon: 'info',
    },
    [NotificationPriority.NORMAL]: {
        name: 'Normal',
        description: 'Standard business notifications',
        deliveryDelay: 60,
        retryAttempts: 2,
        retryDelay: 1800,
        respectQuietHours: true,
        batchingAllowed: true,
        channels: ['email', 'in_app', 'push'],
        color: '#3B82F6',
        icon: 'notification',
    },
    [NotificationPriority.HIGH]: {
        name: 'High',
        description: 'Important notifications requiring attention',
        deliveryDelay: 10,
        retryAttempts: 3,
        retryDelay: 600,
        respectQuietHours: false,
        batchingAllowed: false,
        channels: ['email', 'in_app', 'push', 'sms'],
        color: '#F59E0B',
        icon: 'warning',
    },
    [NotificationPriority.CRITICAL]: {
        name: 'Critical',
        description: 'Critical notifications requiring immediate attention',
        deliveryDelay: 0,
        retryAttempts: 5,
        retryDelay: 300,
        respectQuietHours: false,
        batchingAllowed: false,
        channels: ['email', 'in_app', 'push', 'sms', 'slack'],
        color: '#EF4444',
        icon: 'alert',
    },
    [NotificationPriority.URGENT]: {
        name: 'Urgent',
        description: 'Emergency notifications requiring immediate action',
        deliveryDelay: 0,
        retryAttempts: 10,
        retryDelay: 60,
        respectQuietHours: false,
        batchingAllowed: false,
        channels: ['email', 'in_app', 'push', 'sms', 'slack', 'webhook'],
        color: '#DC2626',
        icon: 'emergency',
    },
};
function getPriorityCharacteristics(priority) {
    return exports.PRIORITY_CHARACTERISTICS[priority];
}
function getPriorityName(priority) {
    return exports.PRIORITY_CHARACTERISTICS[priority].name;
}
function getPriorityColor(priority) {
    return exports.PRIORITY_CHARACTERISTICS[priority].color;
}
function getDeliveryDelay(priority) {
    return exports.PRIORITY_CHARACTERISTICS[priority].deliveryDelay;
}
function getRetryConfig(priority) {
    const characteristics = exports.PRIORITY_CHARACTERISTICS[priority];
    return {
        attempts: characteristics.retryAttempts,
        delay: characteristics.retryDelay,
    };
}
function respectsQuietHours(priority) {
    return exports.PRIORITY_CHARACTERISTICS[priority].respectQuietHours;
}
function allowsBatching(priority) {
    return exports.PRIORITY_CHARACTERISTICS[priority].batchingAllowed;
}
function getRecommendedChannelsForPriority(priority) {
    return exports.PRIORITY_CHARACTERISTICS[priority].channels;
}
function comparePriorities(a, b) {
    return b - a;
}
function isHighPriority(priority) {
    return priority >= NotificationPriority.HIGH;
}
function isCriticalPriority(priority) {
    return priority >= NotificationPriority.CRITICAL;
}
function parsePriority(value) {
    const upperValue = value.toUpperCase();
    switch (upperValue) {
        case 'LOW': return NotificationPriority.LOW;
        case 'NORMAL': return NotificationPriority.NORMAL;
        case 'HIGH': return NotificationPriority.HIGH;
        case 'CRITICAL': return NotificationPriority.CRITICAL;
        case 'URGENT': return NotificationPriority.URGENT;
        default: return NotificationPriority.NORMAL;
    }
}
function getAllPriorities() {
    return Object.values(NotificationPriority)
        .filter(value => typeof value === 'number')
        .map(priority => {
        const characteristics = exports.PRIORITY_CHARACTERISTICS[priority];
        return {
            value: priority,
            name: characteristics.name,
            description: characteristics.description,
            color: characteristics.color,
        };
    })
        .sort((a, b) => a.value - b.value);
}
//# sourceMappingURL=notification-priority.enum.js.map