import { Team } from '../entities/team.entity';
export interface ITeamRepository {
    save(team: Team): Promise<Team>;
    findById(id: number): Promise<Team | null>;
    findAll(): Promise<Team[]>;
    findByLeader(leaderId: number): Promise<Team[]>;
    findByMember(userId: number): Promise<Team[]>;
    findByUser(userId: number): Promise<Team[]>;
    findLeadTeams(): Promise<Team[]>;
    findOpportunityTeams(): Promise<Team[]>;
    findWithAutoAssignment(): Promise<Team[]>;
    findByCompany(companyId: number): Promise<Team[]>;
    findMany(filters: {
        leaderId?: number;
        memberId?: number;
        useLeads?: boolean;
        useOpportunities?: boolean;
        hasAutoAssignment?: boolean;
        companyId?: number;
        minSize?: number;
        maxSize?: number;
        teamType?: 'individual' | 'small' | 'medium' | 'large';
        offset?: number;
        limit?: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
    }): Promise<{
        teams: Team[];
        total: number;
        analytics: {
            totalTeams: number;
            averageTeamSize: number;
            teamsWithLeaders: number;
            teamsWithAutoAssignment: number;
            teamTypeDistribution: Record<string, number>;
        };
    }>;
    addMember(teamId: number, userId: number): Promise<boolean>;
    removeMember(teamId: number, userId: number): Promise<boolean>;
    changeLeader(teamId: number, newLeaderId?: number): Promise<boolean>;
    updateConfiguration(teamId: number, updates: Partial<{
        name: string;
        useLeads: boolean;
        useOpportunities: boolean;
        assignmentDomain: string;
        assignmentOptout: boolean;
        assignmentMax: number;
        color: number;
        description: string;
    }>): Promise<boolean>;
    bulkAddMembers(teamId: number, userIds: number[]): Promise<boolean>;
    bulkRemoveMembers(teamId: number, userIds: number[]): Promise<boolean>;
    transferOwnership(fromTeamId: number, toTeamId: number, transferLeads?: boolean, transferOpportunities?: boolean): Promise<boolean>;
    delete(id: number): Promise<boolean>;
    getStatistics(teamId?: number): Promise<{
        totalTeams: number;
        totalMembers: number;
        averageTeamSize: number;
        teamsWithLeaders: number;
        teamsWithAutoAssignment: number;
        teamPerformance: Array<{
            teamId: number;
            teamName: string;
            memberCount: number;
            leadCount: number;
            opportunityCount: number;
            totalRevenue: number;
            weightedRevenue: number;
            conversionRate: number;
            averageDealSize: number;
            winRate: number;
        }>;
    }>;
    getPerformanceMetrics(teamId: number, dateFrom?: Date, dateTo?: Date): Promise<{
        teamInfo: {
            id: number;
            name: string;
            memberCount: number;
            leaderId?: number;
        };
        metrics: {
            leadsCreated: number;
            leadsConverted: number;
            opportunitiesWon: number;
            opportunitiesLost: number;
            totalRevenue: number;
            weightedRevenue: number;
            averageDealSize: number;
            averageSalesCycle: number;
            conversionRate: number;
            winRate: number;
            lossRate: number;
            activityScore: number;
        };
        memberPerformance: Array<{
            userId: number;
            userName: string;
            isLeader: boolean;
            leadsAssigned: number;
            leadsConverted: number;
            opportunitiesWon: number;
            totalRevenue: number;
            conversionRate: number;
            winRate: number;
            activityScore: number;
        }>;
        trends: {
            leadsCreatedTrend: Array<{
                date: string;
                count: number;
            }>;
            revenueTrend: Array<{
                date: string;
                amount: number;
            }>;
            conversionTrend: Array<{
                date: string;
                rate: number;
            }>;
        };
        comparisons: {
            vsCompanyAverage: {
                conversionRate: number;
                winRate: number;
                averageDealSize: number;
            };
            ranking: {
                conversionRate: number;
                winRate: number;
                totalRevenue: number;
            };
        };
    }>;
    getWorkloadDistribution(teamId: number): Promise<{
        teamCapacity: number;
        currentLoad: number;
        utilizationRate: number;
        memberWorkloads: Array<{
            userId: number;
            userName: string;
            assignedLeads: number;
            assignedOpportunities: number;
            maxAssignments: number;
            utilizationRate: number;
            isOverloaded: boolean;
        }>;
        recommendations: Array<{
            type: 'rebalance' | 'hire' | 'redistribute';
            message: string;
            priority: 'low' | 'medium' | 'high';
        }>;
    }>;
    getOptimalAssignment(teamId: number, leadData?: {
        priority?: string;
        expectedRevenue?: number;
        source?: string;
    }): Promise<{
        recommendedUserId?: number;
        reason: string;
        confidence: number;
        alternatives: Array<{
            userId: number;
            reason: string;
            confidence: number;
        }>;
    }>;
    canAcceptAssignments(teamId: number): Promise<{
        canAccept: boolean;
        reason?: string;
        capacity: number;
        currentLoad: number;
        availableSlots: number;
    }>;
    getCollaborationMetrics(teamId: number, dateFrom?: Date, dateTo?: Date): Promise<{
        teamCohesion: number;
        communicationFrequency: number;
        knowledgeSharing: number;
        crossCollaboration: Array<{
            fromUserId: number;
            toUserId: number;
            collaborationCount: number;
            collaborationType: string[];
        }>;
        mentorshipPairs: Array<{
            mentorId: number;
            menteeId: number;
            effectivenessScore: number;
        }>;
    }>;
    search(query: string, filters?: {
        useLeads?: boolean;
        useOpportunities?: boolean;
        hasAutoAssignment?: boolean;
        limit?: number;
    }): Promise<Team[]>;
    clone(id: number, newName: string, copyMembers?: boolean): Promise<Team>;
    mergeTeams(sourceTeamIds: number[], targetTeamId: number, transferData?: {
        leads?: boolean;
        opportunities?: boolean;
        stages?: boolean;
    }): Promise<boolean>;
    getHierarchy(): Promise<Array<{
        teamId: number;
        teamName: string;
        parentTeamId?: number;
        childTeamIds: number[];
        level: number;
    }>>;
    validateConfiguration(teamId: number): Promise<{
        isValid: boolean;
        issues: Array<{
            type: 'warning' | 'error';
            field: string;
            message: string;
        }>;
        suggestions: string[];
    }>;
}
