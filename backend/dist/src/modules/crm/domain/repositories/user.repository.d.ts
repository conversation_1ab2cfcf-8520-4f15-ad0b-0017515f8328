export interface IUserRepository {
    findById(id: string): Promise<User | null>;
    findByTeamId(teamId: number, filters?: UserFilters): Promise<User[]>;
    findAll(filters?: UserFilters): Promise<User[]>;
    create(userData: CreateUserData): Promise<User>;
    update(id: string, userData: UpdateUserData): Promise<User>;
    delete(id: string): Promise<boolean>;
    findByRole(role: string): Promise<User[]>;
    findAvailableUsers(teamId?: number): Promise<User[]>;
    updateAvailability(id: string, isAvailable: boolean): Promise<void>;
    getUserWorkloadStats(id: string): Promise<UserWorkloadStats>;
    findByTerritory(territory: string): Promise<User[]>;
}
export interface User {
    id: string;
    name: string;
    email: string;
    role: string;
    teamId?: number;
    seniority?: string;
    territories?: string[];
    skills?: string[];
    isActive: boolean;
    isAvailable: boolean;
    maxLeads?: number;
    timezone?: string;
    workingHours?: WorkingHours;
    createdAt: Date;
    updatedAt: Date;
}
export interface UserFilters {
    isActive?: boolean;
    isAvailable?: boolean;
    role?: string;
    seniority?: string;
    territory?: string;
    skill?: string;
}
export interface CreateUserData {
    name: string;
    email: string;
    role: string;
    teamId?: number;
    seniority?: string;
    territories?: string[];
    skills?: string[];
    maxLeads?: number;
    timezone?: string;
    workingHours?: WorkingHours;
}
export interface UpdateUserData {
    name?: string;
    email?: string;
    role?: string;
    teamId?: number;
    seniority?: string;
    territories?: string[];
    skills?: string[];
    isActive?: boolean;
    isAvailable?: boolean;
    maxLeads?: number;
    timezone?: string;
    workingHours?: WorkingHours;
}
export interface WorkingHours {
    monday?: TimeSlot;
    tuesday?: TimeSlot;
    wednesday?: TimeSlot;
    thursday?: TimeSlot;
    friday?: TimeSlot;
    saturday?: TimeSlot;
    sunday?: TimeSlot;
}
export interface TimeSlot {
    start: string;
    end: string;
}
export interface UserWorkloadStats {
    totalLeads: number;
    activeLeads: number;
    convertedLeads: number;
    totalValue: number;
    conversionRate: number;
    averageLeadAge: number;
    lastActivity: Date;
}
