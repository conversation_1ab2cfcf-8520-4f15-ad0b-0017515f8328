import { Stage } from '../entities/stage.entity';
export interface IStageRepository {
    save(stage: Stage): Promise<Stage>;
    findById(id: number): Promise<Stage | null>;
    findAll(): Promise<Stage[]>;
    findByTeam(teamId: number): Promise<Stage[]>;
    findGlobal(): Promise<Stage[]>;
    findActive(teamId?: number): Promise<Stage[]>;
    findTerminal(teamId?: number): Promise<Stage[]>;
    findWonStages(teamId?: number): Promise<Stage[]>;
    findLostStages(teamId?: number): Promise<Stage[]>;
    findOrderedBySequence(teamId?: number): Promise<Stage[]>;
    findMany(filters: {
        teamId?: number;
        isWonStage?: boolean;
        isLostStage?: boolean;
        fold?: boolean;
        isActive?: boolean;
        isTerminal?: boolean;
        minSequence?: number;
        maxSequence?: number;
        offset?: number;
        limit?: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
    }): Promise<{
        stages: Stage[];
        total: number;
    }>;
    updateSequence(id: number, newSequence: number): Promise<boolean>;
    updateStage(id: number, updates: Partial<{
        name: string;
        sequence: number;
        isWonStage: boolean;
        isLostStage: boolean;
        requirements: string;
        fold: boolean;
        probabilityMin: number;
        probabilityMax: number;
    }>): Promise<boolean>;
    reorderStages(stageSequences: Array<{
        id: number;
        sequence: number;
    }>): Promise<boolean>;
    toggleFold(id: number): Promise<boolean>;
    delete(id: number): Promise<boolean>;
    getStatistics(stageId?: number, teamId?: number): Promise<{
        totalStages: number;
        activeStages: number;
        foldedStages: number;
        wonStages: number;
        lostStages: number;
        averageLeadsPerStage: number;
        averageRevenuePerStage: number;
        stageUtilization: Array<{
            stageId: number;
            stageName: string;
            leadCount: number;
            totalRevenue: number;
            averageTimeInStage: number;
            conversionRate: number;
        }>;
    }>;
    getPerformanceMetrics(teamId?: number, dateFrom?: Date, dateTo?: Date): Promise<{
        stageMetrics: Array<{
            stageId: number;
            stageName: string;
            sequence: number;
            leadsEntered: number;
            leadsExited: number;
            leadsWon: number;
            leadsLost: number;
            averageTimeInStage: number;
            conversionRate: number;
            dropOffRate: number;
            totalRevenue: number;
            averageRevenue: number;
        }>;
        bottlenecks: Array<{
            stageId: number;
            stageName: string;
            averageTimeInStage: number;
            dropOffRate: number;
            severity: 'low' | 'medium' | 'high';
        }>;
        recommendations: string[];
    }>;
    getNextStage(currentStageId: number, teamId?: number): Promise<Stage | null>;
    getPreviousStage(currentStageId: number, teamId?: number): Promise<Stage | null>;
    canDelete(id: number): Promise<boolean>;
    getProgressionPath(teamId?: number): Promise<Array<{
        stageId: number;
        stageName: string;
        sequence: number;
        isTerminal: boolean;
        nextStages: number[];
    }>>;
    validateSequences(teamId?: number): Promise<{
        isValid: boolean;
        issues: Array<{
            type: 'gap' | 'duplicate' | 'negative';
            stageId?: number;
            sequence?: number;
            message: string;
        }>;
        suggestions: string[];
    }>;
    search(query: string, teamId?: number): Promise<Stage[]>;
    clone(id: number, newName: string, teamId?: number): Promise<Stage>;
    getTemplates(): Promise<Array<{
        name: string;
        description: string;
        stages: Array<{
            name: string;
            sequence: number;
            isWonStage: boolean;
            isLostStage: boolean;
            probabilityMin?: number;
            probabilityMax?: number;
        }>;
    }>>;
    applyTemplate(templateName: string, teamId?: number): Promise<Stage[]>;
}
