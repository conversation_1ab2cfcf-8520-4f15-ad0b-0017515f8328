"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PIPELINE_REPOSITORY_TOKEN = exports.ATTACHMENT_REPOSITORY_TOKEN = exports.NOTE_REPOSITORY_TOKEN = exports.ACTIVITY_REPOSITORY_TOKEN = exports.CONTACT_REPOSITORY_TOKEN = exports.OPPORTUNITY_REPOSITORY_TOKEN = exports.USER_REPOSITORY_TOKEN = exports.TEAM_REPOSITORY_TOKEN = exports.STAGE_REPOSITORY_TOKEN = exports.LEAD_REPOSITORY_TOKEN = void 0;
exports.LEAD_REPOSITORY_TOKEN = Symbol('ILeadRepository');
exports.STAGE_REPOSITORY_TOKEN = Symbol('IStageRepository');
exports.TEAM_REPOSITORY_TOKEN = Symbol('ITeamRepository');
exports.USER_REPOSITORY_TOKEN = Symbol('IUserRepository');
exports.OPPORTUNITY_REPOSITORY_TOKEN = Symbol('IOpportunityRepository');
exports.CONTACT_REPOSITORY_TOKEN = Symbol('IContactRepository');
exports.ACTIVITY_REPOSITORY_TOKEN = Symbol('IActivityRepository');
exports.NOTE_REPOSITORY_TOKEN = Symbol('INoteRepository');
exports.ATTACHMENT_REPOSITORY_TOKEN = Symbol('IAttachmentRepository');
exports.PIPELINE_REPOSITORY_TOKEN = Symbol('IPipelineRepository');
//# sourceMappingURL=injection-tokens.js.map