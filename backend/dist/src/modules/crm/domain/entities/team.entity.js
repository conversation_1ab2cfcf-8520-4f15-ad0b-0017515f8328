"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Team = void 0;
const odoo_base_entity_1 = require("../../../../shared/domain/entities/odoo-base.entity");
class Team extends odoo_base_entity_1.OdooBaseModel {
    name;
    leaderId;
    memberIds;
    useLeads;
    useOpportunities;
    assignmentDomain;
    assignmentOptout;
    assignmentMax;
    color;
    description;
    companyId;
    constructor(id, name, leaderId, memberIds = [], useLeads = true, useOpportunities = true, assignmentDomain, assignmentOptout = false, assignmentMax = 30, color, description, companyId, createdAt, updatedAt) {
        super(id, createdAt, updatedAt);
        this.name = name;
        this.leaderId = leaderId;
        this.memberIds = memberIds;
        this.useLeads = useLeads;
        this.useOpportunities = useOpportunities;
        this.assignmentDomain = assignmentDomain;
        this.assignmentOptout = assignmentOptout;
        this.assignmentMax = assignmentMax;
        this.color = color;
        this.description = description;
        this.companyId = companyId;
        this.validateBusinessRules();
    }
    validateBusinessRules() {
        if (this.name.trim().length === 0) {
            throw new Error('Team name cannot be empty');
        }
        if (this.assignmentMax < 0) {
            throw new Error('Assignment max cannot be negative');
        }
        if (!this.useLeads && !this.useOpportunities) {
            throw new Error('Team must use either leads or opportunities (or both)');
        }
        if (this.leaderId && this.memberIds.includes(this.leaderId)) {
        }
        if (this.color !== undefined && (this.color < 0 || this.color > 11)) {
            throw new Error('Color must be between 0 and 11 (Odoo color palette)');
        }
    }
    hasLeader() {
        return this.leaderId !== undefined;
    }
    hasMembers() {
        return this.memberIds.length > 0;
    }
    getTeamSize() {
        const uniqueMembers = new Set(this.memberIds);
        if (this.leaderId && !uniqueMembers.has(this.leaderId)) {
            uniqueMembers.add(this.leaderId);
        }
        return uniqueMembers.size;
    }
    isMember(userId) {
        return this.memberIds.includes(userId);
    }
    isLeader(userId) {
        return this.leaderId === userId;
    }
    belongsToTeam(userId) {
        return this.isLeader(userId) || this.isMember(userId);
    }
    canHandleLeads() {
        return this.useLeads;
    }
    canHandleOpportunities() {
        return this.useOpportunities;
    }
    hasAutoAssignment() {
        return !this.assignmentOptout && this.hasMembers();
    }
    canAcceptAssignments() {
        return this.hasAutoAssignment() && this.assignmentMax > 0;
    }
    getCapacity() {
        return this.assignmentMax * this.getTeamSize();
    }
    getTeamColor() {
        const colors = [
            '#FF0000',
            '#FF8000',
            '#FFFF00',
            '#80FF00',
            '#00FF00',
            '#00FF80',
            '#00FFFF',
            '#0080FF',
            '#0000FF',
            '#8000FF',
            '#FF00FF',
            '#FF0080',
        ];
        return colors[this.color || 0] || colors[0];
    }
    getCssClass() {
        const classes = ['team'];
        if (this.hasLeader())
            classes.push('team-has-leader');
        if (this.hasMembers())
            classes.push('team-has-members');
        if (this.canHandleLeads())
            classes.push('team-handles-leads');
        if (this.canHandleOpportunities())
            classes.push('team-handles-opportunities');
        if (this.hasAutoAssignment())
            classes.push('team-auto-assignment');
        return classes.join(' ');
    }
    getIcon() {
        if (this.hasLeader() && this.hasMembers())
            return 'users';
        if (this.hasLeader())
            return 'user-check';
        if (this.hasMembers())
            return 'user-friends';
        return 'user';
    }
    getTeamType() {
        const size = this.getTeamSize();
        if (size <= 1)
            return 'individual';
        if (size <= 5)
            return 'small';
        if (size <= 15)
            return 'medium';
        return 'large';
    }
    getCapabilities() {
        const capabilities = [];
        if (this.canHandleLeads())
            capabilities.push('Leads Management');
        if (this.canHandleOpportunities())
            capabilities.push('Opportunities Management');
        if (this.hasAutoAssignment())
            capabilities.push('Auto Assignment');
        if (this.hasLeader())
            capabilities.push('Leadership');
        return capabilities;
    }
    getAllUserIds() {
        const allUsers = new Set(this.memberIds);
        if (this.leaderId) {
            allUsers.add(this.leaderId);
        }
        return Array.from(allUsers);
    }
    static create(name, leaderId, memberIds = [], useLeads = true, useOpportunities = true) {
        return new Team(0, name, leaderId, memberIds, useLeads, useOpportunities, undefined, false, 30, undefined, undefined, undefined, new Date(), new Date());
    }
    addMember(userId) {
        if (this.isMember(userId)) {
            return this;
        }
        return new Team(this.id, this.name, this.leaderId, [...this.memberIds, userId], this.useLeads, this.useOpportunities, this.assignmentDomain, this.assignmentOptout, this.assignmentMax, this.color, this.description, this.companyId, this.createdAt, new Date());
    }
    removeMember(userId) {
        if (!this.isMember(userId)) {
            return this;
        }
        return new Team(this.id, this.name, this.leaderId, this.memberIds.filter(id => id !== userId), this.useLeads, this.useOpportunities, this.assignmentDomain, this.assignmentOptout, this.assignmentMax, this.color, this.description, this.companyId, this.createdAt, new Date());
    }
    changeLeader(newLeaderId) {
        return new Team(this.id, this.name, newLeaderId, this.memberIds, this.useLeads, this.useOpportunities, this.assignmentDomain, this.assignmentOptout, this.assignmentMax, this.color, this.description, this.companyId, this.createdAt, new Date());
    }
    updateConfiguration(updates) {
        return new Team(this.id, updates.name ?? this.name, this.leaderId, this.memberIds, updates.useLeads ?? this.useLeads, updates.useOpportunities ?? this.useOpportunities, updates.assignmentDomain ?? this.assignmentDomain, updates.assignmentOptout ?? this.assignmentOptout, updates.assignmentMax ?? this.assignmentMax, updates.color ?? this.color, updates.description ?? this.description, this.companyId, this.createdAt, new Date());
    }
    toPlainObject() {
        return {
            id: this.id,
            name: this.name,
            leaderId: this.leaderId,
            memberIds: this.memberIds,
            useLeads: this.useLeads,
            useOpportunities: this.useOpportunities,
            assignmentDomain: this.assignmentDomain,
            assignmentOptout: this.assignmentOptout,
            assignmentMax: this.assignmentMax,
            color: this.color,
            description: this.description,
            companyId: this.companyId,
            hasLeader: this.hasLeader(),
            hasMembers: this.hasMembers(),
            teamSize: this.getTeamSize(),
            canHandleLeads: this.canHandleLeads(),
            canHandleOpportunities: this.canHandleOpportunities(),
            hasAutoAssignment: this.hasAutoAssignment(),
            canAcceptAssignments: this.canAcceptAssignments(),
            capacity: this.getCapacity(),
            teamColor: this.getTeamColor(),
            cssClass: this.getCssClass(),
            icon: this.getIcon(),
            teamType: this.getTeamType(),
            capabilities: this.getCapabilities(),
            allUserIds: this.getAllUserIds(),
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}
exports.Team = Team;
//# sourceMappingURL=team.entity.js.map