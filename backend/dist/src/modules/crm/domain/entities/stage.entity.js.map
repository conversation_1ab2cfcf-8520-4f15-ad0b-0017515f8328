{"version": 3, "file": "stage.entity.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/entities/stage.entity.ts"], "names": [], "mappings": ";;;AAAA,0FAAoF;AAMpF,MAAa,KAAM,SAAQ,gCAAa;IAGpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAXlB,YACE,EAAU,EACM,IAAY,EACZ,QAAgB,EAChB,aAAsB,KAAK,EAC3B,cAAuB,KAAK,EAC5B,MAAe,EACf,YAAqB,EACrB,OAAgB,KAAK,EACrB,cAAuB,EACvB,cAAuB,EACvB,QAAkB,EAClC,SAAgB,EAChB,SAAgB;QAEhB,KAAK,CAAC,EAAE,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QAbhB,SAAI,GAAJ,IAAI,CAAQ;QACZ,aAAQ,GAAR,QAAQ,CAAQ;QAChB,eAAU,GAAV,UAAU,CAAiB;QAC3B,gBAAW,GAAX,WAAW,CAAiB;QAC5B,WAAM,GAAN,MAAM,CAAS;QACf,iBAAY,GAAZ,YAAY,CAAS;QACrB,SAAI,GAAJ,IAAI,CAAiB;QACrB,mBAAc,GAAd,cAAc,CAAS;QACvB,mBAAc,GAAd,cAAc,CAAS;QACvB,aAAQ,GAAR,QAAQ,CAAU;QAKlC,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAKO,qBAAqB;QAC3B,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YAC3E,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBAC9C,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;YACpF,CAAC;YACD,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC;gBACzD,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;IACH,CAAC;IAKD,UAAU;QACR,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC;IAC7C,CAAC;IAKD,QAAQ;QACN,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1C,CAAC;IAKD,cAAc;QACZ,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;IACnC,CAAC;IAKD,QAAQ;QACN,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;IAChC,CAAC;IAKD,qBAAqB;QACnB,IAAI,IAAI,CAAC,UAAU;YAAE,OAAO,GAAG,CAAC;QAChC,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO,CAAC,CAAC;QAG/B,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YAC3E,OAAO,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS;YAAE,OAAO,IAAI,CAAC,cAAc,CAAC;QAClE,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS;YAAE,OAAO,IAAI,CAAC,cAAc,CAAC;QAGlE,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;IAC1C,CAAC;IAKD,kBAAkB,CAAC,WAAmB;QACpC,IAAI,IAAI,CAAC,UAAU;YAAE,OAAO,WAAW,KAAK,GAAG,CAAC;QAChD,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO,WAAW,KAAK,CAAC,CAAC;QAE/C,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,IAAI,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC3E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,IAAI,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC3E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,QAAQ;QACN,IAAI,IAAI,CAAC,UAAU;YAAE,OAAO,SAAS,CAAC;QACtC,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO,SAAS,CAAC;QACvC,IAAI,IAAI,CAAC,IAAI;YAAE,OAAO,SAAS,CAAC;QAGhC,MAAM,MAAM,GAAG;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;SACV,CAAC;QAEF,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC;IAC5D,CAAC;IAKD,WAAW;QACT,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;QAE1B,IAAI,IAAI,CAAC,UAAU;YAAE,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/C,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,IAAI,IAAI,CAAC,IAAI;YAAE,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5C,IAAI,IAAI,CAAC,QAAQ,EAAE;YAAE,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAElD,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAKD,OAAO;QACL,IAAI,IAAI,CAAC,UAAU;YAAE,OAAO,cAAc,CAAC;QAC3C,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO,UAAU,CAAC;QACxC,IAAI,IAAI,CAAC,IAAI;YAAE,OAAO,WAAW,CAAC;QAElC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKD,QAAQ,CAAC,KAAY;QACnB,OAAO,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;IACxC,CAAC;IAKD,OAAO,CAAC,KAAY;QAClB,OAAO,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;IACxC,CAAC;IAKD,wBAAwB,CAAC,WAAmB;QAC1C,IAAI,WAAW,IAAI,CAAC;YAAE,OAAO,GAAG,CAAC;QACjC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IAC/D,CAAC;IAKD,uBAAuB;QACrB,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;IACzC,CAAC;IAKD,eAAe;QACb,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACtE,CAAC;IAKD,YAAY;QACV,IAAI,IAAI,CAAC,UAAU;YAAE,OAAO,KAAK,CAAC;QAClC,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO,MAAM,CAAC;QACpC,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC;YAAE,OAAO,SAAS,CAAC;QAE1C,MAAM,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACxD,IAAI,kBAAkB,IAAI,EAAE;YAAE,OAAO,SAAS,CAAC;QAE/C,OAAO,UAAU,CAAC;IACpB,CAAC;IAKD,MAAM,CAAC,MAAM,CACX,IAAY,EACZ,QAAgB,EAChB,MAAe,EACf,aAAsB,KAAK,EAC3B,cAAuB,KAAK;QAE5B,OAAO,IAAI,KAAK,CACd,CAAC,EACD,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,WAAW,EACX,MAAM,EACN,SAAS,EACT,KAAK,EACL,SAAS,EACT,SAAS,EACT,KAAK,EACL,IAAI,IAAI,EAAE,EACV,IAAI,IAAI,EAAE,CACX,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,OASL;QACA,OAAO,IAAI,KAAK,CACd,IAAI,CAAC,EAAE,EACP,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EACzB,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EACjC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,EACrC,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,EACvC,IAAI,CAAC,MAAM,EACX,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,EACzC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EACzB,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,EAC7C,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,EAC7C,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,EACd,IAAI,IAAI,EAAE,CACX,CAAC;IACJ,CAAC;IAKD,aAAa;QACX,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YAGvB,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YACrC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YAChD,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;YACtB,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE;YACpB,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YAC9B,uBAAuB,EAAE,IAAI,CAAC,uBAAuB,EAAE;YACvD,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE;YAGvC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;CACF;AAtSD,sBAsSC"}