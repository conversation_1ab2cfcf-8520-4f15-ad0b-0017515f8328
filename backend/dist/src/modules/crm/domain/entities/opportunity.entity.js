"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Opportunity = void 0;
const lead_entity_1 = require("./lead.entity");
const lead_status_vo_1 = require("../value-objects/lead-status.vo");
const lead_priority_vo_1 = require("../value-objects/lead-priority.vo");
const lead_type_vo_1 = require("../value-objects/lead-type.vo");
class Opportunity extends lead_entity_1.Lead {
    constructor(id, name, contactInfo, status, source, priority, expectedRevenue, probability, description, assignedUserId, companyId, tags = [], partnerId, stageId, teamId, dateDeadline, lostReasonId, campaignId, sourceId, mediumId, createdAt, updatedAt) {
        super(id, name, contactInfo, status, source, lead_type_vo_1.LeadType.OPPORTUNITY, priority, expectedRevenue, probability, description, assignedUserId, companyId, tags, partnerId, stageId, teamId, dateDeadline, lostReasonId, campaignId, sourceId, mediumId, createdAt, updatedAt);
        this.validateOpportunityRules();
    }
    validateOpportunityRules() {
        if (this.expectedRevenue === undefined || this.expectedRevenue <= 0) {
            throw new Error('Opportunities must have a positive expected revenue');
        }
        if (this.probability === undefined) {
            throw new Error('Opportunities must have a probability value');
        }
        if (this.probability < 0 || this.probability > 100) {
            throw new Error('Opportunity probability must be between 0 and 100');
        }
    }
    calculateWeightedRevenue() {
        const forecast = this.getRevenueForecast();
        if (!forecast)
            return 0;
        let adjustment = 1.0;
        if (this.probability >= 90) {
            adjustment = 1.1;
        }
        else if (this.probability <= 20) {
            adjustment = 0.9;
        }
        return forecast.weightedRevenue * adjustment;
    }
    canBeClosed() {
        return this.assignedUserId !== undefined &&
            this.stageId !== undefined &&
            !this.status.isTerminal();
    }
    isInClosingStage() {
        return this.probability >= 80;
    }
    getTimeInStage() {
        if (!this.updatedAt)
            return 0;
        const diffTime = new Date().getTime() - this.updatedAt.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    getSalesVelocity() {
        const ageInDays = this.getAgeInDays();
        if (ageInDays === 0)
            return 0;
        return this.calculateWeightedRevenue() / ageInDays;
    }
    isStale(staleDays = 30) {
        const timeInStage = this.getTimeInStage();
        return timeInStage > staleDays && !this.status.isTerminal();
    }
    getHealthScore() {
        let score = 50;
        score += (this.probability / 100) * 30;
        const timeInStage = this.getTimeInStage();
        if (timeInStage <= 7) {
            score += 20;
        }
        else if (timeInStage <= 14) {
            score += 15;
        }
        else if (timeInStage <= 30) {
            score += 10;
        }
        else {
            score -= 10;
        }
        if (this.assignedUserId) {
            score += 10;
        }
        const daysUntilDeadline = this.getDaysUntilDeadline();
        if (daysUntilDeadline !== null) {
            if (daysUntilDeadline > 30) {
                score += 10;
            }
            else if (daysUntilDeadline > 7) {
                score += 5;
            }
            else if (daysUntilDeadline < 0) {
                score -= 20;
            }
        }
        if (this.contactInfo.isComplete()) {
            score += 10;
        }
        return Math.max(0, Math.min(100, Math.round(score)));
    }
    getNextBestActions() {
        const actions = [];
        if (this.isOverdue()) {
            actions.push('Update deadline - opportunity is overdue');
        }
        if (this.isStale()) {
            actions.push('Schedule follow-up - no recent activity');
        }
        if (!this.contactInfo.isComplete()) {
            actions.push('Complete contact information');
        }
        if (!this.assignedUserId) {
            actions.push('Assign to sales representative');
        }
        if (this.probability >= 80 && !this.isInClosingStage()) {
            actions.push('Move to closing stage');
        }
        if (this.probability <= 20 && this.getTimeInStage() > 14) {
            actions.push('Consider marking as lost or nurturing');
        }
        if (this.requiresImmediateAttention()) {
            actions.push('Requires immediate attention - high priority');
        }
        return actions;
    }
    static fromLead(lead, expectedRevenue, probability, partnerId, stageId) {
        if (!lead.canConvertToOpportunity()) {
            throw new Error('Lead cannot be converted to opportunity');
        }
        return new Opportunity(lead.id, lead.name, lead.contactInfo, lead.status, lead.source, lead.priority, expectedRevenue, probability, lead.description, lead.assignedUserId, lead.companyId, lead.tags, partnerId || lead.partnerId, stageId || lead.stageId, lead.teamId, lead.dateDeadline, lead.lostReasonId, lead.campaignId, lead.sourceId, lead.mediumId, lead.createdAt, new Date());
    }
    static create(name, contactInfo, source, expectedRevenue, probability, teamId, assignedUserId, priority = lead_priority_vo_1.LeadPriority.MEDIUM) {
        return new Opportunity(0, name, contactInfo, new lead_status_vo_1.LeadStatus('qualified'), source, priority, expectedRevenue, probability, undefined, assignedUserId, undefined, [], undefined, undefined, teamId, undefined, undefined, undefined, undefined, undefined, new Date(), new Date());
    }
    toPlainObject() {
        const baseObject = super.toPlainObject();
        return {
            ...baseObject,
            weightedRevenue: this.calculateWeightedRevenue(),
            salesVelocity: this.getSalesVelocity(),
            timeInStage: this.getTimeInStage(),
            healthScore: this.getHealthScore(),
            isStale: this.isStale(),
            canBeClosed: this.canBeClosed(),
            isInClosingStage: this.isInClosingStage(),
            nextBestActions: this.getNextBestActions(),
        };
    }
}
exports.Opportunity = Opportunity;
//# sourceMappingURL=opportunity.entity.js.map