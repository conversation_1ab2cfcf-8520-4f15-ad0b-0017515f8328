import { OdooBaseModel } from '../../../../shared/domain/entities/odoo-base.entity';
export declare class Stage extends OdooBaseModel {
    readonly name: string;
    readonly sequence: number;
    readonly isWonStage: boolean;
    readonly isLostStage: boolean;
    readonly teamId?: number | undefined;
    readonly requirements?: string | undefined;
    readonly fold: boolean;
    readonly probabilityMin?: number | undefined;
    readonly probabilityMax?: number | undefined;
    readonly onChange?: boolean | undefined;
    constructor(id: number, name: string, sequence: number, isWonStage?: boolean, isLostStage?: boolean, teamId?: number | undefined, requirements?: string | undefined, fold?: boolean, probabilityMin?: number | undefined, probabilityMax?: number | undefined, onChange?: boolean | undefined, createdAt?: Date, updatedAt?: Date);
    private validateBusinessRules;
    isTerminal(): boolean;
    isActive(): boolean;
    isTeamSpecific(): boolean;
    isGlobal(): boolean;
    getDefaultProbability(): number;
    isProbabilityValid(probability: number): boolean;
    getColor(): string;
    getCssClass(): string;
    getIcon(): string;
    isBefore(other: Stage): boolean;
    isAfter(other: Stage): boolean;
    getProgressionPercentage(totalStages: number): number;
    canReceiveOpportunities(): boolean;
    hasRequirements(): boolean;
    getStageType(): 'initial' | 'progress' | 'closing' | 'won' | 'lost';
    static create(name: string, sequence: number, teamId?: number, isWonStage?: boolean, isLostStage?: boolean): Stage;
    update(updates: Partial<{
        name: string;
        sequence: number;
        isWonStage: boolean;
        isLostStage: boolean;
        requirements: string;
        fold: boolean;
        probabilityMin: number;
        probabilityMax: number;
    }>): Stage;
    toPlainObject(): {
        id: number;
        name: string;
        sequence: number;
        isWonStage: boolean;
        isLostStage: boolean;
        teamId: number | undefined;
        requirements: string | undefined;
        fold: boolean;
        probabilityMin: number | undefined;
        probabilityMax: number | undefined;
        onChange: boolean | undefined;
        isTerminal: boolean;
        isActive: boolean;
        isTeamSpecific: boolean;
        isGlobal: boolean;
        defaultProbability: number;
        color: string;
        cssClass: string;
        icon: string;
        stageType: "won" | "lost" | "initial" | "progress" | "closing";
        canReceiveOpportunities: boolean;
        hasRequirements: boolean;
        createdAt: Date | undefined;
        updatedAt: Date | undefined;
    };
}
