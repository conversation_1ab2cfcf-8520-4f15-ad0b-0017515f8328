"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Stage = void 0;
const odoo_base_entity_1 = require("../../../../shared/domain/entities/odoo-base.entity");
class Stage extends odoo_base_entity_1.OdooBaseModel {
    name;
    sequence;
    isWonStage;
    isLostStage;
    teamId;
    requirements;
    fold;
    probabilityMin;
    probabilityMax;
    onChange;
    constructor(id, name, sequence, isWonStage = false, isLostStage = false, teamId, requirements, fold = false, probabilityMin, probabilityMax, onChange, createdAt, updatedAt) {
        super(id, createdAt, updatedAt);
        this.name = name;
        this.sequence = sequence;
        this.isWonStage = isWonStage;
        this.isLostStage = isLostStage;
        this.teamId = teamId;
        this.requirements = requirements;
        this.fold = fold;
        this.probabilityMin = probabilityMin;
        this.probabilityMax = probabilityMax;
        this.onChange = onChange;
        this.validateBusinessRules();
    }
    validateBusinessRules() {
        if (this.sequence < 0) {
            throw new Error('Stage sequence cannot be negative');
        }
        if (this.isWonStage && this.isLostStage) {
            throw new Error('Stage cannot be both won and lost');
        }
        if (this.probabilityMin !== undefined && this.probabilityMax !== undefined) {
            if (this.probabilityMin > this.probabilityMax) {
                throw new Error('Minimum probability cannot be greater than maximum probability');
            }
            if (this.probabilityMin < 0 || this.probabilityMax > 100) {
                throw new Error('Probability values must be between 0 and 100');
            }
        }
    }
    isTerminal() {
        return this.isWonStage || this.isLostStage;
    }
    isActive() {
        return !this.isTerminal() && !this.fold;
    }
    isTeamSpecific() {
        return this.teamId !== undefined;
    }
    isGlobal() {
        return !this.isTeamSpecific();
    }
    getDefaultProbability() {
        if (this.isWonStage)
            return 100;
        if (this.isLostStage)
            return 0;
        if (this.probabilityMin !== undefined && this.probabilityMax !== undefined) {
            return (this.probabilityMin + this.probabilityMax) / 2;
        }
        if (this.probabilityMin !== undefined)
            return this.probabilityMin;
        if (this.probabilityMax !== undefined)
            return this.probabilityMax;
        return Math.min(this.sequence * 20, 80);
    }
    isProbabilityValid(probability) {
        if (this.isWonStage)
            return probability === 100;
        if (this.isLostStage)
            return probability === 0;
        if (this.probabilityMin !== undefined && probability < this.probabilityMin) {
            return false;
        }
        if (this.probabilityMax !== undefined && probability > this.probabilityMax) {
            return false;
        }
        return true;
    }
    getColor() {
        if (this.isWonStage)
            return '#28a745';
        if (this.isLostStage)
            return '#dc3545';
        if (this.fold)
            return '#6c757d';
        const colors = [
            '#007bff',
            '#17a2b8',
            '#ffc107',
            '#fd7e14',
            '#6f42c1',
        ];
        return colors[this.sequence % colors.length] || '#007bff';
    }
    getCssClass() {
        const classes = ['stage'];
        if (this.isWonStage)
            classes.push('stage-won');
        if (this.isLostStage)
            classes.push('stage-lost');
        if (this.fold)
            classes.push('stage-folded');
        if (this.isActive())
            classes.push('stage-active');
        return classes.join(' ');
    }
    getIcon() {
        if (this.isWonStage)
            return 'check-circle';
        if (this.isLostStage)
            return 'x-circle';
        if (this.fold)
            return 'eye-slash';
        return 'circle';
    }
    isBefore(other) {
        return this.sequence < other.sequence;
    }
    isAfter(other) {
        return this.sequence > other.sequence;
    }
    getProgressionPercentage(totalStages) {
        if (totalStages <= 1)
            return 100;
        return Math.round((this.sequence / (totalStages - 1)) * 100);
    }
    canReceiveOpportunities() {
        return !this.fold && !this.isLostStage;
    }
    hasRequirements() {
        return !!(this.requirements && this.requirements.trim().length > 0);
    }
    getStageType() {
        if (this.isWonStage)
            return 'won';
        if (this.isLostStage)
            return 'lost';
        if (this.sequence === 0)
            return 'initial';
        const defaultProbability = this.getDefaultProbability();
        if (defaultProbability >= 80)
            return 'closing';
        return 'progress';
    }
    static create(name, sequence, teamId, isWonStage = false, isLostStage = false) {
        return new Stage(0, name, sequence, isWonStage, isLostStage, teamId, undefined, false, undefined, undefined, false, new Date(), new Date());
    }
    update(updates) {
        return new Stage(this.id, updates.name ?? this.name, updates.sequence ?? this.sequence, updates.isWonStage ?? this.isWonStage, updates.isLostStage ?? this.isLostStage, this.teamId, updates.requirements ?? this.requirements, updates.fold ?? this.fold, updates.probabilityMin ?? this.probabilityMin, updates.probabilityMax ?? this.probabilityMax, this.onChange, this.createdAt, new Date());
    }
    toPlainObject() {
        return {
            id: this.id,
            name: this.name,
            sequence: this.sequence,
            isWonStage: this.isWonStage,
            isLostStage: this.isLostStage,
            teamId: this.teamId,
            requirements: this.requirements,
            fold: this.fold,
            probabilityMin: this.probabilityMin,
            probabilityMax: this.probabilityMax,
            onChange: this.onChange,
            isTerminal: this.isTerminal(),
            isActive: this.isActive(),
            isTeamSpecific: this.isTeamSpecific(),
            isGlobal: this.isGlobal(),
            defaultProbability: this.getDefaultProbability(),
            color: this.getColor(),
            cssClass: this.getCssClass(),
            icon: this.getIcon(),
            stageType: this.getStageType(),
            canReceiveOpportunities: this.canReceiveOpportunities(),
            hasRequirements: this.hasRequirements(),
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}
exports.Stage = Stage;
//# sourceMappingURL=stage.entity.js.map