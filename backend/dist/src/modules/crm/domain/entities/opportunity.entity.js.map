{"version": 3, "file": "opportunity.entity.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/entities/opportunity.entity.ts"], "names": [], "mappings": ";;;AAAA,+CAAqC;AAErC,oEAA6D;AAC7D,wEAAiE;AACjE,gEAAyD;AAOzD,MAAa,WAAY,SAAQ,kBAAI;IACnC,YACE,EAAU,EACV,IAAY,EACZ,WAAwB,EACxB,MAAkB,EAClB,MAAc,EACd,QAAsB,EACtB,eAAuB,EACvB,WAAmB,EACnB,WAAoB,EACpB,cAAuB,EACvB,SAAkB,EAClB,OAAiB,EAAE,EACnB,SAAkB,EAClB,OAAgB,EAChB,MAAe,EACf,YAAmB,EACnB,YAAqB,EACrB,UAAmB,EACnB,QAAiB,EACjB,QAAiB,EACjB,SAAgB,EAChB,SAAgB;QAEhB,KAAK,CACH,EAAE,EACF,IAAI,EACJ,WAAW,EACX,MAAM,EACN,MAAM,EACN,uBAAQ,CAAC,WAAW,EACpB,QAAQ,EACR,eAAe,EACf,WAAW,EACX,WAAW,EACX,cAAc,EACd,SAAS,EACT,IAAI,EACJ,SAAS,EACT,OAAO,EACP,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,CACV,CAAC;QAEF,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAKO,wBAAwB;QAC9B,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,EAAE,CAAC;YACpE,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAKD,wBAAwB;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3C,IAAI,CAAC,QAAQ;YAAE,OAAO,CAAC,CAAC;QAGxB,IAAI,UAAU,GAAG,GAAG,CAAC;QAIrB,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE,EAAE,CAAC;YAC3B,UAAU,GAAG,GAAG,CAAC;QACnB,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE,EAAE,CAAC;YAClC,UAAU,GAAG,GAAG,CAAC;QACnB,CAAC;QAED,OAAO,QAAQ,CAAC,eAAe,GAAG,UAAU,CAAC;IAC/C,CAAC;IAKD,WAAW;QACT,OAAO,IAAI,CAAC,cAAc,KAAK,SAAS;YACjC,IAAI,CAAC,OAAO,KAAK,SAAS;YAC1B,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IACnC,CAAC;IAKD,gBAAgB;QAGd,OAAO,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;IAChC,CAAC;IAKD,cAAc;QAGZ,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO,CAAC,CAAC;QAE9B,MAAM,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACjE,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC;IAKD,gBAAgB;QACd,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,IAAI,SAAS,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE9B,OAAO,IAAI,CAAC,wBAAwB,EAAE,GAAG,SAAS,CAAC;IACrD,CAAC;IAKD,OAAO,CAAC,YAAoB,EAAE;QAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,OAAO,WAAW,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IAC9D,CAAC;IAKD,cAAc;QACZ,IAAI,KAAK,GAAG,EAAE,CAAC;QAGf,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;QAGvC,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC;YACrB,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,IAAI,WAAW,IAAI,EAAE,EAAE,CAAC;YAC7B,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,IAAI,WAAW,IAAI,EAAE,EAAE,CAAC;YAC7B,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACN,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAGD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAGD,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACtD,IAAI,iBAAiB,KAAK,IAAI,EAAE,CAAC;YAC/B,IAAI,iBAAiB,GAAG,EAAE,EAAE,CAAC;gBAC3B,KAAK,IAAI,EAAE,CAAC;YACd,CAAC;iBAAM,IAAI,iBAAiB,GAAG,CAAC,EAAE,CAAC;gBACjC,KAAK,IAAI,CAAC,CAAC;YACb,CAAC;iBAAM,IAAI,iBAAiB,GAAG,CAAC,EAAE,CAAC;gBACjC,KAAK,IAAI,EAAE,CAAC;YACd,CAAC;QACH,CAAC;QAGD,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC;YAClC,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IAKD,kBAAkB;QAChB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACrB,OAAO,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YACnB,OAAO,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;YACvD,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE,EAAE,CAAC;YACzD,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,IAAI,CAAC,0BAA0B,EAAE,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,MAAM,CAAC,QAAQ,CACb,IAAU,EACV,eAAuB,EACvB,WAAmB,EACnB,SAAkB,EAClB,OAAgB;QAEhB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO,IAAI,WAAW,CACpB,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,QAAQ,EACb,eAAe,EACf,WAAW,EACX,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,IAAI,EACT,SAAS,IAAI,IAAI,CAAC,SAAS,EAC3B,OAAO,IAAI,IAAI,CAAC,OAAO,EACvB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,EACd,IAAI,IAAI,EAAE,CACX,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,MAAM,CACX,IAAY,EACZ,WAAwB,EACxB,MAAc,EACd,eAAuB,EACvB,WAAmB,EACnB,MAAe,EACf,cAAuB,EACvB,WAAyB,+BAAY,CAAC,MAAM;QAE5C,OAAO,IAAI,WAAW,CACpB,CAAC,EACD,IAAI,EACJ,WAAW,EACX,IAAI,2BAAU,CAAC,WAAW,CAAC,EAC3B,MAAM,EACN,QAAQ,EACR,eAAe,EACf,WAAW,EACX,SAAS,EACT,cAAc,EACd,SAAS,EACT,EAAE,EACF,SAAS,EACT,SAAS,EACT,MAAM,EACN,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,IAAI,IAAI,EAAE,EACV,IAAI,IAAI,EAAE,CACX,CAAC;IACJ,CAAC;IAKD,aAAa;QACX,MAAM,UAAU,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;QAEzC,OAAO;YACL,GAAG,UAAU;YAEb,eAAe,EAAE,IAAI,CAAC,wBAAwB,EAAE;YAChD,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;YACvB,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;YAC/B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACzC,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE;SAC3C,CAAC;IACJ,CAAC;CACF;AAjUD,kCAiUC"}