import { OdooBaseModel } from '../../../../shared/domain/entities/odoo-base.entity';
export declare class Team extends OdooBaseModel {
    readonly name: string;
    readonly leaderId?: number | undefined;
    readonly memberIds: number[];
    readonly useLeads: boolean;
    readonly useOpportunities: boolean;
    readonly assignmentDomain?: string | undefined;
    readonly assignmentOptout: boolean;
    readonly assignmentMax: number;
    readonly color?: number | undefined;
    readonly description?: string | undefined;
    readonly companyId?: number | undefined;
    constructor(id: number, name: string, leaderId?: number | undefined, memberIds?: number[], useLeads?: boolean, useOpportunities?: boolean, assignmentDomain?: string | undefined, assignmentOptout?: boolean, assignmentMax?: number, color?: number | undefined, description?: string | undefined, companyId?: number | undefined, createdAt?: Date, updatedAt?: Date);
    private validateBusinessRules;
    hasLeader(): boolean;
    hasMembers(): boolean;
    getTeamSize(): number;
    isMember(userId: number): boolean;
    isLeader(userId: number): boolean;
    belongsToTeam(userId: number): boolean;
    canHandleLeads(): boolean;
    canHandleOpportunities(): boolean;
    hasAutoAssignment(): boolean;
    canAcceptAssignments(): boolean;
    getCapacity(): number;
    getTeamColor(): string;
    getCssClass(): string;
    getIcon(): string;
    getTeamType(): 'individual' | 'small' | 'medium' | 'large';
    getCapabilities(): string[];
    getAllUserIds(): number[];
    static create(name: string, leaderId?: number, memberIds?: number[], useLeads?: boolean, useOpportunities?: boolean): Team;
    addMember(userId: number): Team;
    removeMember(userId: number): Team;
    changeLeader(newLeaderId?: number): Team;
    updateConfiguration(updates: Partial<{
        name: string;
        useLeads: boolean;
        useOpportunities: boolean;
        assignmentDomain: string;
        assignmentOptout: boolean;
        assignmentMax: number;
        color: number;
        description: string;
    }>): Team;
    toPlainObject(): {
        id: number;
        name: string;
        leaderId: number | undefined;
        memberIds: number[];
        useLeads: boolean;
        useOpportunities: boolean;
        assignmentDomain: string | undefined;
        assignmentOptout: boolean;
        assignmentMax: number;
        color: number | undefined;
        description: string | undefined;
        companyId: number | undefined;
        hasLeader: boolean;
        hasMembers: boolean;
        teamSize: number;
        canHandleLeads: boolean;
        canHandleOpportunities: boolean;
        hasAutoAssignment: boolean;
        canAcceptAssignments: boolean;
        capacity: number;
        teamColor: string;
        cssClass: string;
        icon: string;
        teamType: "small" | "medium" | "individual" | "large";
        capabilities: string[];
        allUserIds: number[];
        createdAt: Date | undefined;
        updatedAt: Date | undefined;
    };
}
