{"version": 3, "file": "team.entity.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/entities/team.entity.ts"], "names": [], "mappings": ";;;AAAA,0FAAoF;AAMpF,MAAa,IAAK,SAAQ,gCAAa;IAGnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAZlB,YACE,EAAU,EACM,IAAY,EACZ,QAAiB,EACjB,YAAsB,EAAE,EACxB,WAAoB,IAAI,EACxB,mBAA4B,IAAI,EAChC,gBAAyB,EACzB,mBAA4B,KAAK,EACjC,gBAAwB,EAAE,EAC1B,KAAc,EACd,WAAoB,EACpB,SAAkB,EAClC,SAAgB,EAChB,SAAgB;QAEhB,KAAK,CAAC,EAAE,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QAdhB,SAAI,GAAJ,IAAI,CAAQ;QACZ,aAAQ,GAAR,QAAQ,CAAS;QACjB,cAAS,GAAT,SAAS,CAAe;QACxB,aAAQ,GAAR,QAAQ,CAAgB;QACxB,qBAAgB,GAAhB,gBAAgB,CAAgB;QAChC,qBAAgB,GAAhB,gBAAgB,CAAS;QACzB,qBAAgB,GAAhB,gBAAgB,CAAiB;QACjC,kBAAa,GAAb,aAAa,CAAa;QAC1B,UAAK,GAAL,KAAK,CAAS;QACd,gBAAW,GAAX,WAAW,CAAS;QACpB,cAAS,GAAT,SAAS,CAAS;QAKlC,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAKO,qBAAqB;QAC3B,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QAE9D,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,EAAE,CAAC;YACpE,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAKD,SAAS;QACP,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC;IACrC,CAAC;IAKD,UAAU;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IACnC,CAAC;IAKD,WAAW;QACT,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvD,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC;QACD,OAAO,aAAa,CAAC,IAAI,CAAC;IAC5B,CAAC;IAKD,QAAQ,CAAC,MAAc;QACrB,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAKD,QAAQ,CAAC,MAAc;QACrB,OAAO,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC;IAClC,CAAC;IAKD,aAAa,CAAC,MAAc;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACxD,CAAC;IAKD,cAAc;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAKD,sBAAsB;QACpB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAKD,iBAAiB;QACf,OAAO,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;IACrD,CAAC;IAKD,oBAAoB;QAClB,OAAO,IAAI,CAAC,iBAAiB,EAAE,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;IAC5D,CAAC;IAKD,WAAW;QACT,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IACjD,CAAC;IAKD,YAAY;QAEV,MAAM,MAAM,GAAG;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;SACV,CAAC;QAEF,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC;IAKD,WAAW;QACT,MAAM,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC;QAEzB,IAAI,IAAI,CAAC,SAAS,EAAE;YAAE,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtD,IAAI,IAAI,CAAC,UAAU,EAAE;YAAE,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACxD,IAAI,IAAI,CAAC,cAAc,EAAE;YAAE,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC9D,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAAE,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC9E,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAAE,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAEnE,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAKD,OAAO;QACL,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;YAAE,OAAO,OAAO,CAAC;QAC1D,IAAI,IAAI,CAAC,SAAS,EAAE;YAAE,OAAO,YAAY,CAAC;QAC1C,IAAI,IAAI,CAAC,UAAU,EAAE;YAAE,OAAO,cAAc,CAAC;QAC7C,OAAO,MAAM,CAAC;IAChB,CAAC;IAKD,WAAW;QACT,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAChC,IAAI,IAAI,IAAI,CAAC;YAAE,OAAO,YAAY,CAAC;QACnC,IAAI,IAAI,IAAI,CAAC;YAAE,OAAO,OAAO,CAAC;QAC9B,IAAI,IAAI,IAAI,EAAE;YAAE,OAAO,QAAQ,CAAC;QAChC,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,eAAe;QACb,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,cAAc,EAAE;YAAE,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACjE,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAAE,YAAY,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACjF,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAAE,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACnE,IAAI,IAAI,CAAC,SAAS,EAAE;YAAE,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEtD,OAAO,YAAY,CAAC;IACtB,CAAC;IAKD,aAAa;QACX,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAKD,MAAM,CAAC,MAAM,CACX,IAAY,EACZ,QAAiB,EACjB,YAAsB,EAAE,EACxB,WAAoB,IAAI,EACxB,mBAA4B,IAAI;QAEhC,OAAO,IAAI,IAAI,CACb,CAAC,EACD,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,gBAAgB,EAChB,SAAS,EACT,KAAK,EACL,EAAE,EACF,SAAS,EACT,SAAS,EACT,SAAS,EACT,IAAI,IAAI,EAAE,EACV,IAAI,IAAI,EAAE,CACX,CAAC;IACJ,CAAC;IAKD,SAAS,CAAC,MAAc;QACtB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,IAAI,CACb,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,QAAQ,EACb,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,EAC3B,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,EACd,IAAI,IAAI,EAAE,CACX,CAAC;IACJ,CAAC;IAKD,YAAY,CAAC,MAAc;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,IAAI,CACb,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,MAAM,CAAC,EAC1C,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,EACd,IAAI,IAAI,EAAE,CACX,CAAC;IACJ,CAAC;IAKD,YAAY,CAAC,WAAoB;QAC/B,OAAO,IAAI,IAAI,CACb,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,EACT,WAAW,EACX,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,EACd,IAAI,IAAI,EAAE,CACX,CAAC;IACJ,CAAC;IAKD,mBAAmB,CAAC,OASlB;QACA,OAAO,IAAI,IAAI,CACb,IAAI,CAAC,EAAE,EACP,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EACzB,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,EACd,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EACjC,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EACjD,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EACjD,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EACjD,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,EAC3C,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAC3B,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,EACvC,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,EACd,IAAI,IAAI,EAAE,CACX,CAAC;IACJ,CAAC;IAKD,aAAa;QACX,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;YAGzB,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE;YACrC,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YACrD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAC3C,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACjD,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YAC9B,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE;YACpB,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;YACpC,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;YAGhC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;CACF;AA/XD,oBA+XC"}