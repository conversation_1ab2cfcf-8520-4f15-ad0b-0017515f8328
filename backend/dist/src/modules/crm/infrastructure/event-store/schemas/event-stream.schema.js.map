{"version": 3, "file": "event-stream.schema.js", "sourceRoot": "", "sources": ["../../../../../../../src/modules/crm/infrastructure/event-store/schemas/event-stream.schema.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAA+D;AAC/D,uCAAoC;AAW7B,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,mBAAQ;IAEvC,UAAU,CAAS;IAGnB,OAAO,CAAS;IAGhB,SAAS,CAAS;IAGlB,WAAW,CAAS;IAGpB,aAAa,CAAS;IAGtB,gBAAgB,CAAS;IAGzB,UAAU,CAAO;IAGjB,WAAW,CAAU;IAGrB,aAAa,CAAU;IAGvB,MAAM,CAAU;IAGhB,QAAQ,CAAU;IAGlB,QAAQ,CAAsB;IAG9B,OAAO,CAAsB;IAG7B,QAAQ,CAAS;CAClB,CAAA;AA1CY,kCAAW;AAEtB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;+CACnB;AAGnB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;4CACvB;AAGhB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;8CACpB;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;gDAClB;AAGpB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;kDAChB;AAGtB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDACA;AAGzB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;8BAC1B,IAAI;+CAAC;AAGjB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;gDAClB;AAGrB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;kDAChB;AAGvB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;2CACvB;AAGhB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;6CACrB;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CACT;AAG9B;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CACV;AAG7B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;6CACtB;sBAzCN,WAAW;IALvB,IAAA,iBAAM,EAAC;QACN,UAAU,EAAE,eAAe;QAC3B,UAAU,EAAE,KAAK;QACjB,UAAU,EAAE,KAAK;KAClB,CAAC;GACW,WAAW,CA0CvB;AAEY,QAAA,iBAAiB,GAAG,wBAAa,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AAG3E,yBAAiB,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AAClF,yBAAiB,CAAC,KAAK,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC;AACnF,yBAAiB,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1D,yBAAiB,CAAC,KAAK,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7D,yBAAiB,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACzD,yBAAiB,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AAcpD,IAAM,aAAa,GAAnB,MAAM,aAAc,SAAQ,mBAAQ;IAEzC,WAAW,CAAS;IAGpB,aAAa,CAAS;IAGtB,OAAO,CAAS;IAGhB,IAAI,CAAsB;IAG1B,SAAS,CAAO;IAGhB,QAAQ,CAAuB;CAChC,CAAA;AAlBY,sCAAa;AAExB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;kDAClB;AAGpB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;oDAChB;AAGtB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CACT;AAGhB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2CACb;AAG1B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;8BAC3B,IAAI;gDAAC;AAGhB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;+CACK;wBAjBpB,aAAa;IALzB,IAAA,iBAAM,EAAC;QACN,UAAU,EAAE,iBAAiB;QAC7B,UAAU,EAAE,KAAK;QACjB,UAAU,EAAE,KAAK;KAClB,CAAC;GACW,aAAa,CAkBzB;AAEY,QAAA,mBAAmB,GAAG,wBAAa,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;AAG/E,2BAAmB,CAAC,KAAK,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AAC/F,2BAAmB,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAG7C,2BAAmB,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC,CAAC;AAWtE,IAAM,eAAe,GAArB,MAAM,eAAgB,SAAQ,mBAAQ;IAE3C,cAAc,CAAS;IAGvB,WAAW,CAAS;IAGpB,aAAa,CAAS;IAGtB,oBAAoB,CAAS;IAG7B,qBAAqB,CAAS;IAG9B,IAAI,CAAsB;IAG1B,SAAS,CAAO;IAGhB,QAAQ,CAAU;CACnB,CAAA;AAxBY,0CAAe;AAE1B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;uDACf;AAGvB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;oDAClB;AAGpB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;sDAChB;AAGtB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6DACI;AAG7B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8DACK;AAG9B;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CACb;AAG1B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;8BAC3B,IAAI;kDAAC;AAGhB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;iDACrB;0BAvBP,eAAe;IAL3B,IAAA,iBAAM,EAAC;QACN,UAAU,EAAE,mBAAmB;QAC/B,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,KAAK;KAClB,CAAC;GACW,eAAe,CAwB3B;AAEY,QAAA,qBAAqB,GAAG,wBAAa,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;AAGnF,6BAAqB,CAAC,KAAK,CAAC;IAC1B,cAAc,EAAE,CAAC;IACjB,aAAa,EAAE,CAAC;IAChB,WAAW,EAAE,CAAC;CACf,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AACrB,6BAAqB,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/E,6BAAqB,CAAC,KAAK,CAAC,EAAE,qBAAqB,EAAE,CAAC,EAAE,CAAC,CAAC;AAWnD,IAAM,iBAAiB,GAAvB,MAAM,iBAAkB,SAAQ,mBAAQ;IAE7C,gBAAgB,CAAS;IAGzB,UAAU,CAAW;IAGrB,qBAAqB,CAAS;IAG9B,QAAQ,CAAU;IAGlB,cAAc,CAAuB;IAGrC,gBAAgB,CAMb;IAGH,eAAe,CAAO;IAGtB,QAAQ,CAAuB;CAChC,CAAA;AA9BY,8CAAiB;AAE5B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;2DACd;AAGzB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDACJ;AAGrB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gEACK;AAG9B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;mDACtB;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;yDACW;AAGrC;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;8BACP,KAAK;2DAMrB;AAGH;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;8BACrB,IAAI;0DAAC;AAGtB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;mDACK;4BA7BpB,iBAAiB;IAL7B,IAAA,iBAAM,EAAC;QACN,UAAU,EAAE,qBAAqB;QACjC,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,KAAK;KAClB,CAAC;GACW,iBAAiB,CA8B7B;AAEY,QAAA,uBAAuB,GAAG,wBAAa,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;AAGvF,+BAAuB,CAAC,KAAK,CAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AACzE,+BAAuB,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,qBAAqB,EAAE,CAAC,EAAE,CAAC,CAAC;AACzE,+BAAuB,CAAC,KAAK,CAAC,EAAE,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAWhD,IAAM,cAAc,GAApB,MAAM,cAAe,SAAQ,mBAAQ;IAE1C,MAAM,CAAS;IAGf,QAAQ,CAAS;IAGjB,WAAW,CAAS;IAGpB,IAAI,CAAsB;IAG1B,MAAM,CAAqD;IAG3D,SAAS,CAAO;IAGhB,WAAW,CAAQ;IAGnB,QAAQ,CAAQ;IAGhB,KAAK,CAAU;IAGf,gBAAgB,CAAuB;IAGvC,aAAa,CAAU;IAGvB,QAAQ,CAAU;IAGlB,QAAQ,CAAuB;CAChC,CAAA;AAvCY,wCAAc;AAEzB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;8CACxB;AAGf;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;gDACrB;AAGjB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDACL;AAGpB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CACb;AAG1B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;8CACqB;AAG3D;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;8BAC3B,IAAI;iDAAC;AAGhB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;8BACZ,IAAI;mDAAC;AAGnB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;8BACf,IAAI;gDAAC;AAGhB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;6CACX;AAGf;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;wDACa;AAGvC;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;qDAChB;AAGvB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;gDACrB;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;gDACK;yBAtCpB,cAAc;IAL1B,IAAA,iBAAM,EAAC;QACN,UAAU,EAAE,mBAAmB;QAC/B,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,KAAK;KAClB,CAAC;GACW,cAAc,CAuC1B;AAEY,QAAA,oBAAoB,GAAG,wBAAa,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAGjF,4BAAoB,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACtE,4BAAoB,CAAC,KAAK,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC;AACjD,4BAAoB,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AACvD,4BAAoB,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;AAGxD,4BAAoB,CAAC,KAAK,CAAC;IACzB,WAAW,EAAE,CAAC;CACf,EAAE;IACD,kBAAkB,EAAE,OAAO;IAC3B,uBAAuB,EAAE,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAE,EAAE;CACtE,CAAC,CAAC"}