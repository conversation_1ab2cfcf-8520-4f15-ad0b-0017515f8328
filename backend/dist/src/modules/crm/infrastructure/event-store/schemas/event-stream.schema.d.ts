import { Document } from 'mongoose';
export declare class EventStream extends Document {
    streamName: string;
    eventId: string;
    eventType: string;
    aggregateId: string;
    aggregateType: string;
    aggregateVersion: number;
    occurredAt: Date;
    causationId?: string;
    correlationId?: string;
    userId?: string;
    tenantId?: string;
    metadata: Record<string, any>;
    payload: Record<string, any>;
    position: number;
}
export declare const EventStreamSchema: import("mongoose").Schema<EventStream, import("mongoose").Model<EventStream, any, any, any, Document<unknown, any, EventStream, any> & EventStream & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, EventStream, Document<unknown, {}, import("mongoose").FlatRecord<EventStream>, {}> & import("mongoose").FlatRecord<EventStream> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
export declare class EventSnapshot extends Document {
    aggregateId: string;
    aggregateType: string;
    version: number;
    data: Record<string, any>;
    createdAt: Date;
    metadata?: Record<string, any>;
}
export declare const EventSnapshotSchema: import("mongoose").Schema<EventSnapshot, import("mongoose").Model<EventSnapshot, any, any, any, Document<unknown, any, EventSnapshot, any> & EventSnapshot & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, EventSnapshot, Document<unknown, {}, import("mongoose").FlatRecord<EventSnapshot>, {}> & import("mongoose").FlatRecord<EventSnapshot> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
export declare class EventProjection extends Document {
    projectionName: string;
    aggregateId: string;
    aggregateType: string;
    lastProcessedVersion: number;
    lastProcessedPosition: number;
    data: Record<string, any>;
    updatedAt: Date;
    tenantId?: string;
}
export declare const EventProjectionSchema: import("mongoose").Schema<EventProjection, import("mongoose").Model<EventProjection, any, any, any, Document<unknown, any, EventProjection, any> & EventProjection & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, EventProjection, Document<unknown, {}, import("mongoose").FlatRecord<EventProjection>, {}> & import("mongoose").FlatRecord<EventProjection> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
export declare class EventSubscription extends Document {
    subscriptionName: string;
    eventTypes: string[];
    lastProcessedPosition: number;
    isActive: boolean;
    filterCriteria?: Record<string, any>;
    processingErrors?: Array<{
        position: number;
        eventId: string;
        error: string;
        occurredAt: Date;
        retryCount: number;
    }>;
    lastProcessedAt: Date;
    metadata?: Record<string, any>;
}
export declare const EventSubscriptionSchema: import("mongoose").Schema<EventSubscription, import("mongoose").Model<EventSubscription, any, any, any, Document<unknown, any, EventSubscription, any> & EventSubscription & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, EventSubscription, Document<unknown, {}, import("mongoose").FlatRecord<EventSubscription>, {}> & import("mongoose").FlatRecord<EventSubscription> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
export declare class EventSagaState extends Document {
    sagaId: string;
    sagaType: string;
    currentStep: string;
    data: Record<string, any>;
    status: 'active' | 'completed' | 'failed' | 'compensating';
    startedAt: Date;
    completedAt?: Date;
    failedAt?: Date;
    error?: string;
    compensationData?: Record<string, any>;
    correlationId?: string;
    tenantId?: string;
    metadata?: Record<string, any>;
}
export declare const EventSagaStateSchema: import("mongoose").Schema<EventSagaState, import("mongoose").Model<EventSagaState, any, any, any, Document<unknown, any, EventSagaState, any> & EventSagaState & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, EventSagaState, Document<unknown, {}, import("mongoose").FlatRecord<EventSagaState>, {}> & import("mongoose").FlatRecord<EventSagaState> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
