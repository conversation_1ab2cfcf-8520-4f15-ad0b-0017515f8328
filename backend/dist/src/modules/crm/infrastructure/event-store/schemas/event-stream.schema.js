"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventSagaStateSchema = exports.EventSagaState = exports.EventSubscriptionSchema = exports.EventSubscription = exports.EventProjectionSchema = exports.EventProjection = exports.EventSnapshotSchema = exports.EventSnapshot = exports.EventStreamSchema = exports.EventStream = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let EventStream = class EventStream extends mongoose_2.Document {
    streamName;
    eventId;
    eventType;
    aggregateId;
    aggregateType;
    aggregateVersion;
    occurredAt;
    causationId;
    correlationId;
    userId;
    tenantId;
    metadata;
    payload;
    position;
};
exports.EventStream = EventStream;
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], EventStream.prototype, "streamName", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], EventStream.prototype, "eventId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], EventStream.prototype, "eventType", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], EventStream.prototype, "aggregateId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], EventStream.prototype, "aggregateType", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], EventStream.prototype, "aggregateVersion", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", Date)
], EventStream.prototype, "occurredAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, index: true }),
    __metadata("design:type", String)
], EventStream.prototype, "causationId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, index: true }),
    __metadata("design:type", String)
], EventStream.prototype, "correlationId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, index: true }),
    __metadata("design:type", String)
], EventStream.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, index: true }),
    __metadata("design:type", String)
], EventStream.prototype, "tenantId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, required: true }),
    __metadata("design:type", Object)
], EventStream.prototype, "metadata", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, required: true }),
    __metadata("design:type", Object)
], EventStream.prototype, "payload", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", Number)
], EventStream.prototype, "position", void 0);
exports.EventStream = EventStream = __decorate([
    (0, mongoose_1.Schema)({
        collection: 'event_streams',
        timestamps: false,
        versionKey: false,
    })
], EventStream);
exports.EventStreamSchema = mongoose_1.SchemaFactory.createForClass(EventStream);
exports.EventStreamSchema.index({ streamName: 1, aggregateVersion: 1 }, { unique: true });
exports.EventStreamSchema.index({ aggregateType: 1, aggregateId: 1, aggregateVersion: 1 });
exports.EventStreamSchema.index({ eventType: 1, occurredAt: -1 });
exports.EventStreamSchema.index({ correlationId: 1, occurredAt: 1 });
exports.EventStreamSchema.index({ tenantId: 1, occurredAt: -1 });
exports.EventStreamSchema.index({ position: 1 }, { unique: true });
let EventSnapshot = class EventSnapshot extends mongoose_2.Document {
    aggregateId;
    aggregateType;
    version;
    data;
    createdAt;
    metadata;
};
exports.EventSnapshot = EventSnapshot;
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], EventSnapshot.prototype, "aggregateId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], EventSnapshot.prototype, "aggregateType", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], EventSnapshot.prototype, "version", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, required: true }),
    __metadata("design:type", Object)
], EventSnapshot.prototype, "data", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", Date)
], EventSnapshot.prototype, "createdAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", Object)
], EventSnapshot.prototype, "metadata", void 0);
exports.EventSnapshot = EventSnapshot = __decorate([
    (0, mongoose_1.Schema)({
        collection: 'event_snapshots',
        timestamps: false,
        versionKey: false,
    })
], EventSnapshot);
exports.EventSnapshotSchema = mongoose_1.SchemaFactory.createForClass(EventSnapshot);
exports.EventSnapshotSchema.index({ aggregateType: 1, aggregateId: 1, version: -1 }, { unique: true });
exports.EventSnapshotSchema.index({ createdAt: -1 });
exports.EventSnapshotSchema.index({ createdAt: 1 }, { expireAfterSeconds: 7776000 });
let EventProjection = class EventProjection extends mongoose_2.Document {
    projectionName;
    aggregateId;
    aggregateType;
    lastProcessedVersion;
    lastProcessedPosition;
    data;
    updatedAt;
    tenantId;
};
exports.EventProjection = EventProjection;
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], EventProjection.prototype, "projectionName", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], EventProjection.prototype, "aggregateId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], EventProjection.prototype, "aggregateType", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], EventProjection.prototype, "lastProcessedVersion", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], EventProjection.prototype, "lastProcessedPosition", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, required: true }),
    __metadata("design:type", Object)
], EventProjection.prototype, "data", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", Date)
], EventProjection.prototype, "updatedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, index: true }),
    __metadata("design:type", String)
], EventProjection.prototype, "tenantId", void 0);
exports.EventProjection = EventProjection = __decorate([
    (0, mongoose_1.Schema)({
        collection: 'event_projections',
        timestamps: true,
        versionKey: false,
    })
], EventProjection);
exports.EventProjectionSchema = mongoose_1.SchemaFactory.createForClass(EventProjection);
exports.EventProjectionSchema.index({
    projectionName: 1,
    aggregateType: 1,
    aggregateId: 1
}, { unique: true });
exports.EventProjectionSchema.index({ projectionName: 1, tenantId: 1, updatedAt: -1 });
exports.EventProjectionSchema.index({ lastProcessedPosition: 1 });
let EventSubscription = class EventSubscription extends mongoose_2.Document {
    subscriptionName;
    eventTypes;
    lastProcessedPosition;
    isActive;
    filterCriteria;
    processingErrors;
    lastProcessedAt;
    metadata;
};
exports.EventSubscription = EventSubscription;
__decorate([
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], EventSubscription.prototype, "subscriptionName", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Array)
], EventSubscription.prototype, "eventTypes", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], EventSubscription.prototype, "lastProcessedPosition", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, default: true }),
    __metadata("design:type", Boolean)
], EventSubscription.prototype, "isActive", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", Object)
], EventSubscription.prototype, "filterCriteria", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", Array)
], EventSubscription.prototype, "processingErrors", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", Date)
], EventSubscription.prototype, "lastProcessedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", Object)
], EventSubscription.prototype, "metadata", void 0);
exports.EventSubscription = EventSubscription = __decorate([
    (0, mongoose_1.Schema)({
        collection: 'event_subscriptions',
        timestamps: true,
        versionKey: false,
    })
], EventSubscription);
exports.EventSubscriptionSchema = mongoose_1.SchemaFactory.createForClass(EventSubscription);
exports.EventSubscriptionSchema.index({ subscriptionName: 1 }, { unique: true });
exports.EventSubscriptionSchema.index({ isActive: 1, lastProcessedPosition: 1 });
exports.EventSubscriptionSchema.index({ lastProcessedAt: -1 });
let EventSagaState = class EventSagaState extends mongoose_2.Document {
    sagaId;
    sagaType;
    currentStep;
    data;
    status;
    startedAt;
    completedAt;
    failedAt;
    error;
    compensationData;
    correlationId;
    tenantId;
    metadata;
};
exports.EventSagaState = EventSagaState;
__decorate([
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], EventSagaState.prototype, "sagaId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], EventSagaState.prototype, "sagaType", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], EventSagaState.prototype, "currentStep", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, required: true }),
    __metadata("design:type", Object)
], EventSagaState.prototype, "data", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], EventSagaState.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", Date)
], EventSagaState.prototype, "startedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", Date)
], EventSagaState.prototype, "completedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", Date)
], EventSagaState.prototype, "failedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], EventSagaState.prototype, "error", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", Object)
], EventSagaState.prototype, "compensationData", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, index: true }),
    __metadata("design:type", String)
], EventSagaState.prototype, "correlationId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, index: true }),
    __metadata("design:type", String)
], EventSagaState.prototype, "tenantId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", Object)
], EventSagaState.prototype, "metadata", void 0);
exports.EventSagaState = EventSagaState = __decorate([
    (0, mongoose_1.Schema)({
        collection: 'event_saga_states',
        timestamps: true,
        versionKey: false,
    })
], EventSagaState);
exports.EventSagaStateSchema = mongoose_1.SchemaFactory.createForClass(EventSagaState);
exports.EventSagaStateSchema.index({ sagaType: 1, status: 1, startedAt: -1 });
exports.EventSagaStateSchema.index({ correlationId: 1 });
exports.EventSagaStateSchema.index({ tenantId: 1, status: 1 });
exports.EventSagaStateSchema.index({ status: 1, startedAt: 1 });
exports.EventSagaStateSchema.index({
    completedAt: 1
}, {
    expireAfterSeconds: 2592000,
    partialFilterExpression: { status: { $in: ['completed', 'failed'] } }
});
//# sourceMappingURL=event-stream.schema.js.map