"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MongoDbEventStore_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MongoDbEventStore = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const domain_event_base_1 = require("../../domain/events/base/domain-event.base");
let MongoDbEventStore = MongoDbEventStore_1 = class MongoDbEventStore {
    eventStreamModel;
    snapshotModel;
    logger = new common_1.Logger(MongoDbEventStore_1.name);
    constructor(eventStreamModel, snapshotModel) {
        this.eventStreamModel = eventStreamModel;
        this.snapshotModel = snapshotModel;
    }
    async append(streamName, events, expectedVersion) {
        this.logger.debug(`Appending ${events.length} events to stream: ${streamName}`);
        const session = await this.eventStreamModel.db.startSession();
        try {
            await session.withTransaction(async () => {
                if (expectedVersion !== undefined) {
                    const currentVersion = await this.getCurrentVersion(streamName, session);
                    if (currentVersion !== expectedVersion) {
                        throw new Error(`Concurrency conflict: expected version ${expectedVersion}, but current version is ${currentVersion}`);
                    }
                }
                const eventDocuments = events.map((event, index) => ({
                    streamName,
                    eventId: event.eventId,
                    eventType: event.eventType,
                    aggregateId: event.aggregateId,
                    aggregateType: event.aggregateType,
                    aggregateVersion: event.aggregateVersion + index,
                    occurredAt: event.occurredAt,
                    causationId: event.causationId,
                    correlationId: event.correlationId,
                    userId: event.userId,
                    tenantId: event.tenantId,
                    metadata: event.metadata,
                    payload: event.getPayload(),
                    position: await this.getNextGlobalPosition(),
                }));
                await this.eventStreamModel.insertMany(eventDocuments, { session });
                this.logger.debug(`Successfully appended ${events.length} events to stream: ${streamName}`);
            });
        }
        catch (error) {
            this.logger.error(`Failed to append events to stream: ${streamName}`, error);
            throw error;
        }
        finally {
            await session.endSession();
        }
    }
    async getEvents(streamName, fromVersion) {
        this.logger.debug(`Getting events from stream: ${streamName}, fromVersion: ${fromVersion}`);
        const query = { streamName };
        if (fromVersion !== undefined) {
            query.aggregateVersion = { $gte: fromVersion };
        }
        const eventDocuments = await this.eventStreamModel
            .find(query)
            .sort({ aggregateVersion: 1 })
            .exec();
        const events = eventDocuments.map(doc => this.deserializeEvent(doc));
        this.logger.debug(`Retrieved ${events.length} events from stream: ${streamName}`);
        return events;
    }
    async getAllEvents(fromPosition) {
        this.logger.debug(`Getting all events from position: ${fromPosition}`);
        const query = {};
        if (fromPosition !== undefined) {
            query.position = { $gte: fromPosition };
        }
        const eventDocuments = await this.eventStreamModel
            .find(query)
            .sort({ position: 1 })
            .exec();
        const events = eventDocuments.map(doc => this.deserializeEvent(doc));
        this.logger.debug(`Retrieved ${events.length} events from position: ${fromPosition}`);
        return events;
    }
    async getSnapshot(aggregateId, aggregateType) {
        this.logger.debug(`Getting snapshot for aggregate: ${aggregateType}-${aggregateId}`);
        const snapshot = await this.snapshotModel
            .findOne({ aggregateId, aggregateType })
            .sort({ version: -1 })
            .exec();
        if (snapshot) {
            this.logger.debug(`Found snapshot for aggregate: ${aggregateType}-${aggregateId} at version: ${snapshot.version}`);
            return {
                data: snapshot.data,
                version: snapshot.version,
                createdAt: snapshot.createdAt,
            };
        }
        this.logger.debug(`No snapshot found for aggregate: ${aggregateType}-${aggregateId}`);
        return null;
    }
    async saveSnapshot(aggregateId, aggregateType, snapshot, version) {
        this.logger.debug(`Saving snapshot for aggregate: ${aggregateType}-${aggregateId} at version: ${version}`);
        await this.snapshotModel.create({
            aggregateId,
            aggregateType,
            version,
            data: snapshot,
            createdAt: new Date(),
        });
        this.logger.debug(`Successfully saved snapshot for aggregate: ${aggregateType}-${aggregateId}`);
    }
    async getCurrentVersion(streamName, session) {
        const lastEvent = await this.eventStreamModel
            .findOne({ streamName })
            .sort({ aggregateVersion: -1 })
            .session(session)
            .exec();
        return lastEvent ? lastEvent.aggregateVersion : 0;
    }
    async getNextGlobalPosition() {
        const lastEvent = await this.eventStreamModel
            .findOne({})
            .sort({ position: -1 })
            .exec();
        return lastEvent ? lastEvent.position + 1 : 1;
    }
    deserializeEvent(doc) {
        const serializedEvent = {
            eventId: doc.eventId,
            eventType: doc.eventType,
            aggregateId: doc.aggregateId,
            aggregateType: doc.aggregateType,
            aggregateVersion: doc.aggregateVersion,
            occurredAt: doc.occurredAt.toISOString(),
            causationId: doc.causationId,
            correlationId: doc.correlationId,
            userId: doc.userId,
            tenantId: doc.tenantId,
            metadata: doc.metadata,
            payload: doc.payload,
        };
        return new GenericDomainEvent(serializedEvent);
    }
    async cleanupOldEvents(beforeDate, keepSnapshots = true) {
        this.logger.log(`Cleaning up events before: ${beforeDate.toISOString()}`);
        const result = await this.eventStreamModel.deleteMany({
            occurredAt: { $lt: beforeDate },
        });
        if (!keepSnapshots) {
            await this.snapshotModel.deleteMany({
                createdAt: { $lt: beforeDate },
            });
        }
        this.logger.log(`Cleaned up ${result.deletedCount} events`);
        return result.deletedCount;
    }
    async getStreamStats(streamName) {
        const stats = await this.eventStreamModel.aggregate([
            { $match: { streamName } },
            {
                $group: {
                    _id: null,
                    eventCount: { $sum: 1 },
                    firstEvent: { $min: '$occurredAt' },
                    lastEvent: { $max: '$occurredAt' },
                    currentVersion: { $max: '$aggregateVersion' },
                },
            },
        ]);
        if (stats.length === 0) {
            return {
                streamName,
                eventCount: 0,
                currentVersion: 0,
                firstEvent: null,
                lastEvent: null,
            };
        }
        return {
            streamName,
            eventCount: stats[0].eventCount,
            currentVersion: stats[0].currentVersion,
            firstEvent: stats[0].firstEvent,
            lastEvent: stats[0].lastEvent,
        };
    }
};
exports.MongoDbEventStore = MongoDbEventStore;
exports.MongoDbEventStore = MongoDbEventStore = MongoDbEventStore_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('EventStream')),
    __param(1, (0, mongoose_1.InjectModel)('EventSnapshot')),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], MongoDbEventStore);
class GenericDomainEvent extends domain_event_base_1.DomainEvent {
    serializedData;
    constructor(serializedData) {
        super(serializedData.aggregateId, serializedData.aggregateType, serializedData.aggregateVersion, serializedData.eventType, serializedData.metadata, serializedData.causationId, serializedData.correlationId, serializedData.userId, serializedData.tenantId);
        this.serializedData = serializedData;
        this.eventId = serializedData.eventId;
        this.occurredAt = new Date(serializedData.occurredAt);
    }
    getPayload() {
        return this.serializedData.payload;
    }
}
//# sourceMappingURL=mongodb-event-store.js.map