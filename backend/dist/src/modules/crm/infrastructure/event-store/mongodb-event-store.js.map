{"version": 3, "file": "mongodb-event-store.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/infrastructure/event-store/mongodb-event-store.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAA+C;AAC/C,uCAAiC;AACjC,kFAA4G;AAOrG,IAAM,iBAAiB,yBAAvB,MAAM,iBAAiB;IAImB;IACE;IAJhC,MAAM,GAAG,IAAI,eAAM,CAAC,mBAAiB,CAAC,IAAI,CAAC,CAAC;IAE7D,YAC+C,gBAA4C,EAC1C,aAAsC;QADxC,qBAAgB,GAAhB,gBAAgB,CAA4B;QAC1C,kBAAa,GAAb,aAAa,CAAyB;IACpF,CAAC;IAKJ,KAAK,CAAC,MAAM,CAAC,UAAkB,EAAE,MAAqB,EAAE,eAAwB;QAC9E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,MAAM,CAAC,MAAM,sBAAsB,UAAU,EAAE,CAAC,CAAC;QAEhF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC;QAE9D,IAAI,CAAC;YACH,MAAM,OAAO,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE;gBAEvC,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;oBAClC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;oBACzE,IAAI,cAAc,KAAK,eAAe,EAAE,CAAC;wBACvC,MAAM,IAAI,KAAK,CAAC,0CAA0C,eAAe,4BAA4B,cAAc,EAAE,CAAC,CAAC;oBACzH,CAAC;gBACH,CAAC;gBAGD,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;oBACnD,UAAU;oBACV,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,WAAW,EAAE,KAAK,CAAC,WAAW;oBAC9B,aAAa,EAAE,KAAK,CAAC,aAAa;oBAClC,gBAAgB,EAAE,KAAK,CAAC,gBAAgB,GAAG,KAAK;oBAChD,UAAU,EAAE,KAAK,CAAC,UAAU;oBAC5B,WAAW,EAAE,KAAK,CAAC,WAAW;oBAC9B,aAAa,EAAE,KAAK,CAAC,aAAa;oBAClC,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,OAAO,EAAE,KAAK,CAAC,UAAU,EAAE;oBAC3B,QAAQ,EAAE,MAAM,IAAI,CAAC,qBAAqB,EAAE;iBAC7C,CAAC,CAAC,CAAC;gBAGJ,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;gBAEpE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,MAAM,CAAC,MAAM,sBAAsB,UAAU,EAAE,CAAC,CAAC;YAC9F,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;YAC7E,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,MAAM,OAAO,CAAC,UAAU,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,WAAoB;QACtD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,UAAU,kBAAkB,WAAW,EAAE,CAAC,CAAC;QAE5F,MAAM,KAAK,GAAQ,EAAE,UAAU,EAAE,CAAC;QAClC,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YAC9B,KAAK,CAAC,gBAAgB,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;QACjD,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,gBAAgB;aAC/C,IAAI,CAAC,KAAK,CAAC;aACX,IAAI,CAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;aAC7B,IAAI,EAAE,CAAC;QAEV,MAAM,MAAM,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;QAErE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,MAAM,CAAC,MAAM,wBAAwB,UAAU,EAAE,CAAC,CAAC;QAClF,OAAO,MAAM,CAAC;IAChB,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,YAAqB;QACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,YAAY,EAAE,CAAC,CAAC;QAEvE,MAAM,KAAK,GAAQ,EAAE,CAAC;QACtB,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,KAAK,CAAC,QAAQ,GAAG,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;QAC1C,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,gBAAgB;aAC/C,IAAI,CAAC,KAAK,CAAC;aACX,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;aACrB,IAAI,EAAE,CAAC;QAEV,MAAM,MAAM,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;QAErE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,MAAM,CAAC,MAAM,0BAA0B,YAAY,EAAE,CAAC,CAAC;QACtF,OAAO,MAAM,CAAC;IAChB,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,WAAmB,EAAE,aAAqB;QAC1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,aAAa,IAAI,WAAW,EAAE,CAAC,CAAC;QAErF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa;aACtC,OAAO,CAAC,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;aACvC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC;aACrB,IAAI,EAAE,CAAC;QAEV,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,aAAa,IAAI,WAAW,gBAAgB,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;YACnH,OAAO;gBACL,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,aAAa,IAAI,WAAW,EAAE,CAAC,CAAC;QACtF,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,WAAmB,EAAE,aAAqB,EAAE,QAAa,EAAE,OAAe;QAC3F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,aAAa,IAAI,WAAW,gBAAgB,OAAO,EAAE,CAAC,CAAC;QAE3G,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YAC9B,WAAW;YACX,aAAa;YACb,OAAO;YACP,IAAI,EAAE,QAAQ;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,aAAa,IAAI,WAAW,EAAE,CAAC,CAAC;IAClG,CAAC;IAKO,KAAK,CAAC,iBAAiB,CAAC,UAAkB,EAAE,OAAa;QAC/D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB;aAC1C,OAAO,CAAC,EAAE,UAAU,EAAE,CAAC;aACvB,IAAI,CAAC,EAAE,gBAAgB,EAAE,CAAC,CAAC,EAAE,CAAC;aAC9B,OAAO,CAAC,OAAO,CAAC;aAChB,IAAI,EAAE,CAAC;QAEV,OAAO,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IAKO,KAAK,CAAC,qBAAqB;QACjC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB;aAC1C,OAAO,CAAC,EAAE,CAAC;aACX,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC;aACtB,IAAI,EAAE,CAAC;QAEV,OAAO,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC;IAKO,gBAAgB,CAAC,GAAwB;QAK/C,MAAM,eAAe,GAA0B;YAC7C,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,WAAW,EAAE,GAAG,CAAC,WAAW;YAC5B,aAAa,EAAE,GAAG,CAAC,aAAa;YAChC,gBAAgB,EAAE,GAAG,CAAC,gBAAgB;YACtC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE;YACxC,WAAW,EAAE,GAAG,CAAC,WAAW;YAC5B,aAAa,EAAE,GAAG,CAAC,aAAa;YAChC,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,OAAO,EAAE,GAAG,CAAC,OAAO;SACrB,CAAC;QAIF,OAAO,IAAI,kBAAkB,CAAC,eAAe,CAAC,CAAC;IACjD,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,UAAgB,EAAE,gBAAyB,IAAI;QACpE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAE1E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;YACpD,UAAU,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE;SAChC,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;gBAClC,SAAS,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE;aAC/B,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,MAAM,CAAC,YAAY,SAAS,CAAC,CAAC;QAC5D,OAAO,MAAM,CAAC,YAAY,CAAC;IAC7B,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,UAAkB;QACrC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;YAClD,EAAE,MAAM,EAAE,EAAE,UAAU,EAAE,EAAE;YAC1B;gBACE,MAAM,EAAE;oBACN,GAAG,EAAE,IAAI;oBACT,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;oBACvB,UAAU,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;oBACnC,SAAS,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;oBAClC,cAAc,EAAE,EAAE,IAAI,EAAE,mBAAmB,EAAE;iBAC9C;aACF;SACF,CAAC,CAAC;QAEH,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO;gBACL,UAAU;gBACV,UAAU,EAAE,CAAC;gBACb,cAAc,EAAE,CAAC;gBACjB,UAAU,EAAE,IAAI;gBAChB,SAAS,EAAE,IAAI;aAChB,CAAC;QACJ,CAAC;QAED,OAAO;YACL,UAAU;YACV,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU;YAC/B,cAAc,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc;YACvC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU;YAC/B,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;SAC9B,CAAC;IACJ,CAAC;CACF,CAAA;AA1PY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,sBAAW,EAAC,aAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,sBAAW,EAAC,eAAe,CAAC,CAAA;qCADkC,gBAAK;QACN,gBAAK;GAL1D,iBAAiB,CA0P7B;AAKD,MAAM,kBAAmB,SAAQ,+BAAW;IACb;IAA7B,YAA6B,cAAqC;QAChE,KAAK,CACH,cAAc,CAAC,WAAW,EAC1B,cAAc,CAAC,aAAa,EAC5B,cAAc,CAAC,gBAAgB,EAC/B,cAAc,CAAC,SAAS,EACxB,cAAc,CAAC,QAAQ,EACvB,cAAc,CAAC,WAAW,EAC1B,cAAc,CAAC,aAAa,EAC5B,cAAc,CAAC,MAAM,EACrB,cAAc,CAAC,QAAQ,CACxB,CAAC;QAXyB,mBAAc,GAAd,cAAc,CAAuB;QAc/D,IAAY,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;QAC9C,IAAY,CAAC,UAAU,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IACjE,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;IACrC,CAAC;CACF"}