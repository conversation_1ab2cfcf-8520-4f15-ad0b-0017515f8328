import { Model } from 'mongoose';
import { DomainEvent, EventStore } from '../../domain/events/base/domain-event.base';
export declare class MongoDbEventStore implements EventStore {
    private readonly eventStreamModel;
    private readonly snapshotModel;
    private readonly logger;
    constructor(eventStreamModel: Model<EventStreamDocument>, snapshotModel: Model<SnapshotDocument>);
    append(streamName: string, events: DomainEvent[], expectedVersion?: number): Promise<void>;
    getEvents(streamName: string, fromVersion?: number): Promise<DomainEvent[]>;
    getAllEvents(fromPosition?: number): Promise<DomainEvent[]>;
    getSnapshot(aggregateId: string, aggregateType: string): Promise<any>;
    saveSnapshot(aggregateId: string, aggregateType: string, snapshot: any, version: number): Promise<void>;
    private getCurrentVersion;
    private getNextGlobalPosition;
    private deserializeEvent;
    cleanupOldEvents(beforeDate: Date, keepSnapshots?: boolean): Promise<number>;
    getStreamStats(streamName: string): Promise<StreamStats>;
}
interface EventStreamDocument {
    streamName: string;
    eventId: string;
    eventType: string;
    aggregateId: string;
    aggregateType: string;
    aggregateVersion: number;
    occurredAt: Date;
    causationId?: string;
    correlationId?: string;
    userId?: string;
    tenantId?: string;
    metadata: any;
    payload: any;
    position: number;
}
interface SnapshotDocument {
    aggregateId: string;
    aggregateType: string;
    version: number;
    data: any;
    createdAt: Date;
}
interface StreamStats {
    streamName: string;
    eventCount: number;
    currentVersion: number;
    firstEvent: Date | null;
    lastEvent: Date | null;
}
export {};
