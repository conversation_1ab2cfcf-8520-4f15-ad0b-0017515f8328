"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EmailQueueProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailQueueProcessor = void 0;
const bullmq_1 = require("@nestjs/bullmq");
const common_1 = require("@nestjs/common");
const bullmq_2 = require("bullmq");
const queue_module_1 = require("../queue.module");
let EmailQueueProcessor = EmailQueueProcessor_1 = class EmailQueueProcessor extends bullmq_1.WorkerHost {
    logger = new common_1.Logger(EmailQueueProcessor_1.name);
    async process(job) {
        this.logger.debug(`Processing email job: ${job.name} (${job.id})`);
        try {
            await job.updateProgress(10);
            switch (job.name) {
                case queue_module_1.JOB_TYPES.SEND_WELCOME_EMAIL:
                    return await this.processWelcomeEmail(job);
                case queue_module_1.JOB_TYPES.SEND_LEAD_NOTIFICATION:
                    return await this.processLeadNotification(job);
                case queue_module_1.JOB_TYPES.SEND_OPPORTUNITY_UPDATE:
                    return await this.processOpportunityUpdate(job);
                case queue_module_1.JOB_TYPES.SEND_BULK_EMAIL:
                    return await this.processBulkEmail(job);
                default:
                    throw new Error(`Unknown email job type: ${job.name}`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to process email job: ${job.name} (${job.id})`, error);
            throw error;
        }
    }
    async processWelcomeEmail(job) {
        const { to, subject, template, data } = job.data;
        this.logger.log(`Sending welcome email to: ${to}`);
        await job.updateProgress(30);
        const renderedContent = await this.renderEmailTemplate(template, data);
        await job.updateProgress(60);
        const result = await this.sendEmail({
            to,
            subject,
            html: renderedContent.html,
            text: renderedContent.text,
        });
        await job.updateProgress(100);
        this.logger.log(`Welcome email sent successfully to: ${to}`);
        return {
            messageId: result.messageId,
            recipient: to,
            template,
            sentAt: new Date().toISOString(),
        };
    }
    async processLeadNotification(job) {
        const { to, subject, template, data } = job.data;
        this.logger.log(`Sending lead notification to: ${to}`);
        await job.updateProgress(30);
        const renderedContent = await this.renderEmailTemplate(template, data);
        await job.updateProgress(60);
        const result = await this.sendEmail({
            to,
            subject,
            html: renderedContent.html,
            text: renderedContent.text,
        });
        await job.updateProgress(100);
        this.logger.log(`Lead notification sent successfully to: ${to}`);
        return {
            messageId: result.messageId,
            recipient: to,
            leadId: data.leadId,
            template,
            sentAt: new Date().toISOString(),
        };
    }
    async processOpportunityUpdate(job) {
        const { to, subject, template, data } = job.data;
        this.logger.log(`Sending opportunity update to: ${to}`);
        await job.updateProgress(30);
        const renderedContent = await this.renderEmailTemplate(template, data);
        await job.updateProgress(60);
        const result = await this.sendEmail({
            to,
            subject,
            html: renderedContent.html,
            text: renderedContent.text,
        });
        await job.updateProgress(100);
        this.logger.log(`Opportunity update sent successfully to: ${to}`);
        return {
            messageId: result.messageId,
            recipient: to,
            opportunityId: data.opportunityId,
            updateType: data.updateType,
            template,
            sentAt: new Date().toISOString(),
        };
    }
    async processBulkEmail(job) {
        const { to, subject, template, data } = job.data;
        this.logger.log(`Sending bulk email to: ${to}`);
        await job.updateProgress(30);
        const renderedContent = await this.renderEmailTemplate(template, data);
        await job.updateProgress(60);
        const result = await this.sendEmail({
            to,
            subject,
            html: renderedContent.html,
            text: renderedContent.text,
        });
        await job.updateProgress(100);
        this.logger.log(`Bulk email sent successfully to: ${to}`);
        return {
            messageId: result.messageId,
            recipient: to,
            campaignId: data.campaignId,
            template,
            sentAt: new Date().toISOString(),
        };
    }
    async renderEmailTemplate(template, data) {
        const html = `
      <html>
        <body>
          <h1>Email Template: ${template}</h1>
          <p>Data: ${JSON.stringify(data, null, 2)}</p>
        </body>
      </html>
    `;
        const text = `Email Template: ${template}\nData: ${JSON.stringify(data, null, 2)}`;
        return { html, text };
    }
    async sendEmail(emailData) {
        this.logger.debug(`Simulating email send to: ${emailData.to}`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        if (Math.random() < 0.05) {
            throw new Error('Simulated email service failure');
        }
        return {
            messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        };
    }
    onCompleted(job, result) {
        this.logger.log(`Email job completed: ${job.name} (${job.id})`);
    }
    onFailed(job, error) {
        this.logger.error(`Email job failed: ${job.name} (${job.id})`, error);
    }
    onProgress(job, progress) {
        this.logger.debug(`Email job progress: ${job.name} (${job.id}) - ${progress}%`);
    }
    onError(error) {
        this.logger.error('Email worker error:', error);
    }
    onStalled(jobId) {
        this.logger.warn(`Email job stalled: ${jobId}`);
    }
};
exports.EmailQueueProcessor = EmailQueueProcessor;
__decorate([
    (0, bullmq_1.OnWorkerEvent)('completed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [bullmq_2.Job, Object]),
    __metadata("design:returntype", void 0)
], EmailQueueProcessor.prototype, "onCompleted", null);
__decorate([
    (0, bullmq_1.OnWorkerEvent)('failed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [bullmq_2.Job, Error]),
    __metadata("design:returntype", void 0)
], EmailQueueProcessor.prototype, "onFailed", null);
__decorate([
    (0, bullmq_1.OnWorkerEvent)('progress'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [bullmq_2.Job, Object]),
    __metadata("design:returntype", void 0)
], EmailQueueProcessor.prototype, "onProgress", null);
__decorate([
    (0, bullmq_1.OnWorkerEvent)('error'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Error]),
    __metadata("design:returntype", void 0)
], EmailQueueProcessor.prototype, "onError", null);
__decorate([
    (0, bullmq_1.OnWorkerEvent)('stalled'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], EmailQueueProcessor.prototype, "onStalled", null);
exports.EmailQueueProcessor = EmailQueueProcessor = EmailQueueProcessor_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, bullmq_1.Processor)(queue_module_1.QUEUE_NAMES.EMAIL)
], EmailQueueProcessor);
//# sourceMappingURL=email-queue.processor.js.map