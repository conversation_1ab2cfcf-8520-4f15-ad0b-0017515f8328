{"version": 3, "file": "email-queue.processor.js", "sourceRoot": "", "sources": ["../../../../../../../src/modules/crm/infrastructure/async/processors/email-queue.processor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAsE;AACtE,2CAAoD;AACpD,mCAA6B;AAC7B,kDAAuE;AAQhE,IAAM,mBAAmB,2BAAzB,MAAM,mBAAoB,SAAQ,mBAAU;IAChC,MAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;IAK/D,KAAK,CAAC,OAAO,CAAC,GAAmC;QAC/C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QAEnE,IAAI,CAAC;YAEH,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YAE7B,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;gBACjB,KAAK,wBAAS,CAAC,kBAAkB;oBAC/B,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;gBAE7C,KAAK,wBAAS,CAAC,sBAAsB;oBACnC,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;gBAEjD,KAAK,wBAAS,CAAC,uBAAuB;oBACpC,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;gBAElD,KAAK,wBAAS,CAAC,eAAe;oBAC5B,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAE1C;oBACE,MAAM,IAAI,KAAK,CAAC,2BAA2B,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACjF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,mBAAmB,CAAC,GAAsB;QACtD,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,EAAE,EAAE,CAAC,CAAC;QAEnD,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAG7B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAEvE,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAG7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC;YAClC,EAAE;YACF,OAAO;YACP,IAAI,EAAE,eAAe,CAAC,IAAI;YAC1B,IAAI,EAAE,eAAe,CAAC,IAAI;SAC3B,CAAC,CAAC;QAEH,MAAM,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAE9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,EAAE,EAAE,CAAC,CAAC;QAE7D,OAAO;YACL,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,SAAS,EAAE,EAAE;YACb,QAAQ;YACR,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACjC,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,uBAAuB,CAAC,GAAsB;QAC1D,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,EAAE,EAAE,CAAC,CAAC;QAEvD,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAE7B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAEvE,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAE7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC;YAClC,EAAE;YACF,OAAO;YACP,IAAI,EAAE,eAAe,CAAC,IAAI;YAC1B,IAAI,EAAE,eAAe,CAAC,IAAI;SAC3B,CAAC,CAAC;QAEH,MAAM,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAE9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2CAA2C,EAAE,EAAE,CAAC,CAAC;QAEjE,OAAO;YACL,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,SAAS,EAAE,EAAE;YACb,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ;YACR,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACjC,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,wBAAwB,CAAC,GAAsB;QAC3D,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,EAAE,EAAE,CAAC,CAAC;QAExD,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAE7B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAEvE,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAE7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC;YAClC,EAAE;YACF,OAAO;YACP,IAAI,EAAE,eAAe,CAAC,IAAI;YAC1B,IAAI,EAAE,eAAe,CAAC,IAAI;SAC3B,CAAC,CAAC;QAEH,MAAM,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAE9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4CAA4C,EAAE,EAAE,CAAC,CAAC;QAElE,OAAO;YACL,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,SAAS,EAAE,EAAE;YACb,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ;YACR,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACjC,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,gBAAgB,CAAC,GAAsB;QACnD,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC;QAEhD,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAE7B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAEvE,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAE7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC;YAClC,EAAE;YACF,OAAO;YACP,IAAI,EAAE,eAAe,CAAC,IAAI;YAC1B,IAAI,EAAE,eAAe,CAAC,IAAI;SAC3B,CAAC,CAAC;QAEH,MAAM,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAE9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oCAAoC,EAAE,EAAE,CAAC,CAAC;QAE1D,OAAO;YACL,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ;YACR,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACjC,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,mBAAmB,CAC/B,QAAgB,EAChB,IAAyB;QAKzB,MAAM,IAAI,GAAG;;;gCAGe,QAAQ;qBACnB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;;;KAG7C,CAAC;QAEF,MAAM,IAAI,GAAG,mBAAmB,QAAQ,WAAW,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;QAEnF,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACxB,CAAC;IAKO,KAAK,CAAC,SAAS,CAAC,SAKvB;QAIC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QAG/D,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAGxD,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,OAAO;YACL,SAAS,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;SAC1E,CAAC;IACJ,CAAC;IAMD,WAAW,CAAC,GAAsB,EAAE,MAAW;QAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;IASlE,CAAC;IAMD,QAAQ,CAAC,GAAsB,EAAE,KAAY;QAC3C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IAUxE,CAAC;IAMD,UAAU,CAAC,GAAsB,EAAE,QAAyB;QAC1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,OAAO,QAAQ,GAAG,CAAC,CAAC;IAClF,CAAC;IAMD,OAAO,CAAC,KAAY;QAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IAMD,SAAS,CAAC,KAAa;QACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,KAAK,EAAE,CAAC,CAAC;IAClD,CAAC;CACF,CAAA;AAxRY,kDAAmB;AAoO9B;IADC,IAAA,sBAAa,EAAC,WAAW,CAAC;;qCACV,YAAG;;sDAUnB;AAMD;IADC,IAAA,sBAAa,EAAC,QAAQ,CAAC;;qCACV,YAAG,EAAuB,KAAK;;mDAW5C;AAMD;IADC,IAAA,sBAAa,EAAC,UAAU,CAAC;;qCACV,YAAG;;qDAElB;AAMD;IADC,IAAA,sBAAa,EAAC,OAAO,CAAC;;qCACR,KAAK;;kDAEnB;AAMD;IADC,IAAA,sBAAa,EAAC,SAAS,CAAC;;;;oDAGxB;8BAvRU,mBAAmB;IAF/B,IAAA,mBAAU,GAAE;IACZ,IAAA,kBAAS,EAAC,0BAAW,CAAC,KAAK,CAAC;GAChB,mBAAmB,CAwR/B"}