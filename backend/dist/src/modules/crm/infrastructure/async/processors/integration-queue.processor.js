"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var IntegrationQueueProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegrationQueueProcessor = void 0;
const common_1 = require("@nestjs/common");
let IntegrationQueueProcessor = IntegrationQueueProcessor_1 = class IntegrationQueueProcessor {
    logger = new common_1.Logger(IntegrationQueueProcessor_1.name);
    async process(job) {
        this.logger.log('IntegrationQueueProcessor - Placeholder implementation');
        return { success: true };
    }
};
exports.IntegrationQueueProcessor = IntegrationQueueProcessor;
exports.IntegrationQueueProcessor = IntegrationQueueProcessor = IntegrationQueueProcessor_1 = __decorate([
    (0, common_1.Injectable)()
], IntegrationQueueProcessor);
//# sourceMappingURL=integration-queue.processor.js.map