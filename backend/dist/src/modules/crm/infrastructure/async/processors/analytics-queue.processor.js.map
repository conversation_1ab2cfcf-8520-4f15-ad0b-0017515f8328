{"version": 3, "file": "analytics-queue.processor.js", "sourceRoot": "", "sources": ["../../../../../../../src/modules/crm/infrastructure/async/processors/analytics-queue.processor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAsE;AACtE,2CAA4D;AAC5D,mCAA6B;AAC7B,kDAA2G;AAC3G,wFAAwF;AACxF,oFAAsG;AAEtG,oFAA2F;AAQpF,IAAM,uBAAuB,+BAA7B,MAAM,uBAAwB,SAAQ,mBAAU;IAIlC;IACA;IAC+B;IALjC,MAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;IAEnE,YACmB,kBAAsC,EACtC,wBAAkD,EACnB,cAA+B;QAE/E,KAAK,EAAE,CAAC;QAJS,uBAAkB,GAAlB,kBAAkB,CAAoB;QACtC,6BAAwB,GAAxB,wBAAwB,CAA0B;QACnB,mBAAc,GAAd,cAAc,CAAiB;IAGjF,CAAC;IAKD,KAAK,CAAC,OAAO,CAAC,GAAuC;QACnD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QAEvE,IAAI,CAAC;YACH,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YAE7B,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;gBACjB,KAAK,wBAAS,CAAC,oBAAoB;oBACjC,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBAE5C,KAAK,wBAAS,CAAC,uBAAuB;oBACpC,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;gBAEhD,KAAK,wBAAS,CAAC,eAAe;oBAC5B,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;gBAEjD,KAAK,wBAAS,CAAC,mBAAmB;oBAChC,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;gBAE9C;oBACE,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACrF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,kBAAkB,CAAC,GAA0B;QACzD,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAE1C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oCAAoC,QAAQ,EAAE,CAAC,CAAC;QAEhE,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;YACpE,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;YACjD,CAAC;YAED,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YAG7B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;YAEjG,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YAG7B,IAAI,OAAO,GAAkB,IAAI,CAAC;YAClC,IAAI,CAAC;gBACH,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;YAC/F,CAAC;YAAC,OAAO,OAAO,EAAE,CAAC;gBACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,QAAQ,0BAA0B,EAAE,OAAO,CAAC,CAAC;YAC9F,CAAC;YAED,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YAG7B,IAAI,UAAU,GAAG,SAAS,CAAC;YAC3B,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;gBAErB,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC;gBACxE,UAAU,GAAG,IAAK,SAAS,CAAC,WAAmB,CAC7C,aAAa,EACb,SAAS,CAAC,OAAO,EACjB,SAAS,CAAC,KAAK,EACf,SAAS,CAAC,kBAAkB,EAC5B,SAAS,CAAC,YAAY,EACtB;oBACE,GAAG,SAAS,CAAC,QAAQ;oBACrB,OAAO;oBACP,aAAa,EAAE,IAAI;iBACpB,CACF,CAAC;YACJ,CAAC;YAKD,MAAM,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAE9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gDAAgD,QAAQ,YAAY,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;YAEzG,OAAO;gBACL,MAAM,EAAE,QAAQ;gBAChB,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;gBACvD,kBAAkB,EAAE,UAAU,CAAC,kBAAkB;gBACjD,UAAU,EAAE,OAAO,KAAK,IAAI;gBAC5B,YAAY,EAAE,UAAU,CAAC,YAAY,CAAC,WAAW,EAAE;aACpD,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YACjF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,sBAAsB,CAAC,GAA0B;QAC7D,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEtC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,IAAI,EAAE,CAAC,CAAC;QAE/D,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;YAExE,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YAG7B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;YAE1F,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YAG7B,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YAEhD,MAAM,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAE9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mDAAmD,IAAI,EAAE,CAAC,CAAC;YAE3E,OAAO;gBACL,IAAI;gBACJ,OAAO,EAAE,cAAc;gBACvB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAA+C,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;YAChF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,uBAAuB,CAAC,GAA0B;QAC9D,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAE5C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,UAAU,EAAE,CAAC,CAAC;QAEpD,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,UAAe,CAAC;YAEpB,QAAQ,UAAU,EAAE,CAAC;gBACnB,KAAK,sBAAsB;oBACzB,UAAU,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;oBAC/D,MAAM;gBAER,KAAK,sBAAsB;oBACzB,UAAU,GAAG,MAAM,IAAI,CAAC,iCAAiC,CAAC,UAAU,CAAC,CAAC;oBACtE,MAAM;gBAER,KAAK,qBAAqB;oBACxB,UAAU,GAAG,MAAM,IAAI,CAAC,gCAAgC,CAAC,UAAU,CAAC,CAAC;oBACrE,MAAM;gBAER;oBACE,MAAM,IAAI,KAAK,CAAC,wBAAwB,UAAU,EAAE,CAAC,CAAC;YAC1D,CAAC;YAED,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YAG7B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YAEpF,MAAM,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAE9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,UAAU,EAAE,CAAC,CAAC;YAEhE,OAAO;gBACL,UAAU;gBACV,QAAQ,EAAE,GAAG,UAAU,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;gBACvC,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACrC,UAAU;aACX,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,oBAAoB,CAAC,GAA0B;QAC3D,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEtC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oCAAoC,IAAI,EAAE,CAAC,CAAC;QAE5D,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QAE7B,IAAI,CAAC;YAEH,IAAI,UAAe,CAAC;YAEpB,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,aAAa;oBAChB,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;oBACnD,MAAM;gBAER,KAAK,kBAAkB;oBACrB,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;oBACxD,MAAM;gBAER,KAAK,iBAAiB;oBACpB,UAAU,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;oBACvD,MAAM;gBAER;oBACE,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,EAAE,CAAC,CAAC;YAClD,CAAC;YAED,MAAM,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAE9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gDAAgD,IAAI,EAAE,CAAC,CAAC;YAExE,OAAO;gBACL,IAAI;gBACJ,UAAU;gBACV,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACnC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;YAC5E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,yBAAyB,CAAC,UAAe;QAErD,OAAO;YACL,UAAU,EAAE,CAAC;YACb,cAAc,EAAE,CAAC;YACjB,cAAc,EAAE,CAAC;YACjB,YAAY,EAAE,CAAC;YACf,cAAc,EAAE,CAAC;SAClB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,IAAY,EAAE,UAAe,EAAE,cAAmB;QAEpF,MAAM,cAAc,GAAG,EAAE,GAAG,cAAc,EAAE,CAAC;QAE7C,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,cAAc;gBACjB,cAAc,CAAC,UAAU,IAAI,CAAC,CAAC;gBAC/B,MAAM;YACR,KAAK,gBAAgB;gBACnB,cAAc,CAAC,cAAc,IAAI,CAAC,CAAC;gBACnC,MAAM;YACR,KAAK,gBAAgB;gBACnB,cAAc,CAAC,cAAc,IAAI,CAAC,CAAC;gBACnC,MAAM;QACV,CAAC;QAGD,cAAc,CAAC,cAAc,GAAG,cAAc,CAAC,UAAU,GAAG,CAAC;YAC3D,CAAC,CAAC,CAAC,cAAc,CAAC,cAAc,GAAG,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG;YACnE,CAAC,CAAC,CAAC,CAAC;QAEN,OAAO,cAAc,CAAC;IACxB,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,OAAY;QAE7C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,UAAe;QAEtD,OAAO;YACL,gBAAgB,EAAE,CAAC;YACnB,YAAY,EAAE,CAAC;YACf,iBAAiB,EAAE,EAAE;YACrB,UAAU,EAAE,EAAE;SACf,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,iCAAiC,CAAC,UAAe;QAE7D,OAAO;YACL,gBAAgB,EAAE,CAAC;YACnB,eAAe,EAAE,EAAE;YACnB,WAAW,EAAE,EAAE;YACf,eAAe,EAAE,EAAE;SACpB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,gCAAgC,CAAC,UAAe;QAE5D,OAAO;YACL,iBAAiB,EAAE,EAAE;YACrB,aAAa,EAAE,EAAE;YACjB,cAAc,EAAE,EAAE;YAClB,gBAAgB,EAAE,EAAE;SACrB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,IAAS,EAAE,UAAe;QAEvE,OAAO;YACL,KAAK,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YAC3E,OAAO,EAAE,gBAAgB;YACzB,IAAI;YACJ,MAAM,EAAE,EAAE;YACV,eAAe,EAAE,EAAE;SACpB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,UAAe;QAE1C,OAAO,EAAE,WAAW,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,UAAe;QAE/C,OAAO,EAAE,WAAW,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,UAAe;QAE9C,OAAO,EAAE,WAAW,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;IACxC,CAAC;IAMD,WAAW,CAAC,GAA0B,EAAE,MAAW;QACjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;IACtE,CAAC;IAGD,QAAQ,CAAC,GAA0B,EAAE,KAAY;QAC/C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IAC5E,CAAC;IAGD,UAAU,CAAC,GAA0B,EAAE,QAAyB;QAC9D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,OAAO,QAAQ,GAAG,CAAC,CAAC;IACtF,CAAC;CACF,CAAA;AA1XY,0DAAuB;AA6WlC;IADC,IAAA,sBAAa,EAAC,WAAW,CAAC;;qCACV,YAAG;;0DAEnB;AAGD;IADC,IAAA,sBAAa,EAAC,QAAQ,CAAC;;qCACV,YAAG,EAA2B,KAAK;;uDAEhD;AAGD;IADC,IAAA,sBAAa,EAAC,UAAU,CAAC;;qCACV,YAAG;;yDAElB;kCAzXU,uBAAuB;IAFnC,IAAA,mBAAU,GAAE;IACZ,IAAA,kBAAS,EAAC,0BAAW,CAAC,SAAS,CAAC;IAO5B,WAAA,IAAA,eAAM,EAAC,wCAAqB,CAAC,CAAA;qCAFO,yCAAkB;QACZ,qDAAwB;GAL1D,uBAAuB,CA0XnC"}