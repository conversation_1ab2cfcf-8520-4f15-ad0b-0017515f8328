"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AnalyticsQueueProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsQueueProcessor = void 0;
const bullmq_1 = require("@nestjs/bullmq");
const common_1 = require("@nestjs/common");
const bullmq_2 = require("bullmq");
const queue_module_1 = require("../queue.module");
const lead_scoring_service_1 = require("../../../domain/services/lead-scoring.service");
const tensorflow_scoring_service_1 = require("../../ml/tensorflow-scoring.service");
const injection_tokens_1 = require("../../../domain/repositories/injection-tokens");
let AnalyticsQueueProcessor = AnalyticsQueueProcessor_1 = class AnalyticsQueueProcessor extends bullmq_1.WorkerHost {
    leadScoringService;
    tensorFlowScoringService;
    leadRepository;
    logger = new common_1.Logger(AnalyticsQueueProcessor_1.name);
    constructor(leadScoringService, tensorFlowScoringService, leadRepository) {
        super();
        this.leadScoringService = leadScoringService;
        this.tensorFlowScoringService = tensorFlowScoringService;
        this.leadRepository = leadRepository;
    }
    async process(job) {
        this.logger.debug(`Processing analytics job: ${job.name} (${job.id})`);
        try {
            await job.updateProgress(10);
            switch (job.name) {
                case queue_module_1.JOB_TYPES.CALCULATE_LEAD_SCORE:
                    return await this.processLeadScoring(job);
                case queue_module_1.JOB_TYPES.UPDATE_PIPELINE_METRICS:
                    return await this.processPipelineMetrics(job);
                case queue_module_1.JOB_TYPES.GENERATE_REPORT:
                    return await this.processReportGeneration(job);
                case queue_module_1.JOB_TYPES.SYNC_ANALYTICS_DATA:
                    return await this.processAnalyticsSync(job);
                default:
                    throw new Error(`Unknown analytics job type: ${job.name}`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to process analytics job: ${job.name} (${job.id})`, error);
            throw error;
        }
    }
    async processLeadScoring(job) {
        const { entityId, parameters } = job.data;
        if (!entityId) {
            throw new Error('Lead ID is required for lead scoring');
        }
        this.logger.log(`Calculating lead score for lead: ${entityId}`);
        await job.updateProgress(30);
        try {
            const lead = await this.leadRepository.findById(parseInt(entityId));
            if (!lead) {
                throw new Error(`Lead not found: ${entityId}`);
            }
            await job.updateProgress(50);
            const leadScore = await this.leadScoringService.calculateScore(lead, parameters?.historicalData);
            await job.updateProgress(70);
            let mlScore = null;
            try {
                mlScore = await this.tensorFlowScoringService.predictScore(lead, parameters?.historicalData);
            }
            catch (mlError) {
                this.logger.warn(`ML scoring failed for lead ${entityId}, using rule-based score`, mlError);
            }
            await job.updateProgress(90);
            let finalScore = leadScore;
            if (mlScore !== null) {
                const combinedScore = Math.round(leadScore.score * 0.7 + mlScore * 0.3);
                finalScore = new leadScore.constructor(combinedScore, leadScore.factors, leadScore.grade, leadScore.recommendedActions, leadScore.calculatedAt, {
                    ...leadScore.metadata,
                    mlScore,
                    combinedScore: true,
                });
            }
            await job.updateProgress(100);
            this.logger.log(`Lead score calculated successfully for lead: ${entityId} (score: ${finalScore.score})`);
            return {
                leadId: entityId,
                score: finalScore.score,
                grade: finalScore.grade,
                factors: finalScore.factors.map(f => f.toPlainObject()),
                recommendedActions: finalScore.recommendedActions,
                mlEnhanced: mlScore !== null,
                calculatedAt: finalScore.calculatedAt.toISOString(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to calculate lead score for lead: ${entityId}`, error);
            throw error;
        }
    }
    async processPipelineMetrics(job) {
        const { type, parameters } = job.data;
        this.logger.log(`Updating pipeline metrics for type: ${type}`);
        await job.updateProgress(30);
        try {
            const currentMetrics = await this.getCurrentPipelineMetrics(parameters);
            await job.updateProgress(60);
            const updatedMetrics = await this.updateMetricsForEvent(type, parameters, currentMetrics);
            await job.updateProgress(90);
            await this.storePipelineMetrics(updatedMetrics);
            await job.updateProgress(100);
            this.logger.log(`Pipeline metrics updated successfully for type: ${type}`);
            return {
                type,
                metrics: updatedMetrics,
                updatedAt: new Date().toISOString(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to update pipeline metrics for type: ${type}`, error);
            throw error;
        }
    }
    async processReportGeneration(job) {
        const { reportType, parameters } = job.data;
        this.logger.log(`Generating report: ${reportType}`);
        await job.updateProgress(20);
        try {
            let reportData;
            switch (reportType) {
                case 'lead_scoring_summary':
                    reportData = await this.generateLeadScoringSummary(parameters);
                    break;
                case 'pipeline_performance':
                    reportData = await this.generatePipelinePerformanceReport(parameters);
                    break;
                case 'conversion_analysis':
                    reportData = await this.generateConversionAnalysisReport(parameters);
                    break;
                default:
                    throw new Error(`Unknown report type: ${reportType}`);
            }
            await job.updateProgress(80);
            const formattedReport = await this.formatReport(reportType, reportData, parameters);
            await job.updateProgress(100);
            this.logger.log(`Report generated successfully: ${reportType}`);
            return {
                reportType,
                reportId: `${reportType}_${Date.now()}`,
                data: formattedReport,
                generatedAt: new Date().toISOString(),
                parameters,
            };
        }
        catch (error) {
            this.logger.error(`Failed to generate report: ${reportType}`, error);
            throw error;
        }
    }
    async processAnalyticsSync(job) {
        const { type, parameters } = job.data;
        this.logger.log(`Syncing analytics data for type: ${type}`);
        await job.updateProgress(25);
        try {
            let syncResult;
            switch (type) {
                case 'lead_scores':
                    syncResult = await this.syncLeadScores(parameters);
                    break;
                case 'pipeline_metrics':
                    syncResult = await this.syncPipelineMetrics(parameters);
                    break;
                case 'conversion_data':
                    syncResult = await this.syncConversionData(parameters);
                    break;
                default:
                    throw new Error(`Unknown sync type: ${type}`);
            }
            await job.updateProgress(100);
            this.logger.log(`Analytics data synced successfully for type: ${type}`);
            return {
                type,
                syncResult,
                syncedAt: new Date().toISOString(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to sync analytics data for type: ${type}`, error);
            throw error;
        }
    }
    async getCurrentPipelineMetrics(parameters) {
        return {
            totalLeads: 0,
            qualifiedLeads: 0,
            convertedLeads: 0,
            averageScore: 0,
            conversionRate: 0,
        };
    }
    async updateMetricsForEvent(type, parameters, currentMetrics) {
        const updatedMetrics = { ...currentMetrics };
        switch (type) {
            case 'lead_created':
                updatedMetrics.totalLeads += 1;
                break;
            case 'lead_qualified':
                updatedMetrics.qualifiedLeads += 1;
                break;
            case 'lead_converted':
                updatedMetrics.convertedLeads += 1;
                break;
        }
        updatedMetrics.conversionRate = updatedMetrics.totalLeads > 0
            ? (updatedMetrics.convertedLeads / updatedMetrics.totalLeads) * 100
            : 0;
        return updatedMetrics;
    }
    async storePipelineMetrics(metrics) {
        this.logger.debug('Storing pipeline metrics', metrics);
    }
    async generateLeadScoringSummary(parameters) {
        return {
            totalLeadsScored: 0,
            averageScore: 0,
            scoreDistribution: {},
            topFactors: [],
        };
    }
    async generatePipelinePerformanceReport(parameters) {
        return {
            pipelineVelocity: 0,
            conversionRates: {},
            bottlenecks: [],
            recommendations: [],
        };
    }
    async generateConversionAnalysisReport(parameters) {
        return {
            conversionFunnels: {},
            dropOffPoints: [],
            successFactors: [],
            improvementAreas: [],
        };
    }
    async formatReport(reportType, data, parameters) {
        return {
            title: reportType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            summary: 'Report summary',
            data,
            charts: [],
            recommendations: [],
        };
    }
    async syncLeadScores(parameters) {
        return { syncedCount: 0, errors: [] };
    }
    async syncPipelineMetrics(parameters) {
        return { syncedCount: 0, errors: [] };
    }
    async syncConversionData(parameters) {
        return { syncedCount: 0, errors: [] };
    }
    onCompleted(job, result) {
        this.logger.log(`Analytics job completed: ${job.name} (${job.id})`);
    }
    onFailed(job, error) {
        this.logger.error(`Analytics job failed: ${job.name} (${job.id})`, error);
    }
    onProgress(job, progress) {
        this.logger.debug(`Analytics job progress: ${job.name} (${job.id}) - ${progress}%`);
    }
};
exports.AnalyticsQueueProcessor = AnalyticsQueueProcessor;
__decorate([
    (0, bullmq_1.OnWorkerEvent)('completed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [bullmq_2.Job, Object]),
    __metadata("design:returntype", void 0)
], AnalyticsQueueProcessor.prototype, "onCompleted", null);
__decorate([
    (0, bullmq_1.OnWorkerEvent)('failed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [bullmq_2.Job, Error]),
    __metadata("design:returntype", void 0)
], AnalyticsQueueProcessor.prototype, "onFailed", null);
__decorate([
    (0, bullmq_1.OnWorkerEvent)('progress'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [bullmq_2.Job, Object]),
    __metadata("design:returntype", void 0)
], AnalyticsQueueProcessor.prototype, "onProgress", null);
exports.AnalyticsQueueProcessor = AnalyticsQueueProcessor = AnalyticsQueueProcessor_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, bullmq_1.Processor)(queue_module_1.QUEUE_NAMES.ANALYTICS),
    __param(2, (0, common_1.Inject)(injection_tokens_1.LEAD_REPOSITORY_TOKEN)),
    __metadata("design:paramtypes", [lead_scoring_service_1.LeadScoringService,
        tensorflow_scoring_service_1.TensorFlowScoringService, Object])
], AnalyticsQueueProcessor);
//# sourceMappingURL=analytics-queue.processor.js.map