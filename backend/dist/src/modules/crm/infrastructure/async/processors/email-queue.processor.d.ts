import { WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { EmailJobData } from '../queue.module';
export declare class EmailQueueProcessor extends WorkerHost {
    private readonly logger;
    process(job: Job<EmailJobData, any, string>): Promise<any>;
    private processWelcomeEmail;
    private processLeadNotification;
    private processOpportunityUpdate;
    private processBulkEmail;
    private renderEmailTemplate;
    private sendEmail;
    onCompleted(job: Job<EmailJobData>, result: any): void;
    onFailed(job: Job<EmailJobData>, error: Error): void;
    onProgress(job: Job<EmailJobData>, progress: number | object): void;
    onError(error: Error): void;
    onStalled(jobId: string): void;
}
