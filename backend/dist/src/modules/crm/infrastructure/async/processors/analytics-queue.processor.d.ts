import { WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { AnalyticsJobData } from '@/modules/crm/infrastructure/async/queue.module';
import { LeadScoringService } from '@/modules/crm/domain/services/lead-scoring.service';
import { TensorFlowScoringService } from '@/modules/crm/infrastructure/ml/tensorflow-scoring.service';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
export declare class AnalyticsQueueProcessor extends WorkerHost {
    private readonly leadScoringService;
    private readonly tensorFlowScoringService;
    private readonly leadRepository;
    private readonly logger;
    constructor(leadScoringService: LeadScoringService, tensorFlowScoringService: TensorFlowScoringService, leadRepository: ILeadRepository);
    process(job: Job<AnalyticsJobData, any, string>): Promise<any>;
    private processLeadScoring;
    private processPipelineMetrics;
    private processReportGeneration;
    private processAnalyticsSync;
    private getCurrentPipelineMetrics;
    private updateMetricsForEvent;
    private storePipelineMetrics;
    private generateLeadScoringSummary;
    private generatePipelinePerformanceReport;
    private generateConversionAnalysisReport;
    private formatReport;
    private syncLeadScores;
    private syncPipelineMetrics;
    private syncConversionData;
    onCompleted(job: Job<AnalyticsJobData>, result: any): void;
    onFailed(job: Job<AnalyticsJobData>, error: Error): void;
    onProgress(job: Job<AnalyticsJobData>, progress: number | object): void;
}
