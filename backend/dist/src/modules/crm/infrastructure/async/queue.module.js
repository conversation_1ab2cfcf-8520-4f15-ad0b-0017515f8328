"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobPriority = exports.JOB_TYPES = exports.QueueModule = exports.QUEUE_NAMES = void 0;
const common_1 = require("@nestjs/common");
const bullmq_1 = require("@nestjs/bullmq");
const config_1 = require("@nestjs/config");
const email_queue_processor_1 = require("./processors/email-queue.processor");
const notification_queue_processor_1 = require("./processors/notification-queue.processor");
const analytics_queue_processor_1 = require("./processors/analytics-queue.processor");
const integration_queue_processor_1 = require("./processors/integration-queue.processor");
const workflow_queue_processor_1 = require("./processors/workflow-queue.processor");
const email_queue_producer_1 = require("./producers/email-queue.producer");
const notification_queue_producer_1 = require("./producers/notification-queue.producer");
const analytics_queue_producer_1 = require("./producers/analytics-queue.producer");
const integration_queue_producer_1 = require("./producers/integration-queue.producer");
const workflow_queue_producer_1 = require("./producers/workflow-queue.producer");
exports.QUEUE_NAMES = {
    EMAIL: 'crm-email-queue',
    NOTIFICATION: 'crm-notification-queue',
    ANALYTICS: 'crm-analytics-queue',
    INTEGRATION: 'crm-integration-queue',
    WORKFLOW: 'crm-workflow-queue',
};
let QueueModule = class QueueModule {
};
exports.QueueModule = QueueModule;
exports.QueueModule = QueueModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            bullmq_1.BullModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    connection: {
                        host: configService.get('REDIS_HOST', 'localhost'),
                        port: configService.get('REDIS_PORT', 6379),
                        password: configService.get('REDIS_PASSWORD'),
                        db: configService.get('REDIS_DB', 0),
                        maxRetriesPerRequest: 3,
                        retryDelayOnFailover: 100,
                        enableReadyCheck: false,
                        maxRetriesPerRequest: null,
                    },
                    defaultJobOptions: {
                        removeOnComplete: 100,
                        removeOnFail: 50,
                        attempts: 3,
                        backoff: {
                            type: 'exponential',
                            delay: 2000,
                        },
                    },
                }),
                inject: [config_1.ConfigService],
            }),
            bullmq_1.BullModule.registerQueue({
                name: exports.QUEUE_NAMES.EMAIL,
                defaultJobOptions: {
                    removeOnComplete: 50,
                    removeOnFail: 25,
                    attempts: 5,
                    backoff: {
                        type: 'exponential',
                        delay: 3000,
                    },
                },
            }, {
                name: exports.QUEUE_NAMES.NOTIFICATION,
                defaultJobOptions: {
                    removeOnComplete: 100,
                    removeOnFail: 50,
                    attempts: 3,
                    backoff: {
                        type: 'fixed',
                        delay: 1000,
                    },
                },
            }, {
                name: exports.QUEUE_NAMES.ANALYTICS,
                defaultJobOptions: {
                    removeOnComplete: 200,
                    removeOnFail: 100,
                    attempts: 2,
                    backoff: {
                        type: 'exponential',
                        delay: 5000,
                    },
                },
            }, {
                name: exports.QUEUE_NAMES.INTEGRATION,
                defaultJobOptions: {
                    removeOnComplete: 50,
                    removeOnFail: 25,
                    attempts: 5,
                    backoff: {
                        type: 'exponential',
                        delay: 10000,
                    },
                },
            }, {
                name: exports.QUEUE_NAMES.WORKFLOW,
                defaultJobOptions: {
                    removeOnComplete: 100,
                    removeOnFail: 50,
                    attempts: 3,
                    backoff: {
                        type: 'exponential',
                        delay: 2000,
                    },
                },
            }),
        ],
        providers: [
            email_queue_processor_1.EmailQueueProcessor,
            notification_queue_processor_1.NotificationQueueProcessor,
            analytics_queue_processor_1.AnalyticsQueueProcessor,
            integration_queue_processor_1.IntegrationQueueProcessor,
            workflow_queue_processor_1.WorkflowQueueProcessor,
            email_queue_producer_1.EmailQueueProducer,
            notification_queue_producer_1.NotificationQueueProducer,
            analytics_queue_producer_1.AnalyticsQueueProducer,
            integration_queue_producer_1.IntegrationQueueProducer,
            workflow_queue_producer_1.WorkflowQueueProducer,
        ],
        exports: [
            email_queue_producer_1.EmailQueueProducer,
            notification_queue_producer_1.NotificationQueueProducer,
            analytics_queue_producer_1.AnalyticsQueueProducer,
            integration_queue_producer_1.IntegrationQueueProducer,
            workflow_queue_producer_1.WorkflowQueueProducer,
        ],
    })
], QueueModule);
exports.JOB_TYPES = {
    SEND_WELCOME_EMAIL: 'send-welcome-email',
    SEND_LEAD_NOTIFICATION: 'send-lead-notification',
    SEND_OPPORTUNITY_UPDATE: 'send-opportunity-update',
    SEND_BULK_EMAIL: 'send-bulk-email',
    PUSH_NOTIFICATION: 'push-notification',
    SMS_NOTIFICATION: 'sms-notification',
    IN_APP_NOTIFICATION: 'in-app-notification',
    SLACK_NOTIFICATION: 'slack-notification',
    CALCULATE_LEAD_SCORE: 'calculate-lead-score',
    UPDATE_PIPELINE_METRICS: 'update-pipeline-metrics',
    GENERATE_REPORT: 'generate-report',
    SYNC_ANALYTICS_DATA: 'sync-analytics-data',
    SYNC_TO_ODOO: 'sync-to-odoo',
    SYNC_FROM_ODOO: 'sync-from-odoo',
    WEBHOOK_DELIVERY: 'webhook-delivery',
    EXTERNAL_API_CALL: 'external-api-call',
    LEAD_NURTURING_STEP: 'lead-nurturing-step',
    OPPORTUNITY_FOLLOW_UP: 'opportunity-follow-up',
    AUTO_ASSIGNMENT: 'auto-assignment',
    DATA_ENRICHMENT: 'data-enrichment',
};
var JobPriority;
(function (JobPriority) {
    JobPriority[JobPriority["LOW"] = 1] = "LOW";
    JobPriority[JobPriority["NORMAL"] = 2] = "NORMAL";
    JobPriority[JobPriority["HIGH"] = 3] = "HIGH";
    JobPriority[JobPriority["CRITICAL"] = 4] = "CRITICAL";
})(JobPriority || (exports.JobPriority = JobPriority = {}));
//# sourceMappingURL=queue.module.js.map