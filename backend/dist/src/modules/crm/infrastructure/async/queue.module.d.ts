export declare const QUEUE_NAMES: {
    readonly EMAIL: "crm-email-queue";
    readonly NOTIFICATION: "crm-notification-queue";
    readonly ANALYTICS: "crm-analytics-queue";
    readonly INTEGRATION: "crm-integration-queue";
    readonly WORKFLOW: "crm-workflow-queue";
};
export declare class QueueModule {
}
export declare const JOB_TYPES: {
    readonly SEND_WELCOME_EMAIL: "send-welcome-email";
    readonly SEND_LEAD_NOTIFICATION: "send-lead-notification";
    readonly SEND_OPPORTUNITY_UPDATE: "send-opportunity-update";
    readonly SEND_BULK_EMAIL: "send-bulk-email";
    readonly PUSH_NOTIFICATION: "push-notification";
    readonly SMS_NOTIFICATION: "sms-notification";
    readonly IN_APP_NOTIFICATION: "in-app-notification";
    readonly SLACK_NOTIFICATION: "slack-notification";
    readonly CALCULATE_LEAD_SCORE: "calculate-lead-score";
    readonly UPDATE_PIPELINE_METRICS: "update-pipeline-metrics";
    readonly GENERATE_REPORT: "generate-report";
    readonly SYNC_ANALYTICS_DATA: "sync-analytics-data";
    readonly SYNC_TO_ODOO: "sync-to-odoo";
    readonly SYNC_FROM_ODOO: "sync-from-odoo";
    readonly WEBHOOK_DELIVERY: "webhook-delivery";
    readonly EXTERNAL_API_CALL: "external-api-call";
    readonly LEAD_NURTURING_STEP: "lead-nurturing-step";
    readonly OPPORTUNITY_FOLLOW_UP: "opportunity-follow-up";
    readonly AUTO_ASSIGNMENT: "auto-assignment";
    readonly DATA_ENRICHMENT: "data-enrichment";
};
export declare enum JobPriority {
    LOW = 1,
    NORMAL = 2,
    HIGH = 3,
    CRITICAL = 4
}
export interface BaseJobOptions {
    priority?: JobPriority;
    delay?: number;
    repeat?: {
        pattern?: string;
        every?: number;
        limit?: number;
    };
    attempts?: number;
    backoff?: {
        type: 'fixed' | 'exponential';
        delay: number;
    };
    removeOnComplete?: number | boolean;
    removeOnFail?: number | boolean;
}
export interface EmailJobData {
    to: string | string[];
    subject: string;
    template: string;
    data: Record<string, any>;
    from?: string;
    cc?: string[];
    bcc?: string[];
    attachments?: Array<{
        filename: string;
        content: Buffer | string;
        contentType?: string;
    }>;
}
export interface NotificationJobData {
    userId: string | string[];
    type: 'push' | 'sms' | 'in-app' | 'slack';
    title: string;
    message: string;
    data?: Record<string, any>;
    channels?: string[];
}
export interface AnalyticsJobData {
    type: 'lead-score' | 'pipeline-metrics' | 'report' | 'sync';
    entityId?: string;
    entityType?: string;
    parameters?: Record<string, any>;
    reportType?: string;
    dateRange?: {
        from: Date;
        to: Date;
    };
}
export interface IntegrationJobData {
    type: 'sync-to-odoo' | 'sync-from-odoo' | 'webhook' | 'api-call';
    entityId?: string;
    entityType?: string;
    endpoint?: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    payload?: Record<string, any>;
    headers?: Record<string, string>;
    retryConfig?: {
        maxRetries: number;
        backoffStrategy: 'linear' | 'exponential';
    };
}
export interface WorkflowJobData {
    type: 'nurturing' | 'follow-up' | 'assignment' | 'enrichment';
    leadId?: number;
    opportunityId?: number;
    userId?: number;
    teamId?: number;
    stepId?: string;
    workflowId?: string;
    parameters?: Record<string, any>;
}
