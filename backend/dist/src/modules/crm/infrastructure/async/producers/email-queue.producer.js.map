{"version": 3, "file": "email-queue.producer.js", "sourceRoot": "", "sources": ["../../../../../../../src/modules/crm/infrastructure/async/producers/email-queue.producer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA6C;AAC7C,mCAA+B;AAC/B,kDAAoG;AAO7F,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAIsB;IAHlC,MAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;IAE9D,YACmD,UAAiB;QAAjB,eAAU,GAAV,UAAU,CAAO;IACjE,CAAC;IAKJ,KAAK,CAAC,gBAAgB,CACpB,QAKC,EACD,UAA0B,EAAE;QAE5B,MAAM,OAAO,GAAiB;YAC5B,EAAE,EAAE,QAAQ,CAAC,KAAK;YAClB,OAAO,EAAE,2BAA2B;YACpC,QAAQ,EAAE,cAAc;YACxB,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,MAAM,EAAE,QAAQ,CAAC,MAAM;aACxB;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,WAAW,CACpB,wBAAS,CAAC,kBAAkB,EAC5B,OAAO,EACP;YACE,QAAQ,EAAE,0BAAW,CAAC,IAAI;YAC1B,GAAG,OAAO;SACX,CACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;IACtE,CAAC;IAKD,KAAK,CAAC,8BAA8B,CAClC,YAGC,EACD,QAKC,EACD,UAA0B,EAAE;QAE5B,MAAM,OAAO,GAAiB;YAC5B,EAAE,EAAE,YAAY,CAAC,KAAK;YACtB,OAAO,EAAE,sBAAsB,QAAQ,CAAC,IAAI,EAAE;YAC9C,QAAQ,EAAE,iBAAiB;YAC3B,IAAI,EAAE;gBACJ,YAAY,EAAE,YAAY,CAAC,IAAI;gBAC/B,MAAM,EAAE,QAAQ,CAAC,EAAE;gBACnB,QAAQ,EAAE,QAAQ,CAAC,IAAI;gBACvB,WAAW,EAAE,QAAQ,CAAC,OAAO;gBAC7B,YAAY,EAAE,QAAQ,CAAC,QAAQ;gBAC/B,YAAY,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,UAAU,QAAQ,CAAC,EAAE,EAAE;aACjE;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,WAAW,CACpB,wBAAS,CAAC,sBAAsB,EAChC,OAAO,EACP;YACE,QAAQ,EAAE,0BAAW,CAAC,IAAI;YAC1B,GAAG,OAAO;SACX,CACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4CAA4C,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;IACpF,CAAC;IAKD,KAAK,CAAC,iCAAiC,CACrC,aAGC,EACD,eAMC,EACD,UAA4D,EAC5D,UAA0B,EAAE;QAE5B,MAAM,OAAO,GAAiB;YAC5B,EAAE,EAAE,aAAa,CAAC,KAAK;YACvB,OAAO,EAAE,uBAAuB,eAAe,CAAC,IAAI,EAAE;YACtD,QAAQ,EAAE,oBAAoB;YAC9B,IAAI,EAAE;gBACJ,aAAa,EAAE,aAAa,CAAC,IAAI;gBACjC,aAAa,EAAE,eAAe,CAAC,EAAE;gBACjC,eAAe,EAAE,eAAe,CAAC,IAAI;gBACrC,KAAK,EAAE,eAAe,CAAC,KAAK;gBAC5B,KAAK,EAAE,eAAe,CAAC,KAAK;gBAC5B,WAAW,EAAE,eAAe,CAAC,WAAW;gBACxC,UAAU;gBACV,YAAY,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,kBAAkB,eAAe,CAAC,EAAE,EAAE;aAChF;SACF,CAAC;QAEF,MAAM,QAAQ,GAAG,UAAU,KAAK,KAAK,IAAI,UAAU,KAAK,MAAM;YAC5D,CAAC,CAAC,0BAAW,CAAC,QAAQ;YACtB,CAAC,CAAC,0BAAW,CAAC,MAAM,CAAC;QAEvB,MAAM,IAAI,CAAC,WAAW,CACpB,wBAAS,CAAC,uBAAuB,EACjC,OAAO,EACP;YACE,QAAQ;YACR,GAAG,OAAO;SACX,CACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+CAA+C,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC;IACxF,CAAC;IAKD,KAAK,CAAC,qBAAqB,CACzB,YAWC,EACD,UAA0B,EAAE;QAG5B,MAAM,IAAI,GAAG,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;YAC5D,MAAM,OAAO,GAAiB;gBAC5B,EAAE,EAAE,SAAS,CAAC,KAAK;gBACnB,OAAO,EAAE,YAAY,CAAC,OAAO;gBAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,IAAI,EAAE;oBACJ,GAAG,YAAY,CAAC,UAAU;oBAC1B,GAAG,SAAS,CAAC,UAAU;oBACvB,aAAa,EAAE,SAAS,CAAC,IAAI;oBAC7B,UAAU,EAAE,YAAY,CAAC,EAAE;oBAC3B,YAAY,EAAE,YAAY,CAAC,IAAI;iBAChC;aACF,CAAC;YAEF,OAAO;gBACL,IAAI,EAAE,wBAAS,CAAC,eAAe;gBAC/B,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE;oBACJ,QAAQ,EAAE,0BAAW,CAAC,GAAG;oBACzB,KAAK,EAAE,KAAK,GAAG,GAAG;oBAClB,GAAG,OAAO;iBACX;aACF,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEpC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,MAAM,8BAA8B,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;IAC1F,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAC3B,UAKC,EACD,UAA0B,EAAE;QAE5B,MAAM,OAAO,GAAiB;YAC5B,EAAE,EAAE,UAAU,CAAC,UAAU;YACzB,OAAO,EAAE,qBAAqB,UAAU,CAAC,IAAI,EAAE;YAC/C,QAAQ,EAAE,kBAAkB;YAC5B,IAAI,EAAE;gBACJ,UAAU,EAAE,UAAU,CAAC,IAAI;gBAC3B,UAAU,EAAE,UAAU,CAAC,UAAU;gBACjC,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,WAAW,CACpB,oBAAoB,UAAU,CAAC,IAAI,EAAE,EACrC,OAAO,EACP;YACE,QAAQ,EAAE,0BAAW,CAAC,GAAG;YACzB,MAAM,EAAE;gBACN,OAAO,EAAE,UAAU,CAAC,QAAQ;aAC7B;YACD,GAAG,OAAO;SACX,CACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,UAAU,CAAC,IAAI,kBAAkB,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;IACzG,CAAC;IAKO,KAAK,CAAC,WAAW,CACvB,OAAe,EACf,OAAqB,EACrB,UAA0B,EAAE;QAE5B,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE;gBAC1C,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,0BAAW,CAAC,MAAM;gBAChD,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,YAAY,EAAE,OAAO,CAAC,YAAY;aACnC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YAChE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa;QAOjB,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACtE,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;YAC5B,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;YAC3B,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;YAC9B,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;YAC3B,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;SAC7B,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,MAAM;YACvB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,SAAS,EAAE,SAAS,CAAC,MAAM;YAC3B,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,OAAO,EAAE,OAAO,CAAC,MAAM;SACxB,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IACxC,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QAC/B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;IACzC,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,YAAoB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QAC9D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;QACzE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,OAAO,CAAC,MAAM,uBAAuB,CAAC,CAAC;QAClE,OAAO,OAAO,CAAC,MAAM,CAAC;IACxB,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,YAAoB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QAC/D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;QACrE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,OAAO,CAAC,MAAM,oBAAoB,CAAC,CAAC;QAC/D,OAAO,OAAO,CAAC,MAAM,CAAC;IACxB,CAAC;CACF,CAAA;AAjTY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,oBAAW,EAAC,0BAAW,CAAC,KAAK,CAAC,CAAA;qCAA8B,cAAK;GAJzD,kBAAkB,CAiT9B"}