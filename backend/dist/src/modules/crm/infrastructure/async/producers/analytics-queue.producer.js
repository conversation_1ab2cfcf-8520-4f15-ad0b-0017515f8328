"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AnalyticsQueueProducer_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsQueueProducer = void 0;
const common_1 = require("@nestjs/common");
const bullmq_1 = require("@nestjs/bullmq");
const bullmq_2 = require("bullmq");
const queue_module_1 = require("../queue.module");
let AnalyticsQueueProducer = AnalyticsQueueProducer_1 = class AnalyticsQueueProducer {
    analyticsQueue;
    logger = new common_1.Logger(AnalyticsQueueProducer_1.name);
    constructor(analyticsQueue) {
        this.analyticsQueue = analyticsQueue;
    }
    async calculateLeadScore(data, options = {}) {
        const jobData = {
            type: 'lead-score',
            entityId: data.leadId,
            entityType: 'lead',
            parameters: {
                factors: data.factors,
                historicalData: data.historicalData,
            },
        };
        await this.addAnalyticsJob(queue_module_1.JOB_TYPES.CALCULATE_LEAD_SCORE, jobData, {
            priority: data.priority || queue_module_1.JobPriority.NORMAL,
            ...options,
        });
        this.logger.log(`Queued lead scoring for lead: ${data.leadId}`);
    }
    async updatePipelineMetrics(data, options = {}) {
        const jobData = {
            type: 'pipeline-metrics',
            entityId: data.leadId || data.opportunityId,
            entityType: data.leadId ? 'lead' : 'opportunity',
            parameters: {
                eventType: data.type,
                teamId: data.teamId,
                source: data.source,
                timestamp: data.timestamp || new Date(),
                value: data.value,
            },
        };
        await this.addAnalyticsJob(queue_module_1.JOB_TYPES.UPDATE_PIPELINE_METRICS, jobData, {
            priority: queue_module_1.JobPriority.LOW,
            ...options,
        });
        this.logger.log(`Queued pipeline metrics update for type: ${data.type}`);
    }
    async generateReport(data, options = {}) {
        const jobData = {
            type: 'report',
            reportType: data.reportType,
            parameters: {
                dateRange: data.dateRange,
                filters: data.filters,
                teamId: data.teamId,
                userId: data.userId,
                format: data.format || 'json',
            },
        };
        await this.addAnalyticsJob(queue_module_1.JOB_TYPES.GENERATE_REPORT, jobData, {
            priority: queue_module_1.JobPriority.LOW,
            ...options,
        });
        this.logger.log(`Queued report generation: ${data.reportType}`);
    }
    async syncAnalyticsData(data, options = {}) {
        const jobData = {
            type: 'sync',
            parameters: {
                syncType: data.type,
                entityIds: data.entityIds,
                dateRange: data.dateRange,
                destination: data.destination,
                batchSize: data.batchSize || 100,
            },
        };
        await this.addAnalyticsJob(queue_module_1.JOB_TYPES.SYNC_ANALYTICS_DATA, jobData, {
            priority: queue_module_1.JobPriority.LOW,
            ...options,
        });
        this.logger.log(`Queued analytics data sync for type: ${data.type}`);
    }
    async batchCalculateLeadScores(data, options = {}) {
        const batchSize = data.batchSize || 10;
        const batches = this.chunkArray(data.leadIds, batchSize);
        const jobs = batches.map((batch, index) => ({
            name: queue_module_1.JOB_TYPES.CALCULATE_LEAD_SCORE,
            data: {
                type: 'lead-score-batch',
                parameters: {
                    leadIds: batch,
                    batchIndex: index,
                    totalBatches: batches.length,
                },
            },
            opts: {
                priority: data.priority || queue_module_1.JobPriority.NORMAL,
                delay: index * 1000,
                ...options,
            },
        }));
        await this.analyticsQueue.addBulk(jobs);
        this.logger.log(`Queued ${batches.length} batch lead scoring jobs for ${data.leadIds.length} leads`);
    }
    async scheduleRecurringAnalytics(data, options = {}) {
        const jobData = {
            type: 'recurring',
            parameters: {
                taskType: data.taskType,
                ...data.parameters,
            },
        };
        await this.addAnalyticsJob(`recurring-${data.taskType}`, jobData, {
            priority: queue_module_1.JobPriority.LOW,
            repeat: {
                pattern: data.schedule,
            },
            ...options,
        });
        this.logger.log(`Scheduled recurring analytics task: ${data.taskType} with pattern: ${data.schedule}`);
    }
    async addAnalyticsJob(jobName, jobData, options = {}) {
        try {
            await this.analyticsQueue.add(jobName, jobData, {
                priority: options.priority || queue_module_1.JobPriority.NORMAL,
                delay: options.delay,
                repeat: options.repeat,
                attempts: options.attempts || 3,
                backoff: options.backoff || {
                    type: 'exponential',
                    delay: 2000,
                },
                removeOnComplete: options.removeOnComplete || 100,
                removeOnFail: options.removeOnFail || 50,
            });
        }
        catch (error) {
            this.logger.error(`Failed to add analytics job: ${jobName}`, error);
            throw error;
        }
    }
    chunkArray(array, chunkSize) {
        const chunks = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }
    async getQueueStats() {
        const [waiting, active, completed, failed, delayed] = await Promise.all([
            this.analyticsQueue.getWaiting(),
            this.analyticsQueue.getActive(),
            this.analyticsQueue.getCompleted(),
            this.analyticsQueue.getFailed(),
            this.analyticsQueue.getDelayed(),
        ]);
        return {
            waiting: waiting.length,
            active: active.length,
            completed: completed.length,
            failed: failed.length,
            delayed: delayed.length,
        };
    }
    async cleanCompletedJobs(olderThan = 24 * 60 * 60 * 1000) {
        const cleaned = await this.analyticsQueue.clean(olderThan, 100, 'completed');
        this.logger.log(`Cleaned ${cleaned.length} completed analytics jobs`);
        return cleaned.length;
    }
    async cleanFailedJobs(olderThan = 7 * 24 * 60 * 60 * 1000) {
        const cleaned = await this.analyticsQueue.clean(olderThan, 50, 'failed');
        this.logger.log(`Cleaned ${cleaned.length} failed analytics jobs`);
        return cleaned.length;
    }
};
exports.AnalyticsQueueProducer = AnalyticsQueueProducer;
exports.AnalyticsQueueProducer = AnalyticsQueueProducer = AnalyticsQueueProducer_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, bullmq_1.InjectQueue)(queue_module_1.QUEUE_NAMES.ANALYTICS)),
    __metadata("design:paramtypes", [bullmq_2.Queue])
], AnalyticsQueueProducer);
//# sourceMappingURL=analytics-queue.producer.js.map