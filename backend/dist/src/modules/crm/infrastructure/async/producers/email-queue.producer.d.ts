import { Queue } from 'bullmq';
import { BaseJobOptions } from '../queue.module';
export declare class EmailQueueProducer {
    private readonly emailQueue;
    private readonly logger;
    constructor(emailQueue: Queue);
    sendWelcomeEmail(leadData: {
        email: string;
        name: string;
        company?: string;
        source?: string;
    }, options?: BaseJobOptions): Promise<void>;
    sendLeadAssignmentNotification(assigneeData: {
        email: string;
        name: string;
    }, leadData: {
        id: number;
        name: string;
        company?: string;
        priority: string;
    }, options?: BaseJobOptions): Promise<void>;
    sendOpportunityUpdateNotification(recipientData: {
        email: string;
        name: string;
    }, opportunityData: {
        id: number;
        name: string;
        stage: string;
        value: number;
        probability: number;
    }, updateType: 'stage_change' | 'value_change' | 'won' | 'lost', options?: BaseJobOptions): Promise<void>;
    sendBulkEmailCampaign(campaignData: {
        id: string;
        name: string;
        subject: string;
        template: string;
        recipients: Array<{
            email: string;
            name: string;
            customData?: Record<string, any>;
        }>;
        globalData?: Record<string, any>;
    }, options?: BaseJobOptions): Promise<void>;
    scheduleRecurringReport(reportData: {
        type: string;
        recipients: string[];
        schedule: string;
        parameters?: Record<string, any>;
    }, options?: BaseJobOptions): Promise<void>;
    private addEmailJob;
    getQueueStats(): Promise<{
        waiting: number;
        active: number;
        completed: number;
        failed: number;
        delayed: number;
    }>;
    pauseQueue(): Promise<void>;
    resumeQueue(): Promise<void>;
    cleanCompletedJobs(olderThan?: number): Promise<number>;
    cleanFailedJobs(olderThan?: number): Promise<number>;
}
