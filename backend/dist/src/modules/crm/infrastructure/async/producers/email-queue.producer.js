"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var EmailQueueProducer_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailQueueProducer = void 0;
const common_1 = require("@nestjs/common");
const bullmq_1 = require("@nestjs/bullmq");
const bullmq_2 = require("bullmq");
const queue_module_1 = require("../queue.module");
let EmailQueueProducer = EmailQueueProducer_1 = class EmailQueueProducer {
    emailQueue;
    logger = new common_1.Logger(EmailQueueProducer_1.name);
    constructor(emailQueue) {
        this.emailQueue = emailQueue;
    }
    async sendWelcomeEmail(leadData, options = {}) {
        const jobData = {
            to: leadData.email,
            subject: 'Welcome to our CRM system',
            template: 'welcome-lead',
            data: {
                name: leadData.name,
                company: leadData.company,
                source: leadData.source,
            },
        };
        await this.addEmailJob(queue_module_1.JOB_TYPES.SEND_WELCOME_EMAIL, jobData, {
            priority: queue_module_1.JobPriority.HIGH,
            ...options,
        });
        this.logger.log(`Queued welcome email for lead: ${leadData.email}`);
    }
    async sendLeadAssignmentNotification(assigneeData, leadData, options = {}) {
        const jobData = {
            to: assigneeData.email,
            subject: `New Lead Assigned: ${leadData.name}`,
            template: 'lead-assignment',
            data: {
                assigneeName: assigneeData.name,
                leadId: leadData.id,
                leadName: leadData.name,
                leadCompany: leadData.company,
                leadPriority: leadData.priority,
                dashboardUrl: `${process.env.FRONTEND_URL}/leads/${leadData.id}`,
            },
        };
        await this.addEmailJob(queue_module_1.JOB_TYPES.SEND_LEAD_NOTIFICATION, jobData, {
            priority: queue_module_1.JobPriority.HIGH,
            ...options,
        });
        this.logger.log(`Queued lead assignment notification for: ${assigneeData.email}`);
    }
    async sendOpportunityUpdateNotification(recipientData, opportunityData, updateType, options = {}) {
        const jobData = {
            to: recipientData.email,
            subject: `Opportunity Update: ${opportunityData.name}`,
            template: 'opportunity-update',
            data: {
                recipientName: recipientData.name,
                opportunityId: opportunityData.id,
                opportunityName: opportunityData.name,
                stage: opportunityData.stage,
                value: opportunityData.value,
                probability: opportunityData.probability,
                updateType,
                dashboardUrl: `${process.env.FRONTEND_URL}/opportunities/${opportunityData.id}`,
            },
        };
        const priority = updateType === 'won' || updateType === 'lost'
            ? queue_module_1.JobPriority.CRITICAL
            : queue_module_1.JobPriority.NORMAL;
        await this.addEmailJob(queue_module_1.JOB_TYPES.SEND_OPPORTUNITY_UPDATE, jobData, {
            priority,
            ...options,
        });
        this.logger.log(`Queued opportunity update notification for: ${recipientData.email}`);
    }
    async sendBulkEmailCampaign(campaignData, options = {}) {
        const jobs = campaignData.recipients.map((recipient, index) => {
            const jobData = {
                to: recipient.email,
                subject: campaignData.subject,
                template: campaignData.template,
                data: {
                    ...campaignData.globalData,
                    ...recipient.customData,
                    recipientName: recipient.name,
                    campaignId: campaignData.id,
                    campaignName: campaignData.name,
                },
            };
            return {
                name: queue_module_1.JOB_TYPES.SEND_BULK_EMAIL,
                data: jobData,
                opts: {
                    priority: queue_module_1.JobPriority.LOW,
                    delay: index * 100,
                    ...options,
                },
            };
        });
        await this.emailQueue.addBulk(jobs);
        this.logger.log(`Queued ${jobs.length} bulk emails for campaign: ${campaignData.name}`);
    }
    async scheduleRecurringReport(reportData, options = {}) {
        const jobData = {
            to: reportData.recipients,
            subject: `Scheduled Report: ${reportData.type}`,
            template: 'scheduled-report',
            data: {
                reportType: reportData.type,
                parameters: reportData.parameters,
                generatedAt: new Date().toISOString(),
            },
        };
        await this.addEmailJob(`scheduled-report-${reportData.type}`, jobData, {
            priority: queue_module_1.JobPriority.LOW,
            repeat: {
                pattern: reportData.schedule,
            },
            ...options,
        });
        this.logger.log(`Scheduled recurring report: ${reportData.type} with pattern: ${reportData.schedule}`);
    }
    async addEmailJob(jobName, jobData, options = {}) {
        try {
            await this.emailQueue.add(jobName, jobData, {
                priority: options.priority || queue_module_1.JobPriority.NORMAL,
                delay: options.delay,
                repeat: options.repeat,
                attempts: options.attempts,
                backoff: options.backoff,
                removeOnComplete: options.removeOnComplete,
                removeOnFail: options.removeOnFail,
            });
        }
        catch (error) {
            this.logger.error(`Failed to add email job: ${jobName}`, error);
            throw error;
        }
    }
    async getQueueStats() {
        const [waiting, active, completed, failed, delayed] = await Promise.all([
            this.emailQueue.getWaiting(),
            this.emailQueue.getActive(),
            this.emailQueue.getCompleted(),
            this.emailQueue.getFailed(),
            this.emailQueue.getDelayed(),
        ]);
        return {
            waiting: waiting.length,
            active: active.length,
            completed: completed.length,
            failed: failed.length,
            delayed: delayed.length,
        };
    }
    async pauseQueue() {
        await this.emailQueue.pause();
        this.logger.log('Email queue paused');
    }
    async resumeQueue() {
        await this.emailQueue.resume();
        this.logger.log('Email queue resumed');
    }
    async cleanCompletedJobs(olderThan = 24 * 60 * 60 * 1000) {
        const cleaned = await this.emailQueue.clean(olderThan, 100, 'completed');
        this.logger.log(`Cleaned ${cleaned.length} completed email jobs`);
        return cleaned.length;
    }
    async cleanFailedJobs(olderThan = 7 * 24 * 60 * 60 * 1000) {
        const cleaned = await this.emailQueue.clean(olderThan, 50, 'failed');
        this.logger.log(`Cleaned ${cleaned.length} failed email jobs`);
        return cleaned.length;
    }
};
exports.EmailQueueProducer = EmailQueueProducer;
exports.EmailQueueProducer = EmailQueueProducer = EmailQueueProducer_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, bullmq_1.InjectQueue)(queue_module_1.QUEUE_NAMES.EMAIL)),
    __metadata("design:paramtypes", [bullmq_2.Queue])
], EmailQueueProducer);
//# sourceMappingURL=email-queue.producer.js.map