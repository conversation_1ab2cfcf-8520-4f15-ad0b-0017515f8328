"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var IntegrationQueueProducer_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegrationQueueProducer = void 0;
const common_1 = require("@nestjs/common");
let IntegrationQueueProducer = IntegrationQueueProducer_1 = class IntegrationQueueProducer {
    logger = new common_1.Logger(IntegrationQueueProducer_1.name);
    async syncToOdoo(data) {
        this.logger.log('IntegrationQueueProducer - syncToOdoo - Placeholder');
    }
    async sendWebhook(data) {
        this.logger.log('IntegrationQueueProducer - sendWebhook - Placeholder');
    }
};
exports.IntegrationQueueProducer = IntegrationQueueProducer;
exports.IntegrationQueueProducer = IntegrationQueueProducer = IntegrationQueueProducer_1 = __decorate([
    (0, common_1.Injectable)()
], IntegrationQueueProducer);
//# sourceMappingURL=integration-queue.producer.js.map