"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OdooTeamRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooTeamRepository = void 0;
const common_1 = require("@nestjs/common");
const team_entity_1 = require("../../domain/entities/team.entity");
const odoo_connection_use_case_1 = require("../../../../shared/application/use-cases/odoo-connection.use-case");
let OdooTeamRepository = OdooTeamRepository_1 = class OdooTeamRepository {
    odooConnection;
    logger = new common_1.Logger(OdooTeamRepository_1.name);
    TEAM_FIELDS = [
        'id',
        'name',
        'user_id',
        'member_ids',
        'use_leads',
        'use_opportunities',
        'assignment_domain',
        'assignment_optout',
        'assignment_max',
        'color',
        'description',
        'company_id',
        'create_date',
        'write_date',
    ];
    constructor(odooConnection) {
        this.odooConnection = odooConnection;
    }
    async save(team) {
        try {
            const odooData = this.mapTeamToOdoo(team);
            if (team.isNew()) {
                const id = await this.odooConnection.create('crm.team', odooData);
                this.logger.log(`Created new team with ID: ${id}`);
                const createdTeam = await this.findById(id);
                if (!createdTeam) {
                    throw new Error(`Failed to fetch created team with ID: ${id}`);
                }
                return createdTeam;
            }
            else {
                const success = await this.odooConnection.update('crm.team', [team.id], odooData);
                if (!success) {
                    throw new Error(`Failed to update team with ID: ${team.id}`);
                }
                const updatedTeam = await this.findById(team.id);
                if (!updatedTeam) {
                    throw new Error(`Failed to fetch updated team with ID: ${team.id}`);
                }
                return updatedTeam;
            }
        }
        catch (error) {
            this.logger.error(`Failed to save team: ${team.name}`, error);
            throw error;
        }
    }
    async findById(id) {
        try {
            const records = await this.odooConnection.searchRead('crm.team', [['id', '=', id]], { fields: this.TEAM_FIELDS });
            return records.length > 0 ? this.mapOdooToTeam(records[0]) : null;
        }
        catch (error) {
            this.logger.error(`Failed to find team by ID: ${id}`, error);
            throw error;
        }
    }
    async findAll() {
        try {
            const records = await this.odooConnection.searchRead('crm.team', [], { fields: this.TEAM_FIELDS });
            return records.map(record => this.mapOdooToTeam(record));
        }
        catch (error) {
            this.logger.error('Failed to find all teams', error);
            throw error;
        }
    }
    async findByLeader(leaderId) {
        try {
            const records = await this.odooConnection.searchRead('crm.team', [['user_id', '=', leaderId]], { fields: this.TEAM_FIELDS });
            return records.map(record => this.mapOdooToTeam(record));
        }
        catch (error) {
            this.logger.error(`Failed to find teams by leader: ${leaderId}`, error);
            throw error;
        }
    }
    async findByMember(userId) {
        try {
            const records = await this.odooConnection.searchRead('crm.team', [['member_ids', 'in', [userId]]], { fields: this.TEAM_FIELDS });
            return records.map(record => this.mapOdooToTeam(record));
        }
        catch (error) {
            this.logger.error(`Failed to find teams by member: ${userId}`, error);
            throw error;
        }
    }
    async findByUser(userId) {
        try {
            const records = await this.odooConnection.searchRead('crm.team', [
                '|',
                ['user_id', '=', userId],
                ['member_ids', 'in', [userId]]
            ], { fields: this.TEAM_FIELDS });
            return records.map(record => this.mapOdooToTeam(record));
        }
        catch (error) {
            this.logger.error(`Failed to find teams by user: ${userId}`, error);
            throw error;
        }
    }
    async findLeadTeams() {
        try {
            const records = await this.odooConnection.searchRead('crm.team', [['use_leads', '=', true]], { fields: this.TEAM_FIELDS });
            return records.map(record => this.mapOdooToTeam(record));
        }
        catch (error) {
            this.logger.error('Failed to find lead teams', error);
            throw error;
        }
    }
    async findOpportunityTeams() {
        try {
            const records = await this.odooConnection.searchRead('crm.team', [['use_opportunities', '=', true]], { fields: this.TEAM_FIELDS });
            return records.map(record => this.mapOdooToTeam(record));
        }
        catch (error) {
            this.logger.error('Failed to find opportunity teams', error);
            throw error;
        }
    }
    async findWithAutoAssignment() {
        try {
            const records = await this.odooConnection.searchRead('crm.team', [['assignment_optout', '=', false]], { fields: this.TEAM_FIELDS });
            return records.map(record => this.mapOdooToTeam(record));
        }
        catch (error) {
            this.logger.error('Failed to find teams with auto assignment', error);
            throw error;
        }
    }
    async findByCompany(companyId) {
        try {
            const records = await this.odooConnection.searchRead('crm.team', [['company_id', '=', companyId]], { fields: this.TEAM_FIELDS });
            return records.map(record => this.mapOdooToTeam(record));
        }
        catch (error) {
            this.logger.error(`Failed to find teams by company: ${companyId}`, error);
            throw error;
        }
    }
    async findMany(filters) {
        try {
            const domain = this.buildOdooDomain(filters);
            const options = {
                fields: this.TEAM_FIELDS,
                offset: filters.offset || 0,
                limit: filters.limit || 100,
            };
            const records = await this.odooConnection.searchRead('crm.team', domain, options);
            const teams = records.map(record => this.mapOdooToTeam(record));
            const totalCount = await this.odooConnection.searchRead('crm.team', domain, { fields: ['id'] });
            const analytics = this.calculateAnalytics(teams);
            return {
                teams,
                total: totalCount.length,
                analytics,
            };
        }
        catch (error) {
            this.logger.error('Failed to find teams with filters', error);
            throw error;
        }
    }
    async addMember(teamId, userId) {
        try {
            const success = await this.odooConnection.update('crm.team', [teamId], {
                member_ids: [[4, userId]],
            });
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to add member to team: ${teamId}`, error);
            throw error;
        }
    }
    async removeMember(teamId, userId) {
        try {
            const success = await this.odooConnection.update('crm.team', [teamId], {
                member_ids: [[3, userId]],
            });
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to remove member from team: ${teamId}`, error);
            throw error;
        }
    }
    async changeLeader(teamId, newLeaderId) {
        try {
            const success = await this.odooConnection.update('crm.team', [teamId], {
                user_id: newLeaderId || false,
            });
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to change leader for team: ${teamId}`, error);
            throw error;
        }
    }
    async updateConfiguration(teamId, updates) {
        try {
            const odooUpdates = this.mapTeamUpdatesToOdoo(updates);
            const success = await this.odooConnection.update('crm.team', [teamId], odooUpdates);
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to update configuration for team: ${teamId}`, error);
            throw error;
        }
    }
    async bulkAddMembers(teamId, userIds) {
        try {
            const memberUpdates = userIds.map(userId => [4, userId]);
            const success = await this.odooConnection.update('crm.team', [teamId], {
                member_ids: memberUpdates,
            });
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to bulk add members to team: ${teamId}`, error);
            throw error;
        }
    }
    async bulkRemoveMembers(teamId, userIds) {
        try {
            const memberUpdates = userIds.map(userId => [3, userId]);
            const success = await this.odooConnection.update('crm.team', [teamId], {
                member_ids: memberUpdates,
            });
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to bulk remove members from team: ${teamId}`, error);
            throw error;
        }
    }
    async transferOwnership() {
        return true;
    }
    async delete(id) {
        try {
            const success = await this.odooConnection.unlink('crm.team', [id]);
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to delete team: ${id}`, error);
            throw error;
        }
    }
    async getStatistics() {
        return {
            totalTeams: 0,
            totalMembers: 0,
            averageTeamSize: 0,
            teamsWithLeaders: 0,
            teamsWithAutoAssignment: 0,
            teamPerformance: [],
        };
    }
    async getPerformanceMetrics() {
        return {
            teamInfo: { id: 0, name: '', memberCount: 0 },
            metrics: {},
            memberPerformance: [],
            trends: {},
            comparisons: {},
        };
    }
    async getWorkloadDistribution() {
        return {
            teamCapacity: 0,
            currentLoad: 0,
            utilizationRate: 0,
            memberWorkloads: [],
            recommendations: [],
        };
    }
    async getOptimalAssignment() {
        return {
            reason: 'No assignment logic implemented',
            confidence: 0,
            alternatives: [],
        };
    }
    async canAcceptAssignments() {
        return {
            canAccept: true,
            capacity: 0,
            currentLoad: 0,
            availableSlots: 0,
        };
    }
    async getCollaborationMetrics() {
        return {
            teamCohesion: 0,
            communicationFrequency: 0,
            knowledgeSharing: 0,
            crossCollaboration: [],
            mentorshipPairs: [],
        };
    }
    async search(query) {
        try {
            const records = await this.odooConnection.searchRead('crm.team', [['name', 'ilike', query]], { fields: this.TEAM_FIELDS });
            return records.map(record => this.mapOdooToTeam(record));
        }
        catch (error) {
            this.logger.error(`Failed to search teams: ${query}`, error);
            throw error;
        }
    }
    async clone() {
        throw new Error('Clone method not implemented');
    }
    async mergeTeams() {
        throw new Error('Merge teams method not implemented');
    }
    async getHierarchy() {
        return [];
    }
    async validateConfiguration() {
        return {
            isValid: true,
            issues: [],
            suggestions: [],
        };
    }
    mapTeamToOdoo(team) {
        return {
            name: team.name,
            user_id: team.leaderId || false,
            member_ids: [[6, 0, team.memberIds]],
            use_leads: team.useLeads,
            use_opportunities: team.useOpportunities,
            assignment_domain: team.assignmentDomain,
            assignment_optout: team.assignmentOptout,
            assignment_max: team.assignmentMax,
            color: team.color,
            description: team.description,
            company_id: team.companyId || false,
        };
    }
    mapOdooToTeam(odooRecord) {
        return new team_entity_1.Team(odooRecord.id, odooRecord.name, odooRecord.user_id?.[0], odooRecord.member_ids || [], odooRecord.use_leads || true, odooRecord.use_opportunities || true, odooRecord.assignment_domain, odooRecord.assignment_optout || false, odooRecord.assignment_max || 30, odooRecord.color, odooRecord.description, odooRecord.company_id?.[0], new Date(odooRecord.create_date), new Date(odooRecord.write_date));
    }
    buildOdooDomain(filters) {
        const domain = [];
        if (filters.leaderId !== undefined) {
            domain.push(['user_id', '=', filters.leaderId]);
        }
        if (filters.memberId !== undefined) {
            domain.push(['member_ids', 'in', [filters.memberId]]);
        }
        if (filters.useLeads !== undefined) {
            domain.push(['use_leads', '=', filters.useLeads]);
        }
        if (filters.useOpportunities !== undefined) {
            domain.push(['use_opportunities', '=', filters.useOpportunities]);
        }
        if (filters.hasAutoAssignment !== undefined) {
            domain.push(['assignment_optout', '=', !filters.hasAutoAssignment]);
        }
        if (filters.companyId !== undefined) {
            domain.push(['company_id', '=', filters.companyId]);
        }
        return domain;
    }
    calculateAnalytics(teams) {
        const totalTeams = teams.length;
        const teamsWithLeaders = teams.filter(team => team.hasLeader()).length;
        const teamsWithAutoAssignment = teams.filter(team => team.hasAutoAssignment()).length;
        const totalMembers = teams.reduce((sum, team) => sum + team.getTeamSize(), 0);
        return {
            totalTeams,
            averageTeamSize: totalTeams > 0 ? totalMembers / totalTeams : 0,
            teamsWithLeaders,
            teamsWithAutoAssignment,
            teamTypeDistribution: this.calculateTeamTypeDistribution(teams),
        };
    }
    calculateTeamTypeDistribution(teams) {
        const distribution = {
            individual: 0,
            small: 0,
            medium: 0,
            large: 0,
        };
        teams.forEach(team => {
            const type = team.getTeamType();
            distribution[type]++;
        });
        return distribution;
    }
    mapTeamUpdatesToOdoo(updates) {
        const odooUpdates = {};
        if (updates.name !== undefined) {
            odooUpdates.name = updates.name;
        }
        if (updates.useLeads !== undefined) {
            odooUpdates.use_leads = updates.useLeads;
        }
        if (updates.useOpportunities !== undefined) {
            odooUpdates.use_opportunities = updates.useOpportunities;
        }
        if (updates.assignmentDomain !== undefined) {
            odooUpdates.assignment_domain = updates.assignmentDomain;
        }
        if (updates.assignmentOptout !== undefined) {
            odooUpdates.assignment_optout = updates.assignmentOptout;
        }
        if (updates.assignmentMax !== undefined) {
            odooUpdates.assignment_max = updates.assignmentMax;
        }
        if (updates.color !== undefined) {
            odooUpdates.color = updates.color;
        }
        if (updates.description !== undefined) {
            odooUpdates.description = updates.description;
        }
        return odooUpdates;
    }
};
exports.OdooTeamRepository = OdooTeamRepository;
exports.OdooTeamRepository = OdooTeamRepository = OdooTeamRepository_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [odoo_connection_use_case_1.OdooConnectionUseCase])
], OdooTeamRepository);
//# sourceMappingURL=odoo-team.repository.js.map