"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OdooLeadRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooLeadRepository = void 0;
const common_1 = require("@nestjs/common");
const lead_entity_1 = require("../../domain/entities/lead.entity");
const opportunity_entity_1 = require("../../domain/entities/opportunity.entity");
const contact_info_vo_1 = require("../../domain/value-objects/contact-info.vo");
const lead_status_vo_1 = require("../../domain/value-objects/lead-status.vo");
const lead_priority_vo_1 = require("../../domain/value-objects/lead-priority.vo");
const lead_type_vo_1 = require("../../domain/value-objects/lead-type.vo");
const odoo_connection_use_case_1 = require("../../../../shared/application/use-cases/odoo-connection.use-case");
let OdooLeadRepository = OdooLeadRepository_1 = class OdooLeadRepository {
    odooConnection;
    logger = new common_1.Logger(OdooLeadRepository_1.name);
    LEAD_FIELDS = [
        'id',
        'name',
        'email_from',
        'phone',
        'partner_name',
        'contact_name',
        'street',
        'city',
        'country_id',
        'website',
        'stage_id',
        'type',
        'priority',
        'expected_revenue',
        'probability',
        'description',
        'user_id',
        'team_id',
        'company_id',
        'partner_id',
        'date_deadline',
        'lost_reason_id',
        'campaign_id',
        'source_id',
        'medium_id',
        'tag_ids',
        'create_date',
        'write_date',
        'active',
    ];
    constructor(odooConnection) {
        this.odooConnection = odooConnection;
    }
    async save(lead) {
        try {
            const odooData = this.mapLeadToOdoo(lead);
            if (lead.isNew()) {
                const id = await this.odooConnection.create('crm.lead', odooData);
                this.logger.log(`Created new lead with ID: ${id}`);
                const createdLead = await this.findById(id);
                if (!createdLead) {
                    throw new Error(`Failed to fetch created lead with ID: ${id}`);
                }
                return createdLead;
            }
            else {
                const success = await this.odooConnection.update('crm.lead', [lead.id], odooData);
                if (!success) {
                    throw new Error(`Failed to update lead with ID: ${lead.id}`);
                }
                const updatedLead = await this.findById(lead.id);
                if (!updatedLead) {
                    throw new Error(`Failed to fetch updated lead with ID: ${lead.id}`);
                }
                return updatedLead;
            }
        }
        catch (error) {
            this.logger.error(`Failed to save lead: ${lead.name}`, error);
            throw error;
        }
    }
    async findById(id) {
        try {
            const records = await this.odooConnection.searchRead('crm.lead', [['id', '=', id]], { fields: this.LEAD_FIELDS });
            return records.length > 0 ? this.mapOdooToLead(records[0]) : null;
        }
        catch (error) {
            this.logger.error(`Failed to find lead by ID: ${id}`, error);
            throw error;
        }
    }
    async findByEmail(email) {
        try {
            const records = await this.odooConnection.searchRead('crm.lead', [['email_from', '=', email]], { fields: this.LEAD_FIELDS, limit: 1 });
            return records.length > 0 ? this.mapOdooToLead(records[0]) : null;
        }
        catch (error) {
            this.logger.error(`Failed to find lead by email: ${email}`, error);
            throw error;
        }
    }
    async findByStage(stageId) {
        try {
            const records = await this.odooConnection.searchRead('crm.lead', [['stage_id', '=', stageId]], { fields: this.LEAD_FIELDS });
            return records.map(record => this.mapOdooToLead(record));
        }
        catch (error) {
            this.logger.error(`Failed to find leads by stage: ${stageId}`, error);
            throw error;
        }
    }
    async findByTeam(teamId) {
        try {
            const records = await this.odooConnection.searchRead('crm.lead', [['team_id', '=', teamId]], { fields: this.LEAD_FIELDS });
            return records.map(record => this.mapOdooToLead(record));
        }
        catch (error) {
            this.logger.error(`Failed to find leads by team: ${teamId}`, error);
            throw error;
        }
    }
    async findByType(type) {
        try {
            const records = await this.odooConnection.searchRead('crm.lead', [['type', '=', type.value]], { fields: this.LEAD_FIELDS });
            return records.map(record => this.mapOdooToLead(record));
        }
        catch (error) {
            this.logger.error(`Failed to find leads by type: ${type.value}`, error);
            throw error;
        }
    }
    async findByPriority(priority) {
        try {
            const records = await this.odooConnection.searchRead('crm.lead', [['priority', '=', priority.value.toString()]], { fields: this.LEAD_FIELDS });
            return records.map(record => this.mapOdooToLead(record));
        }
        catch (error) {
            this.logger.error(`Failed to find leads by priority: ${priority.value}`, error);
            throw error;
        }
    }
    async findByAssignedUser(userId) {
        try {
            const records = await this.odooConnection.searchRead('crm.lead', [['user_id', '=', userId]], { fields: this.LEAD_FIELDS });
            return records.map(record => this.mapOdooToLead(record));
        }
        catch (error) {
            this.logger.error(`Failed to find leads by assigned user: ${userId}`, error);
            throw error;
        }
    }
    async findOverdue() {
        try {
            const today = new Date().toISOString().split('T')[0];
            const records = await this.odooConnection.searchRead('crm.lead', [
                ['date_deadline', '<', today],
                ['stage_id.is_won', '=', false],
                ['active', '=', true]
            ], { fields: this.LEAD_FIELDS });
            return records.map(record => this.mapOdooToLead(record));
        }
        catch (error) {
            this.logger.error('Failed to find overdue leads', error);
            throw error;
        }
    }
    async findRequiringAttention() {
        try {
            const records = await this.odooConnection.searchRead('crm.lead', [
                '|',
                ['priority', 'in', ['2', '3']],
                '&',
                ['type', '=', 'opportunity'],
                ['probability', '>=', 80]
            ], { fields: this.LEAD_FIELDS });
            return records.map(record => this.mapOdooToLead(record));
        }
        catch (error) {
            this.logger.error('Failed to find leads requiring attention', error);
            throw error;
        }
    }
    mapLeadToOdoo(lead) {
        const odooData = {
            name: lead.name,
            email_from: lead.contactInfo.email,
            phone: lead.contactInfo.phone,
            partner_name: lead.contactInfo.company,
            contact_name: lead.contactInfo.company,
            street: lead.contactInfo.address,
            city: lead.contactInfo.city,
            website: lead.contactInfo.website,
            type: lead.type.value,
            priority: lead.priority.value.toString(),
            description: lead.description,
            user_id: lead.assignedUserId,
            team_id: lead.teamId,
            company_id: lead.companyId,
            partner_id: lead.partnerId,
            stage_id: lead.stageId,
            lost_reason_id: lead.lostReasonId,
            campaign_id: lead.campaignId,
            source_id: lead.sourceId,
            medium_id: lead.mediumId,
        };
        if (lead.type.isOpportunity()) {
            odooData.expected_revenue = lead.expectedRevenue;
            odooData.probability = lead.probability;
        }
        if (lead.dateDeadline) {
            odooData.date_deadline = lead.dateDeadline.toISOString().split('T')[0];
        }
        if (lead.tags.length > 0) {
        }
        Object.keys(odooData).forEach(key => {
            if (odooData[key] === undefined) {
                delete odooData[key];
            }
        });
        return odooData;
    }
    mapOdooToLead(odooRecord) {
        const status = this.mapOdooStageToStatus(odooRecord.stage_id);
        const contactInfo = new contact_info_vo_1.ContactInfo(odooRecord.email_from, odooRecord.phone, odooRecord.partner_name || odooRecord.contact_name, odooRecord.website, odooRecord.street, odooRecord.city, odooRecord.country_id?.[1]);
        const priority = lead_priority_vo_1.LeadPriority.fromValue(parseInt(odooRecord.priority || '1'));
        const type = lead_type_vo_1.LeadType.fromValue(odooRecord.type || 'lead');
        return new lead_entity_1.Lead(odooRecord.id, odooRecord.name, contactInfo, status, 'odoo', type, priority, odooRecord.expected_revenue, odooRecord.probability, odooRecord.description, odooRecord.user_id?.[0], odooRecord.company_id?.[0], [], odooRecord.partner_id?.[0], odooRecord.stage_id?.[0], odooRecord.team_id?.[0], odooRecord.date_deadline ? new Date(odooRecord.date_deadline) : undefined, odooRecord.lost_reason_id?.[0], odooRecord.campaign_id?.[0], odooRecord.source_id?.[0], odooRecord.medium_id?.[0], new Date(odooRecord.create_date), new Date(odooRecord.write_date));
    }
    async findMany(filters) {
        try {
            const domain = this.buildOdooDomain(filters);
            const options = {
                fields: this.LEAD_FIELDS,
                offset: filters.offset || 0,
                limit: filters.limit || 100,
            };
            if (filters.sortBy) {
                const order = `${filters.sortBy} ${filters.sortOrder || 'asc'}`;
                options.order = order;
            }
            const records = await this.odooConnection.searchRead('crm.lead', domain, options);
            const leads = records.map(record => this.mapOdooToLead(record));
            const totalCount = await this.odooConnection.searchRead('crm.lead', domain, { fields: ['id'] });
            const analytics = this.calculateAnalytics(leads);
            return {
                leads,
                total: totalCount.length,
                analytics,
            };
        }
        catch (error) {
            this.logger.error('Failed to find leads with filters', error);
            throw error;
        }
    }
    async updateStatus(id, status) {
        try {
            const success = await this.odooConnection.update('crm.lead', [id], {});
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to update status for lead: ${id}`, error);
            throw error;
        }
    }
    async updatePriority(id, priority) {
        try {
            const success = await this.odooConnection.update('crm.lead', [id], {
                priority: priority.value.toString(),
            });
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to update priority for lead: ${id}`, error);
            throw error;
        }
    }
    async convertToOpportunity(leadId, partnerId, stageId) {
        try {
            const result = await this.odooConnection.execute('crm.lead', 'convert_opportunity', [leadId], {
                partner_id: partnerId,
                stage_id: stageId,
            });
            const opportunity = await this.findById(leadId);
            if (!opportunity || !opportunity.type.isOpportunity()) {
                throw new Error('Failed to convert lead to opportunity');
            }
            return opportunity_entity_1.Opportunity.fromLead(opportunity, opportunity.expectedRevenue || 0, opportunity.probability || 0);
        }
        catch (error) {
            this.logger.error(`Failed to convert lead to opportunity: ${leadId}`, error);
            throw error;
        }
    }
    async assignToUser(id, userId, teamId) {
        try {
            const updateData = { user_id: userId };
            if (teamId) {
                updateData.team_id = teamId;
            }
            const success = await this.odooConnection.update('crm.lead', [id], updateData);
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to assign lead to user: ${id}`, error);
            throw error;
        }
    }
    async assignToTeam(id, teamId) {
        try {
            const success = await this.odooConnection.update('crm.lead', [id], {
                team_id: teamId,
            });
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to assign lead to team: ${id}`, error);
            throw error;
        }
    }
    async updateRevenueForecast(id, expectedRevenue, probability) {
        try {
            const updateData = { expected_revenue: expectedRevenue };
            if (probability !== undefined) {
                updateData.probability = probability;
            }
            const success = await this.odooConnection.update('crm.lead', [id], updateData);
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to update revenue forecast for lead: ${id}`, error);
            throw error;
        }
    }
    async setDeadline(id, deadline) {
        try {
            const success = await this.odooConnection.update('crm.lead', [id], {
                date_deadline: deadline.toISOString().split('T')[0],
            });
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to set deadline for lead: ${id}`, error);
            throw error;
        }
    }
    async addTag(id, tag) {
        try {
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to add tag to lead: ${id}`, error);
            throw error;
        }
    }
    async removeTag(id, tag) {
        try {
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to remove tag from lead: ${id}`, error);
            throw error;
        }
    }
    async bulkUpdate(ids, updates) {
        try {
            const odooUpdates = this.mapBulkUpdatesToOdoo(updates);
            const success = await this.odooConnection.update('crm.lead', ids, odooUpdates);
            return success;
        }
        catch (error) {
            this.logger.error('Failed to bulk update leads', error);
            throw error;
        }
    }
    async delete(id) {
        try {
            const success = await this.odooConnection.update('crm.lead', [id], {
                active: false,
            });
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to delete lead: ${id}`, error);
            throw error;
        }
    }
    async bulkDelete(ids) {
        try {
            const success = await this.odooConnection.update('crm.lead', ids, {
                active: false,
            });
            return success;
        }
        catch (error) {
            this.logger.error('Failed to bulk delete leads', error);
            throw error;
        }
    }
    async getStatistics(filters) {
        return {
            totalLeads: 0,
            totalOpportunities: 0,
            qualifiedLeads: 0,
            convertedLeads: 0,
            averageScore: 0,
            conversionRate: 0,
            totalRevenue: 0,
            weightedRevenue: 0,
            averageDealSize: 0,
            averageSalesCycle: 0,
            winRate: 0,
            lossRate: 0,
            pipelineVelocity: 0,
            byPriority: {},
            byStage: {},
            byTeam: {},
            bySource: {},
        };
    }
    async getPipelineAnalytics(teamId) {
        return {
            stages: [],
            totalValue: 0,
            totalWeightedValue: 0,
            conversionRates: {},
            bottlenecks: [],
        };
    }
    async search(query, filters) {
        try {
            const domain = [
                '|', '|', '|',
                ['name', 'ilike', query],
                ['email_from', 'ilike', query],
                ['phone', 'ilike', query],
                ['partner_name', 'ilike', query],
            ];
            if (filters?.type) {
                domain.push(['type', '=', filters.type.value]);
            }
            if (filters?.teamId) {
                domain.push(['team_id', '=', filters.teamId]);
            }
            const records = await this.odooConnection.searchRead('crm.lead', domain, {
                fields: this.LEAD_FIELDS,
                limit: filters?.limit || 50,
            });
            return records.map(record => this.mapOdooToLead(record));
        }
        catch (error) {
            this.logger.error(`Failed to search leads: ${query}`, error);
            throw error;
        }
    }
    buildOdooDomain(filters) {
        const domain = [['active', '=', true]];
        if (filters.status) {
        }
        if (filters.type) {
            domain.push(['type', '=', filters.type.value]);
        }
        if (filters.priority) {
            domain.push(['priority', '=', filters.priority.value.toString()]);
        }
        if (filters.assignedUserId) {
            domain.push(['user_id', '=', filters.assignedUserId]);
        }
        if (filters.teamId) {
            domain.push(['team_id', '=', filters.teamId]);
        }
        if (filters.stageId) {
            domain.push(['stage_id', '=', filters.stageId]);
        }
        if (filters.partnerId) {
            domain.push(['partner_id', '=', filters.partnerId]);
        }
        if (filters.minRevenue) {
            domain.push(['expected_revenue', '>=', filters.minRevenue]);
        }
        if (filters.maxRevenue) {
            domain.push(['expected_revenue', '<=', filters.maxRevenue]);
        }
        if (filters.minProbability) {
            domain.push(['probability', '>=', filters.minProbability]);
        }
        if (filters.maxProbability) {
            domain.push(['probability', '<=', filters.maxProbability]);
        }
        if (filters.dateFrom) {
            domain.push(['create_date', '>=', filters.dateFrom.toISOString()]);
        }
        if (filters.dateTo) {
            domain.push(['create_date', '<=', filters.dateTo.toISOString()]);
        }
        return domain;
    }
    calculateAnalytics(leads) {
        const totalLeads = leads.length;
        const qualifiedLeads = leads.filter(lead => lead.isQualified()).length;
        const opportunities = leads.filter(lead => lead.type.isOpportunity());
        return {
            averageScore: totalLeads > 0 ? leads.reduce((sum, lead) => sum + lead.calculateScore(), 0) / totalLeads : 0,
            conversionRate: totalLeads > 0 ? (qualifiedLeads / totalLeads) * 100 : 0,
            totalRevenue: opportunities.reduce((sum, opp) => sum + (opp.expectedRevenue || 0), 0),
            weightedRevenue: opportunities.reduce((sum, opp) => sum + opp.getWeightedRevenue(), 0),
            topSources: [],
            topTeams: [],
            priorityDistribution: [],
            stageDistribution: [],
        };
    }
    mapBulkUpdatesToOdoo(updates) {
        const odooUpdates = {};
        if (updates.status) {
        }
        if (updates.priority) {
            odooUpdates.priority = updates.priority.value.toString();
        }
        if (updates.assignedUserId) {
            odooUpdates.user_id = updates.assignedUserId;
        }
        if (updates.teamId) {
            odooUpdates.team_id = updates.teamId;
        }
        if (updates.stageId) {
            odooUpdates.stage_id = updates.stageId;
        }
        return odooUpdates;
    }
    mapOdooStageToStatus(stageData) {
        if (!stageData)
            return new lead_status_vo_1.LeadStatus('new');
        const stageName = stageData[1]?.toLowerCase() || 'new';
        if (stageName.includes('new') || stageName.includes('draft'))
            return new lead_status_vo_1.LeadStatus('new');
        if (stageName.includes('contact') || stageName.includes('call'))
            return new lead_status_vo_1.LeadStatus('contacted');
        if (stageName.includes('qualif'))
            return new lead_status_vo_1.LeadStatus('qualified');
        if (stageName.includes('proposal') || stageName.includes('quote'))
            return new lead_status_vo_1.LeadStatus('proposal');
        if (stageName.includes('negotiat') || stageName.includes('closing'))
            return new lead_status_vo_1.LeadStatus('negotiation');
        if (stageName.includes('won') || stageName.includes('closed'))
            return new lead_status_vo_1.LeadStatus('won');
        if (stageName.includes('lost') || stageName.includes('cancel'))
            return new lead_status_vo_1.LeadStatus('lost');
        return new lead_status_vo_1.LeadStatus('new');
    }
};
exports.OdooLeadRepository = OdooLeadRepository;
exports.OdooLeadRepository = OdooLeadRepository = OdooLeadRepository_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [odoo_connection_use_case_1.OdooConnectionUseCase])
], OdooLeadRepository);
//# sourceMappingURL=odoo-lead.repository.js.map