"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OdooStageRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooStageRepository = void 0;
const common_1 = require("@nestjs/common");
const stage_entity_1 = require("../../domain/entities/stage.entity");
const odoo_connection_use_case_1 = require("../../../../shared/application/use-cases/odoo-connection.use-case");
let OdooStageRepository = OdooStageRepository_1 = class OdooStageRepository {
    odooConnection;
    logger = new common_1.Logger(OdooStageRepository_1.name);
    STAGE_FIELDS = [
        'id',
        'name',
        'sequence',
        'is_won',
        'fold',
        'team_id',
        'requirements',
        'on_change',
        'probability',
        'create_date',
        'write_date',
    ];
    constructor(odooConnection) {
        this.odooConnection = odooConnection;
    }
    async save(stage) {
        try {
            const odooData = this.mapStageToOdoo(stage);
            if (stage.isNew()) {
                const id = await this.odooConnection.create('crm.stage', odooData);
                this.logger.log(`Created new stage with ID: ${id}`);
                const createdStage = await this.findById(id);
                if (!createdStage) {
                    throw new Error(`Failed to fetch created stage with ID: ${id}`);
                }
                return createdStage;
            }
            else {
                const success = await this.odooConnection.update('crm.stage', [stage.id], odooData);
                if (!success) {
                    throw new Error(`Failed to update stage with ID: ${stage.id}`);
                }
                const updatedStage = await this.findById(stage.id);
                if (!updatedStage) {
                    throw new Error(`Failed to fetch updated stage with ID: ${stage.id}`);
                }
                return updatedStage;
            }
        }
        catch (error) {
            this.logger.error(`Failed to save stage: ${stage.name}`, error);
            throw error;
        }
    }
    async findById(id) {
        try {
            const records = await this.odooConnection.searchRead('crm.stage', [['id', '=', id]], { fields: this.STAGE_FIELDS });
            return records.length > 0 ? this.mapOdooToStage(records[0]) : null;
        }
        catch (error) {
            this.logger.error(`Failed to find stage by ID: ${id}`, error);
            throw error;
        }
    }
    async findAll() {
        try {
            const records = await this.odooConnection.searchRead('crm.stage', [], { fields: this.STAGE_FIELDS, order: 'sequence asc' });
            return records.map(record => this.mapOdooToStage(record));
        }
        catch (error) {
            this.logger.error('Failed to find all stages', error);
            throw error;
        }
    }
    async findByTeam(teamId) {
        try {
            const records = await this.odooConnection.searchRead('crm.stage', [['team_id', '=', teamId]], { fields: this.STAGE_FIELDS, order: 'sequence asc' });
            return records.map(record => this.mapOdooToStage(record));
        }
        catch (error) {
            this.logger.error(`Failed to find stages by team: ${teamId}`, error);
            throw error;
        }
    }
    async findGlobal() {
        try {
            const records = await this.odooConnection.searchRead('crm.stage', [['team_id', '=', false]], { fields: this.STAGE_FIELDS, order: 'sequence asc' });
            return records.map(record => this.mapOdooToStage(record));
        }
        catch (error) {
            this.logger.error('Failed to find global stages', error);
            throw error;
        }
    }
    async findActive(teamId) {
        try {
            const domain = [['fold', '=', false]];
            if (teamId) {
                domain.push(['team_id', '=', teamId]);
            }
            const records = await this.odooConnection.searchRead('crm.stage', domain, { fields: this.STAGE_FIELDS, order: 'sequence asc' });
            return records.map(record => this.mapOdooToStage(record));
        }
        catch (error) {
            this.logger.error('Failed to find active stages', error);
            throw error;
        }
    }
    async findTerminal(teamId) {
        try {
            const domain = [['is_won', '=', true]];
            if (teamId) {
                domain.push(['team_id', '=', teamId]);
            }
            const records = await this.odooConnection.searchRead('crm.stage', domain, { fields: this.STAGE_FIELDS, order: 'sequence asc' });
            return records.map(record => this.mapOdooToStage(record));
        }
        catch (error) {
            this.logger.error('Failed to find terminal stages', error);
            throw error;
        }
    }
    async findWonStages(teamId) {
        try {
            const domain = [['is_won', '=', true]];
            if (teamId) {
                domain.push(['team_id', '=', teamId]);
            }
            const records = await this.odooConnection.searchRead('crm.stage', domain, { fields: this.STAGE_FIELDS, order: 'sequence asc' });
            return records.map(record => this.mapOdooToStage(record));
        }
        catch (error) {
            this.logger.error('Failed to find won stages', error);
            throw error;
        }
    }
    async findLostStages(teamId) {
        try {
            const domain = [['fold', '=', true], ['is_won', '=', false]];
            if (teamId) {
                domain.push(['team_id', '=', teamId]);
            }
            const records = await this.odooConnection.searchRead('crm.stage', domain, { fields: this.STAGE_FIELDS, order: 'sequence asc' });
            return records.map(record => this.mapOdooToStage(record));
        }
        catch (error) {
            this.logger.error('Failed to find lost stages', error);
            throw error;
        }
    }
    async findOrderedBySequence(teamId) {
        try {
            const domain = teamId ? [['team_id', '=', teamId]] : [];
            const records = await this.odooConnection.searchRead('crm.stage', domain, { fields: this.STAGE_FIELDS, order: 'sequence asc' });
            return records.map(record => this.mapOdooToStage(record));
        }
        catch (error) {
            this.logger.error('Failed to find stages ordered by sequence', error);
            throw error;
        }
    }
    async findMany(filters) {
        try {
            const domain = this.buildOdooDomain(filters);
            const options = {
                fields: this.STAGE_FIELDS,
                offset: filters.offset || 0,
                limit: filters.limit || 100,
                order: 'sequence asc',
            };
            const records = await this.odooConnection.searchRead('crm.stage', domain, options);
            const stages = records.map(record => this.mapOdooToStage(record));
            const totalCount = await this.odooConnection.searchRead('crm.stage', domain, { fields: ['id'] });
            return {
                stages,
                total: totalCount.length,
            };
        }
        catch (error) {
            this.logger.error('Failed to find stages with filters', error);
            throw error;
        }
    }
    async updateSequence(id, newSequence) {
        try {
            const success = await this.odooConnection.update('crm.stage', [id], {
                sequence: newSequence,
            });
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to update sequence for stage: ${id}`, error);
            throw error;
        }
    }
    async updateStage(id, updates) {
        try {
            const odooUpdates = this.mapStageUpdatesToOdoo(updates);
            const success = await this.odooConnection.update('crm.stage', [id], odooUpdates);
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to update stage: ${id}`, error);
            throw error;
        }
    }
    async reorderStages(stageSequences) {
        try {
            for (const { id, sequence } of stageSequences) {
                await this.updateSequence(id, sequence);
            }
            return true;
        }
        catch (error) {
            this.logger.error('Failed to reorder stages', error);
            throw error;
        }
    }
    async toggleFold(id) {
        try {
            const stage = await this.findById(id);
            if (!stage) {
                throw new Error(`Stage not found: ${id}`);
            }
            const success = await this.odooConnection.update('crm.stage', [id], {
                fold: !stage.fold,
            });
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to toggle fold for stage: ${id}`, error);
            throw error;
        }
    }
    async delete(id) {
        try {
            const success = await this.odooConnection.unlink('crm.stage', [id]);
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to delete stage: ${id}`, error);
            throw error;
        }
    }
    async getStatistics() {
        return {
            totalStages: 0,
            activeStages: 0,
            foldedStages: 0,
            wonStages: 0,
            lostStages: 0,
            averageLeadsPerStage: 0,
            averageRevenuePerStage: 0,
            stageUtilization: [],
        };
    }
    async getPerformanceMetrics() {
        return {
            stageMetrics: [],
            bottlenecks: [],
            recommendations: [],
        };
    }
    async getNextStage() {
        return null;
    }
    async getPreviousStage() {
        return null;
    }
    async canDelete() {
        return true;
    }
    async getProgressionPath() {
        return [];
    }
    async validateSequences() {
        return {
            isValid: true,
            issues: [],
            suggestions: [],
        };
    }
    async search(query) {
        try {
            const records = await this.odooConnection.searchRead('crm.stage', [['name', 'ilike', query]], { fields: this.STAGE_FIELDS, order: 'sequence asc' });
            return records.map(record => this.mapOdooToStage(record));
        }
        catch (error) {
            this.logger.error(`Failed to search stages: ${query}`, error);
            throw error;
        }
    }
    async clone() {
        throw new Error('Clone method not implemented');
    }
    async getTemplates() {
        return [];
    }
    async applyTemplate() {
        return [];
    }
    mapStageToOdoo(stage) {
        return {
            name: stage.name,
            sequence: stage.sequence,
            is_won: stage.isWonStage,
            fold: stage.fold,
            team_id: stage.teamId || false,
            requirements: stage.requirements,
            on_change: stage.onChange,
        };
    }
    mapOdooToStage(odooRecord) {
        return new stage_entity_1.Stage(odooRecord.id, odooRecord.name, odooRecord.sequence, odooRecord.is_won || false, false, odooRecord.team_id?.[0], odooRecord.requirements, odooRecord.fold || false, undefined, undefined, odooRecord.on_change || false, new Date(odooRecord.create_date), new Date(odooRecord.write_date));
    }
    buildOdooDomain(filters) {
        const domain = [];
        if (filters.teamId !== undefined) {
            domain.push(['team_id', '=', filters.teamId || false]);
        }
        if (filters.isWonStage !== undefined) {
            domain.push(['is_won', '=', filters.isWonStage]);
        }
        if (filters.fold !== undefined) {
            domain.push(['fold', '=', filters.fold]);
        }
        if (filters.minSequence !== undefined) {
            domain.push(['sequence', '>=', filters.minSequence]);
        }
        if (filters.maxSequence !== undefined) {
            domain.push(['sequence', '<=', filters.maxSequence]);
        }
        return domain;
    }
    mapStageUpdatesToOdoo(updates) {
        const odooUpdates = {};
        if (updates.name !== undefined) {
            odooUpdates.name = updates.name;
        }
        if (updates.sequence !== undefined) {
            odooUpdates.sequence = updates.sequence;
        }
        if (updates.isWonStage !== undefined) {
            odooUpdates.is_won = updates.isWonStage;
        }
        if (updates.fold !== undefined) {
            odooUpdates.fold = updates.fold;
        }
        if (updates.requirements !== undefined) {
            odooUpdates.requirements = updates.requirements;
        }
        return odooUpdates;
    }
};
exports.OdooStageRepository = OdooStageRepository;
exports.OdooStageRepository = OdooStageRepository = OdooStageRepository_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [odoo_connection_use_case_1.OdooConnectionUseCase])
], OdooStageRepository);
//# sourceMappingURL=odoo-stage.repository.js.map