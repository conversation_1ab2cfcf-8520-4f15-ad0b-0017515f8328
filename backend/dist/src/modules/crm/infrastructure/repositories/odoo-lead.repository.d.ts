import { ILeadRepository } from '../../domain/repositories/lead.repository';
import { Lead } from '../../domain/entities/lead.entity';
import { Opportunity } from '../../domain/entities/opportunity.entity';
import { LeadStatus } from '../../domain/value-objects/lead-status.vo';
import { LeadPriority } from '../../domain/value-objects/lead-priority.vo';
import { LeadType } from '../../domain/value-objects/lead-type.vo';
import { OdooConnectionUseCase } from '../../../../shared/application/use-cases/odoo-connection.use-case';
export declare class OdooLeadRepository implements ILeadRepository {
    private readonly odooConnection;
    private readonly logger;
    private readonly LEAD_FIELDS;
    constructor(odooConnection: OdooConnectionUseCase);
    save(lead: Lead): Promise<Lead>;
    findById(id: number): Promise<Lead | null>;
    findByEmail(email: string): Promise<Lead | null>;
    findByStage(stageId: number): Promise<Lead[]>;
    findByTeam(teamId: number): Promise<Lead[]>;
    findByType(type: LeadType): Promise<Lead[]>;
    findByPriority(priority: LeadPriority): Promise<Lead[]>;
    findByAssignedUser(userId: number): Promise<Lead[]>;
    findOverdue(): Promise<Lead[]>;
    findRequiringAttention(): Promise<Lead[]>;
    private mapLeadToOdoo;
    private mapOdooToLead;
    findMany(filters: any): Promise<any>;
    updateStatus(id: number, status: LeadStatus): Promise<boolean>;
    updatePriority(id: number, priority: LeadPriority): Promise<boolean>;
    convertToOpportunity(leadId: number, partnerId?: number, stageId?: number): Promise<Opportunity>;
    assignToUser(id: number, userId: number, teamId?: number): Promise<boolean>;
    assignToTeam(id: number, teamId: number): Promise<boolean>;
    updateRevenueForecast(id: number, expectedRevenue: number, probability?: number): Promise<boolean>;
    setDeadline(id: number, deadline: Date): Promise<boolean>;
    addTag(id: number, tag: string): Promise<boolean>;
    removeTag(id: number, tag: string): Promise<boolean>;
    bulkUpdate(ids: number[], updates: any): Promise<boolean>;
    delete(id: number): Promise<boolean>;
    bulkDelete(ids: number[]): Promise<boolean>;
    getStatistics(filters?: any): Promise<any>;
    getPipelineAnalytics(teamId?: number): Promise<any>;
    search(query: string, filters?: any): Promise<Lead[]>;
    private buildOdooDomain;
    private calculateAnalytics;
    private mapBulkUpdatesToOdoo;
    private mapOdooStageToStatus;
}
