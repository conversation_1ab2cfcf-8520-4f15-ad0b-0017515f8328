import { IStageRepository } from '../../domain/repositories/stage.repository';
import { Stage } from '../../domain/entities/stage.entity';
import { OdooConnectionUseCase } from '../../../../shared/application/use-cases/odoo-connection.use-case';
export declare class OdooStageRepository implements IStageRepository {
    private readonly odooConnection;
    private readonly logger;
    private readonly STAGE_FIELDS;
    constructor(odooConnection: OdooConnectionUseCase);
    save(stage: Stage): Promise<Stage>;
    findById(id: number): Promise<Stage | null>;
    findAll(): Promise<Stage[]>;
    findByTeam(teamId: number): Promise<Stage[]>;
    findGlobal(): Promise<Stage[]>;
    findActive(teamId?: number): Promise<Stage[]>;
    findTerminal(teamId?: number): Promise<Stage[]>;
    findWonStages(teamId?: number): Promise<Stage[]>;
    findLostStages(teamId?: number): Promise<Stage[]>;
    findOrderedBySequence(teamId?: number): Promise<Stage[]>;
    findMany(filters: any): Promise<any>;
    updateSequence(id: number, newSequence: number): Promise<boolean>;
    updateStage(id: number, updates: any): Promise<boolean>;
    reorderStages(stageSequences: Array<{
        id: number;
        sequence: number;
    }>): Promise<boolean>;
    toggleFold(id: number): Promise<boolean>;
    delete(id: number): Promise<boolean>;
    getStatistics(): Promise<any>;
    getPerformanceMetrics(): Promise<any>;
    getNextStage(): Promise<Stage | null>;
    getPreviousStage(): Promise<Stage | null>;
    canDelete(): Promise<boolean>;
    getProgressionPath(): Promise<any[]>;
    validateSequences(): Promise<any>;
    search(query: string): Promise<Stage[]>;
    clone(): Promise<Stage>;
    getTemplates(): Promise<any[]>;
    applyTemplate(): Promise<Stage[]>;
    private mapStageToOdoo;
    private mapOdooToStage;
    private buildOdooDomain;
    private mapStageUpdatesToOdoo;
}
