import { ITeamRepository } from '../../domain/repositories/team.repository';
import { Team } from '../../domain/entities/team.entity';
import { OdooConnectionUseCase } from '../../../../shared/application/use-cases/odoo-connection.use-case';
export declare class OdooTeamRepository implements ITeamRepository {
    private readonly odooConnection;
    private readonly logger;
    private readonly TEAM_FIELDS;
    constructor(odooConnection: OdooConnectionUseCase);
    save(team: Team): Promise<Team>;
    findById(id: number): Promise<Team | null>;
    findAll(): Promise<Team[]>;
    findByLeader(leaderId: number): Promise<Team[]>;
    findByMember(userId: number): Promise<Team[]>;
    findByUser(userId: number): Promise<Team[]>;
    findLeadTeams(): Promise<Team[]>;
    findOpportunityTeams(): Promise<Team[]>;
    findWithAutoAssignment(): Promise<Team[]>;
    findByCompany(companyId: number): Promise<Team[]>;
    findMany(filters: any): Promise<any>;
    addMember(teamId: number, userId: number): Promise<boolean>;
    removeMember(teamId: number, userId: number): Promise<boolean>;
    changeLeader(teamId: number, newLeaderId?: number): Promise<boolean>;
    updateConfiguration(teamId: number, updates: any): Promise<boolean>;
    bulkAddMembers(teamId: number, userIds: number[]): Promise<boolean>;
    bulkRemoveMembers(teamId: number, userIds: number[]): Promise<boolean>;
    transferOwnership(): Promise<boolean>;
    delete(id: number): Promise<boolean>;
    getStatistics(): Promise<any>;
    getPerformanceMetrics(): Promise<any>;
    getWorkloadDistribution(): Promise<any>;
    getOptimalAssignment(): Promise<any>;
    canAcceptAssignments(): Promise<any>;
    getCollaborationMetrics(): Promise<any>;
    search(query: string): Promise<Team[]>;
    clone(): Promise<Team>;
    mergeTeams(): Promise<boolean>;
    getHierarchy(): Promise<any[]>;
    validateConfiguration(): Promise<any>;
    private mapTeamToOdoo;
    private mapOdooToTeam;
    private buildOdooDomain;
    private calculateAnalytics;
    private calculateTeamTypeDistribution;
    private mapTeamUpdatesToOdoo;
}
