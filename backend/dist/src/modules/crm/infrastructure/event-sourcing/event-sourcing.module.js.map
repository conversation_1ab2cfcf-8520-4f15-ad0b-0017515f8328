{"version": 3, "file": "event-sourcing.module.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/infrastructure/event-sourcing/event-sourcing.module.ts"], "names": [], "mappings": ";;;;;;;;;AAoJA,sCAKC;AAKD,gCAMC;AAKD,gCAKC;AAKD,8CAMC;AAKD,oBAIC;AAKD,4BAMC;AA7MD,2CAAwC;AACxC,+CAAkD;AAClD,2CAA8C;AAG9C,4EAAuE;AAIvE,oFAA4F;AAC5F,oFAAgG;AAChG,oFAAoG;AACpG,oFAAwG;AACxG,oFAAkG;AAGlG,oEAA+D;AAC/D,gFAA2E;AAC3E,sFAAiF;AAGjF,sEAAkE;AAClE,kEAA8D;AAG9D,0DAAsD;AAuD/C,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;CAAG,CAAA;AAAtB,kDAAmB;8BAAnB,mBAAmB;IAjD/B,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,qBAAY;YAGZ,yBAAc,CAAC,UAAU,CAAC;gBACxB,EAAE,IAAI,EAAE,iCAAW,CAAC,IAAI,EAAE,MAAM,EAAE,uCAAiB,EAAE;gBACrD,EAAE,IAAI,EAAE,mCAAa,CAAC,IAAI,EAAE,MAAM,EAAE,yCAAmB,EAAE;gBACzD,EAAE,IAAI,EAAE,qCAAe,CAAC,IAAI,EAAE,MAAM,EAAE,2CAAqB,EAAE;gBAC7D,EAAE,IAAI,EAAE,uCAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,6CAAuB,EAAE;gBACjE,EAAE,IAAI,EAAE,oCAAc,CAAC,IAAI,EAAE,MAAM,EAAE,0CAAoB,EAAE;aAC5D,CAAC;SACH;QACD,SAAS,EAAE;YAET;gBACE,OAAO,EAAE,8BAAU;gBACnB,QAAQ,EAAE,uCAAiB;aAC5B;YACD,uCAAiB;YAGjB,mCAAe;YACf,+CAAqB;YACrB,qDAAwB;YAGxB,sCAAiB;YACjB,kCAAe;YAGf,0BAAW;SACZ;QACD,OAAO,EAAE;YAEP,8BAAU;YAGV,mCAAe;YACf,+CAAqB;YACrB,qDAAwB;YACxB,sCAAiB;YACjB,kCAAe;YACf,0BAAW;YAGX,uCAAiB;SAClB;KACF,CAAC;GACW,mBAAmB,CAAG;AAsCtB,QAAA,6BAA6B,GAAwB;IAChE,UAAU,EAAE;QACV,iBAAiB,EAAE,GAAG;QACtB,kBAAkB,EAAE,IAAI;QACxB,gBAAgB,EAAE,GAAG;KACtB;IACD,WAAW,EAAE;QACX,SAAS,EAAE,EAAE;QACb,UAAU,EAAE,CAAC;QACb,UAAU,EAAE,IAAI;KACjB;IACD,KAAK,EAAE;QACL,eAAe,EAAE,MAAM;QACvB,UAAU,EAAE,CAAC;QACb,mBAAmB,EAAE,KAAK;KAC3B;IACD,UAAU,EAAE;QACV,SAAS,EAAE,EAAE;QACb,aAAa,EAAE,CAAC;QAChB,UAAU,EAAE,IAAI;KACjB;CACF,CAAC;AASF,SAAgB,aAAa,CAAC,aAAqB;IACjD,OAAO,UAAU,WAAgB;QAC/B,OAAO,CAAC,cAAc,CAAC,gBAAgB,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;QACrE,OAAO,CAAC,cAAc,CAAC,gBAAgB,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;IAC9D,CAAC,CAAC;AACJ,CAAC;AAKD,SAAgB,UAAU,CAAC,SAAiB;IAC1C,OAAO,UAAU,MAAW,EAAE,WAAmB,EAAE,UAA8B;QAC/E,MAAM,cAAc,GAAG,OAAO,CAAC,WAAW,CAAC,kBAAkB,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QACzF,cAAc,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;QACzD,OAAO,CAAC,cAAc,CAAC,kBAAkB,EAAE,cAAc,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;IACjF,CAAC,CAAC;AACJ,CAAC;AAKD,SAAgB,UAAU,CAAC,cAAsB,EAAE,UAAoB;IACrE,OAAO,UAAU,WAAgB;QAC/B,OAAO,CAAC,cAAc,CAAC,iBAAiB,EAAE,cAAc,EAAE,WAAW,CAAC,CAAC;QACvE,OAAO,CAAC,cAAc,CAAC,mBAAmB,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;IACvE,CAAC,CAAC;AACJ,CAAC;AAKD,SAAgB,iBAAiB,CAAC,SAAiB;IACjD,OAAO,UAAU,MAAW,EAAE,WAAmB,EAAE,UAA8B;QAC/E,MAAM,gBAAgB,GAAG,OAAO,CAAC,WAAW,CAAC,qBAAqB,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QAC9F,gBAAgB,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;QAC3D,OAAO,CAAC,cAAc,CAAC,qBAAqB,EAAE,gBAAgB,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;IACtF,CAAC,CAAC;AACJ,CAAC;AAKD,SAAgB,IAAI,CAAC,QAAgB;IACnC,OAAO,UAAU,WAAgB;QAC/B,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;IAC7D,CAAC,CAAC;AACJ,CAAC;AAKD,SAAgB,QAAQ,CAAC,QAAgB,EAAE,UAAoB;IAC7D,OAAO,UAAU,MAAW,EAAE,WAAmB,EAAE,UAA8B;QAC/E,MAAM,aAAa,GAAG,OAAO,CAAC,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QAClF,aAAa,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;QACnE,OAAO,CAAC,cAAc,CAAC,YAAY,EAAE,aAAa,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;IAC1E,CAAC,CAAC;AACJ,CAAC"}