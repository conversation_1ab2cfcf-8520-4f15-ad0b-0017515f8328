"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_EVENT_SOURCING_CONFIG = exports.EventSourcingModule = void 0;
exports.AggregateRoot = AggregateRoot;
exports.ApplyEvent = ApplyEvent;
exports.Projection = Projection;
exports.ProjectionHandler = ProjectionHandler;
exports.Saga = Saga;
exports.SagaStep = SagaStep;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const config_1 = require("@nestjs/config");
const mongodb_event_store_1 = require("../event-store/mongodb-event-store");
const event_stream_schema_1 = require("../event-store/schemas/event-stream.schema");
const event_stream_schema_2 = require("../event-store/schemas/event-stream.schema");
const event_stream_schema_3 = require("../event-store/schemas/event-stream.schema");
const event_stream_schema_4 = require("../event-store/schemas/event-stream.schema");
const event_stream_schema_5 = require("../event-store/schemas/event-stream.schema");
const event_bus_service_1 = require("./services/event-bus.service");
const event_publisher_service_1 = require("./services/event-publisher.service");
const event_subscription_service_1 = require("./services/event-subscription.service");
const projection_service_1 = require("./services/projection.service");
const snapshot_service_1 = require("./services/snapshot.service");
const saga_service_1 = require("./services/saga.service");
let EventSourcingModule = class EventSourcingModule {
};
exports.EventSourcingModule = EventSourcingModule;
exports.EventSourcingModule = EventSourcingModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            mongoose_1.MongooseModule.forFeature([
                { name: event_stream_schema_1.EventStream.name, schema: event_stream_schema_1.EventStreamSchema },
                { name: event_stream_schema_2.EventSnapshot.name, schema: event_stream_schema_2.EventSnapshotSchema },
                { name: event_stream_schema_3.EventProjection.name, schema: event_stream_schema_3.EventProjectionSchema },
                { name: event_stream_schema_4.EventSubscription.name, schema: event_stream_schema_4.EventSubscriptionSchema },
                { name: event_stream_schema_5.EventSagaState.name, schema: event_stream_schema_5.EventSagaStateSchema },
            ]),
        ],
        providers: [
            {
                provide: domain_event_base_1.EventStore,
                useClass: mongodb_event_store_1.MongoDbEventStore,
            },
            mongodb_event_store_1.MongoDbEventStore,
            event_bus_service_1.EventBusService,
            event_publisher_service_1.EventPublisherService,
            event_subscription_service_1.EventSubscriptionService,
            projection_service_1.ProjectionService,
            snapshot_service_1.SnapshotService,
            saga_service_1.SagaService,
        ],
        exports: [
            domain_event_base_1.EventStore,
            event_bus_service_1.EventBusService,
            event_publisher_service_1.EventPublisherService,
            event_subscription_service_1.EventSubscriptionService,
            projection_service_1.ProjectionService,
            snapshot_service_1.SnapshotService,
            saga_service_1.SagaService,
            mongodb_event_store_1.MongoDbEventStore,
        ],
    })
], EventSourcingModule);
exports.DEFAULT_EVENT_SOURCING_CONFIG = {
    eventStore: {
        snapshotFrequency: 100,
        maxEventsPerStream: 1000,
        cleanupOlderThan: 365,
    },
    projections: {
        batchSize: 50,
        maxRetries: 3,
        retryDelay: 1000,
    },
    sagas: {
        timeoutDuration: 300000,
        maxRetries: 3,
        compensationTimeout: 60000,
    },
    publishing: {
        batchSize: 25,
        retryAttempts: 3,
        retryDelay: 2000,
    },
};
function AggregateRoot(aggregateType) {
    return function (constructor) {
        Reflect.defineMetadata('aggregate:type', aggregateType, constructor);
        Reflect.defineMetadata('aggregate:root', true, constructor);
    };
}
function ApplyEvent(eventType) {
    return function (target, propertyKey, descriptor) {
        const existingEvents = Reflect.getMetadata('aggregate:events', target.constructor) || [];
        existingEvents.push({ eventType, handler: propertyKey });
        Reflect.defineMetadata('aggregate:events', existingEvents, target.constructor);
    };
}
function Projection(projectionName, eventTypes) {
    return function (constructor) {
        Reflect.defineMetadata('projection:name', projectionName, constructor);
        Reflect.defineMetadata('projection:events', eventTypes, constructor);
    };
}
function ProjectionHandler(eventType) {
    return function (target, propertyKey, descriptor) {
        const existingHandlers = Reflect.getMetadata('projection:handlers', target.constructor) || [];
        existingHandlers.push({ eventType, handler: propertyKey });
        Reflect.defineMetadata('projection:handlers', existingHandlers, target.constructor);
    };
}
function Saga(sagaType) {
    return function (constructor) {
        Reflect.defineMetadata('saga:type', sagaType, constructor);
    };
}
function SagaStep(stepName, eventTypes) {
    return function (target, propertyKey, descriptor) {
        const existingSteps = Reflect.getMetadata('saga:steps', target.constructor) || [];
        existingSteps.push({ stepName, eventTypes, handler: propertyKey });
        Reflect.defineMetadata('saga:steps', existingSteps, target.constructor);
    };
}
//# sourceMappingURL=event-sourcing.module.js.map