import { EventEmitter2 } from '@nestjs/event-emitter';
import { DomainEvent, EventBus, EventHandler } from '../../../domain/events/base/domain-event.base';
import { EventPublisherService } from './event-publisher.service';
export declare class EventBusService implements EventBus {
    private readonly eventEmitter;
    private readonly eventPublisher;
    private readonly logger;
    private readonly handlers;
    constructor(eventEmitter: EventEmitter2, eventPublisher: EventPublisherService);
    publish(event: DomainEvent): Promise<void>;
    publishBatch(events: DomainEvent[]): Promise<void>;
    subscribe<T extends DomainEvent>(eventType: string, handler: EventHandler<T>): void;
    unsubscribe(eventType: string, handler: EventHandler): void;
    private emitToLocalHandlers;
    getEventStats(): {
        totalHandlers: number;
        handlersByEventType: Record<string, number>;
    };
    clearAllHandlers(): void;
    getRegisteredEventTypes(): string[];
    hasHandlers(eventType: string): boolean;
    publishWithRetry(event: DomainEvent, maxRetries?: number, retryDelay?: number): Promise<void>;
    publishOrdered(events: DomainEvent[]): Promise<void>;
    private delay;
}
