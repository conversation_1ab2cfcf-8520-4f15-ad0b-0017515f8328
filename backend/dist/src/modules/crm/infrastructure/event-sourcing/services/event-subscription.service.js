"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var EventSubscriptionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventSubscriptionService = void 0;
const common_1 = require("@nestjs/common");
let EventSubscriptionService = EventSubscriptionService_1 = class EventSubscriptionService {
    logger = new common_1.Logger(EventSubscriptionService_1.name);
    async subscribe(eventType, handler) {
        this.logger.log(`EventSubscriptionService - subscribe - Placeholder for: ${eventType}`);
    }
};
exports.EventSubscriptionService = EventSubscriptionService;
exports.EventSubscriptionService = EventSubscriptionService = EventSubscriptionService_1 = __decorate([
    (0, common_1.Injectable)()
], EventSubscriptionService);
//# sourceMappingURL=event-subscription.service.js.map