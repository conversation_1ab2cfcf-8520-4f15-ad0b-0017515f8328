"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EventBusService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventBusService = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const event_publisher_service_1 = require("./event-publisher.service");
let EventBusService = EventBusService_1 = class EventBusService {
    eventEmitter;
    eventPublisher;
    logger = new common_1.Logger(EventBusService_1.name);
    handlers = new Map();
    constructor(eventEmitter, eventPublisher) {
        this.eventEmitter = eventEmitter;
        this.eventPublisher = eventPublisher;
    }
    async publish(event) {
        this.logger.debug(`Publishing event: ${event.eventType} (${event.eventId})`);
        try {
            await this.emitToLocalHandlers(event);
            if (event.shouldPublishExternally()) {
                await this.eventPublisher.publishExternal(event);
            }
            this.logger.debug(`Successfully published event: ${event.eventType} (${event.eventId})`);
        }
        catch (error) {
            this.logger.error(`Failed to publish event: ${event.eventType} (${event.eventId})`, error);
            throw error;
        }
    }
    async publishBatch(events) {
        this.logger.debug(`Publishing batch of ${events.length} events`);
        try {
            await Promise.all(events.map(event => this.publish(event)));
            this.logger.debug(`Successfully published batch of ${events.length} events`);
        }
        catch (error) {
            this.logger.error(`Failed to publish event batch`, error);
            throw error;
        }
    }
    subscribe(eventType, handler) {
        this.logger.debug(`Subscribing handler to event type: ${eventType}`);
        if (!this.handlers.has(eventType)) {
            this.handlers.set(eventType, new Set());
        }
        this.handlers.get(eventType).add(handler);
        this.eventEmitter.on(eventType, async (event) => {
            try {
                if (handler.canHandle(event)) {
                    await handler.handle(event);
                }
            }
            catch (error) {
                this.logger.error(`Handler failed for event: ${eventType}`, error);
            }
        });
        this.logger.debug(`Handler subscribed to event type: ${eventType}`);
    }
    unsubscribe(eventType, handler) {
        this.logger.debug(`Unsubscribing handler from event type: ${eventType}`);
        const handlers = this.handlers.get(eventType);
        if (handlers) {
            handlers.delete(handler);
            if (handlers.size === 0) {
                this.handlers.delete(eventType);
            }
        }
        this.logger.debug(`Handler unsubscribed from event type: ${eventType}`);
    }
    async emitToLocalHandlers(event) {
        this.eventEmitter.emit(event.eventType, event);
        const handlers = this.handlers.get(event.eventType);
        if (handlers && handlers.size > 0) {
            const handlerPromises = Array.from(handlers)
                .filter(handler => handler.canHandle(event))
                .map(async (handler) => {
                try {
                    await handler.handle(event);
                }
                catch (error) {
                    this.logger.error(`Handler failed for event: ${event.eventType}`, error);
                }
            });
            await Promise.allSettled(handlerPromises);
        }
    }
    getEventStats() {
        const handlersByEventType = {};
        let totalHandlers = 0;
        for (const [eventType, handlers] of this.handlers.entries()) {
            handlersByEventType[eventType] = handlers.size;
            totalHandlers += handlers.size;
        }
        return {
            totalHandlers,
            handlersByEventType,
        };
    }
    clearAllHandlers() {
        this.logger.warn('Clearing all event handlers');
        this.handlers.clear();
        this.eventEmitter.removeAllListeners();
    }
    getRegisteredEventTypes() {
        return Array.from(this.handlers.keys());
    }
    hasHandlers(eventType) {
        const handlers = this.handlers.get(eventType);
        return handlers ? handlers.size > 0 : false;
    }
    async publishWithRetry(event, maxRetries = 3, retryDelay = 1000) {
        let lastError = null;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                await this.publish(event);
                return;
            }
            catch (error) {
                lastError = error;
                this.logger.warn(`Failed to publish event (attempt ${attempt}/${maxRetries}): ${event.eventType}`, error);
                if (attempt < maxRetries) {
                    await this.delay(retryDelay * attempt);
                }
            }
        }
        this.logger.error(`Failed to publish event after ${maxRetries} attempts: ${event.eventType}`, lastError);
        throw lastError;
    }
    async publishOrdered(events) {
        this.logger.debug(`Publishing ${events.length} events in order`);
        for (const event of events) {
            await this.publish(event);
        }
        this.logger.debug(`Successfully published ${events.length} events in order`);
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
};
exports.EventBusService = EventBusService;
exports.EventBusService = EventBusService = EventBusService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [event_emitter_1.EventEmitter2,
        event_publisher_service_1.EventPublisherService])
], EventBusService);
//# sourceMappingURL=event-bus.service.js.map