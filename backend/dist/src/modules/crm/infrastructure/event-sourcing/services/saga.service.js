"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var SagaService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SagaService = void 0;
const common_1 = require("@nestjs/common");
let SagaService = SagaService_1 = class SagaService {
    logger = new common_1.Logger(SagaService_1.name);
    async startSaga(sagaType, data) {
        this.logger.log(`SagaService - startSaga - Placeholder for: ${sagaType}`);
    }
};
exports.SagaService = SagaService;
exports.SagaService = SagaService = SagaService_1 = __decorate([
    (0, common_1.Injectable)()
], SagaService);
//# sourceMappingURL=saga.service.js.map