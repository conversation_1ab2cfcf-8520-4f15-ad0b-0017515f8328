"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var SnapshotService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SnapshotService = void 0;
const common_1 = require("@nestjs/common");
let SnapshotService = SnapshotService_1 = class SnapshotService {
    logger = new common_1.Logger(SnapshotService_1.name);
    async createSnapshot(aggregateId, data) {
        this.logger.log(`SnapshotService - createSnapshot - Placeholder for: ${aggregateId}`);
    }
};
exports.SnapshotService = SnapshotService;
exports.SnapshotService = SnapshotService = SnapshotService_1 = __decorate([
    (0, common_1.Injectable)()
], SnapshotService);
//# sourceMappingURL=snapshot.service.js.map