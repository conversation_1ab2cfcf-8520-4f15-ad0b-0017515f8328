export declare class EventSourcingModule {
}
export interface EventSourcingConfig {
    eventStore: {
        snapshotFrequency: number;
        maxEventsPerStream: number;
        cleanupOlderThan: number;
    };
    projections: {
        batchSize: number;
        maxRetries: number;
        retryDelay: number;
    };
    sagas: {
        timeoutDuration: number;
        maxRetries: number;
        compensationTimeout: number;
    };
    publishing: {
        batchSize: number;
        retryAttempts: number;
        retryDelay: number;
    };
}
export declare const DEFAULT_EVENT_SOURCING_CONFIG: EventSourcingConfig;
export declare function AggregateRoot(aggregateType: string): (constructor: any) => void;
export declare function ApplyEvent(eventType: string): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => void;
export declare function Projection(projectionName: string, eventTypes: string[]): (constructor: any) => void;
export declare function ProjectionHandler(eventType: string): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => void;
export declare function Saga(sagaType: string): (constructor: any) => void;
export declare function SagaStep(stepName: string, eventTypes: string[]): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => void;
