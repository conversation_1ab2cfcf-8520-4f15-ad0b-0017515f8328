import { OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
export declare class TensorFlowScoringService implements OnModuleInit {
    private readonly configService;
    private readonly logger;
    private model;
    private isModelLoaded;
    private featureScaler;
    constructor(configService: ConfigService);
    onModuleInit(): Promise<void>;
    private initializeModel;
    private warmUpModel;
    predictScore(lead: Lead, historicalData?: any): Promise<number | null>;
    private extractFeatures;
    trainModel(trainingData: TrainingData[]): Promise<void>;
    private prepareTrainingData;
    private saveModel;
    getModelInfo(): ModelInfo;
    private getModelSummary;
    dispose(): void;
}
interface TrainingData {
    lead: Lead;
    actualScore: number;
    historicalData?: any;
}
interface ModelInfo {
    isLoaded: boolean;
    modelSummary: any;
    featureCount: number;
}
export {};
