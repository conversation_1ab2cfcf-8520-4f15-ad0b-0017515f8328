"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TensorFlowScoringService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TensorFlowScoringService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const tf = require("@tensorflow/tfjs-node");
let TensorFlowScoringService = TensorFlowScoringService_1 = class TensorFlowScoringService {
    configService;
    logger = new common_1.Logger(TensorFlowScoringService_1.name);
    model = null;
    isModelLoaded = false;
    featureScaler = null;
    constructor(configService) {
        this.configService = configService;
    }
    async onModuleInit() {
        try {
            await this.initializeModel();
        }
        catch (error) {
            this.logger.warn('Failed to initialize ML model, falling back to rule-based scoring', error);
        }
    }
    async initializeModel() {
        const modelPath = this.configService.get('ML_MODEL_PATH');
        if (!modelPath) {
            this.logger.warn('ML_MODEL_PATH not configured, skipping ML model initialization');
            return;
        }
        try {
            this.logger.log('Loading TensorFlow.js model...');
            this.model = await tf.loadLayersModel(modelPath);
            this.featureScaler = new FeatureScaler();
            await this.featureScaler.loadScalingParameters();
            this.isModelLoaded = true;
            this.logger.log('TensorFlow.js model loaded successfully');
            await this.warmUpModel();
        }
        catch (error) {
            this.logger.error('Failed to load TensorFlow.js model', error);
            this.isModelLoaded = false;
        }
    }
    async warmUpModel() {
        if (!this.model || !this.featureScaler)
            return;
        try {
            const dummyFeatures = this.featureScaler.createDummyFeatures();
            const prediction = this.model.predict(dummyFeatures);
            prediction.dispose();
            dummyFeatures.dispose();
            this.logger.debug('Model warm-up completed');
        }
        catch (error) {
            this.logger.warn('Model warm-up failed', error);
        }
    }
    async predictScore(lead, historicalData) {
        if (!this.isModelLoaded || !this.model || !this.featureScaler) {
            return null;
        }
        try {
            const features = this.extractFeatures(lead, historicalData);
            const scaledFeatures = this.featureScaler.scaleFeatures(features);
            const inputTensor = tf.tensor2d([scaledFeatures], [1, scaledFeatures.length]);
            const prediction = this.model.predict(inputTensor);
            const scoreArray = await prediction.data();
            const score = scoreArray[0] * 100;
            inputTensor.dispose();
            prediction.dispose();
            const clampedScore = Math.max(0, Math.min(100, score));
            this.logger.debug(`ML prediction for lead ${lead.id}: ${clampedScore}`);
            return clampedScore;
        }
        catch (error) {
            this.logger.error(`ML prediction failed for lead ${lead.id}`, error);
            return null;
        }
    }
    extractFeatures(lead, historicalData) {
        const features = [];
        features.push(lead.name ? 1 : 0);
        features.push(lead.contactInfo?.email ? 1 : 0);
        features.push(lead.contactInfo?.phone ? 1 : 0);
        features.push(lead.contactInfo?.company ? 1 : 0);
        features.push(lead.contactInfo?.website ? 1 : 0);
        features.push(lead.expectedRevenue || 0);
        features.push(lead.probability || 0);
        const daysSinceCreated = Math.floor((Date.now() - lead.createdAt.getTime()) / (1000 * 60 * 60 * 24));
        features.push(daysSinceCreated);
        features.push(lead.priority?.value === 'high' ? 1 : 0);
        features.push(lead.priority?.value === 'medium' ? 1 : 0);
        features.push(lead.priority?.value === 'low' ? 1 : 0);
        features.push(lead.assignedUserId ? 1 : 0);
        features.push(lead.teamId ? 1 : 0);
        const sources = ['referral', 'website', 'email', 'social', 'paid', 'organic'];
        sources.forEach(source => {
            features.push(lead.source?.toLowerCase() === source ? 1 : 0);
        });
        features.push(lead.type?.value === 'opportunity' ? 1 : 0);
        features.push(lead.type?.value === 'lead' ? 1 : 0);
        features.push(lead.tags?.length || 0);
        if (lead.dateDeadline) {
            const daysToDeadline = Math.floor((lead.dateDeadline.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
            features.push(Math.max(0, daysToDeadline));
        }
        else {
            features.push(365);
        }
        if (historicalData) {
            features.push(historicalData.averageConversionRate || 0);
            features.push(historicalData.sourceConversionRate || 0);
            features.push(historicalData.industryConversionRate || 0);
        }
        else {
            features.push(0, 0, 0);
        }
        return features;
    }
    async trainModel(trainingData) {
        if (!this.model) {
            throw new Error('Model not initialized');
        }
        try {
            this.logger.log('Starting model training...');
            const { features, labels } = this.prepareTrainingData(trainingData);
            const xs = tf.tensor2d(features);
            const ys = tf.tensor2d(labels, [labels.length, 1]);
            this.model.compile({
                optimizer: tf.train.adam(0.001),
                loss: 'meanSquaredError',
                metrics: ['mae'],
            });
            const history = await this.model.fit(xs, ys, {
                epochs: 100,
                batchSize: 32,
                validationSplit: 0.2,
                shuffle: true,
                callbacks: {
                    onEpochEnd: (epoch, logs) => {
                        if (epoch % 10 === 0) {
                            this.logger.debug(`Epoch ${epoch}: loss = ${logs?.loss}, mae = ${logs?.mae}`);
                        }
                    },
                },
            });
            xs.dispose();
            ys.dispose();
            this.logger.log('Model training completed');
            await this.saveModel();
        }
        catch (error) {
            this.logger.error('Model training failed', error);
            throw error;
        }
    }
    prepareTrainingData(trainingData) {
        const features = [];
        const labels = [];
        trainingData.forEach(data => {
            const leadFeatures = this.extractFeatures(data.lead, data.historicalData);
            features.push(leadFeatures);
            labels.push(data.actualScore / 100);
        });
        return { features, labels };
    }
    async saveModel() {
        if (!this.model)
            return;
        const modelSavePath = this.configService.get('ML_MODEL_SAVE_PATH', 'file://./models/lead-scoring');
        try {
            await this.model.save(modelSavePath);
            this.logger.log(`Model saved to ${modelSavePath}`);
        }
        catch (error) {
            this.logger.error('Failed to save model', error);
        }
    }
    getModelInfo() {
        return {
            isLoaded: this.isModelLoaded,
            modelSummary: this.model ? this.getModelSummary() : null,
            featureCount: this.featureScaler?.getFeatureCount() || 0,
        };
    }
    getModelSummary() {
        if (!this.model)
            return null;
        return {
            inputShape: this.model.inputs[0].shape,
            outputShape: this.model.outputs[0].shape,
            trainableParams: this.model.countParams(),
            layers: this.model.layers.length,
        };
    }
    dispose() {
        if (this.model) {
            this.model.dispose();
            this.model = null;
            this.isModelLoaded = false;
            this.logger.log('TensorFlow.js model disposed');
        }
    }
};
exports.TensorFlowScoringService = TensorFlowScoringService;
exports.TensorFlowScoringService = TensorFlowScoringService = TensorFlowScoringService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], TensorFlowScoringService);
class FeatureScaler {
    means = [];
    stds = [];
    featureCount = 0;
    async loadScalingParameters() {
        this.featureCount = 25;
        this.means = new Array(this.featureCount).fill(0.5);
        this.stds = new Array(this.featureCount).fill(0.3);
    }
    scaleFeatures(features) {
        return features.map((feature, index) => {
            const mean = this.means[index] || 0;
            const std = this.stds[index] || 1;
            return (feature - mean) / std;
        });
    }
    createDummyFeatures() {
        const dummyFeatures = new Array(this.featureCount).fill(0.5);
        return tf.tensor2d([dummyFeatures], [1, this.featureCount]);
    }
    getFeatureCount() {
        return this.featureCount;
    }
}
//# sourceMappingURL=tensorflow-scoring.service.js.map