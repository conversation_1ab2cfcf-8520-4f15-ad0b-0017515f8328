"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MongoLeadRepository = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const lead_entity_1 = require("../../../../domain/entities/lead.entity");
let MongoLeadRepository = class MongoLeadRepository {
    leadModel;
    constructor(leadModel) {
        this.leadModel = leadModel;
    }
    async save(lead) {
        const leadData = this.toDocument(lead);
        if (lead.id) {
            const updated = await this.leadModel.findByIdAndUpdate(lead.id, leadData, { new: true, upsert: true }).exec();
            return this.toDomain(updated);
        }
        else {
            const created = new this.leadModel(leadData);
            const saved = await created.save();
            return this.toDomain(saved);
        }
    }
    async findById(id) {
        const leadDoc = await this.leadModel.findById(id).exec();
        return leadDoc ? this.toDomain(leadDoc) : null;
    }
    async findAll() {
        const leadDocs = await this.leadModel.find().exec();
        return leadDocs.map(doc => this.toDomain(doc));
    }
    async findByTeamId(teamId) {
        const leadDocs = await this.leadModel.find({ teamId }).exec();
        return leadDocs.map(doc => this.toDomain(doc));
    }
    async findByFilters(filters) {
        const query = {};
        if (filters.status) {
            query['status.value'] = filters.status;
        }
        if (filters.priority) {
            query['priority.value'] = filters.priority;
        }
        if (filters.assignedUserId) {
            query.assignedUserId = filters.assignedUserId;
        }
        if (filters.teamId) {
            query.teamId = filters.teamId;
        }
        if (filters.source) {
            query.source = filters.source;
        }
        if (filters.tags && filters.tags.length > 0) {
            query.tags = { $in: filters.tags };
        }
        if (filters.dateFrom || filters.dateTo) {
            query.createdAt = {};
            if (filters.dateFrom) {
                query.createdAt.$gte = filters.dateFrom;
            }
            if (filters.dateTo) {
                query.createdAt.$lte = filters.dateTo;
            }
        }
        if (filters.dateDeadlineBefore) {
            query.dateDeadline = { $lt: filters.dateDeadlineBefore };
        }
        if (filters.minRevenue || filters.maxRevenue) {
            query.expectedRevenue = {};
            if (filters.minRevenue) {
                query.expectedRevenue.$gte = filters.minRevenue;
            }
            if (filters.maxRevenue) {
                query.expectedRevenue.$lte = filters.maxRevenue;
            }
        }
        let mongoQuery = this.leadModel.find(query);
        if (filters.limit) {
            mongoQuery = mongoQuery.limit(filters.limit);
        }
        if (filters.offset) {
            mongoQuery = mongoQuery.skip(filters.offset);
        }
        const leadDocs = await mongoQuery.exec();
        return leadDocs.map(doc => this.toDomain(doc));
    }
    async findByAssignedUserId(userId) {
        const leadDocs = await this.leadModel.find({ assignedUserId: parseInt(userId) }).exec();
        return leadDocs.map(doc => this.toDomain(doc));
    }
    async search(searchFilters) {
        const query = {};
        if (searchFilters.searchTerm) {
            query.$or = [
                { name: { $regex: searchFilters.searchTerm, $options: 'i' } },
                { 'contactInfo.email': { $regex: searchFilters.searchTerm, $options: 'i' } },
                { 'contactInfo.company': { $regex: searchFilters.searchTerm, $options: 'i' } },
                { 'contactInfo.phone': { $regex: searchFilters.searchTerm, $options: 'i' } },
            ];
        }
        if (searchFilters.status) {
            query['status.value'] = searchFilters.status;
        }
        if (searchFilters.teamId) {
            query.teamId = searchFilters.teamId;
        }
        let mongoQuery = this.leadModel.find(query);
        if (searchFilters.limit) {
            mongoQuery = mongoQuery.limit(searchFilters.limit);
        }
        const leadDocs = await mongoQuery.exec();
        return leadDocs.map(doc => this.toDomain(doc));
    }
    async update(id, lead) {
        const leadData = this.toDocument(lead);
        const updated = await this.leadModel.findByIdAndUpdate(id, leadData, { new: true }).exec();
        if (!updated) {
            throw new Error(`Lead with ID ${id} not found`);
        }
        return this.toDomain(updated);
    }
    async delete(id) {
        const result = await this.leadModel.findByIdAndDelete(id).exec();
        return !!result;
    }
    async count(filters) {
        const query = filters ? this.buildQuery(filters) : {};
        return this.leadModel.countDocuments(query).exec();
    }
    async exists(id) {
        const count = await this.leadModel.countDocuments({ _id: id }).exec();
        return count > 0;
    }
    toDocument(lead) {
        return {
            name: lead.name,
            description: lead.description,
            contactInfo: lead.contactInfo?.toPlainObject(),
            status: lead.status?.toPlainObject(),
            priority: lead.priority?.toPlainObject(),
            type: lead.type?.toPlainObject(),
            source: lead.source,
            expectedRevenue: lead.expectedRevenue,
            probability: lead.probability,
            assignedUserId: lead.assignedUserId,
            teamId: lead.teamId,
            stageId: lead.stageId,
            dateDeadline: lead.dateDeadline,
            tags: lead.tags,
            createdAt: lead.createdAt,
            updatedAt: lead.updatedAt,
        };
    }
    toDomain(doc) {
        return new lead_entity_1.Lead(doc._id.toString(), doc.name, doc.description, null, null, null, null, doc.source, doc.expectedRevenue, doc.probability, doc.assignedUserId, doc.teamId, doc.stageId, doc.dateDeadline, doc.tags, doc.createdAt, doc.updatedAt);
    }
    buildQuery(filters) {
        const query = {};
        if (filters.status) {
            query['status.value'] = filters.status;
        }
        return query;
    }
};
exports.MongoLeadRepository = MongoLeadRepository;
exports.MongoLeadRepository = MongoLeadRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('Lead')),
    __metadata("design:paramtypes", [mongoose_2.Model])
], MongoLeadRepository);
//# sourceMappingURL=lead.repository.impl.js.map