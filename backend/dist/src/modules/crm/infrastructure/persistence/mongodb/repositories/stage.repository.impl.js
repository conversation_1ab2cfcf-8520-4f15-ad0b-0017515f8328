"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MongoStageRepository = void 0;
const common_1 = require("@nestjs/common");
let MongoStageRepository = class MongoStageRepository {
    stages = [
        {
            id: 1,
            name: 'New',
            description: 'New leads',
            pipelineId: 1,
            order: 1,
            type: 'lead',
            isActive: true,
            expectedDuration: 3,
            requirements: [],
            color: '#6B7280',
            createdAt: new Date(),
            updatedAt: new Date(),
        },
        {
            id: 2,
            name: 'Qualified',
            description: 'Qualified leads',
            pipelineId: 1,
            order: 2,
            type: 'lead',
            isActive: true,
            expectedDuration: 7,
            requirements: ['Contact information verified'],
            color: '#3B82F6',
            createdAt: new Date(),
            updatedAt: new Date(),
        },
        {
            id: 3,
            name: 'Proposal',
            description: 'Proposal sent',
            pipelineId: 1,
            order: 3,
            type: 'opportunity',
            isActive: true,
            expectedDuration: 14,
            requirements: ['Proposal prepared', 'Budget confirmed'],
            color: '#F59E0B',
            createdAt: new Date(),
            updatedAt: new Date(),
        },
        {
            id: 4,
            name: 'Won',
            description: 'Deal won',
            pipelineId: 1,
            order: 4,
            type: 'opportunity',
            isActive: true,
            expectedDuration: 0,
            requirements: ['Contract signed'],
            color: '#10B981',
            createdAt: new Date(),
            updatedAt: new Date(),
        },
    ];
    async findById(id) {
        return this.stages.find(stage => stage.id === id) || null;
    }
    async findAll() {
        return [...this.stages];
    }
    async findByPipelineId(pipelineId) {
        return this.stages.filter(stage => stage.pipelineId === pipelineId);
    }
    async create(stageData) {
        const newStage = {
            id: Math.max(...this.stages.map(s => s.id)) + 1,
            ...stageData,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.stages.push(newStage);
        return newStage;
    }
    async update(id, stageData) {
        const index = this.stages.findIndex(stage => stage.id === id);
        if (index === -1) {
            throw new Error(`Stage with ID ${id} not found`);
        }
        const updatedStage = {
            ...this.stages[index],
            ...stageData,
            updatedAt: new Date(),
        };
        this.stages[index] = updatedStage;
        return updatedStage;
    }
    async delete(id) {
        const index = this.stages.findIndex(stage => stage.id === id);
        if (index === -1) {
            return false;
        }
        this.stages.splice(index, 1);
        return true;
    }
    async findByType(type) {
        return this.stages.filter(stage => stage.type === type);
    }
    async reorderStages(stageOrders) {
        stageOrders.forEach(({ id, order }) => {
            const stage = this.stages.find(s => s.id === id);
            if (stage) {
                stage.order = order;
                stage.updatedAt = new Date();
            }
        });
        this.stages.sort((a, b) => a.order - b.order);
    }
};
exports.MongoStageRepository = MongoStageRepository;
exports.MongoStageRepository = MongoStageRepository = __decorate([
    (0, common_1.Injectable)()
], MongoStageRepository);
//# sourceMappingURL=stage.repository.impl.js.map