import { IStageRepository, Stage, CreateStageData, UpdateStageData } from '@/modules/crm/domain/repositories/stage.repository';
export declare class MongoStageRepository implements IStageRepository {
    private stages;
    findById(id: number): Promise<Stage | null>;
    findAll(): Promise<Stage[]>;
    findByPipelineId(pipelineId: number): Promise<Stage[]>;
    create(stageData: CreateStageData): Promise<Stage>;
    update(id: number, stageData: UpdateStageData): Promise<Stage>;
    delete(id: number): Promise<boolean>;
    findByType(type: string): Promise<Stage[]>;
    reorderStages(stageOrders: Array<{
        id: number;
        order: number;
    }>): Promise<void>;
}
