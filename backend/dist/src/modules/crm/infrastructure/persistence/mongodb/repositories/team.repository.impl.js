"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MongoTeamRepository = void 0;
const common_1 = require("@nestjs/common");
let MongoTeamRepository = class MongoTeamRepository {
    teams = [
        {
            id: 1,
            name: 'Sales Team',
            description: 'Main sales team',
            managerId: 1,
            memberIds: [1, 2, 3],
            isActive: true,
            territories: ['North America', 'Europe'],
            targets: {
                monthly: 100000,
                quarterly: 300000,
                annual: 1200000,
            },
            createdAt: new Date(),
            updatedAt: new Date(),
        },
        {
            id: 2,
            name: 'Enterprise Team',
            description: 'Enterprise sales team',
            managerId: 2,
            memberIds: [4, 5],
            isActive: true,
            territories: ['Global'],
            targets: {
                monthly: 200000,
                quarterly: 600000,
                annual: 2400000,
            },
            createdAt: new Date(),
            updatedAt: new Date(),
        },
    ];
    async findById(id) {
        return this.teams.find(team => team.id === id) || null;
    }
    async findAll() {
        return [...this.teams];
    }
    async findByManagerId(managerId) {
        return this.teams.filter(team => team.managerId === managerId);
    }
    async findByMemberId(memberId) {
        return this.teams.filter(team => team.memberIds.includes(memberId));
    }
    async create(teamData) {
        const newTeam = {
            id: Math.max(...this.teams.map(t => t.id)) + 1,
            ...teamData,
            memberIds: teamData.memberIds || [],
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.teams.push(newTeam);
        return newTeam;
    }
    async update(id, teamData) {
        const index = this.teams.findIndex(team => team.id === id);
        if (index === -1) {
            throw new Error(`Team with ID ${id} not found`);
        }
        const updatedTeam = {
            ...this.teams[index],
            ...teamData,
            updatedAt: new Date(),
        };
        this.teams[index] = updatedTeam;
        return updatedTeam;
    }
    async delete(id) {
        const index = this.teams.findIndex(team => team.id === id);
        if (index === -1) {
            return false;
        }
        this.teams.splice(index, 1);
        return true;
    }
    async addMember(teamId, memberId) {
        const team = this.teams.find(t => t.id === teamId);
        if (!team) {
            throw new Error(`Team with ID ${teamId} not found`);
        }
        if (!team.memberIds.includes(memberId)) {
            team.memberIds.push(memberId);
            team.updatedAt = new Date();
        }
    }
    async removeMember(teamId, memberId) {
        const team = this.teams.find(t => t.id === teamId);
        if (!team) {
            throw new Error(`Team with ID ${teamId} not found`);
        }
        const index = team.memberIds.indexOf(memberId);
        if (index > -1) {
            team.memberIds.splice(index, 1);
            team.updatedAt = new Date();
        }
    }
    async getTeamMembers(teamId) {
        const team = this.teams.find(t => t.id === teamId);
        return team ? [...team.memberIds] : [];
    }
    async updateTargets(teamId, targets) {
        const team = this.teams.find(t => t.id === teamId);
        if (!team) {
            throw new Error(`Team with ID ${teamId} not found`);
        }
        team.targets = { ...team.targets, ...targets };
        team.updatedAt = new Date();
    }
    async findByTerritory(territory) {
        return this.teams.filter(team => team.territories && team.territories.includes(territory));
    }
};
exports.MongoTeamRepository = MongoTeamRepository;
exports.MongoTeamRepository = MongoTeamRepository = __decorate([
    (0, common_1.Injectable)()
], MongoTeamRepository);
//# sourceMappingURL=team.repository.impl.js.map