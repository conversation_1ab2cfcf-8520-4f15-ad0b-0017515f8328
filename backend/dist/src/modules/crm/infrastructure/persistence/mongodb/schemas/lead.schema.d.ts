import { Document } from 'mongoose';
export declare class LeadDocument extends Document {
    name: string;
    description?: string;
    contactInfo?: {
        email?: string;
        phone?: string;
        mobile?: string;
        website?: string;
        company?: string;
        jobTitle?: string;
        address?: string;
        city?: string;
        state?: string;
        country?: string;
        postalCode?: string;
        socialMedia?: Map<string, string>;
    };
    status: {
        value: string;
    };
    priority: {
        value: string;
    };
    type: {
        value: string;
    };
    source?: string;
    expectedRevenue?: number;
    probability?: number;
    assignedUserId?: number;
    teamId?: number;
    stageId?: number;
    dateDeadline?: Date;
    tags: string[];
    createdAt: Date;
    updatedAt: Date;
}
export declare const LeadSchema: import("mongoose").Schema<LeadDocument, import("mongoose").Model<LeadDocument, any, any, any, Document<unknown, any, LeadDocument, any> & LeadDocument & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, LeadDocument, Document<unknown, {}, import("mongoose").FlatRecord<LeadDocument>, {}> & import("mongoose").FlatRecord<LeadDocument> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
export { LeadDocument as Lead };
