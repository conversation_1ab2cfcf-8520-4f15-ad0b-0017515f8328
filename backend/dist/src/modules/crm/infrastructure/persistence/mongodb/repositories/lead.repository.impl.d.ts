import { Model } from 'mongoose';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
import { LeadDocument } from '../schemas/lead.schema';
export declare class MongoLeadRepository implements ILeadRepository {
    private readonly leadModel;
    constructor(leadModel: Model<LeadDocument>);
    save(lead: Lead): Promise<Lead>;
    findById(id: number): Promise<Lead | null>;
    findAll(): Promise<Lead[]>;
    findByTeamId(teamId: number): Promise<Lead[]>;
    findByFilters(filters: any): Promise<Lead[]>;
    findByAssignedUserId(userId: string): Promise<Lead[]>;
    search(searchFilters: any): Promise<Lead[]>;
    update(id: number, lead: Lead): Promise<Lead>;
    delete(id: number): Promise<boolean>;
    count(filters?: any): Promise<number>;
    exists(id: number): Promise<boolean>;
    private toDocument;
    private toDomain;
    private buildQuery;
}
