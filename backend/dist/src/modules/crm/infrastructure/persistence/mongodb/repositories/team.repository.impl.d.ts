import { ITeamRepository, Team, CreateTeamData, UpdateTeamData } from '@/modules/crm/domain/repositories/team.repository';
export declare class MongoTeamRepository implements ITeamRepository {
    private teams;
    findById(id: number): Promise<Team | null>;
    findAll(): Promise<Team[]>;
    findByManagerId(managerId: number): Promise<Team[]>;
    findByMemberId(memberId: number): Promise<Team[]>;
    create(teamData: CreateTeamData): Promise<Team>;
    update(id: number, teamData: UpdateTeamData): Promise<Team>;
    delete(id: number): Promise<boolean>;
    addMember(teamId: number, memberId: number): Promise<void>;
    removeMember(teamId: number, memberId: number): Promise<void>;
    getTeamMembers(teamId: number): Promise<number[]>;
    updateTargets(teamId: number, targets: any): Promise<void>;
    findByTerritory(territory: string): Promise<Team[]>;
}
