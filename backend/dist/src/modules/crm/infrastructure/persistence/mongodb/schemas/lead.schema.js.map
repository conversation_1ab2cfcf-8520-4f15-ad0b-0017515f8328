{"version": 3, "file": "lead.schema.js", "sourceRoot": "", "sources": ["../../../../../../../../src/modules/crm/infrastructure/persistence/mongodb/schemas/lead.schema.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAA+D;AAC/D,uCAAoC;AAO7B,IAAM,YAAY,GAAlB,MAAM,YAAa,SAAQ,mBAAQ;IAExC,IAAI,CAAS;IAGb,WAAW,CAAU;IAqBrB,WAAW,CAaT;IAQF,MAAM,CAEJ;IAQF,QAAQ,CAEN;IAQF,IAAI,CAEF;IAGF,MAAM,CAAU;IAGhB,eAAe,CAAU;IAGzB,WAAW,CAAU;IAGrB,cAAc,CAAU;IAGxB,MAAM,CAAU;IAGhB,OAAO,CAAU;IAGjB,YAAY,CAAQ;IAGpB,IAAI,CAAW;IAGf,SAAS,CAAO;IAGhB,SAAS,CAAO;CACjB,CAAA;AApGY,oCAAY;AA+HA,4BAAI;AA7H3B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0CACZ;AAGb;IADC,IAAA,eAAI,GAAE;;iDACc;AAqBrB;IAnBC,IAAA,eAAI,EAAC;QACJ,IAAI,EAAE;YACJ,KAAK,EAAE,MAAM;YACb,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,MAAM;YACf,OAAO,EAAE,MAAM;YACf,QAAQ,EAAE,MAAM;YAChB,OAAO,EAAE,MAAM;YACf,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,MAAM;YACb,OAAO,EAAE,MAAM;YACf,UAAU,EAAE,MAAM;YAClB,WAAW,EAAE;gBACX,IAAI,EAAE,GAAG;gBACT,EAAE,EAAE,MAAM;aACX;SACF;KACF,CAAC;;iDAcA;AAQF;IANC,IAAA,eAAI,EAAC;QACJ,IAAI,EAAE;YACJ,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE;SAC/F;QACD,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;KAC1B,CAAC;;4CAGA;AAQF;IANC,IAAA,eAAI,EAAC;QACJ,IAAI,EAAE;YACJ,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE;SACzD;QACD,OAAO,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE;KAC7B,CAAC;;8CAGA;AAQF;IANC,IAAA,eAAI,EAAC;QACJ,IAAI,EAAE;YACJ,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,aAAa,CAAC,EAAE;SACvD;QACD,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;KAC3B,CAAC;;0CAGA;AAGF;IADC,IAAA,eAAI,GAAE;;4CACS;AAGhB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;;qDACN;AAGzB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;;iDACpB;AAGrB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;oDACC;AAGxB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;4CACP;AAGhB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;6CACN;AAGjB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;8BACN,IAAI;kDAAC;AAGpB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;0CACvB;AAGf;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;8BAC7B,IAAI;+CAAC;AAGhB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;8BAC7B,IAAI;+CAAC;sCAnGL,YAAY;IADxB,IAAA,iBAAM,EAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;GAChB,YAAY,CAoGxB;AAEY,QAAA,UAAU,GAAG,wBAAa,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;AAGrE,kBAAU,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC,CAAC;AACxC,kBAAU,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AAChC,kBAAU,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC,CAAC;AACxC,kBAAU,CAAC,KAAK,CAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC;AAC1C,kBAAU,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AAChC,kBAAU,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACpC,kBAAU,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC;AACtC,kBAAU,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AAG9B,kBAAU,CAAC,KAAK,CAAC;IACf,IAAI,EAAE,MAAM;IACZ,mBAAmB,EAAE,MAAM;IAC3B,qBAAqB,EAAE,MAAM;IAC7B,mBAAmB,EAAE,MAAM;CAC5B,CAAC,CAAC;AAGH,kBAAU,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC,CAAC;AACnD,kBAAU,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3D,kBAAU,CAAC,KAAK,CAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC"}