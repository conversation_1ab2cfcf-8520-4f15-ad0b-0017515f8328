import { IUserRepository, User, CreateUserData, UpdateUserData, UserFilters, UserWorkloadStats } from '@/modules/crm/domain/repositories/user.repository';
export declare class MongoUserRepository implements IUserRepository {
    private users;
    findById(id: string): Promise<User | null>;
    findByTeamId(teamId: number, filters?: UserFilters): Promise<User[]>;
    findAll(filters?: UserFilters): Promise<User[]>;
    create(userData: CreateUserData): Promise<User>;
    update(id: string, userData: UpdateUserData): Promise<User>;
    delete(id: string): Promise<boolean>;
    findByRole(role: string): Promise<User[]>;
    findAvailableUsers(teamId?: number): Promise<User[]>;
    updateAvailability(id: string, isAvailable: boolean): Promise<void>;
    getUserWorkloadStats(id: string): Promise<UserWorkloadStats>;
    findByTerritory(territory: string): Promise<User[]>;
    private applyFilters;
}
