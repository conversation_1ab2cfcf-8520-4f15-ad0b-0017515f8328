"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Lead = exports.LeadSchema = exports.LeadDocument = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let LeadDocument = class LeadDocument extends mongoose_2.Document {
    name;
    description;
    contactInfo;
    status;
    priority;
    type;
    source;
    expectedRevenue;
    probability;
    assignedUserId;
    teamId;
    stageId;
    dateDeadline;
    tags;
    createdAt;
    updatedAt;
};
exports.LeadDocument = LeadDocument;
exports.Lead = LeadDocument;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], LeadDocument.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], LeadDocument.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: {
            email: String,
            phone: String,
            mobile: String,
            website: String,
            company: String,
            jobTitle: String,
            address: String,
            city: String,
            state: String,
            country: String,
            postalCode: String,
            socialMedia: {
                type: Map,
                of: String,
            },
        },
    }),
    __metadata("design:type", Object)
], LeadDocument.prototype, "contactInfo", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: {
            value: { type: String, enum: ['new', 'qualified', 'proposition', 'won', 'lost', 'cancelled'] },
        },
        default: { value: 'new' },
    }),
    __metadata("design:type", Object)
], LeadDocument.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: {
            value: { type: String, enum: ['low', 'medium', 'high'] },
        },
        default: { value: 'medium' },
    }),
    __metadata("design:type", Object)
], LeadDocument.prototype, "priority", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: {
            value: { type: String, enum: ['lead', 'opportunity'] },
        },
        default: { value: 'lead' },
    }),
    __metadata("design:type", Object)
], LeadDocument.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], LeadDocument.prototype, "source", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number, min: 0 }),
    __metadata("design:type", Number)
], LeadDocument.prototype, "expectedRevenue", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number, min: 0, max: 100 }),
    __metadata("design:type", Number)
], LeadDocument.prototype, "probability", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number }),
    __metadata("design:type", Number)
], LeadDocument.prototype, "assignedUserId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number }),
    __metadata("design:type", Number)
], LeadDocument.prototype, "teamId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number }),
    __metadata("design:type", Number)
], LeadDocument.prototype, "stageId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Date }),
    __metadata("design:type", Date)
], LeadDocument.prototype, "dateDeadline", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [String], default: [] }),
    __metadata("design:type", Array)
], LeadDocument.prototype, "tags", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Date, default: Date.now }),
    __metadata("design:type", Date)
], LeadDocument.prototype, "createdAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Date, default: Date.now }),
    __metadata("design:type", Date)
], LeadDocument.prototype, "updatedAt", void 0);
exports.Lead = exports.LeadDocument = LeadDocument = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], LeadDocument);
exports.LeadSchema = mongoose_1.SchemaFactory.createForClass(LeadDocument);
exports.LeadSchema.index({ assignedUserId: 1 });
exports.LeadSchema.index({ teamId: 1 });
exports.LeadSchema.index({ 'status.value': 1 });
exports.LeadSchema.index({ 'priority.value': 1 });
exports.LeadSchema.index({ source: 1 });
exports.LeadSchema.index({ createdAt: -1 });
exports.LeadSchema.index({ dateDeadline: 1 });
exports.LeadSchema.index({ tags: 1 });
exports.LeadSchema.index({
    name: 'text',
    'contactInfo.email': 'text',
    'contactInfo.company': 'text',
    'contactInfo.phone': 'text',
});
exports.LeadSchema.index({ teamId: 1, 'status.value': 1 });
exports.LeadSchema.index({ assignedUserId: 1, 'status.value': 1 });
exports.LeadSchema.index({ 'priority.value': 1, createdAt: -1 });
//# sourceMappingURL=lead.schema.js.map