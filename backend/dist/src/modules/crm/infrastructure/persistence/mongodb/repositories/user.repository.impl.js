"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MongoUserRepository = void 0;
const common_1 = require("@nestjs/common");
let MongoUserRepository = class MongoUserRepository {
    users = [
        {
            id: '1',
            name: '<PERSON>',
            email: '<EMAIL>',
            role: 'sales_rep',
            teamId: 1,
            seniority: 'senior',
            territories: ['North America'],
            skills: ['B2B Sales', 'Enterprise'],
            isActive: true,
            isAvailable: true,
            maxLeads: 20,
            timezone: 'America/New_York',
            workingHours: {
                monday: { start: '09:00', end: '17:00' },
                tuesday: { start: '09:00', end: '17:00' },
                wednesday: { start: '09:00', end: '17:00' },
                thursday: { start: '09:00', end: '17:00' },
                friday: { start: '09:00', end: '17:00' },
            },
            createdAt: new Date(),
            updatedAt: new Date(),
        },
        {
            id: '2',
            name: 'Jane Smith',
            email: '<EMAIL>',
            role: 'sales_rep',
            teamId: 1,
            seniority: 'junior',
            territories: ['Europe'],
            skills: ['B2B Sales', 'SMB'],
            isActive: true,
            isAvailable: true,
            maxLeads: 15,
            timezone: 'Europe/London',
            workingHours: {
                monday: { start: '08:00', end: '16:00' },
                tuesday: { start: '08:00', end: '16:00' },
                wednesday: { start: '08:00', end: '16:00' },
                thursday: { start: '08:00', end: '16:00' },
                friday: { start: '08:00', end: '16:00' },
            },
            createdAt: new Date(),
            updatedAt: new Date(),
        },
        {
            id: '3',
            name: 'Mike Johnson',
            email: '<EMAIL>',
            role: 'sales_manager',
            teamId: 1,
            seniority: 'senior',
            territories: ['North America', 'Europe'],
            skills: ['Team Management', 'Enterprise Sales'],
            isActive: true,
            isAvailable: true,
            maxLeads: 10,
            timezone: 'America/New_York',
            workingHours: {
                monday: { start: '08:00', end: '18:00' },
                tuesday: { start: '08:00', end: '18:00' },
                wednesday: { start: '08:00', end: '18:00' },
                thursday: { start: '08:00', end: '18:00' },
                friday: { start: '08:00', end: '18:00' },
            },
            createdAt: new Date(),
            updatedAt: new Date(),
        },
    ];
    async findById(id) {
        return this.users.find(user => user.id === id) || null;
    }
    async findByTeamId(teamId, filters) {
        let users = this.users.filter(user => user.teamId === teamId);
        if (filters) {
            users = this.applyFilters(users, filters);
        }
        return users;
    }
    async findAll(filters) {
        let users = [...this.users];
        if (filters) {
            users = this.applyFilters(users, filters);
        }
        return users;
    }
    async create(userData) {
        const newUser = {
            id: (Math.max(...this.users.map(u => parseInt(u.id))) + 1).toString(),
            ...userData,
            isActive: true,
            isAvailable: true,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.users.push(newUser);
        return newUser;
    }
    async update(id, userData) {
        const index = this.users.findIndex(user => user.id === id);
        if (index === -1) {
            throw new Error(`User with ID ${id} not found`);
        }
        const updatedUser = {
            ...this.users[index],
            ...userData,
            updatedAt: new Date(),
        };
        this.users[index] = updatedUser;
        return updatedUser;
    }
    async delete(id) {
        const index = this.users.findIndex(user => user.id === id);
        if (index === -1) {
            return false;
        }
        this.users.splice(index, 1);
        return true;
    }
    async findByRole(role) {
        return this.users.filter(user => user.role === role);
    }
    async findAvailableUsers(teamId) {
        let users = this.users.filter(user => user.isActive && user.isAvailable);
        if (teamId) {
            users = users.filter(user => user.teamId === teamId);
        }
        return users;
    }
    async updateAvailability(id, isAvailable) {
        const user = this.users.find(u => u.id === id);
        if (!user) {
            throw new Error(`User with ID ${id} not found`);
        }
        user.isAvailable = isAvailable;
        user.updatedAt = new Date();
    }
    async getUserWorkloadStats(id) {
        return {
            totalLeads: 15,
            activeLeads: 12,
            convertedLeads: 3,
            totalValue: 150000,
            conversionRate: 20,
            averageLeadAge: 14,
            lastActivity: new Date(),
        };
    }
    async findByTerritory(territory) {
        return this.users.filter(user => user.territories && user.territories.includes(territory));
    }
    applyFilters(users, filters) {
        let filteredUsers = users;
        if (filters.isActive !== undefined) {
            filteredUsers = filteredUsers.filter(user => user.isActive === filters.isActive);
        }
        if (filters.isAvailable !== undefined) {
            filteredUsers = filteredUsers.filter(user => user.isAvailable === filters.isAvailable);
        }
        if (filters.role) {
            filteredUsers = filteredUsers.filter(user => user.role === filters.role);
        }
        if (filters.seniority) {
            filteredUsers = filteredUsers.filter(user => user.seniority === filters.seniority);
        }
        if (filters.territory) {
            filteredUsers = filteredUsers.filter(user => user.territories && user.territories.includes(filters.territory));
        }
        if (filters.skill) {
            filteredUsers = filteredUsers.filter(user => user.skills && user.skills.includes(filters.skill));
        }
        return filteredUsers;
    }
};
exports.MongoUserRepository = MongoUserRepository;
exports.MongoUserRepository = MongoUserRepository = __decorate([
    (0, common_1.Injectable)()
], MongoUserRepository);
//# sourceMappingURL=user.repository.impl.js.map