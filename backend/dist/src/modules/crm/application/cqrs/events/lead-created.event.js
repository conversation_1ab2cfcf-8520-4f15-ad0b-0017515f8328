"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadCreatedEvent = void 0;
class LeadCreatedEvent {
    leadId;
    lead;
    createdBy;
    commandMetadata;
    timestamp;
    constructor(leadId, lead, createdBy, commandMetadata, timestamp = new Date()) {
        this.leadId = leadId;
        this.lead = lead;
        this.createdBy = createdBy;
        this.commandMetadata = commandMetadata;
        this.timestamp = timestamp;
    }
    getMetadata() {
        return {
            eventType: 'LeadCreatedEvent',
            leadId: this.leadId,
            leadName: this.lead.name,
            leadType: this.lead.type.value,
            priority: this.lead.priority.value,
            source: this.lead.source,
            hasRevenue: !!this.lead.expectedRevenue,
            isAssigned: !!this.lead.assignedUserId,
            hasTeam: !!this.lead.teamId,
            createdBy: this.createdBy,
            timestamp: this.timestamp.toISOString(),
            commandMetadata: this.commandMetadata,
        };
    }
    getPayload() {
        return {
            eventId: `lead-created-${this.leadId}-${this.timestamp.getTime()}`,
            eventType: 'lead.created',
            version: '1.0',
            timestamp: this.timestamp.toISOString(),
            data: {
                lead: this.lead.toPlainObject(),
                createdBy: this.createdBy,
                metadata: this.getMetadata(),
            },
        };
    }
    toPlainObject() {
        return {
            leadId: this.leadId,
            lead: this.lead.toPlainObject(),
            createdBy: this.createdBy,
            commandMetadata: this.commandMetadata,
            timestamp: this.timestamp,
            metadata: this.getMetadata(),
            payload: this.getPayload(),
        };
    }
}
exports.LeadCreatedEvent = LeadCreatedEvent;
//# sourceMappingURL=lead-created.event.js.map