import { IEventHandler } from '@nestjs/cqrs';
import { LeadCreatedEvent } from '../lead-created.event';
export declare class LeadCreatedHandler implements IEventHandler<LeadCreatedEvent> {
    private readonly logger;
    constructor();
    handle(event: LeadCreatedEvent): Promise<void>;
    private notifyAssignedUser;
    private updateAnalytics;
    private handleAutoAssignment;
    private sendWelcomeEmail;
    private logAuditTrail;
    private updateLeadScoring;
    private triggerWorkflowAutomation;
}
