"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadConvertedToOpportunityEvent = void 0;
class LeadConvertedToOpportunityEvent {
    leadId;
    opportunityId;
    opportunity;
    convertedBy;
    reason;
    commandMetadata;
    timestamp;
    constructor(leadId, opportunityId, opportunity, convertedBy, reason, commandMetadata, timestamp = new Date()) {
        this.leadId = leadId;
        this.opportunityId = opportunityId;
        this.opportunity = opportunity;
        this.convertedBy = convertedBy;
        this.reason = reason;
        this.commandMetadata = commandMetadata;
        this.timestamp = timestamp;
    }
    getMetadata() {
        return {
            eventType: 'LeadConvertedToOpportunityEvent',
            leadId: this.leadId,
            opportunityId: this.opportunityId,
            opportunityName: this.opportunity.name,
            expectedRevenue: this.opportunity.expectedRevenue,
            probability: this.opportunity.probability,
            weightedRevenue: this.opportunity.getWeightedRevenue(),
            convertedBy: this.convertedBy,
            reason: this.reason,
            timestamp: this.timestamp.toISOString(),
            commandMetadata: this.commandMetadata,
        };
    }
    getPayload() {
        return {
            eventId: `lead-converted-${this.leadId}-${this.opportunityId}-${this.timestamp.getTime()}`,
            eventType: 'lead.converted_to_opportunity',
            version: '1.0',
            timestamp: this.timestamp.toISOString(),
            data: {
                leadId: this.leadId,
                opportunity: this.opportunity.toPlainObject(),
                convertedBy: this.convertedBy,
                reason: this.reason,
                metadata: this.getMetadata(),
            },
        };
    }
    toPlainObject() {
        return {
            leadId: this.leadId,
            opportunityId: this.opportunityId,
            opportunity: this.opportunity.toPlainObject(),
            convertedBy: this.convertedBy,
            reason: this.reason,
            commandMetadata: this.commandMetadata,
            timestamp: this.timestamp,
            metadata: this.getMetadata(),
            payload: this.getPayload(),
        };
    }
}
exports.LeadConvertedToOpportunityEvent = LeadConvertedToOpportunityEvent;
//# sourceMappingURL=lead-converted-to-opportunity.event.js.map