"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var LeadCreatedHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadCreatedHandler = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
const lead_created_event_1 = require("../lead-created.event");
let LeadCreatedHandler = LeadCreatedHandler_1 = class LeadCreatedHandler {
    logger = new common_1.Logger(LeadCreatedHandler_1.name);
    constructor() { }
    async handle(event) {
        this.logger.log(`Handling LeadCreatedEvent for lead ID: ${event.leadId}`);
        try {
            await this.notifyAssignedUser(event);
            await this.updateAnalytics(event);
            await this.handleAutoAssignment(event);
            await this.sendWelcomeEmail(event);
            await this.logAuditTrail(event);
            await this.updateLeadScoring(event);
            await this.triggerWorkflowAutomation(event);
            this.logger.log(`Successfully handled LeadCreatedEvent for lead ID: ${event.leadId}`);
        }
        catch (error) {
            this.logger.error(`Failed to handle LeadCreatedEvent for lead ID: ${event.leadId}`, error);
        }
    }
    async notifyAssignedUser(event) {
        if (!event.lead.assignedUserId) {
            return;
        }
        try {
            this.logger.log(`Sending notification to user ${event.lead.assignedUserId} for new lead ${event.leadId}`);
            const notification = {
                userId: event.lead.assignedUserId,
                type: 'new_lead_assigned',
                title: 'New Lead Assigned',
                message: `You have been assigned a new lead: ${event.lead.name}`,
                data: {
                    leadId: event.leadId,
                    leadName: event.lead.name,
                    priority: event.lead.priority.label,
                    source: event.lead.source,
                },
                timestamp: event.timestamp,
            };
        }
        catch (error) {
            this.logger.error(`Failed to notify assigned user for lead ${event.leadId}`, error);
        }
    }
    async updateAnalytics(event) {
        try {
            this.logger.log(`Updating analytics for new lead ${event.leadId}`);
            const analyticsData = {
                eventType: 'lead_created',
                leadId: event.leadId,
                source: event.lead.source,
                priority: event.lead.priority.value,
                type: event.lead.type.value,
                hasRevenue: !!event.lead.expectedRevenue,
                revenue: event.lead.expectedRevenue,
                teamId: event.lead.teamId,
                assignedUserId: event.lead.assignedUserId,
                timestamp: event.timestamp,
            };
        }
        catch (error) {
            this.logger.error(`Failed to update analytics for lead ${event.leadId}`, error);
        }
    }
    async handleAutoAssignment(event) {
        if (event.lead.assignedUserId || !event.lead.teamId) {
            return;
        }
        try {
            this.logger.log(`Checking auto-assignment for lead ${event.leadId}`);
        }
        catch (error) {
            this.logger.error(`Failed to handle auto-assignment for lead ${event.leadId}`, error);
        }
    }
    async sendWelcomeEmail(event) {
        if (!event.lead.contactInfo.email) {
            return;
        }
        try {
            this.logger.log(`Sending welcome email to lead ${event.leadId}`);
            const emailData = {
                to: event.lead.contactInfo.email,
                subject: 'Welcome! We received your inquiry',
                template: 'lead_welcome',
                data: {
                    leadName: event.lead.name,
                    companyName: event.lead.contactInfo.company,
                    source: event.lead.source,
                },
            };
        }
        catch (error) {
            this.logger.error(`Failed to send welcome email for lead ${event.leadId}`, error);
        }
    }
    async logAuditTrail(event) {
        try {
            this.logger.log(`Logging audit trail for lead ${event.leadId}`);
            const auditEntry = {
                entityType: 'lead',
                entityId: event.leadId,
                action: 'created',
                userId: event.createdBy,
                timestamp: event.timestamp,
                data: event.getMetadata(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to log audit trail for lead ${event.leadId}`, error);
        }
    }
    async updateLeadScoring(event) {
        try {
            this.logger.log(`Updating lead scoring for lead ${event.leadId}`);
            const score = event.lead.calculateScore();
            if (score >= 80) {
                this.logger.log(`High-score lead detected: ${event.leadId} (score: ${score})`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to update lead scoring for lead ${event.leadId}`, error);
        }
    }
    async triggerWorkflowAutomation(event) {
        try {
            this.logger.log(`Triggering workflow automation for lead ${event.leadId}`);
        }
        catch (error) {
            this.logger.error(`Failed to trigger workflow automation for lead ${event.leadId}`, error);
        }
    }
};
exports.LeadCreatedHandler = LeadCreatedHandler;
exports.LeadCreatedHandler = LeadCreatedHandler = LeadCreatedHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, cqrs_1.EventsHandler)(lead_created_event_1.LeadCreatedEvent),
    __metadata("design:paramtypes", [])
], LeadCreatedHandler);
//# sourceMappingURL=lead-created.handler.js.map