import { IEvent } from '@nestjs/cqrs';
import { Lead } from '../../../domain/entities/lead.entity';
export declare class LeadCreatedEvent implements IEvent {
    readonly leadId: number;
    readonly lead: Lead;
    readonly createdBy?: number | undefined;
    readonly commandMetadata?: any | undefined;
    readonly timestamp: Date;
    constructor(leadId: number, lead: Lead, createdBy?: number | undefined, commandMetadata?: any | undefined, timestamp?: Date);
    getMetadata(): {
        eventType: string;
        leadId: number;
        leadName: string;
        leadType: string;
        priority: number;
        source: string;
        hasRevenue: boolean;
        isAssigned: boolean;
        hasTeam: boolean;
        createdBy: number | undefined;
        timestamp: string;
        commandMetadata: any;
    };
    getPayload(): {
        eventId: string;
        eventType: string;
        version: string;
        timestamp: string;
        data: {
            lead: {
                id: number;
                name: string;
                contactInfo: Record<string, any>;
                status: string;
                source: string;
                type: {
                    value: string;
                    label: string;
                    description: string;
                    color: string;
                    cssClass: string;
                    icon: string;
                    badgeVariant: string;
                    isLead: boolean;
                    isOpportunity: boolean;
                    canConvertToOpportunity: boolean;
                    requiresQualification: boolean;
                    canHaveRevenueForecast: boolean;
                    canHaveProbability: boolean;
                    canBeInPipeline: boolean;
                    defaultProbability: number;
                };
                priority: {
                    value: number;
                    label: string;
                    color: string;
                    cssClass: string;
                    icon: string;
                    requiresImmediateAttention: boolean;
                };
                expectedRevenue: number | undefined;
                probability: number | undefined;
                revenueForecast: {
                    expectedRevenue: number;
                    probability: number;
                    currency: string;
                    weightedRevenue: number;
                    confidenceLevel: "high" | "low" | "medium" | "very-high";
                    isRealistic: boolean;
                    isOptimistic: boolean;
                    isConservative: boolean;
                    isHighValue: boolean;
                    riskLevel: "high" | "low" | "medium";
                    forecastCategory: "commit" | "best-case" | "pipeline" | "upside";
                    revenueRange: {
                        min: number;
                        max: number;
                        expected: number;
                    };
                    confidenceColor: string;
                    formattedRevenue: string;
                    formattedWeightedRevenue: string;
                } | undefined;
                weightedRevenue: number;
                description: string | undefined;
                tags: string[];
                assignedUserId: number | undefined;
                companyId: number | undefined;
                teamId: number | undefined;
                partnerId: number | undefined;
                stageId: number | undefined;
                lostReasonId: number | undefined;
                campaignId: number | undefined;
                sourceId: number | undefined;
                mediumId: number | undefined;
                dateDeadline: Date | undefined;
                daysUntilDeadline: number | null;
                isOverdue: boolean;
                score: number;
                isQualified: boolean;
                canConvert: boolean;
                requiresImmediateAttention: boolean;
                createdAt: Date | undefined;
                updatedAt: Date | undefined;
            };
            createdBy: number | undefined;
            metadata: {
                eventType: string;
                leadId: number;
                leadName: string;
                leadType: string;
                priority: number;
                source: string;
                hasRevenue: boolean;
                isAssigned: boolean;
                hasTeam: boolean;
                createdBy: number | undefined;
                timestamp: string;
                commandMetadata: any;
            };
        };
    };
    toPlainObject(): {
        leadId: number;
        lead: {
            id: number;
            name: string;
            contactInfo: Record<string, any>;
            status: string;
            source: string;
            type: {
                value: string;
                label: string;
                description: string;
                color: string;
                cssClass: string;
                icon: string;
                badgeVariant: string;
                isLead: boolean;
                isOpportunity: boolean;
                canConvertToOpportunity: boolean;
                requiresQualification: boolean;
                canHaveRevenueForecast: boolean;
                canHaveProbability: boolean;
                canBeInPipeline: boolean;
                defaultProbability: number;
            };
            priority: {
                value: number;
                label: string;
                color: string;
                cssClass: string;
                icon: string;
                requiresImmediateAttention: boolean;
            };
            expectedRevenue: number | undefined;
            probability: number | undefined;
            revenueForecast: {
                expectedRevenue: number;
                probability: number;
                currency: string;
                weightedRevenue: number;
                confidenceLevel: "high" | "low" | "medium" | "very-high";
                isRealistic: boolean;
                isOptimistic: boolean;
                isConservative: boolean;
                isHighValue: boolean;
                riskLevel: "high" | "low" | "medium";
                forecastCategory: "commit" | "best-case" | "pipeline" | "upside";
                revenueRange: {
                    min: number;
                    max: number;
                    expected: number;
                };
                confidenceColor: string;
                formattedRevenue: string;
                formattedWeightedRevenue: string;
            } | undefined;
            weightedRevenue: number;
            description: string | undefined;
            tags: string[];
            assignedUserId: number | undefined;
            companyId: number | undefined;
            teamId: number | undefined;
            partnerId: number | undefined;
            stageId: number | undefined;
            lostReasonId: number | undefined;
            campaignId: number | undefined;
            sourceId: number | undefined;
            mediumId: number | undefined;
            dateDeadline: Date | undefined;
            daysUntilDeadline: number | null;
            isOverdue: boolean;
            score: number;
            isQualified: boolean;
            canConvert: boolean;
            requiresImmediateAttention: boolean;
            createdAt: Date | undefined;
            updatedAt: Date | undefined;
        };
        createdBy: number | undefined;
        commandMetadata: any;
        timestamp: Date;
        metadata: {
            eventType: string;
            leadId: number;
            leadName: string;
            leadType: string;
            priority: number;
            source: string;
            hasRevenue: boolean;
            isAssigned: boolean;
            hasTeam: boolean;
            createdBy: number | undefined;
            timestamp: string;
            commandMetadata: any;
        };
        payload: {
            eventId: string;
            eventType: string;
            version: string;
            timestamp: string;
            data: {
                lead: {
                    id: number;
                    name: string;
                    contactInfo: Record<string, any>;
                    status: string;
                    source: string;
                    type: {
                        value: string;
                        label: string;
                        description: string;
                        color: string;
                        cssClass: string;
                        icon: string;
                        badgeVariant: string;
                        isLead: boolean;
                        isOpportunity: boolean;
                        canConvertToOpportunity: boolean;
                        requiresQualification: boolean;
                        canHaveRevenueForecast: boolean;
                        canHaveProbability: boolean;
                        canBeInPipeline: boolean;
                        defaultProbability: number;
                    };
                    priority: {
                        value: number;
                        label: string;
                        color: string;
                        cssClass: string;
                        icon: string;
                        requiresImmediateAttention: boolean;
                    };
                    expectedRevenue: number | undefined;
                    probability: number | undefined;
                    revenueForecast: {
                        expectedRevenue: number;
                        probability: number;
                        currency: string;
                        weightedRevenue: number;
                        confidenceLevel: "high" | "low" | "medium" | "very-high";
                        isRealistic: boolean;
                        isOptimistic: boolean;
                        isConservative: boolean;
                        isHighValue: boolean;
                        riskLevel: "high" | "low" | "medium";
                        forecastCategory: "commit" | "best-case" | "pipeline" | "upside";
                        revenueRange: {
                            min: number;
                            max: number;
                            expected: number;
                        };
                        confidenceColor: string;
                        formattedRevenue: string;
                        formattedWeightedRevenue: string;
                    } | undefined;
                    weightedRevenue: number;
                    description: string | undefined;
                    tags: string[];
                    assignedUserId: number | undefined;
                    companyId: number | undefined;
                    teamId: number | undefined;
                    partnerId: number | undefined;
                    stageId: number | undefined;
                    lostReasonId: number | undefined;
                    campaignId: number | undefined;
                    sourceId: number | undefined;
                    mediumId: number | undefined;
                    dateDeadline: Date | undefined;
                    daysUntilDeadline: number | null;
                    isOverdue: boolean;
                    score: number;
                    isQualified: boolean;
                    canConvert: boolean;
                    requiresImmediateAttention: boolean;
                    createdAt: Date | undefined;
                    updatedAt: Date | undefined;
                };
                createdBy: number | undefined;
                metadata: {
                    eventType: string;
                    leadId: number;
                    leadName: string;
                    leadType: string;
                    priority: number;
                    source: string;
                    hasRevenue: boolean;
                    isAssigned: boolean;
                    hasTeam: boolean;
                    createdBy: number | undefined;
                    timestamp: string;
                    commandMetadata: any;
                };
            };
        };
    };
}
