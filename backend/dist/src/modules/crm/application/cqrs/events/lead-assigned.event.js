"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadAssignedEvent = void 0;
class LeadAssignedEvent {
    leadId;
    assignedUserId;
    assignedTeamId;
    assignedBy;
    assignedAt;
    constructor(leadId, assignedUserId, assignedTeamId, assignedBy, assignedAt = new Date()) {
        this.leadId = leadId;
        this.assignedUserId = assignedUserId;
        this.assignedTeamId = assignedTeamId;
        this.assignedBy = assignedBy;
        this.assignedAt = assignedAt;
    }
}
exports.LeadAssignedEvent = LeadAssignedEvent;
//# sourceMappingURL=lead-assigned.event.js.map