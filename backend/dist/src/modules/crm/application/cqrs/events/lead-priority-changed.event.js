"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadPriorityChangedEvent = void 0;
class LeadPriorityChangedEvent {
    leadId;
    previousPriority;
    newPriority;
    changedBy;
    changedAt;
    constructor(leadId, previousPriority, newPriority, changedBy, changedAt = new Date()) {
        this.leadId = leadId;
        this.previousPriority = previousPriority;
        this.newPriority = newPriority;
        this.changedBy = changedBy;
        this.changedAt = changedAt;
    }
}
exports.LeadPriorityChangedEvent = LeadPriorityChangedEvent;
//# sourceMappingURL=lead-priority-changed.event.js.map