"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrmCqrsModule = void 0;
const common_1 = require("@nestjs/common");
const cqrs_1 = require("@nestjs/cqrs");
const event_emitter_1 = require("@nestjs/event-emitter");
const create_lead_handler_1 = require("./commands/handlers/create-lead.handler");
const update_lead_handler_1 = require("./commands/handlers/update-lead.handler");
const convert_lead_to_opportunity_handler_1 = require("./commands/handlers/convert-lead-to-opportunity.handler");
const update_lead_priority_handler_1 = require("./commands/handlers/update-lead-priority.handler");
const delete_lead_handler_1 = require("./commands/handlers/delete-lead.handler");
const assign_lead_to_user_handler_1 = require("./commands/handlers/assign-lead-to-user.handler");
const assign_lead_to_team_handler_1 = require("./commands/handlers/assign-lead-to-team.handler");
const update_revenue_forecast_handler_1 = require("./commands/handlers/update-revenue-forecast.handler");
const set_lead_deadline_handler_1 = require("./commands/handlers/set-lead-deadline.handler");
const add_lead_tag_handler_1 = require("./commands/handlers/add-lead-tag.handler");
const remove_lead_tag_handler_1 = require("./commands/handlers/remove-lead-tag.handler");
const get_lead_by_id_handler_1 = require("./queries/handlers/get-lead-by-id.handler");
const get_leads_by_filters_handler_1 = require("./queries/handlers/get-leads-by-filters.handler");
const get_pipeline_analytics_handler_1 = require("./queries/handlers/get-pipeline-analytics.handler");
const get_lead_statistics_handler_1 = require("./queries/handlers/get-lead-statistics.handler");
const get_overdue_leads_handler_1 = require("./queries/handlers/get-overdue-leads.handler");
const get_leads_requiring_attention_handler_1 = require("./queries/handlers/get-leads-requiring-attention.handler");
const search_leads_handler_1 = require("./queries/handlers/search-leads.handler");
const lead_created_handler_1 = require("./events/handlers/lead-created.handler");
const lead_status_changed_handler_1 = require("./events/handlers/lead-status-changed.handler");
const lead_converted_to_opportunity_handler_1 = require("./events/handlers/lead-converted-to-opportunity.handler");
const lead_assigned_handler_1 = require("./events/handlers/lead-assigned.handler");
const lead_priority_changed_handler_1 = require("./events/handlers/lead-priority-changed.handler");
const lead_management_saga_1 = require("./sagas/lead-management.saga");
let CrmCqrsModule = class CrmCqrsModule {
};
exports.CrmCqrsModule = CrmCqrsModule;
exports.CrmCqrsModule = CrmCqrsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            cqrs_1.CqrsModule,
            event_emitter_1.EventEmitterModule.forRoot({
                global: true,
                wildcard: false,
                delimiter: '.',
                newListener: false,
                removeListener: false,
                maxListeners: 10,
                verboseMemoryLeak: false,
                ignoreErrors: false,
            }),
        ],
        providers: [
            create_lead_handler_1.CreateLeadHandler,
            update_lead_handler_1.UpdateLeadHandler,
            delete_lead_handler_1.DeleteLeadHandler,
            convert_lead_to_opportunity_handler_1.ConvertLeadToOpportunityHandler,
            update_lead_priority_handler_1.UpdateLeadPriorityHandler,
            assign_lead_to_user_handler_1.AssignLeadToUserHandler,
            assign_lead_to_team_handler_1.AssignLeadToTeamHandler,
            update_revenue_forecast_handler_1.UpdateRevenueForecastHandler,
            set_lead_deadline_handler_1.SetLeadDeadlineHandler,
            add_lead_tag_handler_1.AddLeadTagHandler,
            remove_lead_tag_handler_1.RemoveLeadTagHandler,
            get_lead_by_id_handler_1.GetLeadByIdHandler,
            get_leads_by_filters_handler_1.GetLeadsByFiltersHandler,
            GetLeadsByTeamHandler,
            get_pipeline_analytics_handler_1.GetPipelineAnalyticsHandler,
            get_lead_statistics_handler_1.GetLeadStatisticsHandler,
            get_overdue_leads_handler_1.GetOverdueLeadsHandler,
            get_leads_requiring_attention_handler_1.GetLeadsRequiringAttentionHandler,
            search_leads_handler_1.SearchLeadsHandler,
            lead_created_handler_1.LeadCreatedHandler,
            lead_status_changed_handler_1.LeadStatusChangedHandler,
            lead_converted_to_opportunity_handler_1.LeadConvertedToOpportunityHandler,
            lead_assigned_handler_1.LeadAssignedHandler,
            lead_priority_changed_handler_1.LeadPriorityChangedHandler,
            lead_management_saga_1.LeadManagementSaga,
        ],
        exports: [
            cqrs_1.CqrsModule,
            event_emitter_1.EventEmitterModule,
        ],
    })
], CrmCqrsModule);
//# sourceMappingURL=cqrs.module.js.map