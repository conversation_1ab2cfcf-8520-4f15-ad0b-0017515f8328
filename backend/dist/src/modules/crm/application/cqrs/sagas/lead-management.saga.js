"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var LeadManagementSaga_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadManagementSaga = void 0;
const common_1 = require("@nestjs/common");
const cqrs_1 = require("@nestjs/cqrs");
const operators_1 = require("rxjs/operators");
const lead_created_event_1 = require("../events/lead-created.event");
const lead_converted_to_opportunity_event_1 = require("../events/lead-converted-to-opportunity.event");
const update_lead_command_1 = require("../commands/update-lead.command");
let LeadManagementSaga = LeadManagementSaga_1 = class LeadManagementSaga {
    logger = new common_1.Logger(LeadManagementSaga_1.name);
    leadNurturingWorkflow = (events$) => {
        return events$.pipe((0, cqrs_1.ofType)(lead_created_event_1.LeadCreatedEvent), (0, operators_1.delay)(1000), (0, operators_1.map)((event) => {
            this.logger.log(`Starting lead nurturing workflow for lead: ${event.leadId}`);
            return new update_lead_command_1.UpdateLeadCommand(event.leadId, { lastProcessed: new Date() });
        }));
    };
    opportunityQualificationWorkflow = (events$) => {
        return events$.pipe((0, cqrs_1.ofType)(lead_converted_to_opportunity_event_1.LeadConvertedToOpportunityEvent), (0, operators_1.delay)(500), (0, operators_1.map)((event) => {
            this.logger.log(`Starting opportunity qualification workflow for opportunity: ${event.opportunityId}`);
            if (event.opportunity.expectedRevenue && event.opportunity.expectedRevenue > 50000) {
                this.logger.log(`High-value opportunity detected: ${event.opportunityId} (${event.opportunity.expectedRevenue})`);
            }
            return new update_lead_command_1.UpdateLeadCommand(event.leadId, { opportunityId: event.opportunityId });
        }));
    };
    leadScoringWorkflow = (events$) => {
        return events$.pipe((0, cqrs_1.ofType)(lead_created_event_1.LeadCreatedEvent), (0, operators_1.delay)(2000), (0, operators_1.map)((event) => {
            this.logger.log(`Starting lead scoring workflow for lead: ${event.leadId}`);
            const score = event.lead.calculateScore();
            if (score >= 80) {
                this.logger.log(`High-priority lead detected: ${event.leadId} (score: ${score})`);
            }
            else if (score >= 60) {
                this.logger.log(`Medium-priority lead detected: ${event.leadId} (score: ${score})`);
            }
            else {
                this.logger.log(`Low-priority lead detected: ${event.leadId} (score: ${score})`);
            }
            return new update_lead_command_1.UpdateLeadCommand(event.leadId, { scoringProcessed: true });
        }));
    };
    teamAssignmentWorkflow = (events$) => {
        return events$.pipe((0, cqrs_1.ofType)(lead_created_event_1.LeadCreatedEvent), (0, operators_1.delay)(3000), (0, operators_1.map)((event) => {
            if (event.lead.assignedUserId || !event.lead.teamId) {
                return null;
            }
            this.logger.log(`Starting team assignment workflow for unassigned lead: ${event.leadId}`);
            return new update_lead_command_1.UpdateLeadCommand(event.leadId, { assignmentProcessed: true });
        }));
    };
    dataEnrichmentWorkflow = (events$) => {
        return events$.pipe((0, cqrs_1.ofType)(lead_created_event_1.LeadCreatedEvent), (0, operators_1.delay)(5000), (0, operators_1.map)((event) => {
            const needsEnrichment = this.checkIfEnrichmentNeeded(event.lead);
            if (!needsEnrichment) {
                return new update_lead_command_1.UpdateLeadCommand(event.leadId, { enrichmentNotNeeded: true });
            }
            this.logger.log(`Starting data enrichment workflow for lead: ${event.leadId}`);
            return new update_lead_command_1.UpdateLeadCommand(event.leadId, { enrichmentProcessed: true });
        }));
    };
    checkIfEnrichmentNeeded(lead) {
        const hasMinimalInfo = !lead.contactInfo.email ||
            !lead.contactInfo.phone ||
            !lead.contactInfo.company;
        return hasMinimalInfo;
    }
    complianceWorkflow = (events$) => {
        return events$.pipe((0, cqrs_1.ofType)(lead_created_event_1.LeadCreatedEvent), (0, operators_1.delay)(1000), (0, operators_1.map)((event) => {
            this.logger.log(`Starting compliance workflow for lead: ${event.leadId}`);
            return new update_lead_command_1.UpdateLeadCommand(event.leadId, { complianceProcessed: true });
        }));
    };
};
exports.LeadManagementSaga = LeadManagementSaga;
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], LeadManagementSaga.prototype, "leadNurturingWorkflow", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], LeadManagementSaga.prototype, "opportunityQualificationWorkflow", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], LeadManagementSaga.prototype, "leadScoringWorkflow", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], LeadManagementSaga.prototype, "teamAssignmentWorkflow", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], LeadManagementSaga.prototype, "dataEnrichmentWorkflow", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], LeadManagementSaga.prototype, "complianceWorkflow", void 0);
exports.LeadManagementSaga = LeadManagementSaga = LeadManagementSaga_1 = __decorate([
    (0, common_1.Injectable)()
], LeadManagementSaga);
//# sourceMappingURL=lead-management.saga.js.map