import { ICommand } from '@nestjs/cqrs';
import { Observable } from 'rxjs';
export declare class LeadManagementSaga {
    private readonly logger;
    leadNurturingWorkflow: (events$: Observable<any>) => Observable<ICommand>;
    opportunityQualificationWorkflow: (events$: Observable<any>) => Observable<ICommand>;
    leadScoringWorkflow: (events$: Observable<any>) => Observable<ICommand>;
    teamAssignmentWorkflow: (events$: Observable<any>) => Observable<ICommand>;
    dataEnrichmentWorkflow: (events$: Observable<any>) => Observable<ICommand>;
    private checkIfEnrichmentNeeded;
    complianceWorkflow: (events$: Observable<any>) => Observable<ICommand>;
}
