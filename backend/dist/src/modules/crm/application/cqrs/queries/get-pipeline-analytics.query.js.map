{"version": 3, "file": "get-pipeline-analytics.query.js", "sourceRoot": "", "sources": ["../../../../../../../src/modules/crm/application/cqrs/queries/get-pipeline-analytics.query.ts"], "names": [], "mappings": ";;;AAMA,MAAa,yBAAyB;IAElB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IAEA;IAlBlB,YACkB,MAAe,EACf,MAAe,EACf,QAAe,EACf,MAAa,EACb,sBAA+B,IAAI,EACnC,yBAAkC,IAAI,EACtC,qBAA8B,IAAI,EAClC,kBAA2B,IAAI,EAC/B,qBAA8B,KAAK,EAEnC,QAAmB,EACnB,cAAyB,EACzB,OAAkB,EAElB,OAA2D,EAC3D,eAAsD,EAEtD,WAAoB;QAjBpB,WAAM,GAAN,MAAM,CAAS;QACf,WAAM,GAAN,MAAM,CAAS;QACf,aAAQ,GAAR,QAAQ,CAAO;QACf,WAAM,GAAN,MAAM,CAAO;QACb,wBAAmB,GAAnB,mBAAmB,CAAgB;QACnC,2BAAsB,GAAtB,sBAAsB,CAAgB;QACtC,uBAAkB,GAAlB,kBAAkB,CAAgB;QAClC,oBAAe,GAAf,eAAe,CAAgB;QAC/B,uBAAkB,GAAlB,kBAAkB,CAAiB;QAEnC,aAAQ,GAAR,QAAQ,CAAW;QACnB,mBAAc,GAAd,cAAc,CAAW;QACzB,YAAO,GAAP,OAAO,CAAW;QAElB,YAAO,GAAP,OAAO,CAAoD;QAC3D,oBAAe,GAAf,eAAe,CAAuC;QAEtD,gBAAW,GAAX,WAAW,CAAS;QAGpC,IAAI,QAAQ,IAAI,MAAM,IAAI,QAAQ,GAAG,MAAM,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAGD,IAAI,CAAC,mBAAmB,IAAI,CAAC,sBAAsB,IAAI,CAAC,kBAAkB,IAAI,CAAC,eAAe,EAAE,CAAC;YAC/F,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAKD,WAAW;QACT,OAAO;YACL,SAAS,EAAE,2BAA2B;YACtC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE;gBAClC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE;aAC/B;YACD,OAAO,EAAE;gBACP,YAAY,EAAE,IAAI,CAAC,mBAAmB;gBACtC,eAAe,EAAE,IAAI,CAAC,sBAAsB;gBAC5C,WAAW,EAAE,IAAI,CAAC,kBAAkB;gBACpC,QAAQ,EAAE,IAAI,CAAC,eAAe;gBAC9B,WAAW,EAAE,IAAI,CAAC,kBAAkB;aACrC;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB;YACD,QAAQ,EAAE;gBACR,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,eAAe,EAAE,IAAI,CAAC,eAAe;aACtC;YACD,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;IAKD,WAAW;QACT,MAAM,KAAK,GAAG;YACZ,oBAAoB;YACpB,IAAI,CAAC,MAAM,IAAI,WAAW;YAC1B,IAAI,CAAC,MAAM,IAAI,WAAW;YAC1B,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU;YACxD,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ;YACpD,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YACxC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;YAChD,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;YACtC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,OAAO,IAAI,UAAU;YAC1B,IAAI,CAAC,eAAe,IAAI,SAAS;SAClC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAElB,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAKD,aAAa;QACX,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;YACnD,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;SAC7B,CAAC;IACJ,CAAC;CACF;AA/GD,8DA+GC"}