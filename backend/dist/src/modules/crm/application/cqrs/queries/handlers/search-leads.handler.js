"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchLeadsHandler = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
const search_leads_query_1 = require("../search-leads.query");
const injection_tokens_1 = require("../../../../domain/repositories/injection-tokens");
let SearchLeadsHandler = class SearchLeadsHandler {
    leadRepository;
    constructor(leadRepository) {
        this.leadRepository = leadRepository;
    }
    async execute(query) {
        const { searchTerm, filters, limit } = query;
        const searchFilters = {
            ...filters,
            searchTerm,
            limit: limit || 50,
        };
        return this.leadRepository.search(searchFilters);
    }
};
exports.SearchLeadsHandler = SearchLeadsHandler;
exports.SearchLeadsHandler = SearchLeadsHandler = __decorate([
    (0, cqrs_1.QueryHandler)(search_leads_query_1.SearchLeadsQuery),
    __param(0, (0, common_1.Inject)(injection_tokens_1.LEAD_REPOSITORY_TOKEN)),
    __metadata("design:paramtypes", [Object])
], SearchLeadsHandler);
//# sourceMappingURL=search-leads.handler.js.map