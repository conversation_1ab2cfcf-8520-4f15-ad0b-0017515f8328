import { <PERSON><PERSON>ueryHandler } from '@nestjs/cqrs';
import { GetPipelineAnalyticsQuery } from '../get-pipeline-analytics.query';
import { ILeadRepository } from '../../../../domain/repositories/lead.repository';
import { IStageRepository } from '../../../../domain/repositories/stage.repository';
import { ITeamRepository } from '../../../../domain/repositories/team.repository';
export interface PipelineAnalyticsResult {
    summary: {
        totalLeads: number;
        totalOpportunities: number;
        totalRevenue: number;
        weightedRevenue: number;
        averageDealSize: number;
        conversionRate: number;
        winRate: number;
        averageSalesCycle: number;
    };
    stageMetrics?: Array<{
        stageId: number;
        stageName: string;
        sequence: number;
        leadCount: number;
        opportunityCount: number;
        totalRevenue: number;
        weightedRevenue: number;
        averageProbability: number;
        averageTimeInStage: number;
        conversionRate: number;
        dropOffRate: number;
    }>;
    conversionRates?: {
        leadToOpportunity: number;
        opportunityToWon: number;
        overallConversion: number;
        byStage: Record<number, number>;
        byTeam: Record<number, number>;
        bySource: Record<string, number>;
    };
    bottlenecks?: Array<{
        stageId: number;
        stageName: string;
        averageTimeInStage: number;
        dropOffRate: number;
        severity: 'low' | 'medium' | 'high';
        recommendations: string[];
    }>;
    forecast?: {
        nextMonth: {
            expectedRevenue: number;
            weightedRevenue: number;
            expectedDeals: number;
        };
        nextQuarter: {
            expectedRevenue: number;
            weightedRevenue: number;
            expectedDeals: number;
        };
        byMonth: Array<{
            month: string;
            expectedRevenue: number;
            weightedRevenue: number;
            expectedDeals: number;
        }>;
    };
    trends?: {
        revenueGrowth: number;
        dealVelocityChange: number;
        conversionTrend: number;
        pipelineHealthScore: number;
    };
    comparisons?: {
        vsLastPeriod: {
            revenueChange: number;
            dealCountChange: number;
            conversionRateChange: number;
        };
        vsTeamAverage: {
            revenuePerformance: number;
            conversionPerformance: number;
            velocityPerformance: number;
        };
    };
    metadata: {
        generatedAt: Date;
        dataRange: {
            from?: Date;
            to?: Date;
        };
        filters: any;
        cacheKey: string;
    };
}
export declare class GetPipelineAnalyticsHandler implements IQueryHandler<GetPipelineAnalyticsQuery> {
    private readonly leadRepository;
    private readonly stageRepository;
    private readonly teamRepository;
    private readonly logger;
    constructor(leadRepository: ILeadRepository, stageRepository: IStageRepository, teamRepository: ITeamRepository);
    execute(query: GetPipelineAnalyticsQuery): Promise<PipelineAnalyticsResult>;
    private calculateBottleneckSeverity;
    private generateBottleneckRecommendations;
    private generateForecast;
    private calculateTrends;
    private generateComparisons;
}
