"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GetPipelineAnalyticsHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetPipelineAnalyticsHandler = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
const get_pipeline_analytics_query_1 = require("../get-pipeline-analytics.query");
let GetPipelineAnalyticsHandler = GetPipelineAnalyticsHandler_1 = class GetPipelineAnalyticsHandler {
    leadRepository;
    stageRepository;
    teamRepository;
    logger = new common_1.Logger(GetPipelineAnalyticsHandler_1.name);
    constructor(leadRepository, stageRepository, teamRepository) {
        this.leadRepository = leadRepository;
        this.stageRepository = stageRepository;
        this.teamRepository = teamRepository;
    }
    async execute(query) {
        this.logger.log(`Executing GetPipelineAnalyticsQuery for team: ${query.teamId || 'all'}`);
        try {
            const pipelineData = await this.leadRepository.getPipelineAnalytics(query.teamId);
            const statistics = await this.leadRepository.getStatistics({
                teamId: query.teamId,
                assignedUserId: query.userId,
                dateFrom: query.dateFrom,
                dateTo: query.dateTo,
            });
            const result = {
                summary: {
                    totalLeads: statistics.totalLeads,
                    totalOpportunities: statistics.totalOpportunities,
                    totalRevenue: statistics.totalRevenue,
                    weightedRevenue: statistics.weightedRevenue,
                    averageDealSize: statistics.averageDealSize,
                    conversionRate: statistics.conversionRate,
                    winRate: statistics.winRate,
                    averageSalesCycle: statistics.averageSalesCycle,
                },
                metadata: {
                    generatedAt: new Date(),
                    dataRange: {
                        from: query.dateFrom,
                        to: query.dateTo,
                    },
                    filters: {
                        teamId: query.teamId,
                        userId: query.userId,
                        stageIds: query.stageIds,
                        priorityLevels: query.priorityLevels,
                        sources: query.sources,
                    },
                    cacheKey: query.getCacheKey(),
                },
            };
            if (query.includeStageMetrics) {
                result.stageMetrics = pipelineData.stages.map(stage => ({
                    stageId: stage.stageId,
                    stageName: stage.stageName,
                    sequence: 0,
                    leadCount: stage.leadCount,
                    opportunityCount: stage.leadCount,
                    totalRevenue: stage.totalRevenue,
                    weightedRevenue: stage.weightedRevenue,
                    averageProbability: stage.averageProbability,
                    averageTimeInStage: stage.averageTimeInStage,
                    conversionRate: 0,
                    dropOffRate: 0,
                }));
            }
            if (query.includeConversionRates) {
                result.conversionRates = {
                    leadToOpportunity: statistics.conversionRate,
                    opportunityToWon: statistics.winRate,
                    overallConversion: statistics.conversionRate * statistics.winRate / 100,
                    byStage: pipelineData.conversionRates,
                    byTeam: statistics.byTeam,
                    bySource: statistics.bySource,
                };
            }
            if (query.includeBottlenecks) {
                result.bottlenecks = pipelineData.bottlenecks.map(bottleneck => ({
                    stageId: bottleneck.stageId,
                    stageName: bottleneck.stageName,
                    averageTimeInStage: bottleneck.averageTimeInStage,
                    dropOffRate: bottleneck.dropOffRate,
                    severity: this.calculateBottleneckSeverity(bottleneck.averageTimeInStage, bottleneck.dropOffRate),
                    recommendations: this.generateBottleneckRecommendations(bottleneck),
                }));
            }
            if (query.includeForecast) {
                result.forecast = await this.generateForecast(query, statistics);
            }
            if (query.includeComparisons) {
                result.trends = await this.calculateTrends(query, statistics);
                result.comparisons = await this.generateComparisons(query, statistics);
            }
            this.logger.log(`Successfully generated pipeline analytics for team: ${query.teamId || 'all'}`);
            return result;
        }
        catch (error) {
            this.logger.error(`Failed to get pipeline analytics`, error);
            if (error instanceof Error) {
                throw new Error(`Failed to get pipeline analytics: ${error.message}`);
            }
            throw new Error('Failed to get pipeline analytics: Unknown error');
        }
    }
    calculateBottleneckSeverity(averageTime, dropOffRate) {
        if (dropOffRate > 30 || averageTime > 30)
            return 'high';
        if (dropOffRate > 15 || averageTime > 14)
            return 'medium';
        return 'low';
    }
    generateBottleneckRecommendations(bottleneck) {
        const recommendations = [];
        if (bottleneck.averageTimeInStage > 30) {
            recommendations.push('Consider automating stage progression or adding follow-up reminders');
        }
        if (bottleneck.dropOffRate > 20) {
            recommendations.push('Review stage requirements and provide additional training to sales team');
        }
        return recommendations;
    }
    async generateForecast(query, statistics) {
        const monthlyGrowthRate = 0.05;
        return {
            nextMonth: {
                expectedRevenue: statistics.totalRevenue * (1 + monthlyGrowthRate),
                weightedRevenue: statistics.weightedRevenue * (1 + monthlyGrowthRate),
                expectedDeals: Math.round(statistics.totalOpportunities * 1.1),
            },
            nextQuarter: {
                expectedRevenue: statistics.totalRevenue * Math.pow(1 + monthlyGrowthRate, 3),
                weightedRevenue: statistics.weightedRevenue * Math.pow(1 + monthlyGrowthRate, 3),
                expectedDeals: Math.round(statistics.totalOpportunities * 1.3),
            },
            byMonth: [],
        };
    }
    async calculateTrends(query, statistics) {
        return {
            revenueGrowth: 5.2,
            dealVelocityChange: -2.1,
            conversionTrend: 1.8,
            pipelineHealthScore: 78,
        };
    }
    async generateComparisons(query, statistics) {
        return {
            vsLastPeriod: {
                revenueChange: 12.5,
                dealCountChange: 8.3,
                conversionRateChange: 2.1,
            },
            vsTeamAverage: {
                revenuePerformance: 115,
                conversionPerformance: 108,
                velocityPerformance: 95,
            },
        };
    }
};
exports.GetPipelineAnalyticsHandler = GetPipelineAnalyticsHandler;
exports.GetPipelineAnalyticsHandler = GetPipelineAnalyticsHandler = GetPipelineAnalyticsHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, cqrs_1.QueryHandler)(get_pipeline_analytics_query_1.GetPipelineAnalyticsQuery),
    __metadata("design:paramtypes", [Object, Object, Object])
], GetPipelineAnalyticsHandler);
//# sourceMappingURL=get-pipeline-analytics.handler.js.map