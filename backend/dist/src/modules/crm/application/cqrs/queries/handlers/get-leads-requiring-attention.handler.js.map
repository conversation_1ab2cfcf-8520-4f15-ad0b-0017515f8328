{"version": 3, "file": "get-leads-requiring-attention.handler.js", "sourceRoot": "", "sources": ["../../../../../../../../src/modules/crm/application/cqrs/queries/handlers/get-leads-requiring-attention.handler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,uCAA2D;AAC3D,2CAAwC;AACxC,gGAAyF;AAEzF,uFAA2F;AAQpF,IAAM,iCAAiC,GAAvC,MAAM,iCAAiC;IAGzB;IAFnB,YAEmB,cAA+B;QAA/B,mBAAc,GAAd,cAAc,CAAiB;IAC/C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAAsC;QAClD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;QAGjC,MAAM,OAAO,GAAQ,EAAE,CAAC;QAExB,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QAC1B,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC;QAClC,CAAC;QAGD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAG/D,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEO,iBAAiB,CAAC,IAAU;QAClC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAGvB,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,KAAK,MAAM,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,kBAAkB,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YAC5F,IAAI,kBAAkB,IAAI,EAAE,IAAI,kBAAkB,GAAG,CAAC,EAAE,CAAC;gBACvD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAGD,MAAM,gBAAgB,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAC5F,IAAI,gBAAgB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YAChE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAA;AA7DY,8EAAiC;4CAAjC,iCAAiC;IAD7C,IAAA,mBAAY,EAAC,qEAA+B,CAAC;IAGzC,WAAA,IAAA,eAAM,EAAC,wCAAqB,CAAC,CAAA;;GAFrB,iCAAiC,CA6D7C"}