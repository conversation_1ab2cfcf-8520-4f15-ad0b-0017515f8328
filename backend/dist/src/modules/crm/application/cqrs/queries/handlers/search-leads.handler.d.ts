import { IQueryHandler } from '@nestjs/cqrs';
import { SearchLeadsQuery } from '../search-leads.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
export declare class Search<PERSON>eadsHandler implements IQueryHandler<SearchLeadsQuery> {
    private readonly leadRepository;
    constructor(leadRepository: ILeadRepository);
    execute(query: SearchLeadsQuery): Promise<Lead[]>;
}
