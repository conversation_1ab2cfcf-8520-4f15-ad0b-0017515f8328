"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetLeadStatisticsHandler = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
const get_lead_statistics_query_1 = require("../get-lead-statistics.query");
const injection_tokens_1 = require("../../../../domain/repositories/injection-tokens");
let GetLeadStatisticsHandler = class GetLeadStatisticsHandler {
    leadRepository;
    constructor(leadRepository) {
        this.leadRepository = leadRepository;
    }
    async execute(query) {
        const { filters } = query;
        const leads = await this.leadRepository.findByFilters(filters);
        const totalLeads = leads.length;
        const qualifiedLeads = leads.filter(lead => lead.isQualified()).length;
        const convertedLeads = leads.filter(lead => lead.isConverted()).length;
        const totalRevenue = leads.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
        const averageRevenue = totalLeads > 0 ? totalRevenue / totalLeads : 0;
        const conversionRate = totalLeads > 0 ? (convertedLeads / totalLeads) * 100 : 0;
        const statusDistribution = leads.reduce((acc, lead) => {
            const status = lead.status?.value || 'unknown';
            acc[status] = (acc[status] || 0) + 1;
            return acc;
        }, {});
        const sourceDistribution = leads.reduce((acc, lead) => {
            const source = lead.source || 'unknown';
            acc[source] = (acc[source] || 0) + 1;
            return acc;
        }, {});
        return {
            totalLeads,
            qualifiedLeads,
            convertedLeads,
            totalRevenue,
            averageRevenue,
            conversionRate,
            statusDistribution,
            sourceDistribution,
        };
    }
};
exports.GetLeadStatisticsHandler = GetLeadStatisticsHandler;
exports.GetLeadStatisticsHandler = GetLeadStatisticsHandler = __decorate([
    (0, cqrs_1.QueryHandler)(get_lead_statistics_query_1.GetLeadStatisticsQuery),
    __param(0, (0, common_1.Inject)(injection_tokens_1.LEAD_REPOSITORY_TOKEN)),
    __metadata("design:paramtypes", [Object])
], GetLeadStatisticsHandler);
//# sourceMappingURL=get-lead-statistics.handler.js.map