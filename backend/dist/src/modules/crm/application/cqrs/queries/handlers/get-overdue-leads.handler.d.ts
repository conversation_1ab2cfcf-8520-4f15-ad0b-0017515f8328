import { IQueryHandler } from '@nestjs/cqrs';
import { GetOverdueLeadsQuery } from '../get-overdue-leads.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
export declare class GetOverdueLeadsHandler implements IQueryHandler<GetOverdueLeadsQuery> {
    private readonly leadRepository;
    constructor(leadRepository: ILeadRepository);
    execute(query: GetOverdueLeadsQuery): Promise<Lead[]>;
}
