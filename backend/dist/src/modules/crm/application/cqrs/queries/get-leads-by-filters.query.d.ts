export declare class GetLeadsByFiltersQuery {
    readonly filters: LeadFilters;
    constructor(filters: LeadFilters);
}
export interface LeadFilters {
    status?: string;
    priority?: string;
    assignedUserId?: number;
    teamId?: number;
    source?: string;
    tags?: string[];
    dateFrom?: Date;
    dateTo?: Date;
    minRevenue?: number;
    maxRevenue?: number;
    limit?: number;
    offset?: number;
}
