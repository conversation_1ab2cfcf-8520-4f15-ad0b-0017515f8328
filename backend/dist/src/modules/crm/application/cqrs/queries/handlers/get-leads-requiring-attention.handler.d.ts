import { <PERSON><PERSON>ueryHandler } from '@nestjs/cqrs';
import { GetLeadsRequiringAttentionQuery } from '../get-leads-requiring-attention.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
export declare class GetLeadsRequiringAttentionHandler implements IQueryHandler<GetLeadsRequiringAttentionQuery> {
    private readonly leadRepository;
    constructor(leadRepository: ILeadRepository);
    execute(query: GetLeadsRequiringAttentionQuery): Promise<Lead[]>;
    private requiresAttention;
}
