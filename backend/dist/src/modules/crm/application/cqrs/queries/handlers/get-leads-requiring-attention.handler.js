"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetLeadsRequiringAttentionHandler = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
const get_leads_requiring_attention_query_1 = require("../get-leads-requiring-attention.query");
const injection_tokens_1 = require("../../../../domain/repositories/injection-tokens");
let GetLeadsRequiringAttentionHandler = class GetLeadsRequiringAttentionHandler {
    leadRepository;
    constructor(leadRepository) {
        this.leadRepository = leadRepository;
    }
    async execute(query) {
        const { teamId, userId } = query;
        const filters = {};
        if (teamId) {
            filters.teamId = teamId;
        }
        if (userId) {
            filters.assignedUserId = userId;
        }
        const leads = await this.leadRepository.findByFilters(filters);
        return leads.filter(lead => this.requiresAttention(lead));
    }
    requiresAttention(lead) {
        const now = new Date();
        if (lead.priority?.value === 'high') {
            return true;
        }
        if (lead.dateDeadline && lead.dateDeadline < now) {
            return true;
        }
        if (lead.dateDeadline) {
            const hoursUntilDeadline = (lead.dateDeadline.getTime() - now.getTime()) / (1000 * 60 * 60);
            if (hoursUntilDeadline <= 24 && hoursUntilDeadline > 0) {
                return true;
            }
        }
        const daysSinceCreated = (now.getTime() - lead.createdAt.getTime()) / (1000 * 60 * 60 * 24);
        if (daysSinceCreated > 7 && !lead.assignedUserId) {
            return true;
        }
        if ((lead.expectedRevenue || 0) > 50000 && !lead.assignedUserId) {
            return true;
        }
        return false;
    }
};
exports.GetLeadsRequiringAttentionHandler = GetLeadsRequiringAttentionHandler;
exports.GetLeadsRequiringAttentionHandler = GetLeadsRequiringAttentionHandler = __decorate([
    (0, cqrs_1.QueryHandler)(get_leads_requiring_attention_query_1.GetLeadsRequiringAttentionQuery),
    __param(0, (0, common_1.Inject)(injection_tokens_1.LEAD_REPOSITORY_TOKEN)),
    __metadata("design:paramtypes", [Object])
], GetLeadsRequiringAttentionHandler);
//# sourceMappingURL=get-leads-requiring-attention.handler.js.map