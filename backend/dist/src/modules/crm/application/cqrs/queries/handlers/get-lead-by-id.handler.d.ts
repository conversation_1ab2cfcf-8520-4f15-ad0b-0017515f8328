import { <PERSON><PERSON>ueryHandler } from '@nestjs/cqrs';
import { GetLeadByIdQuery } from '../get-lead-by-id.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
import { Lead } from '@/modules/crm/domain/entities/lead.entity';
export declare class GetLeadByIdHandler implements IQueryHandler<GetLeadByIdQuery> {
    private readonly leadRepository;
    constructor(leadRepository: ILeadRepository);
    execute(query: GetLeadByIdQuery): Promise<Lead | null>;
}
