{"version": 3, "file": "get-pipeline-analytics.handler.js", "sourceRoot": "", "sources": ["../../../../../../../../src/modules/crm/application/cqrs/queries/handlers/get-pipeline-analytics.handler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,uCAA2D;AAC3D,2CAAoD;AACpD,kFAA4E;AAqGrE,IAAM,2BAA2B,mCAAjC,MAAM,2BAA2B;IAInB;IACA;IACA;IALF,MAAM,GAAG,IAAI,eAAM,CAAC,6BAA2B,CAAC,IAAI,CAAC,CAAC;IAEvE,YACmB,cAA+B,EAC/B,eAAiC,EACjC,cAA+B;QAF/B,mBAAc,GAAd,cAAc,CAAiB;QAC/B,oBAAe,GAAf,eAAe,CAAkB;QACjC,mBAAc,GAAd,cAAc,CAAiB;IAC/C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAAgC;QAC5C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iDAAiD,KAAK,CAAC,MAAM,IAAI,KAAK,EAAE,CAAC,CAAC;QAE1F,IAAI,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAGlF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;gBACzD,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,cAAc,EAAE,KAAK,CAAC,MAAM;gBAC5B,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,MAAM,EAAE,KAAK,CAAC,MAAM;aACrB,CAAC,CAAC;YAGH,MAAM,MAAM,GAA4B;gBACtC,OAAO,EAAE;oBACP,UAAU,EAAE,UAAU,CAAC,UAAU;oBACjC,kBAAkB,EAAE,UAAU,CAAC,kBAAkB;oBACjD,YAAY,EAAE,UAAU,CAAC,YAAY;oBACrC,eAAe,EAAE,UAAU,CAAC,eAAe;oBAC3C,eAAe,EAAE,UAAU,CAAC,eAAe;oBAC3C,cAAc,EAAE,UAAU,CAAC,cAAc;oBACzC,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,iBAAiB,EAAE,UAAU,CAAC,iBAAiB;iBAChD;gBACD,QAAQ,EAAE;oBACR,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,SAAS,EAAE;wBACT,IAAI,EAAE,KAAK,CAAC,QAAQ;wBACpB,EAAE,EAAE,KAAK,CAAC,MAAM;qBACjB;oBACD,OAAO,EAAE;wBACP,MAAM,EAAE,KAAK,CAAC,MAAM;wBACpB,MAAM,EAAE,KAAK,CAAC,MAAM;wBACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ;wBACxB,cAAc,EAAE,KAAK,CAAC,cAAc;wBACpC,OAAO,EAAE,KAAK,CAAC,OAAO;qBACvB;oBACD,QAAQ,EAAE,KAAK,CAAC,WAAW,EAAE;iBAC9B;aACF,CAAC;YAGF,IAAI,KAAK,CAAC,mBAAmB,EAAE,CAAC;gBAC9B,MAAM,CAAC,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;oBACtD,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,QAAQ,EAAE,CAAC;oBACX,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,gBAAgB,EAAE,KAAK,CAAC,SAAS;oBACjC,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,eAAe,EAAE,KAAK,CAAC,eAAe;oBACtC,kBAAkB,EAAE,KAAK,CAAC,kBAAkB;oBAC5C,kBAAkB,EAAE,KAAK,CAAC,kBAAkB;oBAC5C,cAAc,EAAE,CAAC;oBACjB,WAAW,EAAE,CAAC;iBACf,CAAC,CAAC,CAAC;YACN,CAAC;YAGD,IAAI,KAAK,CAAC,sBAAsB,EAAE,CAAC;gBACjC,MAAM,CAAC,eAAe,GAAG;oBACvB,iBAAiB,EAAE,UAAU,CAAC,cAAc;oBAC5C,gBAAgB,EAAE,UAAU,CAAC,OAAO;oBACpC,iBAAiB,EAAE,UAAU,CAAC,cAAc,GAAG,UAAU,CAAC,OAAO,GAAG,GAAG;oBACvE,OAAO,EAAE,YAAY,CAAC,eAAe;oBACrC,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,QAAQ,EAAE,UAAU,CAAC,QAAQ;iBAC9B,CAAC;YACJ,CAAC;YAGD,IAAI,KAAK,CAAC,kBAAkB,EAAE,CAAC;gBAC7B,MAAM,CAAC,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;oBAC/D,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,SAAS,EAAE,UAAU,CAAC,SAAS;oBAC/B,kBAAkB,EAAE,UAAU,CAAC,kBAAkB;oBACjD,WAAW,EAAE,UAAU,CAAC,WAAW;oBACnC,QAAQ,EAAE,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,kBAAkB,EAAE,UAAU,CAAC,WAAW,CAAC;oBACjG,eAAe,EAAE,IAAI,CAAC,iCAAiC,CAAC,UAAU,CAAC;iBACpE,CAAC,CAAC,CAAC;YACN,CAAC;YAGD,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBAC1B,MAAM,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YACnE,CAAC;YAGD,IAAI,KAAK,CAAC,kBAAkB,EAAE,CAAC;gBAC7B,MAAM,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBAC9D,MAAM,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YACzE,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uDAAuD,KAAK,CAAC,MAAM,IAAI,KAAK,EAAE,CAAC,CAAC;YAEhG,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAG7D,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACxE,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAKO,2BAA2B,CAAC,WAAmB,EAAE,WAAmB;QAC1E,IAAI,WAAW,GAAG,EAAE,IAAI,WAAW,GAAG,EAAE;YAAE,OAAO,MAAM,CAAC;QACxD,IAAI,WAAW,GAAG,EAAE,IAAI,WAAW,GAAG,EAAE;YAAE,OAAO,QAAQ,CAAC;QAC1D,OAAO,KAAK,CAAC;IACf,CAAC;IAKO,iCAAiC,CAAC,UAAe;QACvD,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,UAAU,CAAC,kBAAkB,GAAG,EAAE,EAAE,CAAC;YACvC,eAAe,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;QAC9F,CAAC;QAED,IAAI,UAAU,CAAC,WAAW,GAAG,EAAE,EAAE,CAAC;YAChC,eAAe,CAAC,IAAI,CAAC,yEAAyE,CAAC,CAAC;QAClG,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAKO,KAAK,CAAC,gBAAgB,CAAC,KAAgC,EAAE,UAAe;QAE9E,MAAM,iBAAiB,GAAG,IAAI,CAAC;QAE/B,OAAO;YACL,SAAS,EAAE;gBACT,eAAe,EAAE,UAAU,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,iBAAiB,CAAC;gBAClE,eAAe,EAAE,UAAU,CAAC,eAAe,GAAG,CAAC,CAAC,GAAG,iBAAiB,CAAC;gBACrE,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,GAAG,GAAG,CAAC;aAC/D;YACD,WAAW,EAAE;gBACX,eAAe,EAAE,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,iBAAiB,EAAE,CAAC,CAAC;gBAC7E,eAAe,EAAE,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,iBAAiB,EAAE,CAAC,CAAC;gBAChF,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,GAAG,GAAG,CAAC;aAC/D;YACD,OAAO,EAAE,EAAE;SACZ,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,KAAgC,EAAE,UAAe;QAE7E,OAAO;YACL,aAAa,EAAE,GAAG;YAClB,kBAAkB,EAAE,CAAC,GAAG;YACxB,eAAe,EAAE,GAAG;YACpB,mBAAmB,EAAE,EAAE;SACxB,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,mBAAmB,CAAC,KAAgC,EAAE,UAAe;QAEjF,OAAO;YACL,YAAY,EAAE;gBACZ,aAAa,EAAE,IAAI;gBACnB,eAAe,EAAE,GAAG;gBACpB,oBAAoB,EAAE,GAAG;aAC1B;YACD,aAAa,EAAE;gBACb,kBAAkB,EAAE,GAAG;gBACvB,qBAAqB,EAAE,GAAG;gBAC1B,mBAAmB,EAAE,EAAE;aACxB;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AAxMY,kEAA2B;sCAA3B,2BAA2B;IAFvC,IAAA,mBAAU,GAAE;IACZ,IAAA,mBAAY,EAAC,wDAAyB,CAAC;;GAC3B,2BAA2B,CAwMvC"}