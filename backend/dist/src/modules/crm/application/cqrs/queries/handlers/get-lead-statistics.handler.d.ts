import { IQueryHandler } from '@nestjs/cqrs';
import { GetLeadStatisticsQuery } from '../get-lead-statistics.query';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
export declare class GetLeadStatisticsHandler implements IQueryHandler<GetLeadStatisticsQuery> {
    private readonly leadRepository;
    constructor(leadRepository: ILeadRepository);
    execute(query: GetLeadStatisticsQuery): Promise<LeadStatistics>;
}
export interface LeadStatistics {
    totalLeads: number;
    qualifiedLeads: number;
    convertedLeads: number;
    totalRevenue: number;
    averageRevenue: number;
    conversionRate: number;
    statusDistribution: Record<string, number>;
    sourceDistribution: Record<string, number>;
}
