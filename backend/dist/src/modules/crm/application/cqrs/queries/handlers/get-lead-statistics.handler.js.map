{"version": 3, "file": "get-lead-statistics.handler.js", "sourceRoot": "", "sources": ["../../../../../../../../src/modules/crm/application/cqrs/queries/handlers/get-lead-statistics.handler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,uCAA2D;AAC3D,2CAAwC;AACxC,4EAAsE;AAEtE,uFAA2F;AAOpF,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IAGhB;IAFnB,YAEmB,cAA+B;QAA/B,mBAAc,GAAd,cAAc,CAAiB;IAC/C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,KAA6B;QACzC,MAAM,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;QAG1B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAG/D,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAChC,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,MAAM,CAAC;QACvE,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,MAAM,CAAC;QACvE,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACvF,MAAM,cAAc,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE,MAAM,cAAc,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAGhF,MAAM,kBAAkB,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACpD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,SAAS,CAAC;YAC/C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACrC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAGjC,MAAM,kBAAkB,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACpD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC;YACxC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACrC,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC,CAAC;QAEjC,OAAO;YACL,UAAU;YACV,cAAc;YACd,cAAc;YACd,YAAY;YACZ,cAAc;YACd,cAAc;YACd,kBAAkB;YAClB,kBAAkB;SACnB,CAAC;IACJ,CAAC;CACF,CAAA;AA7CY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAY,EAAC,kDAAsB,CAAC;IAGhC,WAAA,IAAA,eAAM,EAAC,wCAAqB,CAAC,CAAA;;GAFrB,wBAAwB,CA6CpC"}