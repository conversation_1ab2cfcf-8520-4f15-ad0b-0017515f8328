"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetPipelineAnalyticsQuery = void 0;
class GetPipelineAnalyticsQuery {
    teamId;
    userId;
    dateFrom;
    dateTo;
    includeStageMetrics;
    includeConversionRates;
    includeBottlenecks;
    includeForecast;
    includeComparisons;
    stageIds;
    priorityLevels;
    sources;
    groupBy;
    timeGranularity;
    requestedBy;
    constructor(teamId, userId, dateFrom, dateTo, includeStageMetrics = true, includeConversionRates = true, includeBottlenecks = true, includeForecast = true, includeComparisons = false, stageIds, priorityLevels, sources, groupBy, timeGranularity, requestedBy) {
        this.teamId = teamId;
        this.userId = userId;
        this.dateFrom = dateFrom;
        this.dateTo = dateTo;
        this.includeStageMetrics = includeStageMetrics;
        this.includeConversionRates = includeConversionRates;
        this.includeBottlenecks = includeBottlenecks;
        this.includeForecast = includeForecast;
        this.includeComparisons = includeComparisons;
        this.stageIds = stageIds;
        this.priorityLevels = priorityLevels;
        this.sources = sources;
        this.groupBy = groupBy;
        this.timeGranularity = timeGranularity;
        this.requestedBy = requestedBy;
        if (dateFrom && dateTo && dateFrom > dateTo) {
            throw new Error('Date from cannot be after date to');
        }
        if (!includeStageMetrics && !includeConversionRates && !includeBottlenecks && !includeForecast) {
            throw new Error('At least one analytics metric must be requested');
        }
    }
    getMetadata() {
        return {
            queryType: 'GetPipelineAnalyticsQuery',
            teamId: this.teamId,
            userId: this.userId,
            dateRange: {
                from: this.dateFrom?.toISOString(),
                to: this.dateTo?.toISOString(),
            },
            metrics: {
                stageMetrics: this.includeStageMetrics,
                conversionRates: this.includeConversionRates,
                bottlenecks: this.includeBottlenecks,
                forecast: this.includeForecast,
                comparisons: this.includeComparisons,
            },
            filters: {
                stageIds: this.stageIds,
                priorityLevels: this.priorityLevels,
                sources: this.sources,
            },
            grouping: {
                groupBy: this.groupBy,
                timeGranularity: this.timeGranularity,
            },
            requestedBy: this.requestedBy,
            timestamp: new Date().toISOString(),
        };
    }
    getCacheKey() {
        const parts = [
            'pipeline-analytics',
            this.teamId || 'all-teams',
            this.userId || 'all-users',
            this.dateFrom?.toISOString().split('T')[0] || 'no-start',
            this.dateTo?.toISOString().split('T')[0] || 'no-end',
            this.includeStageMetrics ? 'stages' : '',
            this.includeConversionRates ? 'conversions' : '',
            this.includeBottlenecks ? 'bottlenecks' : '',
            this.includeForecast ? 'forecast' : '',
            this.includeComparisons ? 'comparisons' : '',
            this.groupBy || 'no-group',
            this.timeGranularity || 'no-time',
        ].filter(Boolean);
        return parts.join(':');
    }
    toPlainObject() {
        return {
            teamId: this.teamId,
            userId: this.userId,
            dateFrom: this.dateFrom,
            dateTo: this.dateTo,
            includeStageMetrics: this.includeStageMetrics,
            includeConversionRates: this.includeConversionRates,
            includeBottlenecks: this.includeBottlenecks,
            includeForecast: this.includeForecast,
            includeComparisons: this.includeComparisons,
            stageIds: this.stageIds,
            priorityLevels: this.priorityLevels,
            sources: this.sources,
            groupBy: this.groupBy,
            timeGranularity: this.timeGranularity,
            requestedBy: this.requestedBy,
            metadata: this.getMetadata(),
            cacheKey: this.getCacheKey(),
        };
    }
}
exports.GetPipelineAnalyticsQuery = GetPipelineAnalyticsQuery;
//# sourceMappingURL=get-pipeline-analytics.query.js.map