"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CreateLeadHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateLeadHandler = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
const create_lead_command_1 = require("../create-lead.command");
const lead_entity_1 = require("../../../../domain/entities/lead.entity");
const contact_info_vo_1 = require("../../../../domain/value-objects/contact-info.vo");
const lead_created_event_1 = require("../../events/lead-created.event");
let CreateLeadHandler = CreateLeadHandler_1 = class CreateLeadHandler {
    leadRepository;
    eventBus;
    logger = new common_1.Logger(CreateLeadHandler_1.name);
    constructor(leadRepository, eventBus) {
        this.leadRepository = leadRepository;
        this.eventBus = eventBus;
    }
    async execute(command) {
        this.logger.log(`Executing CreateLeadCommand for: ${command.name}`);
        try {
            const contactInfo = new contact_info_vo_1.ContactInfo(command.email, command.phone, command.company, command.website, command.address, command.city, command.country);
            const lead = lead_entity_1.Lead.create(command.name, contactInfo, command.source, command.expectedRevenue, command.teamId, command.assignedUserId, command.priority, command.type);
            lead.validateBusinessRules();
            if (command.description) {
                const updatedLead = new lead_entity_1.Lead(lead.id, lead.name, lead.contactInfo, lead.status, lead.source, lead.type, lead.priority, lead.expectedRevenue, lead.probability, command.description, lead.assignedUserId, command.companyId || lead.companyId, command.tags, lead.partnerId, lead.stageId, lead.teamId, command.dateDeadline, lead.lostReasonId, command.campaignId, command.sourceId, command.mediumId, lead.createdAt, lead.updatedAt);
                const savedLead = await this.leadRepository.save(updatedLead);
                const event = new lead_created_event_1.LeadCreatedEvent(savedLead.id, savedLead, command.createdBy, command.getMetadata());
                await this.eventBus.publish(event);
                this.logger.log(`Successfully created lead with ID: ${savedLead.id}`);
                return {
                    leadId: savedLead.id,
                    lead: savedLead,
                };
            }
            const savedLead = await this.leadRepository.save(lead);
            const event = new lead_created_event_1.LeadCreatedEvent(savedLead.id, savedLead, command.createdBy, command.getMetadata());
            await this.eventBus.publish(event);
            this.logger.log(`Successfully created lead with ID: ${savedLead.id}`);
            return {
                leadId: savedLead.id,
                lead: savedLead,
            };
        }
        catch (error) {
            this.logger.error(`Failed to create lead: ${command.name}`, error);
            if (error instanceof Error) {
                throw new Error(`Failed to create lead "${command.name}": ${error.message}`);
            }
            throw new Error(`Failed to create lead "${command.name}": Unknown error`);
        }
    }
    validateCommand(command) {
    }
};
exports.CreateLeadHandler = CreateLeadHandler;
exports.CreateLeadHandler = CreateLeadHandler = CreateLeadHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, cqrs_1.CommandHandler)(create_lead_command_1.CreateLeadCommand),
    __metadata("design:paramtypes", [Object, cqrs_1.EventBus])
], CreateLeadHandler);
//# sourceMappingURL=create-lead.handler.js.map