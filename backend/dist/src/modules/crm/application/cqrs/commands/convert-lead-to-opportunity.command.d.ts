import { ICommand } from '@nestjs/cqrs';
export declare class ConvertLeadToOpportunityCommand implements ICommand {
    readonly leadId: number;
    readonly expectedRevenue: number;
    readonly probability: number;
    readonly partnerId?: number | undefined;
    readonly stageId?: number | undefined;
    readonly assignedUserId?: number | undefined;
    readonly dateDeadline?: Date | undefined;
    readonly description?: string | undefined;
    readonly convertedBy?: number | undefined;
    readonly reason?: string | undefined;
    constructor(leadId: number, expectedRevenue: number, probability?: number, partnerId?: number | undefined, stageId?: number | undefined, assignedUserId?: number | undefined, dateDeadline?: Date | undefined, description?: string | undefined, convertedBy?: number | undefined, reason?: string | undefined);
    getMetadata(): {
        commandType: string;
        leadId: number;
        expectedRevenue: number;
        probability: number;
        hasPartner: boolean;
        hasStage: boolean;
        isAssigned: boolean;
        hasDeadline: boolean;
        convertedBy: number | undefined;
        reason: string | undefined;
        timestamp: string;
    };
    toPlainObject(): {
        leadId: number;
        expectedRevenue: number;
        probability: number;
        partnerId: number | undefined;
        stageId: number | undefined;
        assignedUserId: number | undefined;
        dateDeadline: Date | undefined;
        description: string | undefined;
        convertedBy: number | undefined;
        reason: string | undefined;
        metadata: {
            commandType: string;
            leadId: number;
            expectedRevenue: number;
            probability: number;
            hasPartner: boolean;
            hasStage: boolean;
            isAssigned: boolean;
            hasDeadline: boolean;
            convertedBy: number | undefined;
            reason: string | undefined;
            timestamp: string;
        };
    };
}
