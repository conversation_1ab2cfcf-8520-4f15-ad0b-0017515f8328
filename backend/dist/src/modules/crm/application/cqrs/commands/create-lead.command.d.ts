import { ICommand } from '@nestjs/cqrs';
import { LeadPriority } from '../../../domain/value-objects/lead-priority.vo';
import { LeadType } from '../../../domain/value-objects/lead-type.vo';
export declare class CreateLeadCommand implements ICommand {
    readonly name: string;
    readonly email?: string | undefined;
    readonly phone?: string | undefined;
    readonly company?: string | undefined;
    readonly website?: string | undefined;
    readonly address?: string | undefined;
    readonly city?: string | undefined;
    readonly country?: string | undefined;
    readonly source: string;
    readonly type: LeadType;
    readonly priority: LeadPriority;
    readonly expectedRevenue?: number | undefined;
    readonly probability?: number | undefined;
    readonly description?: string | undefined;
    readonly assignedUserId?: number | undefined;
    readonly teamId?: number | undefined;
    readonly campaignId?: number | undefined;
    readonly sourceId?: number | undefined;
    readonly mediumId?: number | undefined;
    readonly tags: string[];
    readonly dateDeadline?: Date | undefined;
    readonly createdBy?: number | undefined;
    readonly companyId?: number | undefined;
    constructor(name: string, email?: string | undefined, phone?: string | undefined, company?: string | undefined, website?: string | undefined, address?: string | undefined, city?: string | undefined, country?: string | undefined, source?: string, type?: LeadType, priority?: LeadPriority, expectedRevenue?: number | undefined, probability?: number | undefined, description?: string | undefined, assignedUserId?: number | undefined, teamId?: number | undefined, campaignId?: number | undefined, sourceId?: number | undefined, mediumId?: number | undefined, tags?: string[], dateDeadline?: Date | undefined, createdBy?: number | undefined, companyId?: number | undefined);
    private isValidEmail;
    getMetadata(): {
        commandType: string;
        leadName: string;
        leadType: string;
        priority: number;
        source: string;
        hasRevenue: boolean;
        isAssigned: boolean;
        hasTeam: boolean;
        hasDeadline: boolean;
        tagCount: number;
        createdBy: number | undefined;
        timestamp: string;
    };
    toPlainObject(): {
        name: string;
        email: string | undefined;
        phone: string | undefined;
        company: string | undefined;
        website: string | undefined;
        address: string | undefined;
        city: string | undefined;
        country: string | undefined;
        source: string;
        type: {
            value: string;
            label: string;
            description: string;
            color: string;
            cssClass: string;
            icon: string;
            badgeVariant: string;
            isLead: boolean;
            isOpportunity: boolean;
            canConvertToOpportunity: boolean;
            requiresQualification: boolean;
            canHaveRevenueForecast: boolean;
            canHaveProbability: boolean;
            canBeInPipeline: boolean;
            defaultProbability: number;
        };
        priority: {
            value: number;
            label: string;
            color: string;
            cssClass: string;
            icon: string;
            requiresImmediateAttention: boolean;
        };
        expectedRevenue: number | undefined;
        probability: number | undefined;
        description: string | undefined;
        assignedUserId: number | undefined;
        teamId: number | undefined;
        campaignId: number | undefined;
        sourceId: number | undefined;
        mediumId: number | undefined;
        tags: string[];
        dateDeadline: Date | undefined;
        createdBy: number | undefined;
        companyId: number | undefined;
        metadata: {
            commandType: string;
            leadName: string;
            leadType: string;
            priority: number;
            source: string;
            hasRevenue: boolean;
            isAssigned: boolean;
            hasTeam: boolean;
            hasDeadline: boolean;
            tagCount: number;
            createdBy: number | undefined;
            timestamp: string;
        };
    };
}
