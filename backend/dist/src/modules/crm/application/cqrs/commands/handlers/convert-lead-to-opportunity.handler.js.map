{"version": 3, "file": "convert-lead-to-opportunity.handler.js", "sourceRoot": "", "sources": ["../../../../../../../../src/modules/crm/application/cqrs/commands/handlers/convert-lead-to-opportunity.handler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,uCAAyE;AACzE,2CAAoD;AACpD,gGAAyF;AACzF,uFAA6E;AAE7E,0GAAmG;AAQ5F,IAAM,+BAA+B,uCAArC,MAAM,+BAA+B;IAIvB;IACA;IAJF,MAAM,GAAG,IAAI,eAAM,CAAC,iCAA+B,CAAC,IAAI,CAAC,CAAC;IAE3E,YACmB,cAA+B,EAC/B,QAAkB;QADlB,mBAAc,GAAd,cAAc,CAAiB;QAC/B,aAAQ,GAAR,QAAQ,CAAU;IAClC,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,OAAwC;QACpD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0DAA0D,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAE5F,IAAI,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACxE,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,gBAAgB,OAAO,CAAC,MAAM,YAAY,CAAC,CAAC;YAC9D,CAAC;YAGD,IAAI,CAAC,YAAY,CAAC,uBAAuB,EAAE,EAAE,CAAC;gBAC5C,MAAM,IAAI,KAAK,CAAC,gBAAgB,OAAO,CAAC,MAAM,mFAAmF,CAAC,CAAC;YACrI,CAAC;YAGD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAChE,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,SAAS,EACjB,OAAO,CAAC,OAAO,CAChB,CAAC;YAGF,IAAI,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,KAAK,WAAW,CAAC,cAAc,EAAE,CAAC;gBACpF,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;YACjF,CAAC;YAED,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACzB,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;YAC9E,CAAC;YAED,IAAI,OAAO,CAAC,eAAe,KAAK,WAAW,CAAC,eAAe,IAAI,OAAO,CAAC,WAAW,KAAK,WAAW,CAAC,WAAW,EAAE,CAAC;gBAC/G,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAC7C,WAAW,CAAC,EAAE,EACd,OAAO,CAAC,eAAe,EACvB,OAAO,CAAC,WAAW,CACpB,CAAC;YACJ,CAAC;YAGD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACvE,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;gBACtD,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAC9D,CAAC;YAED,MAAM,gBAAgB,GAAG,gCAAW,CAAC,QAAQ,CAC3C,WAAW,EACX,OAAO,CAAC,eAAe,EACvB,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,SAAS,EACjB,OAAO,CAAC,OAAO,CAChB,CAAC;YAGF,MAAM,KAAK,GAAG,IAAI,qEAA+B,CAC/C,OAAO,CAAC,MAAM,EACd,gBAAgB,CAAC,EAAE,EACnB,gBAAgB,EAChB,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,WAAW,EAAE,CACtB,CAAC;YAEF,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAEnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,OAAO,CAAC,MAAM,mBAAmB,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC;YAEvG,OAAO;gBACL,aAAa,EAAE,gBAAgB,CAAC,EAAE;gBAClC,WAAW,EAAE,gBAAgB;aAC9B,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,OAAO,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;YAGrF,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,0BAA0B,OAAO,CAAC,MAAM,oBAAoB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/F,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,0BAA0B,OAAO,CAAC,MAAM,gCAAgC,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,kBAAkB,CAAC,OAAwC;IAUzE,CAAC;CACF,CAAA;AA1GY,0EAA+B;0CAA/B,+BAA+B;IAF3C,IAAA,mBAAU,GAAE;IACZ,IAAA,qBAAc,EAAC,qEAA+B,CAAC;6CAMjB,eAAQ;GAL1B,+BAA+B,CA0G3C"}