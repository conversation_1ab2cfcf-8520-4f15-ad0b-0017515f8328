import { ICommandHandler } from '@nestjs/cqrs';
import { AssignLeadToUserCommand } from '../assign-lead-to-user.command';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
export declare class AssignLeadToUserHandler implements ICommandHandler<AssignLeadToUserCommand> {
    private readonly leadRepository;
    constructor(leadRepository: ILeadRepository);
    execute(command: AssignLeadToUserCommand): Promise<void>;
}
