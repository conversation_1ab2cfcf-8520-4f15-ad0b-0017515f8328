import { ICommandHandler, EventBus } from '@nestjs/cqrs';
import { ConvertLeadToOpportunityCommand } from '../convert-lead-to-opportunity.command';
import { Opportunity } from '../../../../domain/entities/opportunity.entity';
import { ILeadRepository } from '../../../../domain/repositories/lead.repository';
export declare class ConvertLeadToOpportunityHandler implements ICommandHandler<ConvertLeadToOpportunityCommand> {
    private readonly leadRepository;
    private readonly eventBus;
    private readonly logger;
    constructor(leadRepository: ILeadRepository, eventBus: EventBus);
    execute(command: ConvertLeadToOpportunityCommand): Promise<{
        opportunityId: number;
        opportunity: Opportunity;
    }>;
    private validateConversion;
}
