import { ICommandHandler, EventBus } from '@nestjs/cqrs';
import { CreateLeadCommand } from '../create-lead.command';
import { Lead } from '../../../../domain/entities/lead.entity';
import { ILeadRepository } from '../../../../domain/repositories/lead.repository';
export declare class CreateLeadHandler implements ICommandHandler<CreateLeadCommand> {
    private readonly leadRepository;
    private readonly eventBus;
    private readonly logger;
    constructor(leadRepository: ILeadRepository, eventBus: EventBus);
    execute(command: CreateLeadCommand): Promise<{
        leadId: number;
        lead: Lead;
    }>;
    private validateCommand;
}
