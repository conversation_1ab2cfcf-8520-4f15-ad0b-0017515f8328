import { ICommandHandler } from '@nestjs/cqrs';
import { SetLeadDeadlineCommand } from '../set-lead-deadline.command';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
export declare class SetLeadDeadlineHandler implements ICommandHandler<SetLeadDeadlineCommand> {
    private readonly leadRepository;
    constructor(leadRepository: ILeadRepository);
    execute(command: SetLeadDeadlineCommand): Promise<void>;
}
