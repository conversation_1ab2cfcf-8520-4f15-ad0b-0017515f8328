"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var UpdateLeadHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateLeadHandler = exports.UpdateLeadCommand = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
class UpdateLeadCommand {
    leadId;
    updates;
    constructor(leadId, updates) {
        this.leadId = leadId;
        this.updates = updates;
    }
}
exports.UpdateLeadCommand = UpdateLeadCommand;
let UpdateLeadHandler = UpdateLeadHandler_1 = class UpdateLeadHandler {
    logger = new common_1.Logger(UpdateLeadHandler_1.name);
    async execute(command) {
        this.logger.log(`UpdateLeadHandler - Placeholder implementation`);
        return { success: true };
    }
};
exports.UpdateLeadHandler = UpdateLeadHandler;
exports.UpdateLeadHandler = UpdateLeadHandler = UpdateLeadHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, cqrs_1.CommandHandler)(UpdateLeadCommand)
], UpdateLeadHandler);
//# sourceMappingURL=update-lead.handler.js.map