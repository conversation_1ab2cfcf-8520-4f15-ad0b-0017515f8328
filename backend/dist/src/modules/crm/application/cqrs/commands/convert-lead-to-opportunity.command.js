"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConvertLeadToOpportunityCommand = void 0;
class ConvertLeadToOpportunityCommand {
    leadId;
    expectedRevenue;
    probability;
    partnerId;
    stageId;
    assignedUserId;
    dateDeadline;
    description;
    convertedBy;
    reason;
    constructor(leadId, expectedRevenue, probability = 10, partnerId, stageId, assignedUserId, dateDeadline, description, convertedBy, reason) {
        this.leadId = leadId;
        this.expectedRevenue = expectedRevenue;
        this.probability = probability;
        this.partnerId = partnerId;
        this.stageId = stageId;
        this.assignedUserId = assignedUserId;
        this.dateDeadline = dateDeadline;
        this.description = description;
        this.convertedBy = convertedBy;
        this.reason = reason;
        if (!leadId || leadId <= 0) {
            throw new Error('Valid lead ID is required');
        }
        if (!expectedRevenue || expectedRevenue <= 0) {
            throw new Error('Expected revenue is required and must be positive');
        }
        if (probability < 0 || probability > 100) {
            throw new Error('Probability must be between 0 and 100');
        }
        if (dateDeadline && dateDeadline < new Date()) {
            throw new Error('Deadline cannot be in the past');
        }
    }
    getMetadata() {
        return {
            commandType: 'ConvertLeadToOpportunityCommand',
            leadId: this.leadId,
            expectedRevenue: this.expectedRevenue,
            probability: this.probability,
            hasPartner: !!this.partnerId,
            hasStage: !!this.stageId,
            isAssigned: !!this.assignedUserId,
            hasDeadline: !!this.dateDeadline,
            convertedBy: this.convertedBy,
            reason: this.reason,
            timestamp: new Date().toISOString(),
        };
    }
    toPlainObject() {
        return {
            leadId: this.leadId,
            expectedRevenue: this.expectedRevenue,
            probability: this.probability,
            partnerId: this.partnerId,
            stageId: this.stageId,
            assignedUserId: this.assignedUserId,
            dateDeadline: this.dateDeadline,
            description: this.description,
            convertedBy: this.convertedBy,
            reason: this.reason,
            metadata: this.getMetadata(),
        };
    }
}
exports.ConvertLeadToOpportunityCommand = ConvertLeadToOpportunityCommand;
//# sourceMappingURL=convert-lead-to-opportunity.command.js.map