import { ICommandHandler } from '@nestjs/cqrs';
import { AssignLeadToTeamCommand } from '../assign-lead-to-team.command';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
export declare class AssignLeadToTeamHandler implements ICommandHandler<AssignLeadToTeamCommand> {
    private readonly leadRepository;
    constructor(leadRepository: ILeadRepository);
    execute(command: AssignLeadToTeamCommand): Promise<void>;
}
