"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateLeadCommand = void 0;
const lead_priority_vo_1 = require("../../../domain/value-objects/lead-priority.vo");
const lead_type_vo_1 = require("../../../domain/value-objects/lead-type.vo");
class CreateLeadCommand {
    name;
    email;
    phone;
    company;
    website;
    address;
    city;
    country;
    source;
    type;
    priority;
    expectedRevenue;
    probability;
    description;
    assignedUserId;
    teamId;
    campaignId;
    sourceId;
    mediumId;
    tags;
    dateDeadline;
    createdBy;
    companyId;
    constructor(name, email, phone, company, website, address, city, country, source = 'website', type = lead_type_vo_1.LeadType.LEAD, priority = lead_priority_vo_1.LeadPriority.MEDIUM, expectedRevenue, probability, description, assignedUserId, teamId, campaignId, sourceId, mediumId, tags = [], dateDeadline, createdBy, companyId) {
        this.name = name;
        this.email = email;
        this.phone = phone;
        this.company = company;
        this.website = website;
        this.address = address;
        this.city = city;
        this.country = country;
        this.source = source;
        this.type = type;
        this.priority = priority;
        this.expectedRevenue = expectedRevenue;
        this.probability = probability;
        this.description = description;
        this.assignedUserId = assignedUserId;
        this.teamId = teamId;
        this.campaignId = campaignId;
        this.sourceId = sourceId;
        this.mediumId = mediumId;
        this.tags = tags;
        this.dateDeadline = dateDeadline;
        this.createdBy = createdBy;
        this.companyId = companyId;
        if (!name || name.trim().length === 0) {
            throw new Error('Lead name is required');
        }
        if (email && !this.isValidEmail(email)) {
            throw new Error('Invalid email format');
        }
        if (type.isOpportunity()) {
            if (!expectedRevenue || expectedRevenue <= 0) {
                throw new Error('Expected revenue is required for opportunities and must be positive');
            }
            if (probability === undefined || probability < 0 || probability > 100) {
                throw new Error('Probability is required for opportunities and must be between 0 and 100');
            }
        }
        if (dateDeadline && dateDeadline < new Date()) {
            throw new Error('Deadline cannot be in the past');
        }
    }
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    getMetadata() {
        return {
            commandType: 'CreateLeadCommand',
            leadName: this.name,
            leadType: this.type.value,
            priority: this.priority.value,
            source: this.source,
            hasRevenue: !!this.expectedRevenue,
            isAssigned: !!this.assignedUserId,
            hasTeam: !!this.teamId,
            hasDeadline: !!this.dateDeadline,
            tagCount: this.tags.length,
            createdBy: this.createdBy,
            timestamp: new Date().toISOString(),
        };
    }
    toPlainObject() {
        return {
            name: this.name,
            email: this.email,
            phone: this.phone,
            company: this.company,
            website: this.website,
            address: this.address,
            city: this.city,
            country: this.country,
            source: this.source,
            type: this.type.toPlainObject(),
            priority: this.priority.toPlainObject(),
            expectedRevenue: this.expectedRevenue,
            probability: this.probability,
            description: this.description,
            assignedUserId: this.assignedUserId,
            teamId: this.teamId,
            campaignId: this.campaignId,
            sourceId: this.sourceId,
            mediumId: this.mediumId,
            tags: this.tags,
            dateDeadline: this.dateDeadline,
            createdBy: this.createdBy,
            companyId: this.companyId,
            metadata: this.getMetadata(),
        };
    }
}
exports.CreateLeadCommand = CreateLeadCommand;
//# sourceMappingURL=create-lead.command.js.map