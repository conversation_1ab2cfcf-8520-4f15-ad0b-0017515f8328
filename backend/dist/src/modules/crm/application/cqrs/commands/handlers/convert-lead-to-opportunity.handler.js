"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ConvertLeadToOpportunityHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConvertLeadToOpportunityHandler = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
const convert_lead_to_opportunity_command_1 = require("../convert-lead-to-opportunity.command");
const opportunity_entity_1 = require("../../../../domain/entities/opportunity.entity");
const lead_converted_to_opportunity_event_1 = require("../../events/lead-converted-to-opportunity.event");
let ConvertLeadToOpportunityHandler = ConvertLeadToOpportunityHandler_1 = class ConvertLeadToOpportunityHandler {
    leadRepository;
    eventBus;
    logger = new common_1.Logger(ConvertLeadToOpportunityHandler_1.name);
    constructor(leadRepository, eventBus) {
        this.leadRepository = leadRepository;
        this.eventBus = eventBus;
    }
    async execute(command) {
        this.logger.log(`Executing ConvertLeadToOpportunityCommand for lead ID: ${command.leadId}`);
        try {
            const existingLead = await this.leadRepository.findById(command.leadId);
            if (!existingLead) {
                throw new Error(`Lead with ID ${command.leadId} not found`);
            }
            if (!existingLead.canConvertToOpportunity()) {
                throw new Error(`Lead with ID ${command.leadId} cannot be converted to opportunity. Reason: Lead must be qualified and assigned.`);
            }
            const opportunity = await this.leadRepository.convertToOpportunity(command.leadId, command.partnerId, command.stageId);
            if (command.assignedUserId && command.assignedUserId !== opportunity.assignedUserId) {
                await this.leadRepository.assignToUser(opportunity.id, command.assignedUserId);
            }
            if (command.dateDeadline) {
                await this.leadRepository.setDeadline(opportunity.id, command.dateDeadline);
            }
            if (command.expectedRevenue !== opportunity.expectedRevenue || command.probability !== opportunity.probability) {
                await this.leadRepository.updateRevenueForecast(opportunity.id, command.expectedRevenue, command.probability);
            }
            const updatedLead = await this.leadRepository.findById(opportunity.id);
            if (!updatedLead || !updatedLead.type.isOpportunity()) {
                throw new Error('Failed to retrieve converted opportunity');
            }
            const finalOpportunity = opportunity_entity_1.Opportunity.fromLead(updatedLead, command.expectedRevenue, command.probability, command.partnerId, command.stageId);
            const event = new lead_converted_to_opportunity_event_1.LeadConvertedToOpportunityEvent(command.leadId, finalOpportunity.id, finalOpportunity, command.convertedBy, command.reason, command.getMetadata());
            await this.eventBus.publish(event);
            this.logger.log(`Successfully converted lead ${command.leadId} to opportunity ${finalOpportunity.id}`);
            return {
                opportunityId: finalOpportunity.id,
                opportunity: finalOpportunity,
            };
        }
        catch (error) {
            this.logger.error(`Failed to convert lead to opportunity: ${command.leadId}`, error);
            if (error instanceof Error) {
                throw new Error(`Failed to convert lead ${command.leadId} to opportunity: ${error.message}`);
            }
            throw new Error(`Failed to convert lead ${command.leadId} to opportunity: Unknown error`);
        }
    }
    async validateConversion(command) {
    }
};
exports.ConvertLeadToOpportunityHandler = ConvertLeadToOpportunityHandler;
exports.ConvertLeadToOpportunityHandler = ConvertLeadToOpportunityHandler = ConvertLeadToOpportunityHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, cqrs_1.CommandHandler)(convert_lead_to_opportunity_command_1.ConvertLeadToOpportunityCommand),
    __metadata("design:paramtypes", [Object, cqrs_1.EventBus])
], ConvertLeadToOpportunityHandler);
//# sourceMappingURL=convert-lead-to-opportunity.handler.js.map