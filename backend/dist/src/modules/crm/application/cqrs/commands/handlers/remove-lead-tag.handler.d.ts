import { ICommandHandler } from '@nestjs/cqrs';
import { RemoveLeadTagCommand } from '../remove-lead-tag.command';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
export declare class RemoveLeadTagHandler implements ICommandHandler<RemoveLeadTagCommand> {
    private readonly leadRepository;
    constructor(leadRepository: ILeadRepository);
    execute(command: RemoveLeadTagCommand): Promise<void>;
}
