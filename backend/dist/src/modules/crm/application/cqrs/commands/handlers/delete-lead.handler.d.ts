import { ICommandHandler } from '@nestjs/cqrs';
import { DeleteLeadCommand } from '../delete-lead.command';
import { ILeadRepository } from '@/modules/crm/domain/repositories/lead.repository';
export declare class DeleteLeadHandler implements ICommandHandler<DeleteLeadCommand> {
    private readonly leadRepository;
    constructor(leadRepository: ILeadRepository);
    execute(command: DeleteLeadCommand): Promise<void>;
}
