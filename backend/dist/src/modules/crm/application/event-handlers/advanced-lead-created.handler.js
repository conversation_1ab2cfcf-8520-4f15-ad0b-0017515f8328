"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AdvancedLeadCreatedHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedLeadCreatedHandler = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
const lead_events_1 = require("../../domain/events/lead-events");
const email_queue_producer_1 = require("../../infrastructure/async/producers/email-queue.producer");
const notification_queue_producer_1 = require("../../infrastructure/async/producers/notification-queue.producer");
const analytics_queue_producer_1 = require("../../infrastructure/async/producers/analytics-queue.producer");
const integration_queue_producer_1 = require("../../infrastructure/async/producers/integration-queue.producer");
const workflow_queue_producer_1 = require("../../infrastructure/async/producers/workflow-queue.producer");
const queue_module_1 = require("../../infrastructure/async/queue.module");
let AdvancedLeadCreatedHandler = AdvancedLeadCreatedHandler_1 = class AdvancedLeadCreatedHandler {
    emailQueueProducer;
    notificationQueueProducer;
    analyticsQueueProducer;
    integrationQueueProducer;
    workflowQueueProducer;
    logger = new common_1.Logger(AdvancedLeadCreatedHandler_1.name);
    constructor(emailQueueProducer, notificationQueueProducer, analyticsQueueProducer, integrationQueueProducer, workflowQueueProducer) {
        this.emailQueueProducer = emailQueueProducer;
        this.notificationQueueProducer = notificationQueueProducer;
        this.analyticsQueueProducer = analyticsQueueProducer;
        this.integrationQueueProducer = integrationQueueProducer;
        this.workflowQueueProducer = workflowQueueProducer;
    }
    async handle(event) {
        this.logger.log(`Handling LeadCreatedEvent for lead: ${event.lead.id}`);
        try {
            await Promise.allSettled([
                this.handleWelcomeEmail(event),
                this.handleAssignmentNotifications(event),
                this.handleAnalyticsUpdates(event),
                this.handleIntegrationSync(event),
                this.handleWorkflowAutomation(event),
                this.handleDataEnrichment(event),
                this.handleComplianceChecks(event),
            ]);
            this.logger.log(`Successfully processed LeadCreatedEvent for lead: ${event.lead.id}`);
        }
        catch (error) {
            this.logger.error(`Failed to process LeadCreatedEvent for lead: ${event.lead.id}`, error);
        }
    }
    async handleWelcomeEmail(event) {
        if (!event.lead.contactInfo?.email) {
            this.logger.debug(`No email address for lead: ${event.lead.id}, skipping welcome email`);
            return;
        }
        try {
            await this.emailQueueProducer.sendWelcomeEmail({
                email: event.lead.contactInfo.email,
                name: event.lead.name,
                company: event.lead.contactInfo.company,
                source: event.source,
            }, {
                priority: queue_module_1.JobPriority.HIGH,
                delay: 5000,
            });
            this.logger.debug(`Queued welcome email for lead: ${event.lead.id}`);
        }
        catch (error) {
            this.logger.error(`Failed to queue welcome email for lead: ${event.lead.id}`, error);
        }
    }
    async handleAssignmentNotifications(event) {
        try {
            if (event.lead.assignedUserId) {
                await this.notificationQueueProducer.sendAssignmentNotification({
                    userId: event.lead.assignedUserId.toString(),
                    type: 'lead_assigned',
                    title: 'New Lead Assigned',
                    message: `You have been assigned a new lead: ${event.lead.name}`,
                    data: {
                        leadId: event.lead.id,
                        leadName: event.lead.name,
                        leadPriority: event.lead.priority?.value,
                        source: event.source,
                    },
                });
            }
            if (event.lead.teamId) {
                await this.notificationQueueProducer.sendTeamNotification({
                    teamId: event.lead.teamId.toString(),
                    type: 'new_lead_in_team',
                    title: 'New Lead in Team',
                    message: `A new lead has been added to your team: ${event.lead.name}`,
                    data: {
                        leadId: event.lead.id,
                        leadName: event.lead.name,
                        assignedUserId: event.lead.assignedUserId,
                    },
                });
            }
            this.logger.debug(`Queued assignment notifications for lead: ${event.lead.id}`);
        }
        catch (error) {
            this.logger.error(`Failed to queue assignment notifications for lead: ${event.lead.id}`, error);
        }
    }
    async handleAnalyticsUpdates(event) {
        try {
            await this.analyticsQueueProducer.calculateLeadScore({
                leadId: event.lead.id.toString(),
                factors: {
                    source: event.source,
                    hasEmail: !!event.lead.contactInfo?.email,
                    hasPhone: !!event.lead.contactInfo?.phone,
                    hasCompany: !!event.lead.contactInfo?.company,
                    priority: event.lead.priority?.value,
                    expectedRevenue: event.lead.expectedRevenue,
                },
            });
            await this.analyticsQueueProducer.updatePipelineMetrics({
                type: 'lead_created',
                leadId: event.lead.id.toString(),
                teamId: event.lead.teamId?.toString(),
                source: event.source,
                timestamp: event.occurredAt,
            });
            this.logger.debug(`Queued analytics updates for lead: ${event.lead.id}`);
        }
        catch (error) {
            this.logger.error(`Failed to queue analytics updates for lead: ${event.lead.id}`, error);
        }
    }
    async handleIntegrationSync(event) {
        try {
            await this.integrationQueueProducer.syncToOdoo({
                entityType: 'lead',
                entityId: event.lead.id.toString(),
                operation: 'create',
                data: event.getPayload().leadData,
            });
            if (event.shouldPublishExternally()) {
                await this.integrationQueueProducer.sendWebhook({
                    event: 'lead.created',
                    data: event.toJSON(),
                    endpoints: await this.getWebhookEndpoints('lead.created'),
                });
            }
            this.logger.debug(`Queued integration sync for lead: ${event.lead.id}`);
        }
        catch (error) {
            this.logger.error(`Failed to queue integration sync for lead: ${event.lead.id}`, error);
        }
    }
    async handleWorkflowAutomation(event) {
        try {
            if (!event.lead.assignedUserId && event.lead.teamId) {
                await this.workflowQueueProducer.startAutoAssignment({
                    leadId: event.lead.id,
                    teamId: event.lead.teamId,
                    criteria: {
                        source: event.source,
                        priority: event.lead.priority?.value,
                        expectedRevenue: event.lead.expectedRevenue,
                    },
                });
            }
            await this.workflowQueueProducer.startLeadNurturing({
                leadId: event.lead.id,
                nurtureType: this.determineNurtureType(event),
                schedule: this.getNurturingSchedule(event),
            });
            this.logger.debug(`Queued workflow automation for lead: ${event.lead.id}`);
        }
        catch (error) {
            this.logger.error(`Failed to queue workflow automation for lead: ${event.lead.id}`, error);
        }
    }
    async handleDataEnrichment(event) {
        try {
            const needsEnrichment = this.checkIfEnrichmentNeeded(event.lead);
            if (needsEnrichment) {
                await this.workflowQueueProducer.startDataEnrichment({
                    leadId: event.lead.id,
                    enrichmentTypes: this.getEnrichmentTypes(event.lead),
                    priority: event.lead.priority?.value === 'high' ? queue_module_1.JobPriority.HIGH : queue_module_1.JobPriority.NORMAL,
                });
                this.logger.debug(`Queued data enrichment for lead: ${event.lead.id}`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to queue data enrichment for lead: ${event.lead.id}`, error);
        }
    }
    async handleComplianceChecks(event) {
        try {
            await this.workflowQueueProducer.verifyGDPRConsent({
                leadId: event.lead.id,
                email: event.lead.contactInfo?.email,
                source: event.source,
                consentRequired: this.isGDPRConsentRequired(event),
            });
            await this.workflowQueueProducer.applyDataRetentionPolicy({
                leadId: event.lead.id,
                dataTypes: ['contact_info', 'activities', 'communications'],
                retentionPeriod: this.getRetentionPeriod(event),
            });
            this.logger.debug(`Queued compliance checks for lead: ${event.lead.id}`);
        }
        catch (error) {
            this.logger.error(`Failed to queue compliance checks for lead: ${event.lead.id}`, error);
        }
    }
    determineNurtureType(event) {
        if (event.lead.priority?.value === 'high')
            return 'aggressive';
        if (event.source === 'website')
            return 'educational';
        if (event.source === 'referral')
            return 'relationship';
        return 'standard';
    }
    getNurturingSchedule(event) {
        return ['immediate', '1_day', '3_days', '1_week', '2_weeks'];
    }
    checkIfEnrichmentNeeded(lead) {
        return !lead.contactInfo?.company ||
            !lead.contactInfo?.phone ||
            !lead.contactInfo?.website;
    }
    getEnrichmentTypes(lead) {
        const types = [];
        if (!lead.contactInfo?.company)
            types.push('company_info');
        if (!lead.contactInfo?.phone)
            types.push('contact_details');
        if (!lead.contactInfo?.website)
            types.push('social_profiles');
        return types;
    }
    isGDPRConsentRequired(event) {
        return event.source === 'website' || event.source === 'form';
    }
    getRetentionPeriod(event) {
        return 2555;
    }
    async getWebhookEndpoints(eventType) {
        return [
            'https://external-system.com/webhooks/crm',
            'https://analytics-platform.com/events',
        ];
    }
};
exports.AdvancedLeadCreatedHandler = AdvancedLeadCreatedHandler;
exports.AdvancedLeadCreatedHandler = AdvancedLeadCreatedHandler = AdvancedLeadCreatedHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, cqrs_1.EventsHandler)(lead_events_1.LeadCreatedEvent),
    __metadata("design:paramtypes", [email_queue_producer_1.EmailQueueProducer,
        notification_queue_producer_1.NotificationQueueProducer,
        analytics_queue_producer_1.AnalyticsQueueProducer,
        integration_queue_producer_1.IntegrationQueueProducer,
        workflow_queue_producer_1.WorkflowQueueProducer])
], AdvancedLeadCreatedHandler);
//# sourceMappingURL=advanced-lead-created.handler.js.map