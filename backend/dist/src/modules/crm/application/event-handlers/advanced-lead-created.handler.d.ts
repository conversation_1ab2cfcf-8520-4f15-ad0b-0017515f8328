import { IEventHandler } from '@nestjs/cqrs';
import { LeadCreatedEvent } from '../../domain/events/lead-events';
import { EmailQueueProducer } from '../../infrastructure/async/producers/email-queue.producer';
import { NotificationQueueProducer } from '../../infrastructure/async/producers/notification-queue.producer';
import { AnalyticsQueueProducer } from '../../infrastructure/async/producers/analytics-queue.producer';
import { IntegrationQueueProducer } from '../../infrastructure/async/producers/integration-queue.producer';
import { WorkflowQueueProducer } from '../../infrastructure/async/producers/workflow-queue.producer';
export declare class AdvancedLeadCreatedHandler implements IEventHandler<LeadCreatedEvent> {
    private readonly emailQueueProducer;
    private readonly notificationQueueProducer;
    private readonly analyticsQueueProducer;
    private readonly integrationQueueProducer;
    private readonly workflowQueueProducer;
    private readonly logger;
    constructor(emailQueueProducer: EmailQueueProducer, notificationQueueProducer: NotificationQueueProducer, analyticsQueueProducer: AnalyticsQueueProducer, integrationQueueProducer: IntegrationQueueProducer, workflowQueueProducer: WorkflowQueueProducer);
    handle(event: LeadCreatedEvent): Promise<void>;
    private handleWelcomeEmail;
    private handleAssignmentNotifications;
    private handleAnalyticsUpdates;
    private handleIntegrationSync;
    private handleWorkflowAutomation;
    private handleDataEnrichment;
    private handleComplianceChecks;
    private determineNurtureType;
    private getNurturingSchedule;
    private checkIfEnrichmentNeeded;
    private getEnrichmentTypes;
    private isGDPRConsentRequired;
    private getRetentionPeriod;
    private getWebhookEndpoints;
}
