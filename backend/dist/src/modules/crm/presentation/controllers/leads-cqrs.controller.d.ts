import { CommandBus, QueryBus } from '@nestjs/cqrs';
export declare class LeadsCqrsController {
    private readonly commandBus;
    private readonly queryBus;
    private readonly logger;
    constructor(commandBus: CommandBus, queryBus: QueryBus);
    createLead(createLeadDto: CreateLeadDto, user: any): Promise<CreateLeadResponse>;
    convertToOpportunity(leadId: number, convertDto: ConvertLeadDto, user: any): Promise<ConvertLeadResponse>;
    getPipelineAnalytics(analyticsQuery: PipelineAnalyticsQueryDto, user: any): Promise<PipelineAnalyticsResponse>;
}
interface CreateLeadDto {
    name: string;
    email?: string;
    phone?: string;
    company?: string;
    website?: string;
    address?: string;
    city?: string;
    country?: string;
    source?: string;
    type?: string;
    priority?: number;
    expectedRevenue?: number;
    probability?: number;
    description?: string;
    assignedUserId?: number;
    teamId?: number;
    campaignId?: number;
    sourceId?: number;
    mediumId?: number;
    tags?: string[];
    dateDeadline?: string;
}
interface ConvertLeadDto {
    expectedRevenue: number;
    probability?: number;
    partnerId?: number;
    stageId?: number;
    assignedUserId?: number;
    dateDeadline?: string;
    description?: string;
    reason?: string;
}
interface PipelineAnalyticsQueryDto {
    teamId?: number;
    userId?: number;
    dateFrom?: string;
    dateTo?: string;
    includeStageMetrics?: boolean;
    includeConversionRates?: boolean;
    includeBottlenecks?: boolean;
    includeForecast?: boolean;
    includeComparisons?: boolean;
    stageIds?: number[];
    priorityLevels?: string[];
    sources?: string[];
    groupBy?: 'stage' | 'team' | 'user' | 'source' | 'priority';
    timeGranularity?: 'day' | 'week' | 'month' | 'quarter';
}
interface CreateLeadResponse {
    success: boolean;
    message: string;
    data: {
        leadId: number;
        lead: any;
    };
    metadata: any;
}
interface ConvertLeadResponse {
    success: boolean;
    message: string;
    data: {
        leadId: number;
        opportunityId: number;
        opportunity: any;
    };
    metadata: any;
}
interface PipelineAnalyticsResponse {
    success: boolean;
    message: string;
    data: any;
    metadata: any;
}
export {};
