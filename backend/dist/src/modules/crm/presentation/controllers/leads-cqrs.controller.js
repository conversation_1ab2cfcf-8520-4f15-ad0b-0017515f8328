"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var LeadsCqrsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadsCqrsController = void 0;
const common_1 = require("@nestjs/common");
const cqrs_1 = require("@nestjs/cqrs");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../../../shared/infrastructure/guards/jwt-auth.guard");
const current_user_decorator_1 = require("../../../../shared/infrastructure/decorators/current-user.decorator");
const create_lead_command_1 = require("../../application/cqrs/commands/create-lead.command");
const convert_lead_to_opportunity_command_1 = require("../../application/cqrs/commands/convert-lead-to-opportunity.command");
const get_pipeline_analytics_query_1 = require("../../application/cqrs/queries/get-pipeline-analytics.query");
const lead_priority_vo_1 = require("../../domain/value-objects/lead-priority.vo");
const lead_type_vo_1 = require("../../domain/value-objects/lead-type.vo");
let LeadsCqrsController = LeadsCqrsController_1 = class LeadsCqrsController {
    commandBus;
    queryBus;
    logger = new common_1.Logger(LeadsCqrsController_1.name);
    constructor(commandBus, queryBus) {
        this.commandBus = commandBus;
        this.queryBus = queryBus;
    }
    async createLead(createLeadDto, user) {
        this.logger.log(`Creating lead: ${createLeadDto.name} by user: ${user.id}`);
        try {
            const command = new create_lead_command_1.CreateLeadCommand(createLeadDto.name, createLeadDto.email, createLeadDto.phone, createLeadDto.company, createLeadDto.website, createLeadDto.address, createLeadDto.city, createLeadDto.country, createLeadDto.source || 'website', createLeadDto.type ? lead_type_vo_1.LeadType.fromValue(createLeadDto.type) : lead_type_vo_1.LeadType.LEAD, createLeadDto.priority ? lead_priority_vo_1.LeadPriority.fromValue(createLeadDto.priority) : lead_priority_vo_1.LeadPriority.MEDIUM, createLeadDto.expectedRevenue, createLeadDto.probability, createLeadDto.description, createLeadDto.assignedUserId, createLeadDto.teamId, createLeadDto.campaignId, createLeadDto.sourceId, createLeadDto.mediumId, createLeadDto.tags || [], createLeadDto.dateDeadline ? new Date(createLeadDto.dateDeadline) : undefined, user.id, user.companyId);
            const result = await this.commandBus.execute(command);
            return {
                success: true,
                message: 'Lead created successfully',
                data: {
                    leadId: result.leadId,
                    lead: result.lead.toPlainObject(),
                },
                metadata: {
                    commandType: 'CreateLeadCommand',
                    executedAt: new Date().toISOString(),
                    executedBy: user.id,
                },
            };
        }
        catch (error) {
            this.logger.error(`Failed to create lead: ${createLeadDto.name}`, error);
            throw error;
        }
    }
    async convertToOpportunity(leadId, convertDto, user) {
        this.logger.log(`Converting lead ${leadId} to opportunity by user: ${user.id}`);
        try {
            const command = new convert_lead_to_opportunity_command_1.ConvertLeadToOpportunityCommand(leadId, convertDto.expectedRevenue, convertDto.probability || 10, convertDto.partnerId, convertDto.stageId, convertDto.assignedUserId, convertDto.dateDeadline ? new Date(convertDto.dateDeadline) : undefined, convertDto.description, user.id, convertDto.reason);
            const result = await this.commandBus.execute(command);
            return {
                success: true,
                message: 'Lead converted to opportunity successfully',
                data: {
                    leadId,
                    opportunityId: result.opportunityId,
                    opportunity: result.opportunity.toPlainObject(),
                },
                metadata: {
                    commandType: 'ConvertLeadToOpportunityCommand',
                    executedAt: new Date().toISOString(),
                    executedBy: user.id,
                },
            };
        }
        catch (error) {
            this.logger.error(`Failed to convert lead ${leadId} to opportunity`, error);
            throw error;
        }
    }
    async getPipelineAnalytics(analyticsQuery, user) {
        this.logger.log(`Getting pipeline analytics for user: ${user.id}`);
        try {
            const query = new get_pipeline_analytics_query_1.GetPipelineAnalyticsQuery(analyticsQuery.teamId, analyticsQuery.userId, analyticsQuery.dateFrom ? new Date(analyticsQuery.dateFrom) : undefined, analyticsQuery.dateTo ? new Date(analyticsQuery.dateTo) : undefined, analyticsQuery.includeStageMetrics !== false, analyticsQuery.includeConversionRates !== false, analyticsQuery.includeBottlenecks !== false, analyticsQuery.includeForecast !== false, analyticsQuery.includeComparisons || false, analyticsQuery.stageIds, analyticsQuery.priorityLevels, analyticsQuery.sources, analyticsQuery.groupBy, analyticsQuery.timeGranularity, user.id);
            const result = await this.queryBus.execute(query);
            return {
                success: true,
                message: 'Pipeline analytics retrieved successfully',
                data: result,
                metadata: {
                    queryType: 'GetPipelineAnalyticsQuery',
                    executedAt: new Date().toISOString(),
                    executedBy: user.id,
                    cacheKey: query.getCacheKey(),
                },
            };
        }
        catch (error) {
            this.logger.error('Failed to get pipeline analytics', error);
            throw error;
        }
    }
};
exports.LeadsCqrsController = LeadsCqrsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new lead (CQRS)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Lead created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], LeadsCqrsController.prototype, "createLead", null);
__decorate([
    (0, common_1.Post)(':id/convert-to-opportunity'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Convert lead to opportunity (CQRS)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Lead converted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Lead not found' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Lead cannot be converted' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], LeadsCqrsController.prototype, "convertToOpportunity", null);
__decorate([
    (0, common_1.Get)('analytics/pipeline'),
    (0, swagger_1.ApiOperation)({ summary: 'Get pipeline analytics (CQRS)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Pipeline analytics retrieved successfully' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], LeadsCqrsController.prototype, "getPipelineAnalytics", null);
exports.LeadsCqrsController = LeadsCqrsController = LeadsCqrsController_1 = __decorate([
    (0, swagger_1.ApiTags)('CRM - Leads (CQRS)'),
    (0, common_1.Controller)('api/v1/crm/leads-cqrs'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [cqrs_1.CommandBus,
        cqrs_1.QueryBus])
], LeadsCqrsController);
//# sourceMappingURL=leads-cqrs.controller.js.map