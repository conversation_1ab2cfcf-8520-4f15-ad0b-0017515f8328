{"name": "@nestjs/bullmq", "version": "11.0.3", "description": "Nest - modern, fast, powerful node.js web framework (@bullmq)", "homepage": "https://github.com/nestjs/bull", "bugs": {"url": "https://github.com/nestjs/bull/issues"}, "repository": {"type": "git", "url": "git+https://github.com/nestjs/bull.git"}, "license": "MIT", "author": "<PERSON><PERSON><PERSON>", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:e2e": "jest --config=e2e/jest-e2e.config.ts --detectOpenHandles --runInBand --forceExit"}, "dependencies": {"@nestjs/bull-shared": "^11.0.3", "tslib": "2.8.1"}, "devDependencies": {"@nestjs/common": "11.1.5", "@nestjs/core": "11.1.5", "@nestjs/platform-express": "11.1.5", "@nestjs/testing": "11.1.5", "@types/jest": "30.0.0", "@types/node": "22.16.5", "@types/reflect-metadata": "0.1.0", "bullmq": "5.56.5", "reflect-metadata": "0.2.2", "rxjs": "7.8.2"}, "peerDependencies": {"@nestjs/common": "^10.0.0 || ^11.0.0", "@nestjs/core": "^10.0.0 || ^11.0.0", "bullmq": "^3.0.0 || ^4.0.0 || ^5.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "bce1067da5f49cf431e220f1599cb7287f77a919"}