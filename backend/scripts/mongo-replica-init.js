/**
 * MongoDB Replica Set Initialization Script
 * This script initializes a single-node replica set for Event Sourcing transactions
 */

// Wait for MongoDB to be ready
sleep(2000);

print('🔧 Initializing MongoDB Replica Set for Event Sourcing...');

try {
  // Check if replica set is already initialized
  var status = rs.status();
  print('✅ Replica set already initialized:', status.set);
} catch (e) {
  print('📝 Replica set not initialized, creating new replica set...');
  
  // Initialize replica set with single node
  var config = {
    _id: 'rs0',
    version: 1,
    members: [
      {
        _id: 0,
        host: 'mongodb:27017',
        priority: 1
      }
    ]
  };
  
  try {
    var result = rs.initiate(config);
    print('✅ Replica set initialization result:', JSON.stringify(result));
    
    // Wait for replica set to be ready
    print('⏳ Waiting for replica set to become primary...');
    sleep(5000);
    
    // Check status
    var finalStatus = rs.status();
    print('📊 Final replica set status:', JSON.stringify(finalStatus, null, 2));
    
    print('🎉 MongoDB Replica Set initialized successfully!');
    print('💡 Event Sourcing transactions are now supported.');
    
  } catch (initError) {
    print('❌ Failed to initialize replica set:', initError);
  }
}

// Create Event Sourcing specific indexes
print('📚 Creating Event Sourcing indexes...');

// Switch to the application database
db = db.getSiblingDB('universal-odoo-adapter');

try {
  // Event Stream indexes
  db.eventstreams.createIndex({ "aggregateId": 1, "aggregateType": 1 });
  db.eventstreams.createIndex({ "aggregateId": 1, "aggregateVersion": 1 }, { unique: true });
  db.eventstreams.createIndex({ "eventType": 1 });
  db.eventstreams.createIndex({ "timestamp": 1 });
  db.eventstreams.createIndex({ "streamName": 1 });
  
  // Event Snapshots indexes
  db.eventsnapshots.createIndex({ "aggregateId": 1, "aggregateType": 1 });
  db.eventsnapshots.createIndex({ "aggregateId": 1, "version": 1 }, { unique: true });
  db.eventsnapshots.createIndex({ "createdAt": 1 });
  
  // Event Projections indexes
  db.eventprojections.createIndex({ "projectionName": 1 });
  db.eventprojections.createIndex({ "lastEventPosition": 1 });
  db.eventprojections.createIndex({ "updatedAt": 1 });
  
  // Event Subscriptions indexes
  db.eventsubscriptions.createIndex({ "subscriptionName": 1 }, { unique: true });
  db.eventsubscriptions.createIndex({ "eventTypes": 1 });
  db.eventsubscriptions.createIndex({ "isActive": 1 });
  
  // Event Saga State indexes
  db.eventsagastates.createIndex({ "sagaId": 1 }, { unique: true });
  db.eventsagastates.createIndex({ "sagaType": 1 });
  db.eventsagastates.createIndex({ "status": 1 });
  db.eventsagastates.createIndex({ "correlationId": 1 });
  
  // Lead Projections indexes (for CRM Event Sourcing)
  db.leadprojections.createIndex({ "leadId": 1 }, { unique: true });
  db.leadprojections.createIndex({ "status": 1 });
  db.leadprojections.createIndex({ "assignedUserId": 1 });
  db.leadprojections.createIndex({ "teamId": 1 });
  db.leadprojections.createIndex({ "source": 1 });
  db.leadprojections.createIndex({ "type": 1 });
  db.leadprojections.createIndex({ "priority": 1 });
  db.leadprojections.createIndex({ "tags": 1 });
  db.leadprojections.createIndex({ "createdAt": 1 });
  db.leadprojections.createIndex({ "updatedAt": 1 });
  db.leadprojections.createIndex({ "isDeleted": 1 });
  
  // Compound indexes for common queries
  db.leadprojections.createIndex({ "status": 1, "assignedUserId": 1 });
  db.leadprojections.createIndex({ "teamId": 1, "status": 1 });
  db.leadprojections.createIndex({ "isDeleted": 1, "status": 1 });
  db.leadprojections.createIndex({ "isDeleted": 1, "updatedAt": -1 });
  
  print('✅ Event Sourcing indexes created successfully!');
  
} catch (indexError) {
  print('⚠️ Warning: Some indexes may already exist:', indexError);
}

print('🚀 MongoDB is ready for Event Sourcing!');
print('📋 Summary:');
print('  ✅ Replica set initialized (rs0)');
print('  ✅ Transactions enabled');
print('  ✅ Event Sourcing indexes created');
print('  ✅ CRM projection indexes ready');
print('');
print('🔗 Connection string: mongodb://localhost:27018/universal-odoo-adapter?replicaSet=rs0');
print('💡 Use this connection string in your application for transaction support.');
