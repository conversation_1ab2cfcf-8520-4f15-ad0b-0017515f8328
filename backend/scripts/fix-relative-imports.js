#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * <PERSON><PERSON><PERSON> to convert relative imports to @ alias imports in CRM module
 * This ensures consistent import paths across the codebase
 */

// Mapping of relative paths to @ alias paths
const pathMappings = {
  // Domain layer imports
  '../../../../domain/': '@/modules/crm/domain/',
  '../../../domain/': '@/modules/crm/domain/',
  '../../domain/': '@/modules/crm/domain/',
  '../domain/': '@/modules/crm/domain/',
  
  // Infrastructure layer imports
  '../../../../infrastructure/': '@/modules/crm/infrastructure/',
  '../../../infrastructure/': '@/modules/crm/infrastructure/',
  '../../infrastructure/': '@/modules/crm/infrastructure/',
  '../infrastructure/': '@/modules/crm/infrastructure/',
  
  // Application layer imports
  '../../../../application/': '@/modules/crm/application/',
  '../../../application/': '@/modules/crm/application/',
  '../../application/': '@/modules/crm/application/',
  '../application/': '@/modules/crm/application/',
  
  // Presentation layer imports
  '../../../../presentation/': '@/modules/crm/presentation/',
  '../../../presentation/': '@/modules/crm/presentation/',
  '../../presentation/': '@/modules/crm/presentation/',
  '../presentation/': '@/modules/crm/presentation/',
  
  // Shared imports
  '../../../../shared/': '@/shared/',
  '../../../shared/': '@/shared/',
  '../../shared/': '@/shared/',
  '../shared/': '@/shared/',
  
  // Common imports
  '../../../../common/': '@/common/',
  '../../../common/': '@/common/',
  '../../common/': '@/common/',
  '../common/': '@/common/',
};

/**
 * Update imports in a single file
 */
function updateImportsInFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`  ⚠️  File not found: ${filePath}`);
    return false;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  let changesCount = 0;
  
  // Apply path mappings
  for (const [oldPath, newPath] of Object.entries(pathMappings)) {
    const regex = new RegExp(oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
    const matches = content.match(regex);
    
    if (matches) {
      content = content.replace(regex, newPath);
      updated = true;
      changesCount += matches.length;
    }
  }
  
  // Additional specific patterns for CRM module
  const specificPatterns = [
    // Fix imports within CRM module that should use @ alias
    {
      pattern: /from ['"]\.\.\/\.\.\/\.\.\/\.\.\/modules\/crm\//g,
      replacement: "from '@/modules/crm/"
    },
    {
      pattern: /from ['"]\.\.\/\.\.\/\.\.\/modules\/crm\//g,
      replacement: "from '@/modules/crm/"
    },
    {
      pattern: /from ['"]\.\.\/\.\.\/modules\/crm\//g,
      replacement: "from '@/modules/crm/"
    },
    {
      pattern: /from ['"]\.\.\/modules\/crm\//g,
      replacement: "from '@/modules/crm/"
    }
  ];
  
  for (const { pattern, replacement } of specificPatterns) {
    const matches = content.match(pattern);
    if (matches) {
      content = content.replace(pattern, replacement);
      updated = true;
      changesCount += matches.length;
    }
  }
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✅ Updated ${changesCount} imports in ${path.relative(process.cwd(), filePath)}`);
    return true;
  }
  
  return false;
}

/**
 * Update imports in all files in a directory
 */
function updateImportsInDirectory(dirPath, stats = { filesProcessed: 0, filesUpdated: 0 }) {
  if (!fs.existsSync(dirPath)) {
    console.log(`  ⚠️  Directory not found: ${dirPath}`);
    return stats;
  }
  
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and other irrelevant directories
      if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
        updateImportsInDirectory(fullPath, stats);
      }
    } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
      stats.filesProcessed++;
      if (updateImportsInFile(fullPath)) {
        stats.filesUpdated++;
      }
    }
  }
  
  return stats;
}

/**
 * Main execution
 */
function main() {
  console.log('🔄 Starting relative imports to @ alias conversion...\n');
  
  const crmModulePath = path.join(__dirname, '../src/modules/crm');
  
  if (!fs.existsSync(crmModulePath)) {
    console.error('❌ CRM module directory not found:', crmModulePath);
    process.exit(1);
  }
  
  console.log('📁 Processing CRM module:', crmModulePath);
  
  const stats = updateImportsInDirectory(crmModulePath);
  
  console.log('\n📊 Summary:');
  console.log(`  📄 Files processed: ${stats.filesProcessed}`);
  console.log(`  ✅ Files updated: ${stats.filesUpdated}`);
  console.log(`  ⏭️  Files unchanged: ${stats.filesProcessed - stats.filesUpdated}`);
  
  if (stats.filesUpdated > 0) {
    console.log('\n🎉 Import path conversion completed successfully!');
    console.log('💡 Tip: Run `npm run build` to verify all imports are working correctly.');
  } else {
    console.log('\n✨ All imports are already using @ alias - no changes needed!');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  updateImportsInFile,
  updateImportsInDirectory,
  pathMappings
};
