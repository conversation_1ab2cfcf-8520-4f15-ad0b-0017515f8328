# Zenoo - Universal UI Platform for Odoo

**The Modern, Universal Interface for Every Odoo Instance**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![NestJS](https://img.shields.io/badge/NestJS-E0234E?style=flat&logo=nestjs&logoColor=white)](https://nestjs.com/)
[![React Native](https://img.shields.io/badge/React_Native-20232A?style=flat&logo=react&logoColor=61DAFB)](https://reactnative.dev/)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=flat&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![Odoo](https://img.shields.io/badge/Odoo-714B67?style=flat&logo=odoo&logoColor=white)](https://www.odoo.com/)

---

## 🌟 **What is Zenoo?**

Zenoo is a **Universal UI Platform** that transforms how the world interacts with Odoo by providing modern, consistent, and intuitive interfaces for **ANY** Odoo instance - regardless of version, deployment method, or configuration.

### 🎯 **The Problem We Solve**

- **Fragmented Experience**: Different UI/UX across Odoo versions
- **Mobile Limitations**: Poor mobile experience in standard Odoo
- **Multi-Instance Chaos**: Managing multiple Odoo instances is complex
- **Training Costs**: High costs to train users on different Odoo versions
- **Version Lock-in**: Businesses stuck on old Odoo versions due to UI familiarity

### ✨ **The Zenoo Solution**

- **Universal Compatibility**: One interface for ALL Odoo versions (13-18+)
- **Mobile-First Design**: Native mobile experience across all platforms
- **Multi-Tenant Architecture**: Manage unlimited Odoo instances seamlessly
- **Future-Proof**: Automatically adapts to new Odoo releases
- **Enterprise-Grade**: Scalable, secure, and reliable

---

## 🏗️ **Universal Platform Architecture**

```
┌─────────────────────────────────────────────────────────────┐
│                    ZENOO UNIVERSAL UI                      │
│              (Mobile, Web, Desktop Clients)                │
└─────────────────────────┬───────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────┐
│                 UNIVERSAL API LAYER                        │
│              (Consistent REST/GraphQL API)                 │
└─────────────────────────┬───────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────┐
│               UNIVERSAL ODOO ADAPTER                       │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │   V13       │    V15      │    V17      │    V18+     │  │
│  │  Adapter    │   Adapter   │   Adapter   │   Adapter   │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │  XML-RPC    │  JSON-RPC   │  REST API   │  GraphQL    │  │
│  │ (Universal) │ (Modern)    │ (Native)    │ (Future)    │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
└─────────────────────────┬───────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────┐
│                  ODOO INSTANCES                            │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ Customer A  │ Customer B  │ Customer C  │ Customer D  │  │
│  │ Odoo 15     │ Odoo 17     │ Odoo 18     │ Odoo 13     │  │
│  │ Community   │ Enterprise  │ Cloud       │ On-premise  │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

---

## 🚀 **Key Features**

### 🌐 **Universal Compatibility**
- **Multi-Version Support**: Seamlessly works with Odoo 13, 15, 17, 18+ and future versions
- **Automatic Detection**: Intelligent version detection and adapter selection
- **Protocol Optimization**: Automatically selects best communication protocol
- **Field Mapping**: Handles schema differences between versions
- **Graceful Fallback**: Ensures compatibility with any Odoo configuration

### 🏢 **Enterprise Multi-Tenancy**
- **Unlimited Instances**: Connect to hundreds of different Odoo instances
- **Tenant Isolation**: Secure separation between organizations
- **Connection Pooling**: Enterprise-grade pooling supports 1000+ concurrent users
- **Performance Monitoring**: Real-time metrics and health monitoring

### 📱 **Cross-Platform Experience**
- **Mobile Apps**: Native iOS and Android applications (React Native)
- **Web Application**: Progressive Web App with offline capabilities
- **Desktop Apps**: Cross-platform desktop applications (Electron)
- **Consistent UX**: Same interface and experience across all platforms

### 🔐 **Enterprise Security**
- **Multi-Method Authentication**: Password, API Key, OAuth2, SSO support
- **Role-Based Access Control**: Respects Odoo permissions and security
- **Data Encryption**: End-to-end encryption for all communications
- **Audit Logging**: Comprehensive activity tracking and compliance

---

## 📦 **Project Structure**

```
zenoo/
├── backend/                    # Universal API Backend (NestJS)
│   ├── src/
│   │   ├── modules/           # Domain modules (CRM, Sales, Inventory)
│   │   ├── shared/            # Universal Odoo adapter & shared services
│   │   └── infrastructure/    # Database, protocols, version adapters
│   ├── README.md              # Backend documentation
│   └── package.json
├── mobile/                     # Mobile Apps (React Native)
│   ├── src/
│   │   ├── screens/           # Mobile screens and navigation
│   │   ├── components/        # Reusable UI components
│   │   └── services/          # API integration and business logic
│   └── package.json
├── web/                        # Web Application (React)
│   ├── src/
│   │   ├── pages/             # Web pages and routing
│   │   ├── components/        # Shared UI components
│   │   └── services/          # API integration
│   └── package.json
├── docs/                       # Documentation
│   ├── ARCHITECTURE.md         # Technical architecture
│   ├── BUSINESS_MODEL.md       # Business model and strategy
│   └── ROADMAP.md             # Product roadmap
├── PROJECT_OVERVIEW.md         # High-level project overview
└── README.md                   # This file
```

---

## 🚀 **Quick Start**

### 📋 **Prerequisites**
- **Node.js** 18+ (for backend and web development)
- **React Native CLI** (for mobile development)
- **Existing Odoo Instance** - Any version from 13 to 18+

### ⚡ **Backend Setup**
```bash
# Clone the repository
git clone https://github.com/your-org/zenoo.git
cd zenoo/backend

# Install dependencies
npm install

# Configure environment
cp .env.example .env
# Edit .env with your settings

# Start the Universal API
npm run start:dev

# 🎉 API running at http://localhost:3000
```

### 📱 **Mobile App Setup**
```bash
# Navigate to mobile directory
cd ../mobile

# Install dependencies
npm install

# iOS setup (macOS only)
cd ios && pod install && cd ..

# Start Metro bundler
npm start

# Run on iOS
npm run ios

# Run on Android
npm run android
```

### 🌐 **Web App Setup**
```bash
# Navigate to web directory
cd ../web

# Install dependencies
npm install

# Start development server
npm start

# 🎉 Web app running at http://localhost:3001
```

---

## 📚 **Documentation**

- **[Project Overview](PROJECT_OVERVIEW.md)** - Vision, goals, and value proposition
- **[Architecture Guide](ARCHITECTURE.md)** - Technical architecture and design
- **[Business Model](BUSINESS_MODEL.md)** - Market opportunity and strategy
- **[Product Roadmap](ROADMAP.md)** - Development timeline and future plans
- **[Backend Documentation](backend/README.md)** - API and backend details

---

## 🤝 **Contributing**

We welcome contributions from the community! Please read our contributing guidelines and code of conduct before submitting pull requests.

### 🛠️ **Development Workflow**
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests and documentation
5. Submit a pull request

---

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 🌟 **Why Zenoo?**

**Zenoo isn't just another Odoo mobile app - it's the Universal UI Platform that will define how the world interacts with Odoo for years to come.**

- ✅ **Universal**: Works with ANY Odoo instance
- ✅ **Modern**: Beautiful, intuitive user experience
- ✅ **Scalable**: Enterprise-grade architecture
- ✅ **Future-Proof**: Adapts to Odoo evolution
- ✅ **Open**: MIT licensed and community-driven

---

**Ready to transform your Odoo experience? [Get started today!](backend/README.md)** 🚀
